# 日志系统最佳实践指南

## 🎯 目标

本指南提供实用的日志编写技巧和最佳实践，帮助开发者编写高质量、高效率的日志代码。

## 🚀 快速开始

### 1. 基础日志记录
```dart
// 导入对应模块的日志工具类
import 'package:financial_app_market/financial_app_market.dart';

class StockService {
  Future<List<Stock>> getStocks() async {
    MarketLoggerUtils.info('📊 开始获取股票列表');
    
    try {
      final stocks = await _apiService.getStocks();
      
      MarketLoggerUtils.info('📊 股票列表获取成功', metadata: {
        'count': stocks.length,
        'response_time_ms': responseTime,
      });
      
      return stocks;
    } catch (e) {
      MarketLoggerUtils.error('❌ 股票列表获取失败', 
        error: e,
        metadata: {'endpoint': '/api/stocks'},
      );
      rethrow;
    }
  }
}
```

### 2. 专业业务方法
```dart
// 使用专门的业务日志方法
AuthLoggerUtils.logLogin(
  username: username,
  success: true,
  loginMethod: 'password',
);

MarketLoggerUtils.logStockDataFetch(
  symbols: ['AAPL', 'GOOGL'],
  success: true,
  duration: Duration(milliseconds: 150),
);
```

## 💡 实用技巧

### 1. 性能优化技巧

#### 避免字符串拼接
```dart
// ❌ 低效的方式
MarketLoggerUtils.info('获取了' + stocks.length.toString() + '只股票');

// ✅ 高效的方式
MarketLoggerUtils.info('📊 获取股票数据完成', metadata: {
  'count': stocks.length,
});
```

#### 条件日志记录
```dart
// ✅ 在高频操作中使用条件日志
if (LogConfigManager.currentConfig.showDebugLogs) {
  MarketLoggerUtils.debug('处理股票数据', metadata: {
    'symbol': symbol,
    'price': price,
  });
}
```

#### 批量日志记录
```dart
// ✅ 批量处理时的日志策略
void processStocks(List<Stock> stocks) {
  MarketLoggerUtils.info('📊 开始批量处理股票', metadata: {
    'total_count': stocks.length,
  });
  
  int processed = 0;
  int errors = 0;
  
  for (final stock in stocks) {
    try {
      _processStock(stock);
      processed++;
      
      // 每处理100个记录一次进度
      if (processed % 100 == 0) {
        MarketLoggerUtils.debug('📊 批量处理进度', metadata: {
          'processed': processed,
          'total': stocks.length,
          'progress': '${(processed / stocks.length * 100).toStringAsFixed(1)}%',
        });
      }
    } catch (e) {
      errors++;
      MarketLoggerUtils.error('❌ 股票处理失败', 
        error: e,
        metadata: {'symbol': stock.symbol},
      );
    }
  }
  
  MarketLoggerUtils.info('📊 批量处理完成', metadata: {
    'total': stocks.length,
    'processed': processed,
    'errors': errors,
    'success_rate': '${((processed / stocks.length) * 100).toStringAsFixed(1)}%',
  });
}
```

### 2. 错误处理最佳实践

#### 分层错误日志
```dart
// Service层 - 记录业务错误
class StockService {
  Future<Stock> getStock(String symbol) async {
    try {
      return await _repository.getStock(symbol);
    } catch (e) {
      MarketLoggerUtils.error('📊 股票数据获取失败', 
        error: e,
        metadata: {
          'symbol': symbol,
          'service': 'StockService',
        },
      );
      rethrow;
    }
  }
}

// Repository层 - 记录数据访问错误
class StockRepository {
  Future<Stock> getStock(String symbol) async {
    try {
      return await _apiService.getStock(symbol);
    } catch (e) {
      MarketLoggerUtils.error('🌐 API调用失败', 
        error: e,
        metadata: {
          'symbol': symbol,
          'endpoint': '/api/stock/$symbol',
          'repository': 'StockRepository',
        },
      );
      rethrow;
    }
  }
}
```

#### 错误恢复日志
```dart
Future<List<Stock>> getStocksWithRetry(List<String> symbols) async {
  int retryCount = 0;
  const maxRetries = 3;
  
  while (retryCount < maxRetries) {
    try {
      final stocks = await _getStocks(symbols);
      
      if (retryCount > 0) {
        MarketLoggerUtils.info('✅ 重试成功', metadata: {
          'retry_count': retryCount,
          'symbols_count': symbols.length,
        });
      }
      
      return stocks;
    } catch (e) {
      retryCount++;
      
      if (retryCount < maxRetries) {
        MarketLoggerUtils.warning('⚠️ 请求失败，准备重试', metadata: {
          'retry_count': retryCount,
          'max_retries': maxRetries,
          'error': e.toString(),
        });
        
        await Future.delayed(Duration(seconds: retryCount * 2));
      } else {
        MarketLoggerUtils.error('❌ 重试次数耗尽，请求最终失败', 
          error: e,
          metadata: {
            'total_retries': retryCount,
            'symbols_count': symbols.length,
          },
        );
        rethrow;
      }
    }
  }
  
  throw Exception('Unreachable code');
}
```

### 3. 异步操作日志

#### 使用请求ID关联日志
```dart
class TradeService {
  Future<Order> createOrder(OrderRequest request) async {
    final requestId = _generateRequestId();
    
    TradeLoggerUtils.info('💰 开始创建订单', metadata: {
      'request_id': requestId,
      'symbol': request.symbol,
      'quantity': request.quantity,
    });
    
    try {
      // 验证订单
      await _validateOrder(request, requestId);
      
      // 创建订单
      final order = await _createOrder(request, requestId);
      
      // 发送通知
      _sendOrderNotification(order, requestId);
      
      TradeLoggerUtils.info('💰 订单创建成功', metadata: {
        'request_id': requestId,
        'order_id': order.id,
        'symbol': order.symbol,
      });
      
      return order;
    } catch (e) {
      TradeLoggerUtils.error('❌ 订单创建失败', 
        error: e,
        metadata: {
          'request_id': requestId,
          'symbol': request.symbol,
        },
      );
      rethrow;
    }
  }
  
  Future<void> _validateOrder(OrderRequest request, String requestId) async {
    TradeLoggerUtils.debug('🔍 验证订单参数', metadata: {
      'request_id': requestId,
      'validation_step': 'parameters',
    });
    
    // 验证逻辑...
  }
}
```

### 4. 配置和环境相关

#### 环境感知日志
```dart
class ConfigService {
  void loadConfig() {
    final env = _getCurrentEnvironment();
    
    if (env == 'development') {
      // 开发环境显示详细配置
      MarketLoggerUtils.debug('⚙️ 加载开发环境配置', metadata: {
        'config_file': 'config.dev.json',
        'debug_mode': true,
        'api_endpoint': apiEndpoint,
      });
    } else {
      // 生产环境只显示基本信息
      MarketLoggerUtils.info('⚙️ 加载生产环境配置', metadata: {
        'environment': env,
        'config_version': configVersion,
      });
    }
  }
}
```

#### 功能开关日志
```dart
class FeatureToggleService {
  bool isFeatureEnabled(String featureName) {
    final enabled = _checkFeatureFlag(featureName);
    
    MarketLoggerUtils.debug('🔧 功能开关检查', metadata: {
      'feature': featureName,
      'enabled': enabled,
      'user_id': _getCurrentUserId(),
    });
    
    return enabled;
  }
}
```

## 📊 监控和分析

### 1. 性能监控
```dart
class PerformanceLogger {
  static Future<T> measureOperation<T>(
    String operationName,
    Future<T> Function() operation, {
    Map<String, dynamic>? metadata,
  }) async {
    final stopwatch = Stopwatch()..start();
    
    MarketLoggerUtils.debug('⏱️ 开始性能测量', metadata: {
      'operation': operationName,
      ...?metadata,
    });
    
    try {
      final result = await operation();
      
      stopwatch.stop();
      
      MarketLoggerUtils.info('📈 性能测量完成', metadata: {
        'operation': operationName,
        'duration_ms': stopwatch.elapsedMilliseconds,
        'success': true,
        ...?metadata,
      });
      
      return result;
    } catch (e) {
      stopwatch.stop();
      
      MarketLoggerUtils.error('📉 操作执行失败', 
        error: e,
        metadata: {
          'operation': operationName,
          'duration_ms': stopwatch.elapsedMilliseconds,
          'success': false,
          ...?metadata,
        },
      );
      
      rethrow;
    }
  }
}

// 使用示例
final stocks = await PerformanceLogger.measureOperation(
  'getStockList',
  () => stockService.getStocks(),
  metadata: {'user_id': userId},
);
```

### 2. 业务指标记录
```dart
class BusinessMetricsLogger {
  static void logUserAction(String action, {
    required String userId,
    Map<String, dynamic>? metadata,
  }) {
    MarketLoggerUtils.info('👤 用户行为记录', metadata: {
      'action': action,
      'user_id': userId,
      'timestamp': DateTime.now().toIso8601String(),
      ...?metadata,
    });
  }
  
  static void logBusinessEvent(String event, {
    Map<String, dynamic>? metadata,
  }) {
    MarketLoggerUtils.info('📊 业务事件记录', metadata: {
      'event': event,
      'timestamp': DateTime.now().toIso8601String(),
      ...?metadata,
    });
  }
}

// 使用示例
BusinessMetricsLogger.logUserAction('stock_search', 
  userId: userId,
  metadata: {
    'search_term': searchTerm,
    'results_count': results.length,
  },
);
```

## 🔧 调试技巧

### 1. 条件调试日志
```dart
class DebugHelper {
  static void debugLog(String message, {
    Map<String, dynamic>? metadata,
    String? userId,
  }) {
    // 只为特定用户或条件启用调试日志
    if (_shouldEnableDebugFor(userId)) {
      MarketLoggerUtils.debug('🐛 调试信息: $message', metadata: metadata);
    }
  }
  
  static bool _shouldEnableDebugFor(String? userId) {
    // 可以基于用户ID、环境变量或配置来决定
    return userId == 'debug_user' || 
           _isDebugEnvironment() ||
           _isDebugModeEnabled();
  }
}
```

### 2. 临时调试标记
```dart
class TempDebugLogger {
  static const bool _tempDebugEnabled = true; // 临时调试开关
  
  static void tempLog(String message, {Map<String, dynamic>? metadata}) {
    if (_tempDebugEnabled) {
      MarketLoggerUtils.debug('🚧 临时调试: $message', metadata: metadata);
    }
  }
}

// 使用完毕后记得关闭或删除
```

## 📋 检查清单

### 代码提交前检查
- [ ] 是否使用了正确的日志工具类
- [ ] 是否选择了合适的日志级别
- [ ] 是否包含了必要的元数据
- [ ] 是否过滤了敏感信息
- [ ] 是否移除了临时调试日志
- [ ] 是否考虑了性能影响

### 生产部署前检查
- [ ] 是否配置了合适的日志级别
- [ ] 是否启用了日志聚合和监控
- [ ] 是否设置了告警阈值
- [ ] 是否测试了日志导出功能

---

*持续改进，让日志成为开发和运维的得力助手*
