# 团队日志编写规范

## 📋 概述

本文档定义了项目中日志编写的标准和最佳实践，确保团队成员能够编写一致、有用且易于维护的日志代码。

## 🎯 日志编写原则

### 1. 清晰性原则
- 日志消息应该清晰、简洁、易于理解
- 使用有意义的描述，避免技术术语过多
- 包含足够的上下文信息

### 2. 一致性原则
- 使用统一的日志格式和命名约定
- 相同类型的操作使用相似的日志消息
- 保持日志级别的一致性

### 3. 有用性原则
- 记录对调试和监控有价值的信息
- 避免记录无用或冗余的信息
- 包含必要的元数据

### 4. 安全性原则
- 绝不记录敏感信息（密码、token、个人信息等）
- 使用日志过滤功能自动处理敏感数据
- 遵循数据保护法规

## 📊 日志级别使用指南

### DEBUG 级别
**用途**: 详细的调试信息，仅在开发环境显示

**使用场景**:
- 函数进入和退出
- 变量值的详细信息
- 算法执行步骤

**示例**:
```dart
MarketLoggerUtils.debug('开始处理股票数据', metadata: {
  'symbols': symbols,
  'data_count': rawData.length,
});
```

### INFO 级别
**用途**: 一般信息，记录重要的业务操作

**使用场景**:
- 用户操作记录
- 系统状态变化
- 重要业务流程完成

**示例**:
```dart
AuthLoggerUtils.info('用户登录成功', metadata: {
  'user_id': userId,
  'login_method': 'password',
});
```

### WARNING 级别
**用途**: 警告信息，需要注意但不影响功能

**使用场景**:
- 配置问题
- 性能问题
- 兼容性问题

**示例**:
```dart
MarketLoggerUtils.warning('API响应时间较长', metadata: {
  'endpoint': '/api/stocks',
  'response_time_ms': 2500,
  'threshold_ms': 1000,
});
```

### ERROR 级别
**用途**: 错误信息，需要立即处理

**使用场景**:
- 异常捕获
- 业务逻辑错误
- 系统故障

**示例**:
```dart
TradeLoggerUtils.error('订单创建失败', 
  error: exception,
  metadata: {
    'order_data': orderData,
    'user_id': userId,
  },
);
```

## 🔧 模块化日志使用规范

### 1. 导入规范
```dart
// ✅ 正确的导入方式
import 'package:financial_app_market/financial_app_market.dart';

// ❌ 错误的导入方式
import 'package:financial_app_core/src/logger/app_logger.dart';
```

### 2. 调用规范
```dart
// ✅ 推荐的调用方式
MarketLoggerUtils.info('股票数据获取成功');

// ✅ 带元数据的调用
MarketLoggerUtils.info('股票数据获取成功', metadata: {
  'symbols': ['AAPL', 'GOOGL'],
  'count': 2,
});

// ❌ 避免的调用方式
AppLogger.logModule('Market', LogLevel.info, '股票数据获取成功');
```

### 3. 专业方法使用
```dart
// ✅ 使用专业的业务方法
AuthLoggerUtils.logLogin(username: 'user123', success: true);
MarketLoggerUtils.logStockDataFetch(symbols: ['AAPL'], success: true);

// ✅ 通用方法作为补充
MarketLoggerUtils.info('自定义业务逻辑完成');
```

## 📝 日志消息编写规范

### 1. 消息格式
- 使用中文描述，便于团队理解
- 开头使用表情符号增强可读性
- 动词在前，描述具体操作

**格式模板**:
```
[表情符号] [动作] [对象] [结果/状态]
```

**示例**:
```dart
// ✅ 好的消息格式
'📊 获取股票数据成功'
'🔐 用户登录验证失败'
'💰 订单创建完成'
'⚠️ API响应时间超过阈值'

// ❌ 避免的消息格式
'success'
'error occurred'
'data processing'
```

### 2. 表情符号使用指南

| 类别 | 表情符号 | 用途 |
|------|----------|------|
| 数据操作 | 📊 📈 📉 | 数据获取、分析、统计 |
| 用户操作 | 👤 🔐 🚪 | 登录、注册、权限 |
| 交易操作 | 💰 💳 📋 | 订单、支付、交易 |
| 网络操作 | 🌐 📡 🔗 | API调用、连接 |
| 系统操作 | ⚙️ 🔧 🔄 | 配置、初始化、重启 |
| 警告信息 | ⚠️ 🚨 ❗ | 警告、告警、注意 |
| 错误信息 | ❌ 💥 🚫 | 错误、失败、禁止 |
| 成功信息 | ✅ 🎉 ✨ | 成功、完成、优化 |

### 3. 元数据使用规范

**必须包含的元数据**:
- 用户ID（如果涉及用户操作）
- 请求ID或事务ID（如果有）
- 时间戳（自动添加）
- 相关的业务对象ID

**推荐包含的元数据**:
- 性能指标（响应时间、数据量等）
- 配置参数
- 环境信息

**示例**:
```dart
MarketLoggerUtils.info('📊 股票数据获取成功', metadata: {
  'user_id': userId,
  'request_id': requestId,
  'symbols': symbols,
  'data_count': data.length,
  'response_time_ms': responseTime,
  'cache_hit': cacheHit,
});
```

## 🚫 日志编写禁忌

### 1. 绝对禁止
- 记录密码、token、API密钥等敏感信息
- 记录完整的用户个人信息
- 记录信用卡号、银行账号等金融信息
- 在循环中记录大量重复日志

### 2. 强烈不推荐
- 使用print()语句代替正式日志
- 记录过于详细的技术细节
- 使用英文和中文混合的消息
- 记录无意义的调试信息

### 3. 需要注意
- 避免在高频调用的函数中记录过多日志
- 注意日志对性能的影响
- 避免记录可能导致内存泄漏的大对象

## 🔍 Code Review 检查清单

在代码审查时，请检查以下日志相关项目：

### ✅ 必须检查项
- [ ] 是否使用了正确的模块化日志工具类
- [ ] 是否选择了合适的日志级别
- [ ] 是否包含了必要的元数据
- [ ] 是否过滤了敏感信息
- [ ] 日志消息是否清晰易懂

### ✅ 推荐检查项
- [ ] 是否使用了合适的表情符号
- [ ] 是否遵循了消息格式规范
- [ ] 是否使用了专业的业务方法
- [ ] 错误日志是否包含了足够的调试信息
- [ ] 是否避免了性能问题

## 📚 常见问题解答

### Q: 什么时候应该记录日志？
A: 记录所有重要的业务操作、错误情况、性能问题和用户行为。避免记录过于频繁的技术细节。

### Q: 如何处理循环中的日志？
A: 避免在循环内记录大量日志。可以在循环开始和结束时记录，或者使用计数器定期记录。

### Q: 如何记录异步操作的日志？
A: 使用请求ID或事务ID来关联异步操作的多个日志条目。

### Q: 生产环境应该使用什么日志级别？
A: 生产环境建议使用INFO及以上级别，避免DEBUG级别的日志影响性能。

### Q: 如何处理第三方库的日志？
A: 配置第三方库使用适当的日志级别，避免过多的第三方日志干扰业务日志。

## 🎓 培训和学习资源

### 新人培训清单
- [ ] 阅读本规范文档
- [ ] 学习各模块的日志工具类使用
- [ ] 练习编写符合规范的日志代码
- [ ] 参与代码审查，学习最佳实践

### 进阶学习
- [ ] 学习日志聚合和分析功能
- [ ] 了解性能监控和告警机制
- [ ] 掌握日志导出和分析工具
- [ ] 学习日志系统的配置和优化

---

*本规范将根据项目发展和团队反馈持续更新*
