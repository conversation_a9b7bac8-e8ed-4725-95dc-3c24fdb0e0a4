# 新人日志系统培训指南

## 🎓 培训目标

通过本培训，新人将能够：
- 理解项目日志系统的架构和设计理念
- 熟练使用各模块的日志工具类
- 编写符合团队规范的日志代码
- 掌握日志调试和问题排查技巧

## 📚 培训大纲

### 第一阶段：基础概念 (30分钟)
1. 日志系统概述
2. 日志级别和使用场景
3. 模块化日志架构
4. 安全和隐私考虑

### 第二阶段：实践操作 (45分钟)
1. 基础日志记录
2. 错误处理和异常日志
3. 性能监控日志
4. 业务日志记录

### 第三阶段：高级功能 (30分钟)
1. 日志配置管理
2. 日志聚合和分析
3. 监控和告警
4. 调试技巧

### 第四阶段：实战练习 (45分钟)
1. 代码审查练习
2. 问题排查练习
3. 性能优化练习
4. 最佳实践总结

## 🚀 第一阶段：基础概念

### 1.1 日志系统概述

我们的日志系统采用模块化设计，每个业务模块都有专用的日志管理器：

```
financial_app_core (核心日志系统)
├── AppLogger (基础日志功能)
├── LogConfigManager (配置管理)
├── LogAggregator (日志聚合)
└── LogMonitoringDashboard (监控面板)

各业务模块的专用日志工具：
├── MarketLoggerUtils (市场模块)
├── AuthLoggerUtils (认证模块)
├── TradeLoggerUtils (交易模块)
├── PortfolioLoggerUtils (投资组合)
└── ... (其他模块)
```

### 1.2 日志级别详解

| 级别 | 用途 | 生产环境 | 示例场景 |
|------|------|----------|----------|
| DEBUG | 详细调试信息 | ❌ 不显示 | 函数参数、算法步骤 |
| INFO | 一般业务信息 | ✅ 显示 | 用户操作、状态变化 |
| WARNING | 警告信息 | ✅ 显示 | 性能问题、配置问题 |
| ERROR | 错误信息 | ✅ 显示 | 异常、失败操作 |

### 1.3 安全考虑

**绝对禁止记录的信息**：
- 用户密码
- API密钥和Token
- 信用卡号、银行账号
- 身份证号、社保号
- 其他个人敏感信息

**系统会自动过滤包含以下关键词的字段**：
`password`, `token`, `secret`, `key`, `auth`, `pin`, `card`, `ssn` 等

## 🛠️ 第二阶段：实践操作

### 2.1 基础日志记录练习

**练习1：简单日志记录**
```dart
// 任务：为股票查询功能添加日志
class StockSearchService {
  Future<List<Stock>> searchStocks(String keyword) async {
    // TODO: 添加开始日志
    
    try {
      final results = await _apiService.search(keyword);
      
      // TODO: 添加成功日志，包含结果数量
      
      return results;
    } catch (e) {
      // TODO: 添加错误日志
      rethrow;
    }
  }
}
```

**参考答案**：
```dart
class StockSearchService {
  Future<List<Stock>> searchStocks(String keyword) async {
    MarketLoggerUtils.info('🔍 开始搜索股票', metadata: {
      'keyword': keyword,
    });
    
    try {
      final results = await _apiService.search(keyword);
      
      MarketLoggerUtils.info('🔍 股票搜索完成', metadata: {
        'keyword': keyword,
        'results_count': results.length,
      });
      
      return results;
    } catch (e) {
      MarketLoggerUtils.error('❌ 股票搜索失败', 
        error: e,
        metadata: {'keyword': keyword},
      );
      rethrow;
    }
  }
}
```

### 2.2 错误处理练习

**练习2：多层错误处理**
```dart
// 任务：为用户登录流程添加完整的错误处理日志
class AuthService {
  Future<User> login(String username, String password) async {
    // TODO: 添加登录开始日志（注意不要记录密码）
    
    try {
      // 验证用户名密码
      final isValid = await _validateCredentials(username, password);
      if (!isValid) {
        // TODO: 添加验证失败日志
        throw AuthException('Invalid credentials');
      }
      
      // 生成token
      final token = await _generateToken(username);
      
      // 获取用户信息
      final user = await _getUserInfo(username);
      
      // TODO: 添加登录成功日志
      
      return user;
    } catch (e) {
      // TODO: 添加登录失败日志
      rethrow;
    }
  }
}
```

### 2.3 性能监控练习

**练习3：API调用性能监控**
```dart
// 任务：为API调用添加性能监控日志
class MarketDataService {
  Future<List<Stock>> getMarketData() async {
    // TODO: 记录开始时间和开始日志
    
    try {
      final data = await _apiService.getMarketData();
      
      // TODO: 计算耗时并记录成功日志
      
      return data;
    } catch (e) {
      // TODO: 记录失败日志，包含耗时信息
      rethrow;
    }
  }
}
```

## 🔧 第三阶段：高级功能

### 3.1 日志配置管理

**动态切换日志模式**：
```dart
// 切换到开发模式（显示所有日志）
await LogConfigManager.switchToDevelopmentMode();

// 切换到简洁模式（隐藏调试日志）
await LogConfigManager.switchToConciseMode();

// 切换到生产模式（最小日志输出）
await LogConfigManager.switchToProductionMode();

// 查看当前配置状态
LogConfigManager.printConfigStatus();
```

### 3.2 日志聚合和分析

**获取性能报告**：
```dart
final aggregator = LogAggregator.instance;

// 获取性能报告
final perfReport = aggregator.getPerformanceReport();
print('总日志数: ${perfReport['summary']['total_logs']}');
print('错误率: ${perfReport['summary']['error_rate']}');

// 获取错误分析报告
final errorReport = aggregator.getErrorAnalysisReport();
print('错误模式数: ${errorReport['error_analysis']['total_error_patterns']}');
```

### 3.3 监控和告警

**启用实时监控**：
```dart
final monitor = LogMonitoringDashboard.instance;

// 添加告警回调
monitor.addAlertCallback((alert) {
  print('收到告警: ${alert.message}');
  // 可以发送通知、邮件等
});

// 设置告警阈值
monitor.setThreshold('error_rate_threshold', 3.0); // 错误率3%

// 开始监控
monitor.startMonitoring();
```

## 🎯 第四阶段：实战练习

### 4.1 代码审查练习

**场景：审查以下代码的日志使用**
```dart
class OrderService {
  Future<Order> createOrder(OrderRequest request) async {
    print('Creating order for ${request.userId}');
    
    try {
      AppLogger.logModule('Order', LogLevel.info, 'Order creation started');
      
      final order = await _processOrder(request);
      
      print('Order created: ${order.id}');
      
      return order;
    } catch (e) {
      print('Error: $e');
      throw e;
    }
  }
}
```

**问题识别**：
1. 使用了print()而不是正式日志
2. 使用了旧的AppLogger.logModule方式
3. 缺少必要的元数据
4. 错误处理不完整
5. 没有使用专业的业务方法

**改进后的代码**：
```dart
class OrderService {
  Future<Order> createOrder(OrderRequest request) async {
    TradeLoggerUtils.info('💰 开始创建订单', metadata: {
      'user_id': request.userId,
      'symbol': request.symbol,
      'quantity': request.quantity,
    });
    
    try {
      final order = await _processOrder(request);
      
      TradeLoggerUtils.logOrderCreated(
        orderId: order.id,
        symbol: order.symbol,
        success: true,
      );
      
      return order;
    } catch (e) {
      TradeLoggerUtils.error('❌ 订单创建失败', 
        error: e,
        metadata: {
          'user_id': request.userId,
          'symbol': request.symbol,
        },
      );
      rethrow;
    }
  }
}
```

### 4.2 问题排查练习

**场景：用户反馈股票数据加载缓慢**

**排查步骤**：
1. 查看相关日志：
```dart
// 搜索市场数据相关日志
final logs = await LogAggregator.instance.exportLogs(
  modules: ['Market'],
  startTime: DateTime.now().subtract(Duration(hours: 1)),
);
```

2. 分析性能数据：
```dart
final perfReport = LogAggregator.instance.getPerformanceReport();
final marketMetrics = perfReport['modules']['Market'];
print('平均响应时间: ${marketMetrics['average_duration']}');
```

3. 检查错误模式：
```dart
final errorReport = LogAggregator.instance.getErrorAnalysisReport();
// 查看是否有网络或API相关错误
```

### 4.3 培训考核

**考核题目**：
1. 为用户注册功能编写完整的日志代码
2. 识别并修复一段有问题的日志代码
3. 配置日志系统以适应不同环境需求
4. 使用日志工具排查一个模拟的性能问题

**通过标准**：
- 正确使用模块化日志工具类
- 选择合适的日志级别
- 包含必要的元数据
- 遵循安全规范
- 代码清晰易读

## 📋 培训检查清单

### 新人自检清单
- [ ] 我理解了日志系统的整体架构
- [ ] 我知道如何选择合适的日志级别
- [ ] 我会使用各模块的日志工具类
- [ ] 我了解敏感信息过滤规则
- [ ] 我能编写符合规范的日志代码
- [ ] 我掌握了基本的调试技巧
- [ ] 我了解日志配置和监控功能

### 导师验收清单
- [ ] 新人能独立编写规范的日志代码
- [ ] 新人能识别和修复日志相关问题
- [ ] 新人了解性能和安全考虑
- [ ] 新人能使用高级功能进行问题排查
- [ ] 新人通过了实战练习考核

## 📞 支持和帮助

**遇到问题时的求助渠道**：
1. 查阅文档：`docs/LOGGING_STANDARDS.md`
2. 参考最佳实践：`docs/LOGGING_BEST_PRACTICES.md`
3. 询问导师或团队成员
4. 在团队群中提问
5. 提交Issue到项目仓库

**常用调试命令**：
```dart
// 查看当前日志配置
LogConfigManager.printConfigStatus();

// 查看日志统计
LogConfigManager.printLogStatistics();

// 导出最近的日志
final logs = await LogAggregator.instance.exportLogs(
  format: 'json',
  startTime: DateTime.now().subtract(Duration(hours: 1)),
);
```

---

*欢迎加入我们的团队！让我们一起构建高质量的日志系统* 🎉
