# 🎨 前端架构升级详细方案

## 🎯 前端架构升级目标

### 📊 性能目标
```yaml
用户体验目标:
  - 应用启动时间: <2秒
  - 页面切换延迟: <300ms
  - 列表滚动帧率: 60FPS
  - 内存使用峰值: <500MB
  - 网络请求响应: <100ms

技术指标:
  - 支持并发用户: 100万+
  - 实时数据更新: <50ms延迟
  - 离线功能支持: 核心功能可用
  - 多设备适配: iOS/Android/Web
  - 国际化支持: 50+语言
```

## 🏗️ Flutter前端架构设计

### 📱 整体架构模式

#### 🔧 分层架构设计
```dart
// 前端分层架构
lib/
├── presentation/           # 表现层
│   ├── pages/             # 页面组件
│   ├── widgets/           # 通用组件
│   ├── bloc/              # 状态管理
│   └── themes/            # 主题样式
├── domain/                # 业务逻辑层
│   ├── entities/          # 业务实体
│   ├── usecases/          # 用例
│   └── repositories/      # 仓储接口
├── data/                  # 数据层
│   ├── models/            # 数据模型
│   ├── datasources/       # 数据源
│   └── repositories/      # 仓储实现
├── core/                  # 核心层
│   ├── network/           # 网络服务
│   ├── storage/           # 本地存储
│   ├── utils/             # 工具类
│   └── constants/         # 常量定义
└── injection/             # 依赖注入
    └── injection_container.dart
```

#### 🎯 Clean Architecture实现
```dart
// 业务实体定义
// domain/entities/market_data.dart
class MarketData extends Equatable {
  final String symbol;
  final double price;
  final double change;
  final double volume;
  final DateTime timestamp;
  
  const MarketData({
    required this.symbol,
    required this.price,
    required this.change,
    required this.volume,
    required this.timestamp,
  });
  
  @override
  List<Object?> get props => [symbol, price, change, volume, timestamp];
}

// 用例定义
// domain/usecases/get_market_data.dart
class GetMarketData {
  final MarketRepository repository;
  
  GetMarketData(this.repository);
  
  Future<Either<Failure, List<MarketData>>> call({
    required String symbol,
    String? interval,
    int? limit,
  }) async {
    return await repository.getMarketData(
      symbol: symbol,
      interval: interval ?? '1m',
      limit: limit ?? 100,
    );
  }
}

// 仓储接口
// domain/repositories/market_repository.dart
abstract class MarketRepository {
  Future<Either<Failure, List<MarketData>>> getMarketData({
    required String symbol,
    String? interval,
    int? limit,
  });
  
  Stream<MarketData> subscribeToMarketData(String symbol);
}
```

### 🔄 状态管理架构

#### 📊 混合状态管理策略
```dart
// 状态管理分层
// 1. 全局状态 - Riverpod
// core/providers/global_providers.dart
final userProvider = StateNotifierProvider<UserNotifier, UserState>((ref) {
  return UserNotifier(ref.read(authRepositoryProvider));
});

final themeProvider = StateNotifierProvider<ThemeNotifier, ThemeState>((ref) {
  return ThemeNotifier();
});

final localeProvider = StateNotifierProvider<LocaleNotifier, LocaleState>((ref) {
  return LocaleNotifier();
});

// 2. 页面状态 - Bloc
// presentation/bloc/market/market_bloc.dart
class MarketBloc extends Bloc<MarketEvent, MarketState> {
  final GetMarketData _getMarketData;
  final SubscribeToMarketData _subscribeToMarketData;
  
  StreamSubscription<MarketData>? _marketDataSubscription;
  
  MarketBloc({
    required GetMarketData getMarketData,
    required SubscribeToMarketData subscribeToMarketData,
  }) : _getMarketData = getMarketData,
       _subscribeToMarketData = subscribeToMarketData,
       super(MarketInitial()) {
    on<GetMarketDataEvent>(_onGetMarketData);
    on<SubscribeToMarketDataEvent>(_onSubscribeToMarketData);
    on<MarketDataUpdatedEvent>(_onMarketDataUpdated);
  }
  
  Future<void> _onGetMarketData(
    GetMarketDataEvent event,
    Emitter<MarketState> emit,
  ) async {
    emit(MarketLoading());
    
    final result = await _getMarketData(
      symbol: event.symbol,
      interval: event.interval,
      limit: event.limit,
    );
    
    result.fold(
      (failure) => emit(MarketError(failure.message)),
      (data) => emit(MarketLoaded(data)),
    );
  }
  
  Future<void> _onSubscribeToMarketData(
    SubscribeToMarketDataEvent event,
    Emitter<MarketState> emit,
  ) async {
    await _marketDataSubscription?.cancel();
    
    _marketDataSubscription = _subscribeToMarketData(event.symbol).listen(
      (data) => add(MarketDataUpdatedEvent(data)),
      onError: (error) => add(MarketErrorEvent(error.toString())),
    );
  }
  
  @override
  Future<void> close() {
    _marketDataSubscription?.cancel();
    return super.close();
  }
}

// 3. 组件状态 - StatefulWidget + ValueNotifier
// presentation/widgets/trading_chart_widget.dart
class TradingChartWidget extends StatefulWidget {
  final String symbol;
  final String interval;
  
  const TradingChartWidget({
    Key? key,
    required this.symbol,
    required this.interval,
  }) : super(key: key);
  
  @override
  State<TradingChartWidget> createState() => _TradingChartWidgetState();
}

class _TradingChartWidgetState extends State<TradingChartWidget> {
  late final ValueNotifier<ChartData?> _chartDataNotifier;
  late final ValueNotifier<bool> _isLoadingNotifier;
  
  @override
  void initState() {
    super.initState();
    _chartDataNotifier = ValueNotifier(null);
    _isLoadingNotifier = ValueNotifier(false);
    _loadChartData();
  }
  
  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<bool>(
      valueListenable: _isLoadingNotifier,
      builder: (context, isLoading, child) {
        if (isLoading) {
          return const ChartLoadingWidget();
        }
        
        return ValueListenableBuilder<ChartData?>(
          valueListenable: _chartDataNotifier,
          builder: (context, chartData, child) {
            if (chartData == null) {
              return const ChartEmptyWidget();
            }
            
            return CustomChartWidget(data: chartData);
          },
        );
      },
    );
  }
}
```

### 🌐 网络架构设计

#### 📡 多层网络架构
```dart
// 网络服务分层
// 1. 基础网络层
// core/network/dio_client.dart
class DioClient {
  late final Dio _dio;
  late final CancelToken _cancelToken;
  
  DioClient() {
    _dio = Dio();
    _cancelToken = CancelToken();
    _setupInterceptors();
  }
  
  void _setupInterceptors() {
    _dio.interceptors.addAll([
      // 请求拦截器
      InterceptorsWrapper(
        onRequest: (options, handler) {
          options.headers['Authorization'] = 'Bearer ${_getToken()}';
          options.headers['User-Agent'] = _getUserAgent();
          options.headers['X-Request-ID'] = _generateRequestId();
          handler.next(options);
        },
        onResponse: (response, handler) {
          _logResponse(response);
          handler.next(response);
        },
        onError: (error, handler) {
          _handleError(error);
          handler.next(error);
        },
      ),
      
      // 重试拦截器
      RetryInterceptor(
        dio: _dio,
        logPrint: print,
        retries: 3,
        retryDelays: const [
          Duration(seconds: 1),
          Duration(seconds: 2),
          Duration(seconds: 3),
        ],
      ),
      
      // 缓存拦截器
      DioCacheInterceptor(
        options: CacheOptions(
          store: MemCacheStore(),
          policy: CachePolicy.request,
          hitCacheOnErrorExcept: [401, 403],
          maxStale: const Duration(days: 7),
          priority: CachePriority.normal,
          cipher: null,
          keyBuilder: CacheOptions.defaultCacheKeyBuilder,
          allowPostMethod: false,
        ),
      ),
    ]);
  }
  
  // GET请求
  Future<Response<T>> get<T>(
    String path, {
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
  }) async {
    try {
      final response = await _dio.get<T>(
        path,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken ?? _cancelToken,
      );
      return response;
    } catch (e) {
      throw _handleException(e);
    }
  }
  
  // POST请求
  Future<Response<T>> post<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
  }) async {
    try {
      final response = await _dio.post<T>(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken ?? _cancelToken,
      );
      return response;
    } catch (e) {
      throw _handleException(e);
    }
  }
}

// 2. API服务层
// data/datasources/market_remote_data_source.dart
abstract class MarketRemoteDataSource {
  Future<List<MarketDataModel>> getMarketData({
    required String symbol,
    String? interval,
    int? limit,
  });
  
  Stream<MarketDataModel> subscribeToMarketData(String symbol);
}

class MarketRemoteDataSourceImpl implements MarketRemoteDataSource {
  final DioClient _client;
  final WebSocketService _wsService;
  
  MarketRemoteDataSourceImpl({
    required DioClient client,
    required WebSocketService wsService,
  }) : _client = client, _wsService = wsService;
  
  @override
  Future<List<MarketDataModel>> getMarketData({
    required String symbol,
    String? interval,
    int? limit,
  }) async {
    final response = await _client.get(
      '/api/v1/market/klines',
      queryParameters: {
        'symbol': symbol,
        'interval': interval ?? '1m',
        'limit': limit ?? 100,
      },
    );
    
    if (response.statusCode == 200) {
      final List<dynamic> data = response.data['data'];
      return data.map((item) => MarketDataModel.fromJson(item)).toList();
    } else {
      throw ServerException('Failed to fetch market data');
    }
  }
  
  @override
  Stream<MarketDataModel> subscribeToMarketData(String symbol) {
    return _wsService
        .subscribe('market.$symbol')
        .where((message) => message['type'] == 'market_data')
        .map((message) => MarketDataModel.fromJson(message['data']));
  }
}
```

#### 🔗 WebSocket连接管理
```dart
// WebSocket服务优化
// core/network/websocket_service.dart
class WebSocketService {
  static final WebSocketService _instance = WebSocketService._internal();
  factory WebSocketService() => _instance;
  WebSocketService._internal();
  
  WebSocketChannel? _channel;
  final Map<String, StreamController<Map<String, dynamic>>> _controllers = {};
  final Map<String, int> _subscriptionCounts = {};
  
  Timer? _heartbeatTimer;
  Timer? _reconnectTimer;
  
  bool _isConnected = false;
  int _reconnectAttempts = 0;
  static const int _maxReconnectAttempts = 5;
  
  // 连接管理
  Future<void> connect() async {
    if (_isConnected) return;
    
    try {
      _channel = WebSocketChannel.connect(
        Uri.parse('wss://api.financial-app.com/ws'),
        protocols: ['financial-protocol'],
      );
      
      await _channel!.ready;
      _isConnected = true;
      _reconnectAttempts = 0;
      
      _setupMessageListener();
      _startHeartbeat();
      
      AppLogger.logModule('WebSocketService').info('WebSocket连接成功');
    } catch (e) {
      AppLogger.logModule('WebSocketService').error('WebSocket连接失败: $e');
      _scheduleReconnect();
    }
  }
  
  // 消息监听
  void _setupMessageListener() {
    _channel!.stream.listen(
      (data) {
        try {
          final message = jsonDecode(data) as Map<String, dynamic>;
          _handleMessage(message);
        } catch (e) {
          AppLogger.logModule('WebSocketService').error('消息解析失败: $e');
        }
      },
      onError: (error) {
        AppLogger.logModule('WebSocketService').error('WebSocket错误: $error');
        _handleConnectionError();
      },
      onDone: () {
        AppLogger.logModule('WebSocketService').warning('WebSocket连接关闭');
        _handleConnectionClosed();
      },
    );
  }
  
  // 订阅管理
  Stream<Map<String, dynamic>> subscribe(String channel) {
    if (!_controllers.containsKey(channel)) {
      _controllers[channel] = StreamController<Map<String, dynamic>>.broadcast();
      _subscriptionCounts[channel] = 0;
    }
    
    _subscriptionCounts[channel] = _subscriptionCounts[channel]! + 1;
    
    // 发送订阅消息
    if (_subscriptionCounts[channel] == 1) {
      _sendSubscribeMessage(channel);
    }
    
    return _controllers[channel]!.stream;
  }
  
  void unsubscribe(String channel) {
    if (!_subscriptionCounts.containsKey(channel)) return;
    
    _subscriptionCounts[channel] = _subscriptionCounts[channel]! - 1;
    
    if (_subscriptionCounts[channel]! <= 0) {
      _sendUnsubscribeMessage(channel);
      _controllers[channel]?.close();
      _controllers.remove(channel);
      _subscriptionCounts.remove(channel);
    }
  }
  
  // 消息处理
  void _handleMessage(Map<String, dynamic> message) {
    final String? channel = message['channel'];
    if (channel != null && _controllers.containsKey(channel)) {
      _controllers[channel]!.add(message);
    }
  }
  
  // 心跳机制
  void _startHeartbeat() {
    _heartbeatTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
      if (_isConnected) {
        _sendMessage({
          'type': 'ping',
          'timestamp': DateTime.now().millisecondsSinceEpoch,
        });
      }
    });
  }
  
  // 重连机制
  void _scheduleReconnect() {
    if (_reconnectAttempts >= _maxReconnectAttempts) {
      AppLogger.logModule('WebSocketService').error('达到最大重连次数，停止重连');
      return;
    }
    
    final delay = Duration(seconds: math.pow(2, _reconnectAttempts).toInt());
    _reconnectTimer = Timer(delay, () {
      _reconnectAttempts++;
      connect();
    });
  }
}
```

### 🎨 UI组件架构

#### 🧩 组件化设计
```dart
// 设计系统组件
// presentation/widgets/design_system/app_button.dart
class AppButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final AppButtonType type;
  final AppButtonSize size;
  final bool isLoading;
  final Widget? icon;
  
  const AppButton({
    Key? key,
    required this.text,
    this.onPressed,
    this.type = AppButtonType.primary,
    this.size = AppButtonSize.medium,
    this.isLoading = false,
    this.icon,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final buttonStyle = _getButtonStyle(theme);
    
    return SizedBox(
      height: _getHeight(),
      child: ElevatedButton(
        onPressed: isLoading ? null : onPressed,
        style: buttonStyle,
        child: isLoading
            ? _buildLoadingWidget()
            : _buildButtonContent(),
      ),
    );
  }
  
  ButtonStyle _getButtonStyle(ThemeData theme) {
    switch (type) {
      case AppButtonType.primary:
        return ElevatedButton.styleFrom(
          backgroundColor: theme.colorScheme.primary,
          foregroundColor: theme.colorScheme.onPrimary,
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        );
      case AppButtonType.secondary:
        return ElevatedButton.styleFrom(
          backgroundColor: theme.colorScheme.secondary,
          foregroundColor: theme.colorScheme.onSecondary,
          elevation: 1,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        );
      case AppButtonType.outline:
        return OutlinedButton.styleFrom(
          foregroundColor: theme.colorScheme.primary,
          side: BorderSide(color: theme.colorScheme.primary),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        );
    }
  }
  
  double _getHeight() {
    switch (size) {
      case AppButtonSize.small:
        return 32;
      case AppButtonSize.medium:
        return 40;
      case AppButtonSize.large:
        return 48;
    }
  }
  
  Widget _buildLoadingWidget() {
    return SizedBox(
      width: 20,
      height: 20,
      child: CircularProgressIndicator(
        strokeWidth: 2,
        valueColor: AlwaysStoppedAnimation<Color>(
          type == AppButtonType.outline
              ? Theme.of(context).colorScheme.primary
              : Theme.of(context).colorScheme.onPrimary,
        ),
      ),
    );
  }
  
  Widget _buildButtonContent() {
    if (icon != null) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          icon!,
          const SizedBox(width: 8),
          Text(text),
        ],
      );
    }
    return Text(text);
  }
}

// 高性能列表组件
// presentation/widgets/performance/virtual_list_view.dart
class VirtualListView<T> extends StatefulWidget {
  final List<T> items;
  final Widget Function(BuildContext context, T item, int index) itemBuilder;
  final double itemHeight;
  final EdgeInsets? padding;
  final ScrollController? controller;
  final VoidCallback? onLoadMore;
  final bool hasMore;
  
  const VirtualListView({
    Key? key,
    required this.items,
    required this.itemBuilder,
    required this.itemHeight,
    this.padding,
    this.controller,
    this.onLoadMore,
    this.hasMore = false,
  }) : super(key: key);
  
  @override
  State<VirtualListView<T>> createState() => _VirtualListViewState<T>();
}

class _VirtualListViewState<T> extends State<VirtualListView<T>> {
  late ScrollController _scrollController;
  late ValueNotifier<int> _firstVisibleIndex;
  late ValueNotifier<int> _lastVisibleIndex;
  
  @override
  void initState() {
    super.initState();
    _scrollController = widget.controller ?? ScrollController();
    _firstVisibleIndex = ValueNotifier(0);
    _lastVisibleIndex = ValueNotifier(0);
    
    _scrollController.addListener(_onScroll);
  }
  
  void _onScroll() {
    final scrollOffset = _scrollController.offset;
    final viewportHeight = _scrollController.position.viewportDimension;
    
    final firstIndex = (scrollOffset / widget.itemHeight).floor();
    final lastIndex = ((scrollOffset + viewportHeight) / widget.itemHeight).ceil();
    
    _firstVisibleIndex.value = math.max(0, firstIndex - 5); // 预加载5个
    _lastVisibleIndex.value = math.min(widget.items.length - 1, lastIndex + 5);
    
    // 检查是否需要加载更多
    if (widget.onLoadMore != null && 
        widget.hasMore && 
        lastIndex >= widget.items.length - 10) {
      widget.onLoadMore!();
    }
  }
  
  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<int>(
      valueListenable: _firstVisibleIndex,
      builder: (context, firstIndex, child) {
        return ValueListenableBuilder<int>(
          valueListenable: _lastVisibleIndex,
          builder: (context, lastIndex, child) {
            return ListView.builder(
              controller: _scrollController,
              padding: widget.padding,
              itemCount: widget.items.length,
              itemBuilder: (context, index) {
                // 虚拟化：只渲染可见区域的item
                if (index < firstIndex || index > lastIndex) {
                  return SizedBox(height: widget.itemHeight);
                }
                
                return SizedBox(
                  height: widget.itemHeight,
                  child: widget.itemBuilder(context, widget.items[index], index),
                );
              },
            );
          },
        );
      },
    );
  }
}
```

### 💾 本地存储架构

#### 🗄️ 多层存储策略
```dart
// 存储服务抽象
// core/storage/storage_service.dart
abstract class StorageService {
  Future<void> setString(String key, String value);
  Future<String?> getString(String key);
  Future<void> setInt(String key, int value);
  Future<int?> getInt(String key);
  Future<void> setBool(String key, bool value);
  Future<bool?> getBool(String key);
  Future<void> setObject<T>(String key, T object);
  Future<T?> getObject<T>(String key, T Function(Map<String, dynamic>) fromJson);
  Future<void> remove(String key);
  Future<void> clear();
}

// SharedPreferences实现
// core/storage/shared_preferences_service.dart
class SharedPreferencesService implements StorageService {
  static SharedPreferencesService? _instance;
  static SharedPreferences? _preferences;

  static Future<SharedPreferencesService> getInstance() async {
    _instance ??= SharedPreferencesService._();
    _preferences ??= await SharedPreferences.getInstance();
    return _instance!;
  }

  SharedPreferencesService._();

  @override
  Future<void> setString(String key, String value) async {
    await _preferences!.setString(key, value);
  }

  @override
  Future<String?> getString(String key) async {
    return _preferences!.getString(key);
  }

  @override
  Future<void> setObject<T>(String key, T object) async {
    final jsonString = jsonEncode(object);
    await setString(key, jsonString);
  }

  @override
  Future<T?> getObject<T>(String key, T Function(Map<String, dynamic>) fromJson) async {
    final jsonString = await getString(key);
    if (jsonString == null) return null;

    try {
      final jsonMap = jsonDecode(jsonString) as Map<String, dynamic>;
      return fromJson(jsonMap);
    } catch (e) {
      AppLogger.logModule('Storage').error('对象反序列化失败: $e');
      return null;
    }
  }
}

// Hive数据库实现
// core/storage/hive_service.dart
class HiveService implements StorageService {
  static const String _boxName = 'financial_app_box';
  late Box _box;

  Future<void> init() async {
    await Hive.initFlutter();

    // 注册适配器
    Hive.registerAdapter(UserModelAdapter());
    Hive.registerAdapter(MarketDataModelAdapter());
    Hive.registerAdapter(OrderModelAdapter());

    _box = await Hive.openBox(_boxName);
  }

  @override
  Future<void> setObject<T>(String key, T object) async {
    await _box.put(key, object);
  }

  @override
  Future<T?> getObject<T>(String key, T Function(Map<String, dynamic>) fromJson) async {
    final object = _box.get(key);
    if (object is T) {
      return object;
    }
    return null;
  }

  // 批量操作
  Future<void> putAll(Map<String, dynamic> entries) async {
    await _box.putAll(entries);
  }

  Future<Map<String, dynamic>> getAll() async {
    return Map<String, dynamic>.from(_box.toMap());
  }

  // 监听数据变化
  Stream<BoxEvent> watch({String? key}) {
    return _box.watch(key: key);
  }
}

// 缓存管理器
// core/storage/cache_manager.dart
class CacheManager {
  static final CacheManager _instance = CacheManager._internal();
  factory CacheManager() => _instance;
  CacheManager._internal();

  final Map<String, CacheItem> _memoryCache = {};
  late final StorageService _persistentStorage;

  static const Duration _defaultTTL = Duration(minutes: 30);
  static const int _maxMemoryCacheSize = 100;

  Future<void> init() async {
    _persistentStorage = await SharedPreferencesService.getInstance();
    _startCleanupTimer();
  }

  // 设置缓存
  Future<void> set<T>(
    String key,
    T value, {
    Duration? ttl,
    bool persistent = false,
  }) async {
    final expiry = DateTime.now().add(ttl ?? _defaultTTL);
    final cacheItem = CacheItem(
      value: value,
      expiry: expiry,
      persistent: persistent,
    );

    // 内存缓存
    _memoryCache[key] = cacheItem;
    _evictIfNecessary();

    // 持久化缓存
    if (persistent) {
      await _persistentStorage.setObject(key, {
        'value': value,
        'expiry': expiry.millisecondsSinceEpoch,
      });
    }
  }

  // 获取缓存
  Future<T?> get<T>(String key) async {
    // 先检查内存缓存
    final memoryItem = _memoryCache[key];
    if (memoryItem != null && !memoryItem.isExpired) {
      return memoryItem.value as T?;
    }

    // 检查持久化缓存
    final persistentData = await _persistentStorage.getObject(
      key,
      (json) => json,
    );

    if (persistentData != null) {
      final expiry = DateTime.fromMillisecondsSinceEpoch(persistentData['expiry']);
      if (DateTime.now().isBefore(expiry)) {
        final value = persistentData['value'] as T;

        // 重新加载到内存缓存
        _memoryCache[key] = CacheItem(
          value: value,
          expiry: expiry,
          persistent: true,
        );

        return value;
      }
    }

    return null;
  }

  // 清理过期缓存
  void _startCleanupTimer() {
    Timer.periodic(const Duration(minutes: 5), (timer) {
      _cleanupExpiredItems();
    });
  }

  void _cleanupExpiredItems() {
    _memoryCache.removeWhere((key, item) => item.isExpired);
  }

  void _evictIfNecessary() {
    if (_memoryCache.length > _maxMemoryCacheSize) {
      // LRU淘汰策略
      final oldestKey = _memoryCache.keys.first;
      _memoryCache.remove(oldestKey);
    }
  }
}

class CacheItem {
  final dynamic value;
  final DateTime expiry;
  final bool persistent;

  CacheItem({
    required this.value,
    required this.expiry,
    required this.persistent,
  });

  bool get isExpired => DateTime.now().isAfter(expiry);
}
```
