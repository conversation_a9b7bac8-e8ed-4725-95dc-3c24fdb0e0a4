# 📡 REST API 参考文档

本文档详细介绍了金融应用的REST API接口，包括认证、市场数据、交易、投资组合等各个模块的API规范。

## 🌐 API概览

### 📋 基础信息
- **Base URL**: `https://api.financial-app.com/v1`
- **协议**: HTTPS
- **数据格式**: JSON
- **字符编码**: UTF-8
- **API版本**: v1.0

### 🔐 认证方式
所有API请求都需要在请求头中包含认证信息：

```http
Authorization: Bearer <access_token>
Content-Type: application/json
```

### 📊 通用响应格式
```json
{
  "success": true,
  "message": "操作成功",
  "data": {
    // 具体数据内容
  },
  "timestamp": "2025-07-07T12:00:00Z",
  "request_id": "req_123456789"
}
```

### ❌ 错误响应格式
```json
{
  "success": false,
  "message": "错误描述",
  "error_code": "ERROR_CODE",
  "details": {
    // 错误详细信息
  },
  "timestamp": "2025-07-07T12:00:00Z",
  "request_id": "req_123456789"
}
```

## 🔐 认证模块 API

### 🚪 用户登录
**POST** `/auth/login`

#### 请求参数
```json
{
  "username": "string",     // 用户名或手机号
  "password": "string",     // 密码
  "device_id": "string",    // 设备ID (可选)
  "remember_me": boolean    // 记住登录状态 (可选)
}
```

#### 响应示例
```json
{
  "success": true,
  "message": "登录成功",
  "data": {
    "user": {
      "id": "user_123456",
      "username": "testuser",
      "email": "<EMAIL>",
      "phone": "13800138000",
      "avatar": "https://cdn.example.com/avatar.jpg",
      "verified": true,
      "created_at": "2025-01-01T00:00:00Z"
    },
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refresh_token": "refresh_token_string",
    "expires_in": 3600,
    "token_type": "Bearer"
  }
}
```

#### 错误码
- `AUTH_001`: 用户名或密码错误
- `AUTH_002`: 账户已被锁定
- `AUTH_003`: 账户未激活
- `AUTH_004`: 登录频率过高

### 🔄 刷新Token
**POST** `/auth/refresh`

#### 请求参数
```json
{
  "refresh_token": "string"
}
```

#### 响应示例
```json
{
  "success": true,
  "data": {
    "access_token": "new_access_token",
    "expires_in": 3600
  }
}
```

### 🚪 用户登出
**POST** `/auth/logout`

#### 请求头
```http
Authorization: Bearer <access_token>
```

#### 响应示例
```json
{
  "success": true,
  "message": "登出成功"
}
```

## 📊 市场数据 API

### 📈 获取实时行情
**GET** `/market/ticker/{symbol}`

#### 路径参数
- `symbol`: 交易对符号，如 `BTCUSDT`

#### 查询参数
- `interval`: 时间间隔 (可选)，默认为实时数据

#### 响应示例
```json
{
  "success": true,
  "data": {
    "symbol": "BTCUSDT",
    "price": "45000.00",
    "change": "1200.50",
    "change_percent": "2.74",
    "high_24h": "46000.00",
    "low_24h": "43500.00",
    "volume_24h": "1234567.89",
    "timestamp": "2025-07-07T12:00:00Z"
  }
}
```

### 📊 获取K线数据
**GET** `/market/klines/{symbol}`

#### 路径参数
- `symbol`: 交易对符号

#### 查询参数
- `interval`: 时间间隔 (`1m`, `5m`, `15m`, `1h`, `4h`, `1d`)
- `limit`: 数据条数，默认100，最大1000
- `start_time`: 开始时间戳 (可选)
- `end_time`: 结束时间戳 (可选)

#### 响应示例
```json
{
  "success": true,
  "data": {
    "symbol": "BTCUSDT",
    "interval": "1h",
    "klines": [
      {
        "open_time": 1625097600000,
        "open": "45000.00",
        "high": "45500.00",
        "low": "44800.00",
        "close": "45200.00",
        "volume": "123.456",
        "close_time": 1625101199999,
        "quote_volume": "5567890.12",
        "count": 1234
      }
    ]
  }
}
```

### 📋 获取交易对列表
**GET** `/market/symbols`

#### 查询参数
- `status`: 状态筛选 (`TRADING`, `BREAK`) (可选)
- `base_asset`: 基础资产筛选 (可选)
- `quote_asset`: 计价资产筛选 (可选)

#### 响应示例
```json
{
  "success": true,
  "data": {
    "symbols": [
      {
        "symbol": "BTCUSDT",
        "base_asset": "BTC",
        "quote_asset": "USDT",
        "status": "TRADING",
        "min_qty": "0.00001",
        "max_qty": "1000.00000",
        "step_size": "0.00001",
        "min_price": "0.01",
        "max_price": "100000.00",
        "tick_size": "0.01"
      }
    ]
  }
}
```

## 💰 交易模块 API

### 📝 下单
**POST** `/trade/order`

#### 请求参数
```json
{
  "symbol": "BTCUSDT",
  "side": "BUY",           // BUY 或 SELL
  "type": "LIMIT",         // MARKET, LIMIT, STOP_LOSS, STOP_LOSS_LIMIT
  "quantity": "0.001",
  "price": "45000.00",     // 限价单必填
  "time_in_force": "GTC",  // GTC, IOC, FOK
  "stop_price": "44000.00" // 止损单必填
}
```

#### 响应示例
```json
{
  "success": true,
  "data": {
    "order_id": "order_123456789",
    "client_order_id": "client_order_001",
    "symbol": "BTCUSDT",
    "side": "BUY",
    "type": "LIMIT",
    "quantity": "0.001",
    "price": "45000.00",
    "status": "NEW",
    "time_in_force": "GTC",
    "created_at": "2025-07-07T12:00:00Z"
  }
}
```

### ❌ 撤销订单
**DELETE** `/trade/order/{order_id}`

#### 路径参数
- `order_id`: 订单ID

#### 响应示例
```json
{
  "success": true,
  "data": {
    "order_id": "order_123456789",
    "status": "CANCELED",
    "canceled_at": "2025-07-07T12:05:00Z"
  }
}
```

### 📋 查询订单
**GET** `/trade/order/{order_id}`

#### 路径参数
- `order_id`: 订单ID

#### 响应示例
```json
{
  "success": true,
  "data": {
    "order_id": "order_123456789",
    "client_order_id": "client_order_001",
    "symbol": "BTCUSDT",
    "side": "BUY",
    "type": "LIMIT",
    "quantity": "0.001",
    "price": "45000.00",
    "executed_qty": "0.0005",
    "status": "PARTIALLY_FILLED",
    "time_in_force": "GTC",
    "created_at": "2025-07-07T12:00:00Z",
    "updated_at": "2025-07-07T12:03:00Z"
  }
}
```

### 📊 查询当前订单
**GET** `/trade/orders/open`

#### 查询参数
- `symbol`: 交易对筛选 (可选)
- `limit`: 返回数量，默认100

#### 响应示例
```json
{
  "success": true,
  "data": {
    "orders": [
      {
        "order_id": "order_123456789",
        "symbol": "BTCUSDT",
        "side": "BUY",
        "type": "LIMIT",
        "quantity": "0.001",
        "price": "45000.00",
        "executed_qty": "0.0000",
        "status": "NEW",
        "created_at": "2025-07-07T12:00:00Z"
      }
    ]
  }
}
```

### 📈 查询交易历史
**GET** `/trade/history`

#### 查询参数
- `symbol`: 交易对筛选 (可选)
- `start_time`: 开始时间戳 (可选)
- `end_time`: 结束时间戳 (可选)
- `limit`: 返回数量，默认100
- `page`: 页码，默认1

#### 响应示例
```json
{
  "success": true,
  "data": {
    "trades": [
      {
        "trade_id": "trade_123456789",
        "order_id": "order_123456789",
        "symbol": "BTCUSDT",
        "side": "BUY",
        "quantity": "0.001",
        "price": "45000.00",
        "fee": "0.045",
        "fee_asset": "USDT",
        "executed_at": "2025-07-07T12:00:00Z"
      }
    ],
    "pagination": {
      "current_page": 1,
      "total_pages": 10,
      "total_count": 1000,
      "has_next": true
    }
  }
}
```

## 📊 投资组合 API

### 💼 获取账户信息
**GET** `/portfolio/account`

#### 响应示例
```json
{
  "success": true,
  "data": {
    "account_id": "account_123456",
    "account_type": "SPOT",
    "total_balance": "10000.00",
    "available_balance": "8500.00",
    "frozen_balance": "1500.00",
    "balances": [
      {
        "asset": "USDT",
        "free": "5000.00",
        "locked": "500.00"
      },
      {
        "asset": "BTC",
        "free": "0.1",
        "locked": "0.02"
      }
    ],
    "updated_at": "2025-07-07T12:00:00Z"
  }
}
```

### 📈 获取持仓信息
**GET** `/portfolio/positions`

#### 查询参数
- `symbol`: 交易对筛选 (可选)

#### 响应示例
```json
{
  "success": true,
  "data": {
    "positions": [
      {
        "symbol": "BTCUSDT",
        "asset": "BTC",
        "quantity": "0.1",
        "avg_price": "44000.00",
        "market_price": "45000.00",
        "unrealized_pnl": "100.00",
        "unrealized_pnl_percent": "2.27",
        "updated_at": "2025-07-07T12:00:00Z"
      }
    ]
  }
}
```

## 🔔 通知模块 API

### 📱 获取通知列表
**GET** `/notifications`

#### 查询参数
- `type`: 通知类型筛选 (可选)
- `status`: 状态筛选 (`read`, `unread`) (可选)
- `limit`: 返回数量，默认20
- `page`: 页码，默认1

#### 响应示例
```json
{
  "success": true,
  "data": {
    "notifications": [
      {
        "id": "notif_123456789",
        "type": "TRADE_EXECUTED",
        "title": "交易执行通知",
        "content": "您的买入订单已成功执行",
        "status": "unread",
        "created_at": "2025-07-07T12:00:00Z",
        "data": {
          "order_id": "order_123456789",
          "symbol": "BTCUSDT"
        }
      }
    ],
    "pagination": {
      "current_page": 1,
      "total_pages": 5,
      "total_count": 100,
      "unread_count": 15
    }
  }
}
```

### ✅ 标记通知已读
**PUT** `/notifications/{notification_id}/read`

#### 路径参数
- `notification_id`: 通知ID

#### 响应示例
```json
{
  "success": true,
  "message": "通知已标记为已读"
}
```

## 📊 状态码说明

### 🟢 成功状态码
- `200`: 请求成功
- `201`: 创建成功
- `204`: 删除成功

### 🔴 错误状态码
- `400`: 请求参数错误
- `401`: 未授权访问
- `403`: 权限不足
- `404`: 资源不存在
- `429`: 请求频率过高
- `500`: 服务器内部错误
- `503`: 服务暂不可用

## 🔧 使用示例

### 📱 Flutter/Dart 示例
```dart
// API客户端示例
class ApiClient {
  final Dio _dio;
  
  ApiClient(this._dio);
  
  Future<LoginResponse> login(String username, String password) async {
    final response = await _dio.post('/auth/login', data: {
      'username': username,
      'password': password,
    });
    
    return LoginResponse.fromJson(response.data);
  }
  
  Future<List<KlineData>> getKlineData(
    String symbol,
    String interval, {
    int limit = 100,
  }) async {
    final response = await _dio.get('/market/klines/$symbol', queryParameters: {
      'interval': interval,
      'limit': limit,
    });
    
    final data = response.data['data'];
    return (data['klines'] as List)
        .map((item) => KlineData.fromJson(item))
        .toList();
  }
}
```

### 🌐 JavaScript 示例
```javascript
// API客户端示例
class ApiClient {
  constructor(baseURL, token) {
    this.baseURL = baseURL;
    this.token = token;
  }
  
  async request(method, endpoint, data = null) {
    const response = await fetch(`${this.baseURL}${endpoint}`, {
      method,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.token}`,
      },
      body: data ? JSON.stringify(data) : null,
    });
    
    return response.json();
  }
  
  async getMarketData(symbol) {
    return this.request('GET', `/market/ticker/${symbol}`);
  }
  
  async placeOrder(orderData) {
    return this.request('POST', '/trade/order', orderData);
  }
}
```

---

**📡 REST API为金融应用提供了完整、安全、高效的数据接口服务，支持各种客户端的集成和开发！**
