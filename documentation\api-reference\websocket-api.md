# 🌐 WebSocket API 参考文档

本文档详细介绍了金融应用的WebSocket API接口，包括实时行情、交易更新、通知推送等实时数据服务。

## 🔗 连接信息

### 📋 基础信息
- **WebSocket URL**: `wss://ws.financial-app.com/v1/ws`
- **协议**: WSS (WebSocket Secure)
- **数据格式**: JSON
- **字符编码**: UTF-8
- **心跳间隔**: 30秒

### 🔐 连接认证
WebSocket连接需要在连接时提供认证信息：

```javascript
const ws = new WebSocket('wss://ws.financial-app.com/v1/ws', ['financial-protocol']);

// 连接建立后发送认证消息
ws.onopen = function() {
  ws.send(JSON.stringify({
    type: 'auth',
    token: 'your_access_token_here'
  }));
};
```

### 📊 消息格式

#### 📤 发送消息格式
```json
{
  "type": "message_type",
  "id": "unique_request_id",
  "data": {
    // 具体数据内容
  }
}
```

#### 📥 接收消息格式
```json
{
  "type": "message_type",
  "channel": "channel_name",
  "data": {
    // 具体数据内容
  },
  "timestamp": "2025-07-07T12:00:00Z"
}
```

## 🔐 认证和连接管理

### 🚪 认证消息
**发送**: 连接建立后立即发送

```json
{
  "type": "auth",
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

**响应**: 认证成功
```json
{
  "type": "auth_success",
  "channel": "system",
  "data": {
    "user_id": "user_123456",
    "session_id": "session_abcdef",
    "server_time": "2025-07-07T12:00:00Z"
  }
}
```

**响应**: 认证失败
```json
{
  "type": "auth_error",
  "channel": "system",
  "data": {
    "error_code": "AUTH_INVALID_TOKEN",
    "message": "无效的访问令牌"
  }
}
```

### 💓 心跳机制
**发送**: 每30秒发送一次
```json
{
  "type": "ping",
  "timestamp": 1625097600000
}
```

**响应**: 服务器响应
```json
{
  "type": "pong",
  "channel": "system",
  "data": {
    "timestamp": 1625097600000,
    "server_time": "2025-07-07T12:00:00Z"
  }
}
```

## 📊 市场数据订阅

### 📈 订阅实时行情
**发送**: 订阅请求
```json
{
  "type": "subscribe",
  "channel": "ticker",
  "data": {
    "symbol": "BTCUSDT"
  }
}
```

**响应**: 订阅确认
```json
{
  "type": "subscribe_success",
  "channel": "ticker.BTCUSDT",
  "data": {
    "symbol": "BTCUSDT",
    "subscribed_at": "2025-07-07T12:00:00Z"
  }
}
```

**推送**: 实时行情数据
```json
{
  "type": "ticker_update",
  "channel": "ticker.BTCUSDT",
  "data": {
    "symbol": "BTCUSDT",
    "price": "45000.00",
    "change": "1200.50",
    "change_percent": "2.74",
    "high_24h": "46000.00",
    "low_24h": "43500.00",
    "volume_24h": "1234567.89",
    "bid_price": "44999.50",
    "ask_price": "45000.50",
    "bid_qty": "0.5",
    "ask_qty": "0.3"
  },
  "timestamp": "2025-07-07T12:00:00Z"
}
```

### 📊 订阅K线数据
**发送**: 订阅请求
```json
{
  "type": "subscribe",
  "channel": "kline",
  "data": {
    "symbol": "BTCUSDT",
    "interval": "1m"
  }
}
```

**推送**: K线数据更新
```json
{
  "type": "kline_update",
  "channel": "kline.BTCUSDT.1m",
  "data": {
    "symbol": "BTCUSDT",
    "interval": "1m",
    "kline": {
      "open_time": 1625097600000,
      "close_time": 1625097659999,
      "open": "45000.00",
      "high": "45100.00",
      "low": "44950.00",
      "close": "45050.00",
      "volume": "12.345",
      "quote_volume": "556789.12",
      "count": 156,
      "is_closed": false
    }
  },
  "timestamp": "2025-07-07T12:00:30Z"
}
```

### 📋 订阅深度数据
**发送**: 订阅请求
```json
{
  "type": "subscribe",
  "channel": "depth",
  "data": {
    "symbol": "BTCUSDT",
    "level": 20
  }
}
```

**推送**: 深度数据更新
```json
{
  "type": "depth_update",
  "channel": "depth.BTCUSDT",
  "data": {
    "symbol": "BTCUSDT",
    "bids": [
      ["44999.50", "0.5"],
      ["44999.00", "1.2"],
      ["44998.50", "0.8"]
    ],
    "asks": [
      ["45000.50", "0.3"],
      ["45001.00", "0.7"],
      ["45001.50", "1.1"]
    ],
    "last_update_id": 123456789
  },
  "timestamp": "2025-07-07T12:00:00Z"
}
```

## 💰 交易数据订阅

### 📝 订阅订单更新
**发送**: 订阅请求
```json
{
  "type": "subscribe",
  "channel": "user_orders",
  "data": {
    "user_id": "user_123456"
  }
}
```

**推送**: 订单状态更新
```json
{
  "type": "order_update",
  "channel": "user_orders.user_123456",
  "data": {
    "order_id": "order_123456789",
    "client_order_id": "client_order_001",
    "symbol": "BTCUSDT",
    "side": "BUY",
    "type": "LIMIT",
    "quantity": "0.001",
    "price": "45000.00",
    "executed_qty": "0.0005",
    "remaining_qty": "0.0005",
    "status": "PARTIALLY_FILLED",
    "time_in_force": "GTC",
    "created_at": "2025-07-07T12:00:00Z",
    "updated_at": "2025-07-07T12:03:00Z"
  },
  "timestamp": "2025-07-07T12:03:00Z"
}
```

### 📈 订阅交易执行
**推送**: 交易执行通知
```json
{
  "type": "trade_execution",
  "channel": "user_trades.user_123456",
  "data": {
    "trade_id": "trade_123456789",
    "order_id": "order_123456789",
    "symbol": "BTCUSDT",
    "side": "BUY",
    "quantity": "0.0005",
    "price": "45000.00",
    "fee": "0.0225",
    "fee_asset": "USDT",
    "is_maker": false,
    "executed_at": "2025-07-07T12:03:00Z"
  },
  "timestamp": "2025-07-07T12:03:00Z"
}
```

### 💼 订阅账户余额更新
**发送**: 订阅请求
```json
{
  "type": "subscribe",
  "channel": "user_balance",
  "data": {
    "user_id": "user_123456"
  }
}
```

**推送**: 余额变化通知
```json
{
  "type": "balance_update",
  "channel": "user_balance.user_123456",
  "data": {
    "asset": "USDT",
    "free": "4977.50",
    "locked": "522.50",
    "change_type": "TRADE",
    "change_amount": "-22.50",
    "reference_id": "trade_123456789"
  },
  "timestamp": "2025-07-07T12:03:00Z"
}
```

## 🔔 通知推送

### 📱 订阅系统通知
**发送**: 订阅请求
```json
{
  "type": "subscribe",
  "channel": "user_notifications",
  "data": {
    "user_id": "user_123456"
  }
}
```

**推送**: 系统通知
```json
{
  "type": "notification",
  "channel": "user_notifications.user_123456",
  "data": {
    "notification_id": "notif_123456789",
    "type": "TRADE_EXECUTED",
    "title": "交易执行通知",
    "content": "您的买入订单已成功执行",
    "priority": "HIGH",
    "action_url": "/trade/orders/order_123456789",
    "extra_data": {
      "order_id": "order_123456789",
      "symbol": "BTCUSDT",
      "executed_qty": "0.0005"
    }
  },
  "timestamp": "2025-07-07T12:03:00Z"
}
```

### 🚨 价格提醒
**推送**: 价格到达提醒
```json
{
  "type": "price_alert",
  "channel": "user_alerts.user_123456",
  "data": {
    "alert_id": "alert_123456789",
    "symbol": "BTCUSDT",
    "alert_type": "PRICE_ABOVE",
    "target_price": "45000.00",
    "current_price": "45001.00",
    "message": "BTC价格已突破45000 USDT"
  },
  "timestamp": "2025-07-07T12:00:00Z"
}
```

## 🔄 订阅管理

### ✅ 取消订阅
**发送**: 取消订阅请求
```json
{
  "type": "unsubscribe",
  "channel": "ticker.BTCUSDT"
}
```

**响应**: 取消订阅确认
```json
{
  "type": "unsubscribe_success",
  "channel": "ticker.BTCUSDT",
  "data": {
    "unsubscribed_at": "2025-07-07T12:05:00Z"
  }
}
```

### 📋 查询订阅状态
**发送**: 查询请求
```json
{
  "type": "list_subscriptions"
}
```

**响应**: 订阅列表
```json
{
  "type": "subscription_list",
  "channel": "system",
  "data": {
    "subscriptions": [
      {
        "channel": "ticker.BTCUSDT",
        "subscribed_at": "2025-07-07T12:00:00Z"
      },
      {
        "channel": "kline.BTCUSDT.1m",
        "subscribed_at": "2025-07-07T12:01:00Z"
      }
    ]
  }
}
```

## ❌ 错误处理

### 🔴 错误消息格式
```json
{
  "type": "error",
  "channel": "system",
  "data": {
    "error_code": "ERROR_CODE",
    "message": "错误描述",
    "details": {
      // 错误详细信息
    }
  },
  "timestamp": "2025-07-07T12:00:00Z"
}
```

### 📋 常见错误码
- `WS_001`: 认证失败
- `WS_002`: 订阅频道不存在
- `WS_003`: 订阅权限不足
- `WS_004`: 消息格式错误
- `WS_005`: 连接超时
- `WS_006`: 订阅数量超限

## 🔧 客户端实现示例

### 📱 Flutter/Dart 示例
```dart
class WebSocketClient {
  late WebSocketChannel _channel;
  final StreamController<Map<String, dynamic>> _messageController = 
      StreamController.broadcast();
  
  Future<void> connect(String token) async {
    _channel = WebSocketChannel.connect(
      Uri.parse('wss://ws.financial-app.com/v1/ws'),
      protocols: ['financial-protocol'],
    );
    
    // 监听消息
    _channel.stream.listen(
      (data) {
        final message = jsonDecode(data);
        _messageController.add(message);
      },
      onError: (error) => print('WebSocket错误: $error'),
      onDone: () => print('WebSocket连接关闭'),
    );
    
    // 发送认证消息
    await authenticate(token);
  }
  
  Future<void> authenticate(String token) async {
    send({
      'type': 'auth',
      'token': token,
    });
  }
  
  void subscribe(String channel, Map<String, dynamic> params) {
    send({
      'type': 'subscribe',
      'channel': channel,
      'data': params,
    });
  }
  
  void send(Map<String, dynamic> message) {
    _channel.sink.add(jsonEncode(message));
  }
  
  Stream<Map<String, dynamic>> get messageStream => _messageController.stream;
  
  void dispose() {
    _channel.sink.close();
    _messageController.close();
  }
}
```

### 🌐 JavaScript 示例
```javascript
class WebSocketClient {
  constructor() {
    this.ws = null;
    this.subscriptions = new Set();
    this.messageHandlers = new Map();
  }
  
  connect(token) {
    return new Promise((resolve, reject) => {
      this.ws = new WebSocket('wss://ws.financial-app.com/v1/ws', ['financial-protocol']);
      
      this.ws.onopen = () => {
        this.authenticate(token);
        resolve();
      };
      
      this.ws.onmessage = (event) => {
        const message = JSON.parse(event.data);
        this.handleMessage(message);
      };
      
      this.ws.onerror = (error) => {
        reject(error);
      };
      
      this.ws.onclose = () => {
        console.log('WebSocket连接关闭');
      };
    });
  }
  
  authenticate(token) {
    this.send({
      type: 'auth',
      token: token
    });
  }
  
  subscribe(channel, params = {}) {
    this.send({
      type: 'subscribe',
      channel: channel,
      data: params
    });
    this.subscriptions.add(channel);
  }
  
  onMessage(type, handler) {
    this.messageHandlers.set(type, handler);
  }
  
  handleMessage(message) {
    const handler = this.messageHandlers.get(message.type);
    if (handler) {
      handler(message);
    }
  }
  
  send(message) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message));
    }
  }
  
  disconnect() {
    if (this.ws) {
      this.ws.close();
    }
  }
}
```

## 📊 性能和限制

### 🚀 性能指标
- **连接延迟**: < 100ms
- **消息延迟**: < 50ms
- **并发连接**: 支持10,000+连接
- **消息频率**: 每秒1000+消息

### 📋 使用限制
- **最大订阅数**: 每连接50个频道
- **消息大小**: 最大64KB
- **连接超时**: 60秒无活动自动断开
- **重连间隔**: 建议5-30秒指数退避

---

**🌐 WebSocket API为金融应用提供了高效、实时的数据推送服务，确保用户能够及时获取市场动态和交易信息！**
