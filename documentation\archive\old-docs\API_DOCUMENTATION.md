# API 文档 (API Documentation)

## 📋 目录
- [核心模块 API](#核心模块-api)
- [认证模块 API](#认证模块-api)
- [市场数据模块 API](#市场数据模块-api)
- [交易模块 API](#交易模块-api)
- [投资组合模块 API](#投资组合模块-api)
- [通知模块 API](#通知模块-api)
- [WebSocket 客户端 API](#websocket-客户端-api)
- [错误处理](#错误处理)
- [数据模型](#数据模型)

## 🔧 核心模块 API

### AppConfig
应用配置管理类

```dart
class AppConfig {
  /// 创建开发环境配置
  factory AppConfig.dev();
  
  /// 创建生产环境配置
  factory AppConfig.prod();
  
  /// API 基础URL
  String get apiBaseUrl;
  
  /// WebSocket URL
  String get wsUrl;
  
  /// 是否启用调试模式
  bool get isDebugMode;
  
  /// 应用版本
  String get appVersion;
}
```

### Logger
日志管理类

```dart
class Logger {
  /// 调试日志
  void debug(String message, [Object? error, StackTrace? stackTrace]);
  
  /// 信息日志
  void info(String message, [Object? error, StackTrace? stackTrace]);
  
  /// 警告日志
  void warning(String message, [Object? error, StackTrace? stackTrace]);
  
  /// 错误日志
  void error(String message, [Object? error, StackTrace? stackTrace]);
  
  /// 严重错误日志
  void fatal(String message, [Object? error, StackTrace? stackTrace]);
}
```

### ApiService
API 服务基类

```dart
abstract class ApiService {
  /// GET 请求
  Future<T> get<T>(
    String path, {
    Map<String, dynamic>? queryParameters,
    Map<String, String>? headers,
  });
  
  /// POST 请求
  Future<T> post<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Map<String, String>? headers,
  });
  
  /// PUT 请求
  Future<T> put<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Map<String, String>? headers,
  });
  
  /// DELETE 请求
  Future<T> delete<T>(
    String path, {
    Map<String, dynamic>? queryParameters,
    Map<String, String>? headers,
  });
}
```

## 🔐 认证模块 API

### AuthService
认证服务类

```dart
class AuthService {
  /// 用户登录
  /// 
  /// [username] 用户名
  /// [password] 密码
  /// 返回 [AuthResult] 认证结果
  Future<AuthResult> login(String username, String password);
  
  /// 用户登出
  Future<void> logout();
  
  /// 刷新令牌
  Future<AuthResult> refreshToken();
  
  /// 检查认证状态
  bool get isAuthenticated;
  
  /// 获取当前用户
  User? get currentUser;
  
  /// 认证状态流
  Stream<AuthState> get authStateStream;
}
```

### AuthResult
认证结果模型

```dart
class AuthResult {
  /// 是否成功
  final bool isSuccess;
  
  /// 访问令牌
  final String? accessToken;
  
  /// 刷新令牌
  final String? refreshToken;
  
  /// 用户信息
  final User? user;
  
  /// 错误信息
  final String? errorMessage;
  
  /// 令牌过期时间
  final DateTime? expiresAt;
}
```

### User
用户模型

```dart
class User {
  /// 用户ID
  final String id;
  
  /// 用户名
  final String username;
  
  /// 邮箱
  final String email;
  
  /// 显示名称
  final String displayName;
  
  /// 头像URL
  final String? avatarUrl;
  
  /// 用户角色
  final List<String> roles;
  
  /// 权限列表
  final List<String> permissions;
  
  /// 创建时间
  final DateTime createdAt;
  
  /// 最后登录时间
  final DateTime? lastLoginAt;
}
```

## 📈 市场数据模块 API

### MarketDataService
市场数据服务类

```dart
class MarketDataService {
  /// 获取股票列表
  Future<List<Stock>> getStocks({
    String? market,
    int page = 1,
    int pageSize = 20,
  });
  
  /// 获取股票详情
  Future<Stock> getStockDetail(String symbol);
  
  /// 获取实时行情
  Future<Quote> getQuote(String symbol);
  
  /// 获取K线数据
  Future<List<Kline>> getKlineData(
    String symbol,
    KlineInterval interval,
    DateTime startTime,
    DateTime endTime,
  );
  
  /// 搜索股票
  Future<List<Stock>> searchStocks(String keyword);
  
  /// 订阅实时行情
  Stream<Quote> subscribeQuote(String symbol);
  
  /// 取消订阅
  void unsubscribeQuote(String symbol);
}
```

### Stock
股票模型

```dart
class Stock {
  /// 股票代码
  final String symbol;
  
  /// 股票名称
  final String name;
  
  /// 市场
  final String market;
  
  /// 行业
  final String industry;
  
  /// 当前价格
  final double currentPrice;
  
  /// 涨跌额
  final double change;
  
  /// 涨跌幅
  final double changePercent;
  
  /// 成交量
  final int volume;
  
  /// 成交额
  final double turnover;
  
  /// 市值
  final double marketCap;
  
  /// 市盈率
  final double? pe;
  
  /// 市净率
  final double? pb;
}
```

### Quote
行情数据模型

```dart
class Quote {
  /// 股票代码
  final String symbol;
  
  /// 当前价格
  final double price;
  
  /// 开盘价
  final double open;
  
  /// 最高价
  final double high;
  
  /// 最低价
  final double low;
  
  /// 昨收价
  final double previousClose;
  
  /// 涨跌额
  final double change;
  
  /// 涨跌幅
  final double changePercent;
  
  /// 成交量
  final int volume;
  
  /// 成交额
  final double turnover;
  
  /// 时间戳
  final DateTime timestamp;
  
  /// 买一价
  final double? bid1;
  
  /// 卖一价
  final double? ask1;
  
  /// 买一量
  final int? bidVolume1;
  
  /// 卖一量
  final int? askVolume1;
}
```

## 💰 交易模块 API

### TradeService
交易服务类

```dart
class TradeService {
  /// 下单
  Future<OrderResult> placeOrder(OrderRequest request);
  
  /// 撤单
  Future<bool> cancelOrder(String orderId);
  
  /// 获取订单列表
  Future<List<Order>> getOrders({
    OrderStatus? status,
    DateTime? startDate,
    DateTime? endDate,
    int page = 1,
    int pageSize = 20,
  });
  
  /// 获取订单详情
  Future<Order> getOrderDetail(String orderId);
  
  /// 获取持仓列表
  Future<List<Position>> getPositions();
  
  /// 获取资金账户信息
  Future<Account> getAccount();
  
  /// 获取交易历史
  Future<List<Trade>> getTradeHistory({
    DateTime? startDate,
    DateTime? endDate,
    int page = 1,
    int pageSize = 20,
  });
}
```

### OrderRequest
下单请求模型

```dart
class OrderRequest {
  /// 股票代码
  final String symbol;
  
  /// 订单类型
  final OrderType type;
  
  /// 买卖方向
  final OrderSide side;
  
  /// 数量
  final int quantity;
  
  /// 价格（限价单必填）
  final double? price;
  
  /// 有效期类型
  final TimeInForce timeInForce;
  
  /// 备注
  final String? remark;
}
```

### Order
订单模型

```dart
class Order {
  /// 订单ID
  final String id;
  
  /// 股票代码
  final String symbol;
  
  /// 股票名称
  final String stockName;
  
  /// 订单类型
  final OrderType type;
  
  /// 买卖方向
  final OrderSide side;
  
  /// 订单状态
  final OrderStatus status;
  
  /// 委托数量
  final int quantity;
  
  /// 委托价格
  final double? price;
  
  /// 成交数量
  final int filledQuantity;
  
  /// 成交均价
  final double? avgPrice;
  
  /// 成交金额
  final double filledAmount;
  
  /// 手续费
  final double commission;
  
  /// 创建时间
  final DateTime createdAt;
  
  /// 更新时间
  final DateTime updatedAt;
  
  /// 备注
  final String? remark;
}
```

## 📊 投资组合模块 API

### PortfolioService
投资组合服务类

```dart
class PortfolioService {
  /// 获取投资组合概览
  Future<PortfolioSummary> getPortfolioSummary();
  
  /// 获取持仓列表
  Future<List<Holding>> getHoldings();
  
  /// 获取资产分布
  Future<AssetAllocation> getAssetAllocation();
  
  /// 获取收益分析
  Future<ProfitAnalysis> getProfitAnalysis(
    DateTime startDate,
    DateTime endDate,
  );
  
  /// 获取风险分析
  Future<RiskAnalysis> getRiskAnalysis();
  
  /// 获取历史净值
  Future<List<NetValue>> getNetValueHistory(
    DateTime startDate,
    DateTime endDate,
  );
}
```

### PortfolioSummary
投资组合概览模型

```dart
class PortfolioSummary {
  /// 总资产
  final double totalAssets;
  
  /// 总市值
  final double totalMarketValue;
  
  /// 可用资金
  final double availableCash;
  
  /// 冻结资金
  final double frozenCash;
  
  /// 今日盈亏
  final double todayPnl;
  
  /// 今日盈亏率
  final double todayPnlPercent;
  
  /// 累计盈亏
  final double totalPnl;
  
  /// 累计盈亏率
  final double totalPnlPercent;
  
  /// 持仓数量
  final int holdingCount;
  
  /// 更新时间
  final DateTime updatedAt;
}
```

## 🔔 通知模块 API

### NotificationService
通知服务类

```dart
class NotificationService {
  /// 获取通知列表
  Future<List<Notification>> getNotifications({
    NotificationType? type,
    bool? isRead,
    int page = 1,
    int pageSize = 20,
  });
  
  /// 标记为已读
  Future<void> markAsRead(String notificationId);
  
  /// 批量标记为已读
  Future<void> markAllAsRead();
  
  /// 删除通知
  Future<void> deleteNotification(String notificationId);
  
  /// 获取未读数量
  Future<int> getUnreadCount();
  
  /// 订阅通知
  Stream<Notification> get notificationStream;
  
  /// 设置推送令牌
  Future<void> setPushToken(String token);
  
  /// 更新通知设置
  Future<void> updateNotificationSettings(NotificationSettings settings);
}
```

### Notification
通知模型

```dart
class Notification {
  /// 通知ID
  final String id;
  
  /// 通知类型
  final NotificationType type;
  
  /// 标题
  final String title;
  
  /// 内容
  final String content;
  
  /// 是否已读
  final bool isRead;
  
  /// 创建时间
  final DateTime createdAt;
  
  /// 相关数据
  final Map<String, dynamic>? data;
  
  /// 图标URL
  final String? iconUrl;
  
  /// 跳转链接
  final String? actionUrl;
}
```

## 🌐 WebSocket 客户端 API

### WebSocketClient
WebSocket 客户端类

```dart
class WebSocketClient {
  /// 连接状态
  WebSocketState get state;
  
  /// 连接状态流
  Stream<WebSocketState> get stateStream;
  
  /// 连接
  Future<void> connect();
  
  /// 断开连接
  Future<void> disconnect();
  
  /// 发送消息
  void send(String message);
  
  /// 订阅频道
  void subscribe(String channel, Map<String, dynamic>? params);
  
  /// 取消订阅
  void unsubscribe(String channel);
  
  /// 消息流
  Stream<WebSocketMessage> get messageStream;
  
  /// 设置心跳间隔
  void setHeartbeatInterval(Duration interval);
  
  /// 设置重连策略
  void setReconnectStrategy(ReconnectStrategy strategy);
}
```

### WebSocketMessage
WebSocket 消息模型

```dart
class WebSocketMessage {
  /// 消息类型
  final String type;
  
  /// 频道
  final String channel;
  
  /// 数据
  final Map<String, dynamic> data;
  
  /// 时间戳
  final DateTime timestamp;
  
  /// 消息ID
  final String? messageId;
}
```

## ❌ 错误处理

### 异常类型

```dart
/// 网络异常
class NetworkException implements Exception {
  final String message;
  final int? statusCode;
  NetworkException(this.message, [this.statusCode]);
}

/// 认证异常
class AuthenticationException implements Exception {
  final String message;
  AuthenticationException(this.message);
}

/// 业务异常
class BusinessException implements Exception {
  final String message;
  final String code;
  BusinessException(this.message, this.code);
}

/// 数据解析异常
class ParseException implements Exception {
  final String message;
  ParseException(this.message);
}
```

### 错误响应格式

```json
{
  "success": false,
  "code": "ERROR_CODE",
  "message": "错误描述",
  "data": null,
  "timestamp": "2024-01-01T00:00:00Z"
}
```

## 📋 数据模型

### 通用响应格式

```dart
class ApiResponse<T> {
  final bool success;
  final String? code;
  final String? message;
  final T? data;
  final DateTime timestamp;
  
  const ApiResponse({
    required this.success,
    this.code,
    this.message,
    this.data,
    required this.timestamp,
  });
}
```

### 分页响应格式

```dart
class PagedResponse<T> {
  final List<T> items;
  final int page;
  final int pageSize;
  final int totalCount;
  final int totalPages;
  final bool hasNext;
  final bool hasPrevious;
  
  const PagedResponse({
    required this.items,
    required this.page,
    required this.pageSize,
    required this.totalCount,
    required this.totalPages,
    required this.hasNext,
    required this.hasPrevious,
  });
}
```

---

更多详细的 API 信息请参考各模块的具体文档。
