# 部署指南 (Deployment Guide)

## 📋 目录
- [部署环境](#部署环境)
- [构建配置](#构建配置)
- [Android 部署](#android-部署)
- [iOS 部署](#ios-部署)
- [Web 部署](#web-部署)
- [Docker 部署](#docker-部署)
- [CI/CD 配置](#cicd-配置)
- [监控和日志](#监控和日志)
- [故障排除](#故障排除)

## 🌍 部署环境

### 环境分类
- **开发环境 (Development)**: 本地开发和测试
- **测试环境 (Testing)**: 集成测试和用户验收测试
- **预生产环境 (Staging)**: 生产前最后验证
- **生产环境 (Production)**: 正式运行环境

### 环境配置

#### 开发环境
```yaml
# config/environments.yml
development:
  api_base_url: "https://dev-api.company.com"
  ws_url: "wss://dev-ws.company.com"
  debug_mode: true
  log_level: "debug"
  enable_analytics: false
```

#### 生产环境
```yaml
production:
  api_base_url: "https://api.company.com"
  ws_url: "wss://ws.company.com"
  debug_mode: false
  log_level: "info"
  enable_analytics: true
  enable_crash_reporting: true
```

## 🔧 构建配置

### 构建前检查
```bash
# 运行构建优化工具
dart scripts/build_optimization.dart

# 代码质量检查
dart scripts/fix_code_quality.dart

# 性能分析
dart scripts/performance_analyzer.dart

# 测试覆盖率检查
dart scripts/test_coverage_analyzer.dart
```

### 构建脚本
```bash
#!/bin/bash
# scripts/build.sh

set -e

echo "🚀 开始构建应用..."

# 1. 环境检查
echo "📋 检查构建环境..."
flutter doctor
dart --version

# 2. 依赖安装
echo "📦 安装依赖..."
melos bootstrap

# 3. 代码生成
echo "🔧 生成代码..."
melos run build_runner

# 4. 测试
echo "🧪 运行测试..."
melos run test

# 5. 代码分析
echo "🔍 代码分析..."
melos run analyze

# 6. 构建
echo "🏗️ 构建应用..."
cd packages/financial_app_main

case "$1" in
  "android")
    flutter build apk --release
    flutter build appbundle --release
    ;;
  "ios")
    flutter build ios --release
    ;;
  "web")
    flutter build web --release
    ;;
  *)
    echo "请指定构建平台: android, ios, web"
    exit 1
    ;;
esac

echo "✅ 构建完成!"
```

## 🤖 Android 部署

### 签名配置

#### 1. 生成签名密钥
```bash
keytool -genkey -v -keystore ~/financial-app-key.jks \
  -keyalg RSA -keysize 2048 -validity 10000 \
  -alias financial-app
```

#### 2. 配置签名
```properties
# android/key.properties
storePassword=your_store_password
keyPassword=your_key_password
keyAlias=financial-app
storeFile=../financial-app-key.jks
```

#### 3. 配置 build.gradle
```gradle
// android/app/build.gradle
android {
    signingConfigs {
        release {
            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
            storeFile keystoreProperties['storeFile'] ? file(keystoreProperties['storeFile']) : null
            storePassword keystoreProperties['storePassword']
        }
    }
    
    buildTypes {
        release {
            signingConfig signingConfigs.release
            minifyEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
}
```

### 构建命令
```bash
# 构建 APK
flutter build apk --release --target-platform android-arm64

# 构建 App Bundle (推荐)
flutter build appbundle --release

# 构建多架构 APK
flutter build apk --release --split-per-abi
```

### 发布到内部应用商店
```bash
# 使用内部部署脚本
./scripts/deploy_internal.sh android
```

## 🍎 iOS 部署

### 证书配置

#### 1. 开发者证书
- 在 Apple Developer 控制台创建 App ID
- 生成开发和分发证书
- 创建 Provisioning Profile

#### 2. Xcode 配置
```bash
# 打开 iOS 项目
open ios/Runner.xcworkspace

# 配置签名和证书
# - 选择正确的 Team
# - 配置 Bundle Identifier
# - 选择 Provisioning Profile
```

### 构建命令
```bash
# 构建 iOS 应用
flutter build ios --release

# 构建 IPA 文件
flutter build ipa --release
```

### 发布流程
```bash
# 1. 构建归档
xcodebuild -workspace ios/Runner.xcworkspace \
  -scheme Runner \
  -configuration Release \
  -archivePath build/Runner.xcarchive \
  archive

# 2. 导出 IPA
xcodebuild -exportArchive \
  -archivePath build/Runner.xcarchive \
  -exportPath build/ios \
  -exportOptionsPlist ios/ExportOptions.plist

# 3. 上传到内部分发平台
./scripts/deploy_internal.sh ios
```

## 🌐 Web 部署

### 构建配置
```bash
# 构建 Web 应用
flutter build web --release --web-renderer html

# 优化构建
flutter build web --release \
  --web-renderer html \
  --dart-define=FLUTTER_WEB_USE_SKIA=false \
  --dart-define=FLUTTER_WEB_AUTO_DETECT=false
```

### Nginx 配置
```nginx
# docker/nginx.conf
server {
    listen 80;
    server_name financial-app.company.com;
    
    root /usr/share/nginx/html;
    index index.html;
    
    # 启用 gzip 压缩
    gzip on;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
    
    # 缓存静态资源
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # Flutter Web 路由配置
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    # API 代理
    location /api/ {
        proxy_pass https://api.company.com/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### Docker 部署
```dockerfile
# Dockerfile.web
FROM nginx:alpine

# 复制构建产物
COPY build/web /usr/share/nginx/html

# 复制 Nginx 配置
COPY docker/nginx.conf /etc/nginx/conf.d/default.conf

# 暴露端口
EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
```

## 🐳 Docker 部署

### 多阶段构建 Dockerfile
```dockerfile
# Dockerfile
# 构建阶段
FROM cirrusci/flutter:stable AS builder

WORKDIR /app

# 复制依赖文件
COPY pubspec.yaml melos.yaml ./
COPY packages/*/pubspec.yaml packages/

# 安装依赖
RUN dart pub global activate melos
RUN melos bootstrap

# 复制源代码
COPY . .

# 构建应用
RUN cd packages/financial_app_main && flutter build web --release

# 运行阶段
FROM nginx:alpine

# 复制构建产物
COPY --from=builder /app/packages/financial_app_main/build/web /usr/share/nginx/html

# 复制 Nginx 配置
COPY docker/nginx.conf /etc/nginx/conf.d/default.conf

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost/ || exit 1

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
```

### Docker Compose 配置
```yaml
# docker-compose.yml
version: '3.8'

services:
  financial-app:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "80:80"
    environment:
      - NODE_ENV=production
    volumes:
      - ./logs:/var/log/nginx
    restart: unless-stopped
    
  prometheus:
    image: prom/prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./docker/prometheus.yml:/etc/prometheus/prometheus.yml
    restart: unless-stopped
    
  grafana:
    image: grafana/grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana-data:/var/lib/grafana
    restart: unless-stopped

volumes:
  grafana-data:
```

### 部署命令
```bash
# 构建镜像
docker build -t financial-app:latest .

# 运行容器
docker-compose up -d

# 查看日志
docker-compose logs -f financial-app

# 更新部署
docker-compose pull
docker-compose up -d
```

## 🔄 CI/CD 配置

### GitLab CI 配置
```yaml
# .gitlab-ci.yml
stages:
  - test
  - build
  - deploy

variables:
  FLUTTER_VERSION: "3.16.0"

before_script:
  - apt-get update -qq && apt-get install -y -qq git curl unzip
  - git clone https://github.com/flutter/flutter.git -b stable --depth 1
  - export PATH="$PATH:`pwd`/flutter/bin"
  - flutter doctor -v
  - dart pub global activate melos

test:
  stage: test
  script:
    - melos bootstrap
    - melos run test
    - melos run analyze
  coverage: '/lines......: \d+\.\d+\%/'
  artifacts:
    reports:
      coverage_report:
        coverage_format: lcov
        path: coverage/lcov.info

build_android:
  stage: build
  script:
    - melos bootstrap
    - cd packages/financial_app_main
    - flutter build apk --release
    - flutter build appbundle --release
  artifacts:
    paths:
      - packages/financial_app_main/build/app/outputs/
    expire_in: 1 week
  only:
    - main
    - develop

build_web:
  stage: build
  script:
    - melos bootstrap
    - cd packages/financial_app_main
    - flutter build web --release
  artifacts:
    paths:
      - packages/financial_app_main/build/web/
    expire_in: 1 week
  only:
    - main
    - develop

deploy_staging:
  stage: deploy
  script:
    - docker build -t financial-app:staging .
    - docker tag financial-app:staging registry.company.com/financial-app:staging
    - docker push registry.company.com/financial-app:staging
    - kubectl set image deployment/financial-app financial-app=registry.company.com/financial-app:staging
  environment:
    name: staging
    url: https://staging.financial-app.company.com
  only:
    - develop

deploy_production:
  stage: deploy
  script:
    - docker build -t financial-app:latest .
    - docker tag financial-app:latest registry.company.com/financial-app:latest
    - docker push registry.company.com/financial-app:latest
    - kubectl set image deployment/financial-app financial-app=registry.company.com/financial-app:latest
  environment:
    name: production
    url: https://financial-app.company.com
  when: manual
  only:
    - main
```

### 部署脚本
```bash
#!/bin/bash
# scripts/deploy_internal.sh

set -e

PLATFORM=$1
ENVIRONMENT=${2:-staging}

if [ -z "$PLATFORM" ]; then
    echo "使用方法: $0 <platform> [environment]"
    echo "平台: android, ios, web"
    echo "环境: staging, production (默认: staging)"
    exit 1
fi

echo "🚀 部署 $PLATFORM 到 $ENVIRONMENT 环境..."

# 构建应用
./scripts/build.sh $PLATFORM

# 上传到内部分发平台
case "$PLATFORM" in
    "android")
        echo "📱 上传 Android 应用..."
        # 上传到内部应用商店或分发平台
        ;;
    "ios")
        echo "🍎 上传 iOS 应用..."
        # 上传到内部分发平台
        ;;
    "web")
        echo "🌐 部署 Web 应用..."
        docker build -t financial-app:$ENVIRONMENT .
        docker push registry.company.com/financial-app:$ENVIRONMENT
        kubectl set image deployment/financial-app financial-app=registry.company.com/financial-app:$ENVIRONMENT
        ;;
esac

echo "✅ 部署完成!"
```

## 📊 监控和日志

### 应用监控
```yaml
# docker/prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'financial-app'
    static_configs:
      - targets: ['financial-app:80']
    metrics_path: '/metrics'
    scrape_interval: 30s
```

### 日志配置
```dart
// 应用内日志配置
class LoggingConfig {
  static void configure() {
    if (kReleaseMode) {
      // 生产环境：只记录 info 及以上级别
      Logger.root.level = Level.INFO;
    } else {
      // 开发环境：记录所有级别
      Logger.root.level = Level.ALL;
    }
    
    // 添加日志处理器
    Logger.root.onRecord.listen((record) {
      // 发送到日志收集系统
      sendToLogSystem(record);
    });
  }
}
```

### 健康检查
```dart
// 健康检查端点
@RestApi()
abstract class HealthCheckApi {
  @GET('/health')
  Future<HealthStatus> checkHealth();
}

class HealthStatus {
  final String status;
  final Map<String, dynamic> checks;
  final DateTime timestamp;
}
```

## 🔧 故障排除

### 常见问题

#### 1. 构建失败
```bash
# 清理构建缓存
flutter clean
melos clean
melos bootstrap

# 检查 Flutter 版本
flutter doctor -v
```

#### 2. 签名问题
```bash
# 验证签名配置
keytool -list -v -keystore ~/financial-app-key.jks

# 检查证书有效期
keytool -list -v -keystore ~/financial-app-key.jks | grep Valid
```

#### 3. 部署失败
```bash
# 检查 Docker 镜像
docker images | grep financial-app

# 查看容器日志
docker logs financial-app

# 检查 Kubernetes 状态
kubectl get pods
kubectl describe pod financial-app-xxx
```

#### 4. 性能问题
```bash
# 分析包大小
flutter build apk --analyze-size

# 性能分析
dart scripts/performance_analyzer.dart

# 内存分析
flutter run --profile
```

### 回滚策略
```bash
# 回滚到上一个版本
kubectl rollout undo deployment/financial-app

# 回滚到指定版本
kubectl rollout undo deployment/financial-app --to-revision=2

# 查看回滚状态
kubectl rollout status deployment/financial-app
```

---

更多部署相关信息请参考运维文档或联系运维团队。
