# 开发指南 (Development Guide)

## 📋 目录
- [开发环境设置](#开发环境设置)
- [项目结构](#项目结构)
- [开发流程](#开发流程)
- [代码规范](#代码规范)
- [测试指南](#测试指南)
- [调试技巧](#调试技巧)
- [性能优化](#性能优化)
- [常见问题](#常见问题)

## 🛠️ 开发环境设置

### 必需工具
```bash
# Flutter SDK
flutter --version  # 确保 3.0+

# Dart SDK
dart --version     # 确保 3.0+

# Melos (多包管理)
dart pub global activate melos

# 代码生成工具
dart pub global activate build_runner
```

### IDE 配置

#### VS Code 推荐插件
- Flutter
- Dart
- GitLens
- Error Lens
- Bracket Pair Colorizer

#### Android Studio 推荐插件
- Flutter
- Dart
- Rainbow Brackets

### 自动化环境设置
```bash
# 运行自动化设置脚本
dart scripts/dev_environment_setup.dart

# 验证环境
dart scripts/dev_toolbox.dart --check-env
```

## 🏗️ 项目结构

### 工作空间结构
```
financial_app_workspace/
├── packages/                    # 所有模块包
│   ├── financial_app_main/     # 主应用
│   ├── financial_app_core/     # 核心模块
│   ├── financial_app_auth/     # 认证模块
│   ├── financial_app_market/   # 市场数据模块
│   ├── financial_app_trade/    # 交易模块
│   ├── financial_app_portfolio/ # 投资组合模块
│   ├── financial_app_notification/ # 通知模块
│   ├── financial_ws_client/    # WebSocket客户端
│   ├── financial_app_assets/   # 资源模块
│   └── financial_trading_chart/ # 图表模块
├── scripts/                    # 开发脚本
├── config/                     # 配置文件
├── docs/                       # 文档
└── docker/                     # Docker配置
```

### 模块内部结构
```
financial_app_[module]/
├── lib/
│   ├── src/                    # 源代码
│   │   ├── models/            # 数据模型
│   │   ├── services/          # 业务服务
│   │   ├── widgets/           # UI组件
│   │   ├── pages/             # 页面
│   │   └── utils/             # 工具类
│   ├── [module]_injection.dart # 依赖注入
│   └── [module].dart          # 模块导出
├── test/                      # 测试文件
├── pubspec.yaml              # 包配置
└── README.md                 # 模块文档
```

## 🔄 开发流程

### 1. 功能开发流程
```bash
# 1. 创建功能分支
git checkout -b feature/new-feature

# 2. 开发前检查
dart scripts/dev_toolbox.dart --pre-dev-check

# 3. 开发过程中
# - 遵循代码规范
# - 编写单元测试
# - 定期提交代码

# 4. 开发完成后
dart scripts/fix_code_quality.dart    # 代码质量检查
dart scripts/test_coverage_analyzer.dart  # 测试覆盖率检查
flutter test                          # 运行所有测试

# 5. 提交代码
git add .
git commit -m "feat: add new feature"
git push origin feature/new-feature
```

### 2. 代码审查流程
1. 创建 Merge Request
2. 自动化检查通过
3. 代码审查
4. 修复反馈问题
5. 合并到主分支

### 3. 发布流程
```bash
# 1. 版本更新
melos version

# 2. 构建检查
dart scripts/build_optimization.dart

# 3. 性能检查
dart scripts/performance_analyzer.dart

# 4. 发布构建
flutter build apk --release
```

## 📝 代码规范

### Dart 代码规范
```dart
// 1. 命名规范
class UserService {}           // 类名：PascalCase
void getUserData() {}          // 方法名：camelCase
String userName = '';          // 变量名：camelCase
const String API_URL = '';     // 常量：UPPER_SNAKE_CASE

// 2. 文件命名
user_service.dart             // 文件名：snake_case
user_model.dart
auth_page.dart

// 3. 导入顺序
import 'dart:async';          // Dart 核心库
import 'dart:io';

import 'package:flutter/material.dart';  // Flutter 库
import 'package:provider/provider.dart';  // 第三方库

import 'package:financial_app_core/financial_app_core.dart';  // 项目内部库

import '../models/user.dart';  // 相对导入
```

### 注释规范
```dart
/// 用户服务类
/// 
/// 提供用户相关的业务逻辑处理，包括：
/// - 用户登录/登出
/// - 用户信息管理
/// - 用户权限验证
class UserService {
  /// 获取用户信息
  /// 
  /// [userId] 用户ID
  /// 返回 [User] 用户信息对象
  /// 
  /// 抛出 [UserNotFoundException] 当用户不存在时
  Future<User> getUserById(String userId) async {
    // 实现逻辑
  }
}
```

### 错误处理规范
```dart
// 1. 使用具体的异常类型
class UserNotFoundException implements Exception {
  final String message;
  UserNotFoundException(this.message);
}

// 2. 统一的错误处理
try {
  final user = await userService.getUserById(userId);
  return user;
} on UserNotFoundException catch (e) {
  logger.warning('用户未找到: ${e.message}');
  return null;
} catch (e) {
  logger.error('获取用户信息失败: $e');
  rethrow;
}
```

## 🧪 测试指南

### 测试结构
```
test/
├── unit/                      # 单元测试
│   ├── models/
│   ├── services/
│   └── utils/
├── widget/                    # Widget测试
│   ├── pages/
│   └── widgets/
├── integration/               # 集成测试
└── test_utils/               # 测试工具
```

### 单元测试示例
```dart
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';

void main() {
  group('UserService', () {
    late UserService userService;
    late MockApiService mockApiService;

    setUp(() {
      mockApiService = MockApiService();
      userService = UserService(mockApiService);
    });

    test('should return user when getUserById is called with valid id', () async {
      // Arrange
      const userId = '123';
      final expectedUser = User(id: userId, name: 'Test User');
      when(mockApiService.getUser(userId))
          .thenAnswer((_) async => expectedUser);

      // Act
      final result = await userService.getUserById(userId);

      // Assert
      expect(result, equals(expectedUser));
      verify(mockApiService.getUser(userId)).called(1);
    });
  });
}
```

### Widget测试示例
```dart
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  testWidgets('LoginPage should display login form', (tester) async {
    // Arrange
    await tester.pumpWidget(
      MaterialApp(
        home: LoginPage(),
      ),
    );

    // Act & Assert
    expect(find.byType(TextField), findsNWidgets(2)); // 用户名和密码输入框
    expect(find.byType(ElevatedButton), findsOneWidget); // 登录按钮
    expect(find.text('登录'), findsOneWidget);
  });
}
```

### 测试命令
```bash
# 运行所有测试
flutter test

# 运行特定测试文件
flutter test test/unit/services/user_service_test.dart

# 运行测试并生成覆盖率报告
flutter test --coverage

# 使用自动化测试分析工具
dart scripts/test_coverage_analyzer.dart
```

## 🐛 调试技巧

### 1. 日志调试
```dart
import 'package:financial_app_core/financial_app_core.dart';

class UserService {
  final Logger _logger = GetIt.instance<Logger>();

  Future<User> getUserById(String userId) async {
    _logger.debug('开始获取用户信息: $userId');
    
    try {
      final user = await apiService.getUser(userId);
      _logger.info('成功获取用户信息: ${user.name}');
      return user;
    } catch (e) {
      _logger.error('获取用户信息失败: $e');
      rethrow;
    }
  }
}
```

### 2. Flutter Inspector
- 使用 Flutter Inspector 查看 Widget 树
- 检查 Widget 属性和状态
- 分析布局问题

### 3. 性能调试
```bash
# 启动性能分析
flutter run --profile

# 使用性能分析工具
dart scripts/performance_analyzer.dart
```

### 4. 网络调试
```dart
// 使用 Dio 拦截器记录网络请求
dio.interceptors.add(LogInterceptor(
  requestBody: true,
  responseBody: true,
));
```

## ⚡ 性能优化

### 1. 代码优化
- 使用 const 构造函数
- 避免在 build 方法中创建对象
- 合理使用 setState
- 使用 ListView.builder 处理长列表

### 2. 内存优化
- 及时释放资源
- 使用弱引用
- 避免内存泄漏

### 3. 构建优化
```bash
# 使用构建优化工具
dart scripts/build_optimization.dart

# 分析包大小
flutter build apk --analyze-size
```

## ❓ 常见问题

### Q: 如何添加新的模块？
A: 
1. 在 `packages/` 目录下创建新模块
2. 配置 `pubspec.yaml`
3. 实现依赖注入
4. 在主应用中集成
5. 编写测试和文档

### Q: 如何解决依赖冲突？
A:
```bash
# 清理依赖
melos clean
melos bootstrap

# 检查依赖
dart scripts/dev_toolbox.dart --check-deps
```

### Q: 如何提高构建速度？
A:
1. 使用增量构建
2. 优化依赖结构
3. 使用构建缓存
4. 运行构建优化工具

### Q: 如何调试 WebSocket 连接？
A:
1. 检查网络连接
2. 查看 WebSocket 日志
3. 使用网络抓包工具
4. 检查认证状态

---

更多详细信息请参考其他文档文件。
