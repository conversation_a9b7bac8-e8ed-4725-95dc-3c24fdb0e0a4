# 故障排除指南 (Troubleshooting Guide)

## 📋 目录
- [开发环境问题](#开发环境问题)
- [构建问题](#构建问题)
- [运行时问题](#运行时问题)
- [网络问题](#网络问题)
- [性能问题](#性能问题)
- [依赖问题](#依赖问题)
- [平台特定问题](#平台特定问题)
- [调试工具](#调试工具)

## 🛠️ 开发环境问题

### Flutter Doctor 问题

#### 问题：Flutter doctor 显示错误
```bash
# 检查 Flutter 安装
flutter doctor -v

# 常见解决方案
flutter upgrade
flutter config --android-studio-dir /path/to/android-studio
flutter config --android-sdk /path/to/android-sdk
```

#### 问题：Android 许可证未接受
```bash
# 接受 Android 许可证
flutter doctor --android-licenses

# 如果上述命令失败，手动接受
cd $ANDROID_SDK_ROOT/tools/bin
./sdkmanager --licenses
```

#### 问题：iOS 开发环境配置
```bash
# 安装 CocoaPods
sudo gem install cocoapods

# 更新 CocoaPods
pod repo update

# 清理 iOS 依赖
cd ios && pod deintegrate && pod install
```

### VS Code / Android Studio 问题

#### 问题：IDE 无法识别 Flutter 项目
1. 确保安装了 Flutter 和 Dart 插件
2. 重启 IDE
3. 运行 `flutter pub get`
4. 重新加载项目

#### 问题：代码补全不工作
```bash
# 重新生成分析缓存
flutter clean
flutter pub get
dart pub deps
```

## 🏗️ 构建问题

### 依赖解析失败

#### 问题：pub get 失败
```bash
# 清理依赖缓存
flutter clean
dart pub cache repair

# 使用 Melos 重新安装
melos clean
melos bootstrap

# 检查网络连接
ping pub.dev
```

#### 问题：版本冲突
```yaml
# 在 pubspec.yaml 中使用依赖覆盖
dependency_overrides:
  package_name: ^1.0.0
```

### 代码生成问题

#### 问题：build_runner 失败
```bash
# 清理生成的文件
find . -name "*.g.dart" -delete
find . -name "*.freezed.dart" -delete

# 重新生成
melos run build_runner

# 如果仍然失败，逐个包生成
cd packages/financial_app_core
dart run build_runner build --delete-conflicting-outputs
```

#### 问题：JSON 序列化问题
```dart
// 确保模型类正确注解
@JsonSerializable()
class User {
  final String id;
  final String name;
  
  User({required this.id, required this.name});
  
  factory User.fromJson(Map<String, dynamic> json) => _$UserFromJson(json);
  Map<String, dynamic> toJson() => _$UserToJson(this);
}
```

### 构建错误

#### 问题：Android 构建失败
```bash
# 清理 Android 构建
cd android
./gradlew clean

# 检查 Gradle 版本兼容性
# 更新 android/gradle/wrapper/gradle-wrapper.properties

# 检查 Android SDK 版本
# 更新 android/app/build.gradle 中的 compileSdkVersion
```

#### 问题：iOS 构建失败
```bash
# 清理 iOS 构建
cd ios
rm -rf Pods Podfile.lock
pod install

# 更新 iOS 部署目标
# 在 ios/Flutter/AppFrameworkInfo.plist 中检查版本

# 清理 Xcode 缓存
rm -rf ~/Library/Developer/Xcode/DerivedData
```

## 🚀 运行时问题

### 应用启动问题

#### 问题：应用启动崩溃
```dart
// 添加全局异常处理
void main() {
  FlutterError.onError = (FlutterErrorDetails details) {
    FlutterError.presentError(details);
    // 记录错误到日志系统
    logger.error('Flutter Error: ${details.exception}', details.exception, details.stack);
  };
  
  PlatformDispatcher.instance.onError = (error, stack) {
    logger.error('Platform Error: $error', error, stack);
    return true;
  };
  
  runApp(MyApp());
}
```

#### 问题：依赖注入失败
```dart
// 检查依赖注入配置
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  try {
    await initLocator();
    runApp(MyApp());
  } catch (e) {
    logger.error('依赖注入初始化失败: $e');
    // 显示错误页面或重试
  }
}
```

### 内存问题

#### 问题：内存泄漏
```dart
// 正确释放资源
class MyWidget extends StatefulWidget {
  @override
  _MyWidgetState createState() => _MyWidgetState();
}

class _MyWidgetState extends State<MyWidget> {
  StreamSubscription? _subscription;
  Timer? _timer;
  
  @override
  void initState() {
    super.initState();
    _subscription = stream.listen((data) {
      // 处理数据
    });
  }
  
  @override
  void dispose() {
    _subscription?.cancel();
    _timer?.cancel();
    super.dispose();
  }
}
```

#### 问题：图片内存占用过高
```dart
// 使用缓存网络图片
CachedNetworkImage(
  imageUrl: imageUrl,
  memCacheWidth: 300, // 限制内存中的图片宽度
  memCacheHeight: 300, // 限制内存中的图片高度
  placeholder: (context, url) => CircularProgressIndicator(),
  errorWidget: (context, url, error) => Icon(Icons.error),
)
```

## 🌐 网络问题

### API 请求问题

#### 问题：网络请求超时
```dart
// 配置超时时间
final dio = Dio(BaseOptions(
  connectTimeout: Duration(seconds: 30),
  receiveTimeout: Duration(seconds: 30),
  sendTimeout: Duration(seconds: 30),
));

// 添加重试机制
dio.interceptors.add(RetryInterceptor(
  dio: dio,
  logPrint: print,
  retries: 3,
  retryDelays: [
    Duration(seconds: 1),
    Duration(seconds: 2),
    Duration(seconds: 3),
  ],
));
```

#### 问题：SSL 证书验证失败
```dart
// 仅在开发环境禁用证书验证
if (kDebugMode) {
  (dio.httpClientAdapter as DefaultHttpClientAdapter).onHttpClientCreate = (client) {
    client.badCertificateCallback = (cert, host, port) => true;
    return client;
  };
}
```

### WebSocket 连接问题

#### 问题：WebSocket 连接失败
```dart
class WebSocketManager {
  WebSocketChannel? _channel;
  Timer? _heartbeatTimer;
  int _reconnectAttempts = 0;
  static const int maxReconnectAttempts = 5;
  
  Future<void> connect() async {
    try {
      _channel = WebSocketChannel.connect(Uri.parse(wsUrl));
      _startHeartbeat();
      _reconnectAttempts = 0;
      
      _channel!.stream.listen(
        _onMessage,
        onError: _onError,
        onDone: _onDisconnected,
      );
    } catch (e) {
      logger.error('WebSocket 连接失败: $e');
      _scheduleReconnect();
    }
  }
  
  void _scheduleReconnect() {
    if (_reconnectAttempts < maxReconnectAttempts) {
      _reconnectAttempts++;
      Timer(Duration(seconds: _reconnectAttempts * 2), connect);
    }
  }
}
```

## ⚡ 性能问题

### UI 性能问题

#### 问题：列表滚动卡顿
```dart
// 使用 ListView.builder 而不是 ListView
ListView.builder(
  itemCount: items.length,
  itemBuilder: (context, index) {
    return ListTile(
      title: Text(items[index].title),
    );
  },
)

// 对于复杂列表项，使用 AutomaticKeepAliveClientMixin
class ComplexListItem extends StatefulWidget {
  @override
  _ComplexListItemState createState() => _ComplexListItemState();
}

class _ComplexListItemState extends State<ComplexListItem> 
    with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;
  
  @override
  Widget build(BuildContext context) {
    super.build(context); // 必须调用
    return ComplexWidget();
  }
}
```

#### 问题：动画性能差
```dart
// 使用 AnimationController 而不是隐式动画
class MyAnimatedWidget extends StatefulWidget {
  @override
  _MyAnimatedWidgetState createState() => _MyAnimatedWidgetState();
}

class _MyAnimatedWidgetState extends State<MyAnimatedWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;
  
  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: Duration(milliseconds: 300),
      vsync: this,
    );
    _animation = Tween<double>(begin: 0, end: 1).animate(_controller);
  }
  
  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
}
```

### 应用启动性能

#### 问题：启动时间过长
```dart
// 延迟初始化非关键服务
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // 只初始化关键服务
  await initCriticalServices();
  
  runApp(MyApp());
  
  // 应用启动后初始化其他服务
  WidgetsBinding.instance.addPostFrameCallback((_) {
    initNonCriticalServices();
  });
}
```

## 📦 依赖问题

### 版本冲突

#### 问题：依赖版本不兼容
```bash
# 分析依赖树
flutter pub deps

# 查看特定包的依赖
flutter pub deps --style=compact | grep package_name

# 使用依赖覆盖解决冲突
# 在 pubspec.yaml 中添加
dependency_overrides:
  conflicting_package: ^2.0.0
```

### 包导入问题

#### 问题：找不到包
```dart
// 检查导入路径
import 'package:financial_app_core/financial_app_core.dart'; // 正确
// import 'package:financial_app_core/src/models/user.dart'; // 错误：不应直接导入 src

// 确保包在 pubspec.yaml 中正确声明
dependencies:
  financial_app_core:
    path: ../financial_app_core
```

## 📱 平台特定问题

### Android 问题

#### 问题：权限被拒绝
```dart
// 检查和请求权限
import 'package:permission_handler/permission_handler.dart';

Future<bool> requestPermission(Permission permission) async {
  final status = await permission.status;
  if (status.isGranted) {
    return true;
  }
  
  final result = await permission.request();
  return result.isGranted;
}
```

#### 问题：ProGuard 混淆问题
```proguard
# android/app/proguard-rules.pro
-keep class com.example.financial_app.** { *; }
-keep class io.flutter.** { *; }
-dontwarn io.flutter.**
```

### iOS 问题

#### 问题：Info.plist 配置错误
```xml
<!-- ios/Runner/Info.plist -->
<key>NSCameraUsageDescription</key>
<string>此应用需要访问相机来扫描二维码</string>

<key>NSLocationWhenInUseUsageDescription</key>
<string>此应用需要访问位置信息来提供基于位置的服务</string>
```

## 🔧 调试工具

### 日志调试
```dart
// 使用结构化日志
class StructuredLogger {
  static void logApiCall(String method, String url, int statusCode, Duration duration) {
    logger.info('API调用', extra: {
      'method': method,
      'url': url,
      'statusCode': statusCode,
      'duration': duration.inMilliseconds,
      'timestamp': DateTime.now().toIso8601String(),
    });
  }
}
```

### 性能监控
```dart
// 监控方法执行时间
T measurePerformance<T>(String operation, T Function() function) {
  final stopwatch = Stopwatch()..start();
  try {
    final result = function();
    return result;
  } finally {
    stopwatch.stop();
    logger.debug('$operation 执行时间: ${stopwatch.elapsedMilliseconds}ms');
  }
}
```

### 网络调试
```dart
// Dio 请求/响应拦截器
class LoggingInterceptor extends Interceptor {
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    logger.debug('请求: ${options.method} ${options.uri}');
    logger.debug('请求头: ${options.headers}');
    if (options.data != null) {
      logger.debug('请求体: ${options.data}');
    }
    super.onRequest(options, handler);
  }
  
  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    logger.debug('响应: ${response.statusCode} ${response.requestOptions.uri}');
    logger.debug('响应体: ${response.data}');
    super.onResponse(response, handler);
  }
  
  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    logger.error('请求错误: ${err.message}');
    logger.error('错误详情: ${err.response?.data}');
    super.onError(err, handler);
  }
}
```

### 自动化诊断工具
```bash
# 使用项目提供的诊断工具
dart scripts/dev_toolbox.dart --diagnose

# 性能分析
dart scripts/performance_analyzer.dart

# 代码质量检查
dart scripts/fix_code_quality.dart --check-only
```

## 🆘 获取帮助

### 内部资源
- 技术文档：查看 `docs/` 目录下的相关文档
- 开发工具：使用 `dart scripts/dev_toolbox.dart` 获取帮助
- 团队沟通：联系项目负责人或技术团队

### 外部资源
- [Flutter 官方文档](https://flutter.dev/docs)
- [Dart 官方文档](https://dart.dev/guides)
- [Flutter 社区](https://flutter.dev/community)
- [Stack Overflow](https://stackoverflow.com/questions/tagged/flutter)

### 报告问题
1. 收集错误信息和日志
2. 记录重现步骤
3. 提供环境信息（Flutter 版本、设备信息等）
4. 在内部问题跟踪系统中创建问题

---

如果以上解决方案都无法解决问题，请联系技术团队获取进一步支持。
