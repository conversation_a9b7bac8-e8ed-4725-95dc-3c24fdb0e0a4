# 🔌 WebSocket API 文档

## 📋 文档信息

| 项目 | 值 |
|------|-----|
| **API 版本** | v1.0.0 |
| **协议** | WebSocket (WSS) |
| **基础 URL** | `wss://stream.binance.com:9443/ws` |
| **数据格式** | JSON |
| **编码** | UTF-8 |

## 🌐 连接信息

### 🔗 连接端点

```
生产环境: wss://api.company.com/ws
测试环境: wss://test-api.company.com/ws
开发环境: ws://localhost:8080/ws
```

### 🔐 认证方式

```javascript
// 连接时发送认证信息
{
  "method": "AUTH",
  "params": {
    "apiKey": "your-api-key",
    "timestamp": 1640995200000,
    "signature": "calculated-signature"
  },
  "id": 1
}
```

### 📊 连接限制

| 限制类型 | 值 | 说明 |
|----------|-----|------|
| **最大连接数** | 5 | 每个 API Key 最多 5 个连接 |
| **订阅限制** | 200 | 每个连接最多 200 个订阅 |
| **消息频率** | 10/秒 | 每秒最多发送 10 条消息 |
| **心跳间隔** | 30秒 | 超过 60 秒无消息自动断开 |

## 📊 数据订阅

### 1. 📈 K线数据 (Kline)

#### **订阅请求**
```javascript
{
  "method": "SUBSCRIBE",
  "params": [
    "btcusdt@kline_1m",
    "ethusdt@kline_5m"
  ],
  "id": 1
}
```

#### **数据格式**
```javascript
{
  "stream": "btcusdt@kline_1m",
  "data": {
    "e": "kline",          // 事件类型
    "E": 1640995200000,    // 事件时间
    "s": "BTCUSDT",        // 交易对
    "k": {
      "t": 1640995200000,  // K线开始时间
      "T": 1640995259999,  // K线结束时间
      "s": "BTCUSDT",      // 交易对
      "i": "1m",           // 时间间隔
      "f": 100,            // 第一笔交易ID
      "L": 200,            // 最后一笔交易ID
      "o": "50000.00",     // 开盘价
      "c": "50100.00",     // 收盘价
      "h": "50200.00",     // 最高价
      "l": "49900.00",     // 最低价
      "v": "1000.00",      // 成交量
      "n": 100,            // 成交笔数
      "x": false,          // 是否完结
      "q": "50050000.00",  // 成交额
      "V": "500.00",       // 主动买入成交量
      "Q": "25025000.00",  // 主动买入成交额
      "B": "0"             // 忽略此参数
    }
  }
}
```

#### **支持的时间间隔**
```
1s, 1m, 3m, 5m, 15m, 30m, 1h, 2h, 4h, 6h, 8h, 12h, 1d, 3d, 1w, 1M
```

### 2. 💰 实时价格 (Ticker)

#### **订阅请求**
```javascript
{
  "method": "SUBSCRIBE",
  "params": [
    "btcusdt@ticker",
    "ethusdt@ticker"
  ],
  "id": 2
}
```

#### **数据格式**
```javascript
{
  "stream": "btcusdt@ticker",
  "data": {
    "e": "24hrTicker",     // 事件类型
    "E": 1640995200000,    // 事件时间
    "s": "BTCUSDT",        // 交易对
    "p": "100.00",         // 24小时价格变化
    "P": "0.20",           // 24小时价格变化百分比
    "w": "50050.00",       // 24小时加权平均价
    "x": "50000.00",       // 昨日收盘价
    "c": "50100.00",       // 最新价格
    "Q": "10.00",          // 最新成交量
    "b": "50090.00",       // 最优买价
    "B": "5.00",           // 最优买价数量
    "a": "50110.00",       // 最优卖价
    "A": "3.00",           // 最优卖价数量
    "o": "50000.00",       // 24小时开盘价
    "h": "50200.00",       // 24小时最高价
    "l": "49800.00",       // 24小时最低价
    "v": "10000.00",       // 24小时成交量
    "q": "500500000.00",   // 24小时成交额
    "O": 1640908800000,    // 统计开始时间
    "C": 1640995200000,    // 统计结束时间
    "F": 1000,             // 第一笔交易ID
    "L": 2000,             // 最后一笔交易ID
    "n": 1000              // 24小时成交笔数
  }
}
```

### 3. 💱 逐笔交易 (Trade)

#### **订阅请求**
```javascript
{
  "method": "SUBSCRIBE",
  "params": [
    "btcusdt@trade"
  ],
  "id": 3
}
```

#### **数据格式**
```javascript
{
  "stream": "btcusdt@trade",
  "data": {
    "e": "trade",          // 事件类型
    "E": 1640995200000,    // 事件时间
    "s": "BTCUSDT",        // 交易对
    "t": 12345,            // 交易ID
    "p": "50100.00",       // 成交价格
    "q": "0.001",          // 成交数量
    "b": 88,               // 买方订单ID
    "a": 50,               // 卖方订单ID
    "T": 1640995200000,    // 成交时间
    "m": true,             // 买方是否为挂单方
    "M": true              // 忽略此字段
  }
}
```

### 4. 📊 深度信息 (Depth)

#### **订阅请求**
```javascript
{
  "method": "SUBSCRIBE",
  "params": [
    "btcusdt@depth20@100ms"
  ],
  "id": 4
}
```

#### **数据格式**
```javascript
{
  "stream": "btcusdt@depth20",
  "data": {
    "e": "depthUpdate",    // 事件类型
    "E": 1640995200000,    // 事件时间
    "s": "BTCUSDT",        // 交易对
    "U": 157,              // 从上次推送至今新增的第一个 update Id
    "u": 160,              // 从上次推送至今新增的最后一个 update Id
    "b": [                 // 买盘
      [
        "50090.00",        // 价格
        "1.25"             // 数量
      ]
    ],
    "a": [                 // 卖盘
      [
        "50110.00",        // 价格
        "0.75"             // 数量
      ]
    ]
  }
}
```

#### **支持的深度级别**
```
5, 10, 20 (推荐)
```

#### **支持的更新频率**
```
100ms, 1000ms (推荐)
```

## 🔄 订阅管理

### 📝 订阅操作

#### **订阅数据流**
```javascript
{
  "method": "SUBSCRIBE",
  "params": [
    "btcusdt@kline_1m",
    "btcusdt@ticker"
  ],
  "id": 1
}
```

#### **取消订阅**
```javascript
{
  "method": "UNSUBSCRIBE",
  "params": [
    "btcusdt@kline_1m"
  ],
  "id": 2
}
```

#### **查询订阅列表**
```javascript
{
  "method": "LIST_SUBSCRIPTIONS",
  "id": 3
}
```

#### **响应格式**
```javascript
{
  "result": [
    "btcusdt@kline_1m",
    "btcusdt@ticker"
  ],
  "id": 3
}
```

### 🔄 连接管理

#### **心跳检测**
```javascript
// 客户端发送
{
  "method": "PING",
  "id": 4
}

// 服务器响应
{
  "result": "PONG",
  "id": 4
}
```

#### **连接状态**
```javascript
// 连接成功
{
  "result": "connected",
  "id": null
}

// 连接错误
{
  "error": {
    "code": -1,
    "msg": "Invalid symbol"
  },
  "id": 1
}
```

## 🛡️ 错误处理

### 📋 错误代码

| 错误代码 | 说明 | 解决方案 |
|----------|------|----------|
| **-1** | 无效参数 | 检查请求参数格式 |
| **-2** | 订阅数量超限 | 减少订阅数量 |
| **-3** | 认证失败 | 检查 API Key 和签名 |
| **-4** | 频率限制 | 降低请求频率 |
| **-5** | 服务器内部错误 | 稍后重试 |

### 🔄 重连策略

```javascript
class WebSocketManager {
  constructor() {
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectDelay = 1000; // 1秒
  }

  connect() {
    this.ws = new WebSocket('wss://api.company.com/ws');
    
    this.ws.onopen = () => {
      console.log('WebSocket 连接成功');
      this.reconnectAttempts = 0;
    };

    this.ws.onclose = () => {
      console.log('WebSocket 连接关闭');
      this.scheduleReconnect();
    };

    this.ws.onerror = (error) => {
      console.error('WebSocket 错误:', error);
    };
  }

  scheduleReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      setTimeout(() => {
        this.reconnectAttempts++;
        console.log(`重连尝试 ${this.reconnectAttempts}`);
        this.connect();
      }, this.reconnectDelay * Math.pow(2, this.reconnectAttempts));
    }
  }
}
```

## 📊 性能优化

### 🎯 订阅优化

```javascript
// ✅ 推荐：批量订阅
{
  "method": "SUBSCRIBE",
  "params": [
    "btcusdt@kline_1m",
    "btcusdt@ticker",
    "ethusdt@kline_1m",
    "ethusdt@ticker"
  ],
  "id": 1
}

// ❌ 不推荐：单独订阅
// 多次发送单个订阅请求会增加网络开销
```

### 📈 数据处理优化

```javascript
class DataProcessor {
  constructor() {
    this.dataBuffer = new Map();
    this.batchSize = 10;
    this.batchTimeout = 100; // 100ms
  }

  processKlineData(data) {
    const symbol = data.s;
    
    // 缓存数据
    if (!this.dataBuffer.has(symbol)) {
      this.dataBuffer.set(symbol, []);
    }
    
    this.dataBuffer.get(symbol).push(data);
    
    // 批量处理
    if (this.dataBuffer.get(symbol).length >= this.batchSize) {
      this.flushBuffer(symbol);
    }
  }

  flushBuffer(symbol) {
    const data = this.dataBuffer.get(symbol);
    if (data && data.length > 0) {
      // 批量更新 UI
      this.updateChart(symbol, data);
      this.dataBuffer.set(symbol, []);
    }
  }
}
```

## 🧪 测试和调试

### 🔧 连接测试

```bash
# 使用 wscat 测试连接
npm install -g wscat
wscat -c wss://stream.binance.com:9443/ws

# 发送订阅请求
{"method":"SUBSCRIBE","params":["btcusdt@kline_1m"],"id":1}
```

### 📊 性能监控

```javascript
class PerformanceMonitor {
  constructor() {
    this.metrics = {
      messagesReceived: 0,
      messagesPerSecond: 0,
      averageLatency: 0,
      connectionUptime: 0
    };
    
    this.startTime = Date.now();
    this.lastMessageTime = Date.now();
  }

  recordMessage(timestamp) {
    this.metrics.messagesReceived++;
    
    // 计算延迟
    const latency = Date.now() - timestamp;
    this.updateAverageLatency(latency);
    
    // 计算消息频率
    this.updateMessageRate();
    
    this.lastMessageTime = Date.now();
  }

  updateAverageLatency(latency) {
    const count = this.metrics.messagesReceived;
    this.metrics.averageLatency = 
      (this.metrics.averageLatency * (count - 1) + latency) / count;
  }

  updateMessageRate() {
    const elapsed = (Date.now() - this.startTime) / 1000;
    this.metrics.messagesPerSecond = 
      this.metrics.messagesReceived / elapsed;
  }

  getMetrics() {
    return {
      ...this.metrics,
      connectionUptime: Date.now() - this.startTime
    };
  }
}
```

## 📚 使用示例

### 🚀 Flutter 集成示例

```dart
import 'dart:convert';
import 'package:web_socket_channel/web_socket_channel.dart';

class WebSocketClient {
  WebSocketChannel? _channel;
  
  Future<void> connect() async {
    _channel = WebSocketChannel.connect(
      Uri.parse('wss://stream.binance.com:9443/ws'),
    );
    
    _channel!.stream.listen(
      (message) {
        final data = jsonDecode(message);
        _handleMessage(data);
      },
      onError: (error) {
        print('WebSocket 错误: $error');
      },
      onDone: () {
        print('WebSocket 连接关闭');
      },
    );
  }
  
  void subscribeKline(String symbol, String interval) {
    final request = {
      'method': 'SUBSCRIBE',
      'params': ['${symbol.toLowerCase()}@kline_$interval'],
      'id': DateTime.now().millisecondsSinceEpoch,
    };
    
    _channel?.sink.add(jsonEncode(request));
  }
  
  void _handleMessage(Map<String, dynamic> data) {
    if (data['stream']?.contains('@kline_') == true) {
      _handleKlineData(data);
    }
  }
  
  void _handleKlineData(Map<String, dynamic> data) {
    final klineInfo = data['data']['k'];
    print('收到K线数据: ${klineInfo['s']} - ${klineInfo['c']}');
  }
}
```

---

## 📞 技术支持

### 🆘 问题反馈
- **API 问题**: <EMAIL>
- **连接问题**: <EMAIL>
- **紧急问题**: <EMAIL>

### 📚 相关文档
- [WebSocket 架构设计](../architecture/WEBSOCKET_ARCHITECTURE.md)
- [数据模型文档](DATA_MODELS.md)
- [错误处理指南](../development/ERROR_HANDLING.md)

---

**🎯 高效使用 WebSocket API，构建实时金融应用！** 🚀✨
