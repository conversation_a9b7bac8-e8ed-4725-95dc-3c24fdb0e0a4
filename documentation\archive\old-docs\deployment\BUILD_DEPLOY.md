# 📦 构建部署指南

## 📋 文档信息

| 项目 | 值 |
|------|-----|
| **文档版本** | v1.0.0 |
| **适用环境** | 生产、测试、开发 |
| **更新日期** | 2024-12-19 |
| **负责团队** | DevOps 团队 |

## 🎯 部署概述

本文档详细说明了 Financial App Workspace 的构建和部署流程，包括多平台构建、环境配置、CI/CD 流程等。

### 🏗️ 支持平台

| 平台 | 状态 | 构建命令 | 输出格式 |
|------|------|----------|----------|
| **Android** | ✅ 支持 | `flutter build apk/appbundle` | APK/AAB |
| **iOS** | ✅ 支持 | `flutter build ios` | IPA |
| **Web** | ✅ 支持 | `flutter build web` | HTML/JS |
| **Windows** | 🚧 计划中 | `flutter build windows` | EXE |
| **macOS** | 🚧 计划中 | `flutter build macos` | APP |

## 🛠️ 环境准备

### 1. 📋 基础要求

```bash
# Flutter SDK
Flutter 3.16.0 或更高版本
Dart 3.2.0 或更高版本

# 平台工具
Android Studio 2023.1+
Xcode 15.0+ (iOS 构建)
Chrome 120+ (Web 测试)

# 构建工具
Git 2.40+
Node.js 18+ (Web 部署)
Docker 24+ (容器化部署)
```

### 2. 🔧 环境配置

#### **Android 环境**
```bash
# 安装 Android SDK
export ANDROID_HOME=$HOME/Android/Sdk
export PATH=$PATH:$ANDROID_HOME/tools
export PATH=$PATH:$ANDROID_HOME/platform-tools

# 验证环境
flutter doctor
```

#### **iOS 环境**
```bash
# 安装 Xcode Command Line Tools
sudo xcode-select --install

# 安装 CocoaPods
sudo gem install cocoapods

# 验证环境
flutter doctor
```

#### **Web 环境**
```bash
# 启用 Web 支持
flutter config --enable-web

# 验证环境
flutter devices
```

## 🏗️ 构建流程

### 1. 📱 Android 构建

#### **开发版本**
```bash
# 构建 Debug APK
flutter build apk --debug

# 构建 Debug AAB
flutter build appbundle --debug

# 输出位置
build/app/outputs/flutter-apk/app-debug.apk
build/app/outputs/bundle/debug/app-debug.aab
```

#### **生产版本**
```bash
# 构建 Release APK
flutter build apk --release --obfuscate --split-debug-info=build/debug-info

# 构建 Release AAB (推荐)
flutter build appbundle --release --obfuscate --split-debug-info=build/debug-info

# 输出位置
build/app/outputs/flutter-apk/app-release.apk
build/app/outputs/bundle/release/app-release.aab
```

#### **多架构构建**
```bash
# 构建多架构 APK
flutter build apk --split-per-abi --release

# 输出文件
app-arm64-v8a-release.apk    # 64位 ARM
app-armeabi-v7a-release.apk  # 32位 ARM
app-x86_64-release.apk       # 64位 x86
```

### 2. 🍎 iOS 构建

#### **开发版本**
```bash
# 构建 Debug 版本
flutter build ios --debug

# 在模拟器运行
flutter run -d "iPhone 15 Pro"
```

#### **生产版本**
```bash
# 构建 Release 版本
flutter build ios --release --obfuscate --split-debug-info=build/debug-info

# 构建 IPA 文件
flutter build ipa --release --obfuscate --split-debug-info=build/debug-info

# 输出位置
build/ios/ipa/financial_app_workspace.ipa
```

#### **证书配置**
```bash
# 配置签名证书
# 在 ios/Runner.xcworkspace 中配置
# - Team ID
# - Bundle Identifier
# - Provisioning Profile
```

### 3. 🌐 Web 构建

#### **开发版本**
```bash
# 构建 Debug 版本
flutter build web --debug

# 本地服务器运行
flutter run -d chrome --web-port=8080
```

#### **生产版本**
```bash
# 构建 Release 版本
flutter build web --release --web-renderer canvaskit

# 构建优化版本
flutter build web --release \
  --web-renderer canvaskit \
  --dart-define=FLUTTER_WEB_USE_SKIA=true \
  --dart-define=FLUTTER_WEB_AUTO_DETECT=true

# 输出位置
build/web/
```

## 🔧 配置管理

### 1. 🌍 环境配置

#### **环境变量文件**
```bash
# .env.development
ENVIRONMENT=development
API_BASE_URL=https://dev-api.company.com
WEBSOCKET_URL=wss://dev-stream.company.com/ws
LOG_LEVEL=debug
ENABLE_ANALYTICS=false

# .env.testing
ENVIRONMENT=testing
API_BASE_URL=https://test-api.company.com
WEBSOCKET_URL=wss://test-stream.company.com/ws
LOG_LEVEL=info
ENABLE_ANALYTICS=false

# .env.production
ENVIRONMENT=production
API_BASE_URL=https://api.company.com
WEBSOCKET_URL=wss://stream.company.com/ws
LOG_LEVEL=error
ENABLE_ANALYTICS=true
```

#### **Flutter 配置**
```dart
// lib/config/app_config.dart
class AppConfig {
  static const String environment = String.fromEnvironment(
    'ENVIRONMENT',
    defaultValue: 'development',
  );
  
  static const String apiBaseUrl = String.fromEnvironment(
    'API_BASE_URL',
    defaultValue: 'https://dev-api.company.com',
  );
  
  static const String websocketUrl = String.fromEnvironment(
    'WEBSOCKET_URL',
    defaultValue: 'wss://dev-stream.company.com/ws',
  );
  
  static const bool enableAnalytics = bool.fromEnvironment(
    'ENABLE_ANALYTICS',
    defaultValue: false,
  );
  
  static bool get isProduction => environment == 'production';
  static bool get isDevelopment => environment == 'development';
  static bool get isTesting => environment == 'testing';
}
```

### 2. 📱 平台配置

#### **Android 配置**
```gradle
// android/app/build.gradle
android {
    compileSdkVersion 34
    
    defaultConfig {
        applicationId "com.company.financial_app"
        minSdkVersion 21
        targetSdkVersion 34
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName
        
        // 多 DEX 支持
        multiDexEnabled true
    }
    
    buildTypes {
        debug {
            applicationIdSuffix ".debug"
            debuggable true
            minifyEnabled false
        }
        
        release {
            debuggable false
            minifyEnabled true
            useProguard true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            
            // 签名配置
            signingConfig signingConfigs.release
        }
    }
    
    flavorDimensions "environment"
    productFlavors {
        development {
            dimension "environment"
            applicationIdSuffix ".dev"
            versionNameSuffix "-dev"
        }
        
        testing {
            dimension "environment"
            applicationIdSuffix ".test"
            versionNameSuffix "-test"
        }
        
        production {
            dimension "environment"
        }
    }
}
```

#### **iOS 配置**
```xml
<!-- ios/Runner/Info.plist -->
<dict>
    <key>CFBundleDisplayName</key>
    <string>Financial App</string>
    
    <key>CFBundleIdentifier</key>
    <string>com.company.financial-app</string>
    
    <key>CFBundleVersion</key>
    <string>$(FLUTTER_BUILD_NUMBER)</string>
    
    <key>CFBundleShortVersionString</key>
    <string>$(FLUTTER_BUILD_NAME)</string>
    
    <!-- 网络权限 -->
    <key>NSAppTransportSecurity</key>
    <dict>
        <key>NSAllowsArbitraryLoads</key>
        <false/>
        <key>NSExceptionDomains</key>
        <dict>
            <key>company.com</key>
            <dict>
                <key>NSExceptionAllowsInsecureHTTPLoads</key>
                <false/>
                <key>NSExceptionMinimumTLSVersion</key>
                <string>TLSv1.2</string>
            </dict>
        </dict>
    </dict>
</dict>
```

## 🚀 CI/CD 流程

### 1. 📋 GitLab CI 配置

```yaml
# .gitlab-ci.yml
stages:
  - test
  - build
  - deploy

variables:
  FLUTTER_VERSION: "3.16.0"
  ANDROID_SDK_VERSION: "34"

# 缓存配置
cache:
  paths:
    - .pub-cache/
    - build/

# 测试阶段
test:
  stage: test
  image: cirrusci/flutter:$FLUTTER_VERSION
  script:
    - flutter pub get
    - flutter analyze
    - flutter test --coverage
    - genhtml coverage/lcov.info -o coverage/html
  artifacts:
    reports:
      coverage_report:
        coverage_format: cobertura
        path: coverage/cobertura.xml
    paths:
      - coverage/
  coverage: '/lines......: \d+\.\d+%/'

# Android 构建
build_android:
  stage: build
  image: cirrusci/flutter:$FLUTTER_VERSION
  before_script:
    - apt-get update && apt-get install -y openjdk-11-jdk
    - export JAVA_HOME=/usr/lib/jvm/java-11-openjdk-amd64
  script:
    - flutter pub get
    - flutter build appbundle --release --dart-define=ENVIRONMENT=production
  artifacts:
    paths:
      - build/app/outputs/bundle/release/
    expire_in: 1 week
  only:
    - main
    - develop

# iOS 构建
build_ios:
  stage: build
  tags:
    - macos
  script:
    - flutter pub get
    - flutter build ios --release --dart-define=ENVIRONMENT=production
    - flutter build ipa --release --dart-define=ENVIRONMENT=production
  artifacts:
    paths:
      - build/ios/ipa/
    expire_in: 1 week
  only:
    - main
    - develop

# Web 构建
build_web:
  stage: build
  image: cirrusci/flutter:$FLUTTER_VERSION
  script:
    - flutter pub get
    - flutter build web --release --dart-define=ENVIRONMENT=production
  artifacts:
    paths:
      - build/web/
    expire_in: 1 week
  only:
    - main
    - develop

# 部署到测试环境
deploy_testing:
  stage: deploy
  image: alpine:latest
  before_script:
    - apk add --no-cache curl
  script:
    - echo "部署到测试环境"
    - curl -X POST "$DEPLOY_WEBHOOK_URL" -d "environment=testing"
  environment:
    name: testing
    url: https://test-app.company.com
  only:
    - develop

# 部署到生产环境
deploy_production:
  stage: deploy
  image: alpine:latest
  before_script:
    - apk add --no-cache curl
  script:
    - echo "部署到生产环境"
    - curl -X POST "$DEPLOY_WEBHOOK_URL" -d "environment=production"
  environment:
    name: production
    url: https://app.company.com
  when: manual
  only:
    - main
```

### 2. 🔧 构建脚本

#### **自动化构建脚本**
```bash
#!/bin/bash
# scripts/build.sh

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 函数定义
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 参数解析
PLATFORM=""
ENVIRONMENT="development"
BUILD_TYPE="debug"

while [[ $# -gt 0 ]]; do
    case $1 in
        -p|--platform)
            PLATFORM="$2"
            shift 2
            ;;
        -e|--environment)
            ENVIRONMENT="$2"
            shift 2
            ;;
        -t|--type)
            BUILD_TYPE="$2"
            shift 2
            ;;
        *)
            log_error "未知参数: $1"
            exit 1
            ;;
    esac
done

# 验证参数
if [[ -z "$PLATFORM" ]]; then
    log_error "请指定平台: -p android|ios|web"
    exit 1
fi

log_info "开始构建..."
log_info "平台: $PLATFORM"
log_info "环境: $ENVIRONMENT"
log_info "类型: $BUILD_TYPE"

# 清理和准备
log_info "清理构建缓存..."
flutter clean
flutter pub get

# 运行测试
log_info "运行测试..."
flutter analyze
flutter test

# 构建应用
case $PLATFORM in
    android)
        log_info "构建 Android 应用..."
        if [[ "$BUILD_TYPE" == "release" ]]; then
            flutter build appbundle --release \
                --obfuscate \
                --split-debug-info=build/debug-info \
                --dart-define=ENVIRONMENT=$ENVIRONMENT
        else
            flutter build apk --debug \
                --dart-define=ENVIRONMENT=$ENVIRONMENT
        fi
        ;;
    ios)
        log_info "构建 iOS 应用..."
        if [[ "$BUILD_TYPE" == "release" ]]; then
            flutter build ipa --release \
                --obfuscate \
                --split-debug-info=build/debug-info \
                --dart-define=ENVIRONMENT=$ENVIRONMENT
        else
            flutter build ios --debug \
                --dart-define=ENVIRONMENT=$ENVIRONMENT
        fi
        ;;
    web)
        log_info "构建 Web 应用..."
        if [[ "$BUILD_TYPE" == "release" ]]; then
            flutter build web --release \
                --web-renderer canvaskit \
                --dart-define=ENVIRONMENT=$ENVIRONMENT
        else
            flutter build web --debug \
                --dart-define=ENVIRONMENT=$ENVIRONMENT
        fi
        ;;
    *)
        log_error "不支持的平台: $PLATFORM"
        exit 1
        ;;
esac

log_info "构建完成！"
```

#### **使用示例**
```bash
# 构建 Android 生产版本
./scripts/build.sh -p android -e production -t release

# 构建 iOS 测试版本
./scripts/build.sh -p ios -e testing -t release

# 构建 Web 开发版本
./scripts/build.sh -p web -e development -t debug
```

## 📊 性能优化

### 1. ⚡ 构建优化

#### **Android 优化**
```gradle
// android/app/build.gradle
android {
    buildTypes {
        release {
            // 启用代码混淆
            minifyEnabled true
            useProguard true
            
            // 启用资源压缩
            shrinkResources true
            
            // 优化 APK
            zipAlignEnabled true
        }
    }
    
    // 启用 R8 优化
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
}
```

#### **iOS 优化**
```bash
# 构建时启用优化
flutter build ios --release \
  --tree-shake-icons \
  --split-debug-info=build/debug-info \
  --obfuscate
```

#### **Web 优化**
```bash
# 构建优化的 Web 版本
flutter build web --release \
  --web-renderer canvaskit \
  --tree-shake-icons \
  --dart-define=FLUTTER_WEB_USE_SKIA=true
```

### 2. 📦 包体积优化

```yaml
# pubspec.yaml
flutter:
  # 只包含使用的字体
  fonts:
    - family: Roboto
      fonts:
        - asset: fonts/Roboto-Regular.ttf
        - asset: fonts/Roboto-Bold.ttf
          weight: 700

  # 只包含需要的资源
  assets:
    - assets/images/logo.png
    - assets/icons/
```

## 🛡️ 安全配置

### 1. 🔐 代码混淆

```bash
# 启用代码混淆
flutter build apk --release \
  --obfuscate \
  --split-debug-info=build/debug-info
```

### 2. 🔑 密钥管理

```bash
# 创建密钥库
keytool -genkey -v -keystore key.jks \
  -keyalg RSA -keysize 2048 -validity 10000 \
  -alias key

# 配置签名
# android/key.properties
storePassword=your-store-password
keyPassword=your-key-password
keyAlias=key
storeFile=key.jks
```

## 📊 监控和日志

### 1. 📈 性能监控

```dart
// lib/utils/performance_monitor.dart
class PerformanceMonitor {
  static void trackBuildTime(String buildType, Duration duration) {
    print('构建时间 [$buildType]: ${duration.inSeconds}秒');
  }
  
  static void trackAppSize(String platform, int sizeInBytes) {
    final sizeInMB = sizeInBytes / (1024 * 1024);
    print('应用大小 [$platform]: ${sizeInMB.toStringAsFixed(2)}MB');
  }
}
```

### 2. 📋 构建日志

```bash
# 详细构建日志
flutter build apk --release --verbose > build.log 2>&1

# 分析构建日志
grep -i "error\|warning" build.log
```

---

## 🆘 故障排除

### 📋 常见问题

| 问题 | 解决方案 |
|------|----------|
| **构建失败** | 检查 Flutter 版本和依赖 |
| **签名错误** | 验证密钥库和签名配置 |
| **内存不足** | 增加构建机器内存 |
| **网络超时** | 配置代理或镜像源 |

### 📞 技术支持

- **构建问题**: <EMAIL>
- **部署问题**: <EMAIL>
- **紧急问题**: <EMAIL>

---

**🎯 高效构建，稳定部署！** 🚀✨
