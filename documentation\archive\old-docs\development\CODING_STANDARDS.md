# 📝 编码规范指南

## 📋 文档信息

| 项目 | 值 |
|------|-----|
| **文档版本** | v1.0.0 |
| **适用范围** | 所有开发人员 |
| **强制执行** | ✅ 必须遵守 |
| **检查工具** | dart analyze, flutter_lints |

## 🎯 规范目标

- 🔧 **代码一致性** - 统一的代码风格和结构
- 📖 **可读性** - 清晰易懂的代码逻辑
- 🛡️ **可维护性** - 易于修改和扩展
- 🧪 **可测试性** - 便于编写和执行测试
- 👥 **团队协作** - 降低沟通成本

## 📁 文件和目录结构

### 🏗️ 项目结构规范

```
lib/
├── main.dart                    # 应用入口
├── app/                         # 应用级配置
│   ├── app.dart                # 主应用组件
│   ├── routes/                 # 路由配置
│   └── themes/                 # 主题配置
├── core/                       # 核心功能
│   ├── constants/              # 常量定义
│   ├── utils/                  # 工具类
│   ├── services/               # 服务类
│   └── extensions/             # 扩展方法
├── features/                   # 功能模块
│   └── feature_name/
│       ├── data/               # 数据层
│       ├── domain/             # 业务逻辑层
│       └── presentation/       # 表现层
├── shared/                     # 共享组件
│   ├── widgets/                # 通用组件
│   ├── models/                 # 数据模型
│   └── constants/              # 共享常量
└── generated/                  # 自动生成文件
```

### 📝 文件命名规范

```dart
// ✅ 正确的文件命名
user_profile_page.dart          // 页面文件
price_monitor_widget.dart       // 组件文件
websocket_service.dart          // 服务文件
kline_data_model.dart          // 数据模型
app_constants.dart             // 常量文件
string_extensions.dart         // 扩展文件

// ❌ 错误的文件命名
UserProfilePage.dart           // 不要使用大驼峰
userprofilepage.dart          // 不要全小写
user-profile-page.dart        // 不要使用连字符
```

## 🔤 命名规范

### 1. 📦 包和导入

```dart
// ✅ 正确的导入顺序
// 1. Dart 核心库
import 'dart:async';
import 'dart:convert';

// 2. Flutter 框架
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

// 3. 第三方包
import 'package:provider/provider.dart';
import 'package:http/http.dart' as http;

// 4. 项目内部包
import 'package:financial_app_core/financial_app_core.dart';

// 5. 相对导入
import '../models/user_model.dart';
import '../services/api_service.dart';
```

### 2. 🏷️ 类和接口命名

```dart
// ✅ 正确的类命名
class UserProfilePage extends StatefulWidget {}
class WebSocketService {}
class KLineDataModel {}
abstract class PaymentRepository {}
mixin ValidationMixin {}

// ❌ 错误的类命名
class userProfilePage {}              // 首字母应大写
class WebsocketService {}             // 应保持原有大小写
class KlineDataModel {}               // 应保持 KLine 的大小写
```

### 3. 🔧 方法和变量命名

```dart
// ✅ 正确的方法命名
void initializeWebSocket() {}         // 动词开头，描述性
bool isConnected() {}                 // 布尔值用 is/has/can 开头
String getUserName() {}               // get 开头的获取方法
void setUserName(String name) {}      // set 开头的设置方法

// ✅ 正确的变量命名
String userName;                      // 驼峰命名
bool isLoading;                       // 布尔值描述性命名
List<KLineData> klineDataList;        // 集合类型明确
final String apiBaseUrl;              // 常量使用 final

// ❌ 错误的命名
String user_name;                     // 不要使用下划线
bool loading;                         // 布尔值应该更描述性
List<KLineData> data;                 // 变量名太泛化
String API_BASE_URL;                  // 不要全大写
```

### 4. 🎯 常量命名

```dart
// ✅ 正确的常量命名
class AppConstants {
  static const String appName = 'Financial App';
  static const Duration connectionTimeout = Duration(seconds: 30);
  static const double defaultPadding = 16.0;
}

class ApiEndpoints {
  static const String baseUrl = 'https://api.company.com';
  static const String websocketUrl = 'wss://stream.company.com';
}

// ❌ 错误的常量命名
const String APP_NAME = 'Financial App';     // 不要全大写
const String appname = 'Financial App';      // 应该使用驼峰命名
```

## 🏗️ 代码结构规范

### 1. 📱 Widget 结构

```dart
// ✅ 推荐的 Widget 结构
class PriceMonitorWidget extends StatefulWidget {
  // 1. 构造函数参数
  final String symbol;
  final VoidCallback? onTap;
  final EdgeInsets? padding;

  // 2. 构造函数
  const PriceMonitorWidget({
    Key? key,
    required this.symbol,
    this.onTap,
    this.padding,
  }) : super(key: key);

  // 3. createState 方法
  @override
  State<PriceMonitorWidget> createState() => _PriceMonitorWidgetState();
}

class _PriceMonitorWidgetState extends State<PriceMonitorWidget> {
  // 1. 状态变量
  bool _isLoading = false;
  String? _errorMessage;
  
  // 2. 生命周期方法
  @override
  void initState() {
    super.initState();
    _initializeData();
  }

  @override
  void dispose() {
    _cleanup();
    super.dispose();
  }

  // 3. 私有方法
  void _initializeData() {
    // 初始化逻辑
  }

  void _cleanup() {
    // 清理逻辑
  }

  // 4. UI 构建方法
  @override
  Widget build(BuildContext context) {
    return _buildContent();
  }

  Widget _buildContent() {
    if (_isLoading) {
      return _buildLoadingState();
    }

    if (_errorMessage != null) {
      return _buildErrorState();
    }

    return _buildSuccessState();
  }

  Widget _buildLoadingState() {
    return const Center(
      child: CircularProgressIndicator(),
    );
  }

  Widget _buildErrorState() {
    return Center(
      child: Text(
        _errorMessage!,
        style: const TextStyle(color: Colors.red),
      ),
    );
  }

  Widget _buildSuccessState() {
    return Container(
      padding: widget.padding ?? const EdgeInsets.all(16),
      child: Column(
        children: [
          Text(widget.symbol),
          // 其他 UI 元素
        ],
      ),
    );
  }
}
```

### 2. 🔧 服务类结构

```dart
// ✅ 推荐的服务类结构
class WebSocketService {
  // 1. 单例模式
  static final WebSocketService _instance = WebSocketService._internal();
  factory WebSocketService() => _instance;
  WebSocketService._internal();

  // 2. 私有变量
  WebSocketChannel? _channel;
  StreamController<Map<String, dynamic>>? _controller;
  bool _isConnected = false;

  // 3. 公共属性
  bool get isConnected => _isConnected;
  Stream<Map<String, dynamic>> get dataStream => _controller!.stream;

  // 4. 公共方法
  Future<void> connect(String url) async {
    try {
      await _establishConnection(url);
      _setupListeners();
    } catch (e) {
      _handleConnectionError(e);
    }
  }

  Future<void> disconnect() async {
    await _cleanup();
  }

  void sendMessage(Map<String, dynamic> message) {
    if (!_isConnected) {
      throw StateError('WebSocket 未连接');
    }
    _channel?.sink.add(jsonEncode(message));
  }

  // 5. 私有方法
  Future<void> _establishConnection(String url) async {
    _channel = WebSocketChannel.connect(Uri.parse(url));
    _controller = StreamController<Map<String, dynamic>>.broadcast();
    _isConnected = true;
  }

  void _setupListeners() {
    _channel?.stream.listen(
      _handleMessage,
      onError: _handleError,
      onDone: _handleDisconnect,
    );
  }

  void _handleMessage(dynamic message) {
    try {
      final data = jsonDecode(message);
      _controller?.add(data);
    } catch (e) {
      _handleError(e);
    }
  }

  void _handleError(dynamic error) {
    developer.log('WebSocket 错误: $error');
    _isConnected = false;
  }

  void _handleDisconnect() {
    developer.log('WebSocket 连接断开');
    _isConnected = false;
  }

  void _handleConnectionError(dynamic error) {
    developer.log('连接失败: $error');
    throw WebSocketException('连接失败: $error');
  }

  Future<void> _cleanup() async {
    _isConnected = false;
    await _controller?.close();
    await _channel?.sink.close();
    _controller = null;
    _channel = null;
  }
}

// 自定义异常类
class WebSocketException implements Exception {
  final String message;
  const WebSocketException(this.message);
  
  @override
  String toString() => 'WebSocketException: $message';
}
```

## 📝 注释规范

### 1. 📚 文档注释

```dart
/// 价格监控组件
/// 
/// 用于显示实时价格数据，支持多种交易对的价格监控。
/// 
/// 示例用法：
/// ```dart
/// PriceMonitorWidget(
///   symbol: 'BTCUSDT',
///   onPriceChanged: (price) => print('新价格: $price'),
/// )
/// ```
/// 
/// 参见：
/// - [PriceBusinessData] 价格数据模型
/// - [WebSocketService] WebSocket 服务
class PriceMonitorWidget extends StatefulWidget {
  /// 交易对符号，例如 'BTCUSDT'
  final String symbol;

  /// 价格变化回调函数
  /// 
  /// 当价格发生变化时会调用此函数，传入新的价格值。
  final ValueChanged<double>? onPriceChanged;

  /// 创建价格监控组件
  /// 
  /// [symbol] 必须是有效的交易对符号
  /// [onPriceChanged] 可选的价格变化回调
  const PriceMonitorWidget({
    Key? key,
    required this.symbol,
    this.onPriceChanged,
  }) : super(key: key);
}
```

### 2. 💬 行内注释

```dart
class DataProcessor {
  void processKlineData(List<KLineData> rawData) {
    // 过滤无效数据
    final validData = rawData.where((item) => item.isValid).toList();
    
    // 按时间排序
    validData.sort((a, b) => a.timestamp.compareTo(b.timestamp));
    
    // TODO: 添加数据验证逻辑
    // FIXME: 处理时区转换问题
    // NOTE: 这里可能需要优化性能
    
    for (final data in validData) {
      // 计算技术指标
      final sma = _calculateSMA(data, 20);  // 20周期简单移动平均
      final rsi = _calculateRSI(data, 14);  // 14周期相对强弱指数
      
      // 更新缓存
      _updateCache(data.symbol, data);
    }
  }
}
```

### 3. 🏷️ 注释标签

```dart
// TODO: 需要实现的功能
// FIXME: 需要修复的问题
// NOTE: 重要说明
// HACK: 临时解决方案
// OPTIMIZE: 性能优化点
// DEPRECATED: 已废弃的代码
```

## 🧪 测试规范

### 1. 📋 测试文件结构

```dart
// test/widgets/price_monitor_widget_test.dart
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:financial_app_workspace/widgets/price_monitor_widget.dart';

// Mock 类
class MockWebSocketService extends Mock implements WebSocketService {}

void main() {
  group('PriceMonitorWidget', () {
    late MockWebSocketService mockWebSocketService;

    setUp(() {
      mockWebSocketService = MockWebSocketService();
    });

    testWidgets('should display loading state initially', (tester) async {
      // Arrange
      when(mockWebSocketService.isConnected).thenReturn(false);

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: PriceMonitorWidget(symbol: 'BTCUSDT'),
        ),
      );

      // Assert
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
      expect(find.text('加载中...'), findsOneWidget);
    });

    testWidgets('should display price when data is loaded', (tester) async {
      // 测试价格显示逻辑
    });

    testWidgets('should handle error state correctly', (tester) async {
      // 测试错误状态处理
    });
  });
}
```

### 2. 🔧 单元测试规范

```dart
// test/services/websocket_service_test.dart
import 'package:test/test.dart';
import 'package:financial_app_workspace/services/websocket_service.dart';

void main() {
  group('WebSocketService', () {
    late WebSocketService service;

    setUp(() {
      service = WebSocketService();
    });

    tearDown(() {
      service.dispose();
    });

    test('should connect successfully with valid URL', () async {
      // Arrange
      const url = 'wss://test.example.com';

      // Act
      await service.connect(url);

      // Assert
      expect(service.isConnected, isTrue);
    });

    test('should throw exception with invalid URL', () async {
      // Arrange
      const invalidUrl = 'invalid-url';

      // Act & Assert
      expect(
        () => service.connect(invalidUrl),
        throwsA(isA<WebSocketException>()),
      );
    });
  });
}
```

## 🔍 代码检查

### 1. 📊 静态分析配置

```yaml
# analysis_options.yaml
include: package:flutter_lints/flutter.yaml

analyzer:
  exclude:
    - "**/*.g.dart"
    - "**/*.freezed.dart"
  
  strong-mode:
    implicit-casts: false
    implicit-dynamic: false

linter:
  rules:
    # 启用推荐规则
    - prefer_const_constructors
    - prefer_const_literals_to_create_immutables
    - prefer_final_locals
    - prefer_single_quotes
    - sort_constructors_first
    - sort_unnamed_constructors_first
    - unnecessary_const
    - unnecessary_new
    - use_key_in_widget_constructors
    
    # 禁用某些规则
    - avoid_print: false  # 允许在开发时使用 print
```

### 2. 🛠️ 格式化配置

```bash
# 自动格式化代码
dart format .

# 检查代码格式
dart format --set-exit-if-changed .

# 静态分析
flutter analyze

# 修复可自动修复的问题
dart fix --apply
```

## 🚀 性能规范

### 1. ⚡ Widget 性能优化

```dart
// ✅ 正确的性能优化
class OptimizedListWidget extends StatelessWidget {
  final List<String> items;

  const OptimizedListWidget({
    Key? key,
    required this.items,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      itemCount: items.length,
      itemBuilder: (context, index) {
        return _OptimizedListItem(
          key: ValueKey(items[index]),  // 使用 key 优化重建
          item: items[index],
        );
      },
    );
  }
}

class _OptimizedListItem extends StatelessWidget {
  final String item;

  const _OptimizedListItem({
    Key? key,
    required this.item,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ListTile(
      title: Text(item),
    );
  }
}

// ❌ 错误的性能实现
class UnoptimizedListWidget extends StatelessWidget {
  final List<String> items;

  const UnoptimizedListWidget({Key? key, required this.items}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      children: items.map((item) => ListTile(title: Text(item))).toList(),
    );  // 不要在 build 中创建大量 Widget
  }
}
```

### 2. 💾 内存管理

```dart
// ✅ 正确的资源管理
class DataStreamWidget extends StatefulWidget {
  @override
  _DataStreamWidgetState createState() => _DataStreamWidgetState();
}

class _DataStreamWidgetState extends State<DataStreamWidget> {
  StreamSubscription? _subscription;
  Timer? _timer;

  @override
  void initState() {
    super.initState();
    _setupDataStream();
  }

  @override
  void dispose() {
    // 必须清理资源
    _subscription?.cancel();
    _timer?.cancel();
    super.dispose();
  }

  void _setupDataStream() {
    _subscription = dataStream.listen((data) {
      if (mounted) {  // 检查 Widget 是否还在树中
        setState(() {
          // 更新状态
        });
      }
    });
  }
}
```

## 📋 代码审查清单

### ✅ 提交前检查

- [ ] 代码格式化 (`dart format .`)
- [ ] 静态分析通过 (`flutter analyze`)
- [ ] 单元测试通过 (`flutter test`)
- [ ] 性能测试通过
- [ ] 文档注释完整
- [ ] 变量命名规范
- [ ] 资源正确释放
- [ ] 错误处理完善

### 📝 代码审查要点

1. **架构设计**
   - 是否符合项目架构
   - 模块职责是否清晰
   - 依赖关系是否合理

2. **代码质量**
   - 逻辑是否清晰
   - 是否有重复代码
   - 错误处理是否完善

3. **性能考虑**
   - 是否有性能瓶颈
   - 内存使用是否合理
   - 是否有不必要的重建

4. **安全性**
   - 是否有安全漏洞
   - 敏感数据是否加密
   - 输入验证是否充分

---

## 🛠️ 工具推荐

### 📱 开发工具

- **VS Code**: 推荐的 IDE
- **Flutter Inspector**: UI 调试工具
- **Dart DevTools**: 性能分析工具

### 🔧 代码质量工具

- **flutter_lints**: 官方代码规范
- **dart_code_metrics**: 代码质量分析
- **import_sorter**: 导入排序工具

### 🧪 测试工具

- **flutter_test**: 官方测试框架
- **mockito**: Mock 框架
- **golden_toolkit**: 黄金测试工具

---

**🎯 遵循规范，编写高质量代码！** 🚀✨
