# 🚀 快速开始指南

## 📋 文档信息

| 项目 | 值 |
|------|-----|
| **文档版本** | v1.0.0 |
| **适用人员** | 新入职开发者、实习生、外部合作伙伴 |
| **预计完成时间** | 30-60 分钟 |
| **前置要求** | 基础 Flutter 开发经验 |

## 🎯 学习目标

完成本指南后，您将能够：
- ✅ 搭建完整的开发环境
- ✅ 理解项目的模块化架构
- ✅ 运行和调试应用
- ✅ 使用全局 WebSocket 管理
- ✅ 开发第一个功能模块

## 📋 环境准备

### 1. 🛠️ 基础工具安装

#### **Flutter SDK**
```bash
# 下载 Flutter SDK (推荐 3.16.0+)
git clone https://github.com/flutter/flutter.git -b stable
export PATH="$PATH:`pwd`/flutter/bin"

# 验证安装
flutter doctor
```

#### **开发工具**
```bash
# VS Code (推荐)
# 安装扩展：
# - Flutter
# - Dart
# - GitLens
# - Error Lens
# - Bracket Pair Colorizer

# 或者 Android Studio
# 安装 Flutter 和 Dart 插件
```

#### **Git 配置**
```bash
# 配置 Git
git config --global user.name "您的姓名"
git config --global user.email "您的邮箱@company.com"

# 生成 SSH 密钥
ssh-keygen -t rsa -b 4096 -C "您的邮箱@company.com"
# 将公钥添加到公司 GitLab
```

### 2. 📦 项目克隆和依赖安装

```bash
# 1. 克隆项目
git clone http://47.107.249.27:89/app_team/financial_app_workspace.git
cd financial_app_workspace

# 2. 安装依赖
flutter pub get

# 3. 验证环境
flutter doctor
flutter devices
```

### 3. 🔧 IDE 配置

#### **VS Code 配置**
```json
// .vscode/settings.json
{
  "dart.flutterSdkPath": "/path/to/flutter",
  "dart.lineLength": 100,
  "editor.rulers": [100],
  "editor.formatOnSave": true,
  "dart.debugExternalPackageLibraries": true,
  "dart.debugSdkLibraries": false
}
```

#### **调试配置**
```json
// .vscode/launch.json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "financial_app_workspace",
      "request": "launch",
      "type": "dart",
      "program": "lib/main.dart",
      "args": ["--flavor", "development"]
    }
  ]
}
```

## 🏗️ 项目架构理解

### 📦 模块结构

```
financial_app_workspace/
├── 🏠 financial_app_main/          # 主应用 (您可以修改)
├── 🌐 financial_app_core/          # 核心服务 (您可以修改)
├── 📈 financial_app_market/        # 市场数据 (您可以修改)
├── 🔌 financial_ws_client/         # WebSocket (您可以修改)
├── 💰 financial_app_trade/         # 交易模块 (只读)
├── 📋 financial_app_portfolio/     # 投资组合 (只读)
├── 🔔 financial_app_notification/  # 通知服务 (只读)
├── 🎨 financial_app_assets/        # 静态资源 (只读)
└── 📊 financial_trading_chart/     # 图表组件 (只读)
```

### 🔄 依赖关系

```mermaid
graph TD
    A[financial_app_main] --> B[financial_app_core]
    A --> C[financial_app_market]
    A --> D[financial_app_trade]
    A --> E[financial_app_portfolio]
    A --> F[financial_app_notification]
    
    B --> G[financial_ws_client]
    C --> B
    C --> H[financial_trading_chart]
    D --> B
    E --> B
    F --> B
    
    H --> I[financial_app_assets]
```

## 🚀 第一次运行

### 1. 📱 启动应用

```bash
# 查看可用设备
flutter devices

# 运行应用 (开发模式)
flutter run

# 或者指定设备
flutter run -d chrome  # Web
flutter run -d emulator-5554  # Android 模拟器
```

### 2. 🔍 验证功能

启动后您应该看到：
- ✅ 应用正常启动
- ✅ 底部导航栏显示
- ✅ 市场数据页面可以访问
- ✅ WebSocket 连接状态指示器显示绿色
- ✅ K线图表正常显示

### 3. 🐛 常见问题排查

#### **问题 1: 依赖冲突**
```bash
# 清理缓存
flutter clean
flutter pub get

# 如果还有问题，删除 pubspec.lock
rm pubspec.lock
flutter pub get
```

#### **问题 2: WebSocket 连接失败**
```dart
// 检查网络配置
// 确保可以访问 wss://stream.binance.com:9443/ws
// 或者使用模拟数据进行开发
```

#### **问题 3: 模块找不到**
```bash
# 确保所有 Git 子模块都已克隆
git submodule update --init --recursive
```

## 💻 开发第一个功能

### 1. 🎯 目标：添加一个价格监控组件

我们将创建一个简单的价格监控组件，展示如何使用全局 WebSocket 管理。

### 2. 📝 创建组件文件

```dart
// lib/widgets/price_monitor_widget.dart
import 'package:flutter/material.dart';
import 'package:financial_app_core/financial_app_core.dart';

class PriceMonitorWidget extends StatelessWidget {
  final String symbol;
  final String? subscriberId;

  const PriceMonitorWidget({
    Key? key,
    required this.symbol,
    this.subscriberId,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return PriceDataBuilder(
      symbol: symbol,
      subscriberId: subscriberId,
      builder: (context, priceData, isLoading) {
        if (isLoading) {
          return _buildLoadingState();
        }

        if (priceData == null) {
          return _buildErrorState();
        }

        return _buildPriceDisplay(priceData);
      },
    );
  }

  Widget _buildLoadingState() {
    return Container(
      padding: EdgeInsets.all(16),
      child: Row(
        children: [
          SizedBox(
            width: 16,
            height: 16,
            child: CircularProgressIndicator(strokeWidth: 2),
          ),
          SizedBox(width: 12),
          Text('加载中...', style: TextStyle(color: Colors.grey)),
        ],
      ),
    );
  }

  Widget _buildErrorState() {
    return Container(
      padding: EdgeInsets.all(16),
      child: Row(
        children: [
          Icon(Icons.error_outline, color: Colors.red, size: 16),
          SizedBox(width: 8),
          Text('数据加载失败', style: TextStyle(color: Colors.red)),
        ],
      ),
    );
  }

  Widget _buildPriceDisplay(PriceBusinessData priceData) {
    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[900],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[700]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 交易对名称
          Text(
            symbol,
            style: TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 8),
          
          // 价格信息
          Row(
            children: [
              Text(
                '\$${priceData.price.toStringAsFixed(2)}',
                style: TextStyle(
                  color: priceData.priceColor,
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(width: 12),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: priceData.priceColor.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  '${priceData.priceChangePercent >= 0 ? '+' : ''}${priceData.priceChangePercent.toStringAsFixed(2)}%',
                  style: TextStyle(
                    color: priceData.priceColor,
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          
          SizedBox(height: 8),
          
          // 24小时统计
          Row(
            children: [
              _buildStatItem('24h 高', '\$${priceData.high24h.toStringAsFixed(2)}'),
              SizedBox(width: 16),
              _buildStatItem('24h 低', '\$${priceData.low24h.toStringAsFixed(2)}'),
            ],
          ),
          
          SizedBox(height: 4),
          
          Row(
            children: [
              _buildStatItem('24h 量', '${(priceData.volume / 1000000).toStringAsFixed(1)}M'),
              SizedBox(width: 16),
              _buildStatItem('更新时间', _formatTime(priceData.timestamp)),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(color: Colors.grey[400], fontSize: 10),
        ),
        Text(
          value,
          style: TextStyle(color: Colors.white, fontSize: 12),
        ),
      ],
    );
  }

  String _formatTime(DateTime time) {
    return '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
  }
}
```

### 3. 🔧 使用组件

```dart
// 在任何页面中使用
class MyPage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('价格监控')),
      body: Column(
        children: [
          // 监控 BTC
          PriceMonitorWidget(
            symbol: 'BTCUSDT',
            subscriberId: 'my_page_btc',
          ),
          
          SizedBox(height: 16),
          
          // 监控 ETH
          PriceMonitorWidget(
            symbol: 'ETHUSDT',
            subscriberId: 'my_page_eth',
          ),
        ],
      ),
    );
  }
}
```

### 4. ✅ 测试功能

1. **运行应用**
   ```bash
   flutter run
   ```

2. **验证功能**
   - ✅ 组件正常显示
   - ✅ 价格数据实时更新
   - ✅ 颜色根据涨跌变化
   - ✅ 24小时统计数据显示

3. **调试技巧**
   ```dart
   // 添加调试日志
   print('📊 收到价格数据: ${priceData.symbol} - ${priceData.price}');
   
   // 使用 Flutter Inspector 查看组件树
   // 使用 Performance 面板监控性能
   ```

## 🧪 测试和调试

### 1. 🔍 单元测试

```dart
// test/widgets/price_monitor_widget_test.dart
import 'package:flutter_test/flutter_test.dart';
import 'package:financial_app_workspace/widgets/price_monitor_widget.dart';

void main() {
  group('PriceMonitorWidget', () {
    testWidgets('should display loading state initially', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: PriceMonitorWidget(symbol: 'BTCUSDT'),
        ),
      );

      expect(find.text('加载中...'), findsOneWidget);
    });

    testWidgets('should display price data when loaded', (tester) async {
      // 测试价格数据显示
    });
  });
}
```

### 2. 🐛 调试技巧

```dart
// 使用 developer.log 进行调试
import 'dart:developer' as developer;

developer.log('🔗 WebSocket 连接状态: $isConnected');
developer.log('📊 收到数据: ${data.length} 条');

// 使用 debugPrint 输出调试信息
debugPrint('💰 当前价格: ${priceData.price}');

// 使用 assert 进行断言检查
assert(priceData.price > 0, '价格必须大于0');
```

### 3. 📊 性能分析

```bash
# 性能分析
flutter run --profile
# 然后在 DevTools 中查看性能数据

# 内存分析
flutter run --debug
# 使用 Memory 面板监控内存使用
```

## 📚 下一步学习

### 1. 🎯 推荐学习路径

1. **深入理解架构** (1-2天)
   - 阅读 [WebSocket 架构文档](../architecture/WEBSOCKET_ARCHITECTURE.md)
   - 理解模块间依赖关系
   - 学习状态管理策略

2. **掌握开发规范** (1天)
   - 阅读 [编码规范](CODING_STANDARDS.md)
   - 学习 Git 工作流
   - 了解代码审查流程

3. **实践项目开发** (3-5天)
   - 开发一个完整的功能模块
   - 编写单元测试和集成测试
   - 参与代码审查

### 2. 📖 推荐阅读

- [Flutter 官方文档](https://flutter.dev/docs)
- [Dart 语言指南](https://dart.dev/guides)
- [WebSocket 最佳实践](../architecture/WEBSOCKET_ARCHITECTURE.md)
- [性能优化指南](PERFORMANCE_OPTIMIZATION.md)

### 3. 🛠️ 实用工具

```bash
# 代码格式化
dart format .

# 静态分析
flutter analyze

# 测试覆盖率
flutter test --coverage
genhtml coverage/lcov.info -o coverage/html

# 性能分析
flutter run --profile
```

## 🆘 获取帮助

### 📞 联系方式

- **技术支持**: <EMAIL>
- **架构问题**: <EMAIL>
- **紧急问题**: <EMAIL>

### 💬 内部资源

- **技术文档**: https://docs.company.com
- **内部论坛**: https://forum.company.com
- **培训资料**: https://training.company.com

### 🐛 问题反馈

- **Bug 报告**: [GitLab Issues](https://gitlab.company.com/app_team/financial_app_workspace/-/issues)
- **功能建议**: [Feature Request](https://gitlab.company.com/app_team/financial_app_workspace/-/issues/new)

---

## 🎉 恭喜！

您已经完成了快速开始指南！现在您可以：

✅ **运行和调试应用**  
✅ **理解项目架构**  
✅ **使用 WebSocket 管理**  
✅ **开发自己的组件**  
✅ **进行测试和调试**  

**欢迎加入我们的开发团队，一起构建世界级的金融应用！** 🚀✨
