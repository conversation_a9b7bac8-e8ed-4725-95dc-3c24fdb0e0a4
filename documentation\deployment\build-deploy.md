# 📦 构建部署指南

本文档详细介绍了金融应用的构建流程、部署步骤和环境配置，确保应用能够稳定、安全地部署到各种环境中。

## 🎯 部署架构概览

### 🏗️ 部署环境
```
┌─────────────────────────────────────────────────────────────┐
│                    部署环境架构                              │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐     │
│  │ Development │    │   Testing   │    │ Production  │     │
│  │   开发环境   │    │   测试环境   │    │   生产环境   │     │
│  │             │    │             │    │             │     │
│  │ - 本地开发   │    │ - 功能测试   │    │ - 正式服务   │     │
│  │ - 快速迭代   │    │ - 集成测试   │    │ - 高可用性   │     │
│  │ - 调试模式   │    │ - 性能测试   │    │ - 监控告警   │     │
│  └─────────────┘    └─────────────┘    └─────────────┘     │
│         │                   │                   │          │
│         └───────────────────┼───────────────────┘          │
│                             │                              │
│  ┌─────────────────────────▼─────────────────────────┐     │
│  │              CI/CD Pipeline                       │     │
│  │  ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────┐ │     │
│  │  │  Build  │ │  Test   │ │ Package │ │ Deploy  │ │     │
│  │  │  构建   │ │  测试   │ │  打包   │ │  部署   │ │     │
│  │  └─────────┘ └─────────┘ └─────────┘ └─────────┘ │     │
│  └─────────────────────────────────────────────────┘     │
└─────────────────────────────────────────────────────────────┘
```

### 📊 环境配置对比
| 环境 | 用途 | 配置 | 数据库 | 监控 |
|------|------|------|--------|------|
| Development | 开发调试 | 低配置 | 本地SQLite | 基础日志 |
| Testing | 功能测试 | 中等配置 | 测试数据库 | 详细日志 |
| Staging | 预生产 | 生产配置 | 预生产数据库 | 完整监控 |
| Production | 正式环境 | 高配置 | 生产数据库 | 全面监控 |

## 🔧 构建流程

### 📋 构建前准备

#### 1. 环境检查
```bash
# 检查Flutter版本
flutter --version

# 检查Dart版本
dart --version

# 检查依赖工具
which melos
which docker
which git
```

#### 2. 代码质量检查
```bash
# 运行代码质量检查脚本
dart scripts/fix_code_quality.dart

# 检查代码格式
dart format --set-exit-if-changed .

# 静态分析
flutter analyze

# 运行测试
melos run test
```

### 🏗️ 构建脚本

#### Android构建
```bash
#!/bin/bash
# scripts/build_android.sh

set -e

echo "🤖 开始Android构建..."

# 1. 清理构建缓存
echo "🧹 清理构建缓存..."
flutter clean
melos clean

# 2. 安装依赖
echo "📦 安装依赖..."
melos bootstrap

# 3. 代码生成
echo "🔧 代码生成..."
melos run build_runner

# 4. 构建APK (Debug)
echo "📱 构建Debug APK..."
cd packages/financial_app_main
flutter build apk --debug --target-platform android-arm64

# 5. 构建APK (Release)
echo "📱 构建Release APK..."
flutter build apk --release --target-platform android-arm64

# 6. 构建AAB (Release)
echo "📱 构建Release AAB..."
flutter build appbundle --release

echo "✅ Android构建完成!"
echo "📁 输出文件:"
echo "  - Debug APK: build/app/outputs/flutter-apk/app-debug.apk"
echo "  - Release APK: build/app/outputs/flutter-apk/app-release.apk"
echo "  - Release AAB: build/app/outputs/bundle/release/app-release.aab"
```

#### iOS构建
```bash
#!/bin/bash
# scripts/build_ios.sh

set -e

echo "🍎 开始iOS构建..."

# 检查macOS环境
if [[ "$OSTYPE" != "darwin"* ]]; then
  echo "❌ iOS构建需要在macOS环境中进行"
  exit 1
fi

# 1. 清理构建缓存
echo "🧹 清理构建缓存..."
flutter clean
melos clean

# 2. 安装依赖
echo "📦 安装依赖..."
melos bootstrap

# 3. 安装iOS依赖
echo "📱 安装iOS依赖..."
cd packages/financial_app_main/ios
pod install --repo-update

# 4. 构建iOS (Debug)
echo "📱 构建Debug iOS..."
cd ..
flutter build ios --debug --no-codesign

# 5. 构建iOS (Release)
echo "📱 构建Release iOS..."
flutter build ios --release --no-codesign

# 6. 构建IPA (需要证书)
echo "📱 构建IPA..."
flutter build ipa --release

echo "✅ iOS构建完成!"
echo "📁 输出文件:"
echo "  - iOS App: build/ios/iphoneos/Runner.app"
echo "  - IPA: build/ios/ipa/financial_app.ipa"
```

#### Web构建
```bash
#!/bin/bash
# scripts/build_web.sh

set -e

echo "🌐 开始Web构建..."

# 1. 清理构建缓存
echo "🧹 清理构建缓存..."
flutter clean
melos clean

# 2. 安装依赖
echo "📦 安装依赖..."
melos bootstrap

# 3. 构建Web (Release)
echo "🌐 构建Release Web..."
cd packages/financial_app_main
flutter build web --release --web-renderer html

# 4. 优化构建产物
echo "⚡ 优化构建产物..."
# 压缩静态资源
gzip -9 -k build/web/main.dart.js
gzip -9 -k build/web/flutter.js

echo "✅ Web构建完成!"
echo "📁 输出文件: build/web/"
```

### 🔧 自动化构建脚本
```bash
#!/bin/bash
# scripts/build_all.sh

set -e

PLATFORM=${1:-"all"}
BUILD_TYPE=${2:-"release"}

echo "🚀 开始自动化构建..."
echo "📋 平台: $PLATFORM"
echo "📋 类型: $BUILD_TYPE"

# 构建前检查
echo "🔍 构建前检查..."
dart scripts/dev_toolbox.dart --pre-build-check

case $PLATFORM in
  "android")
    ./scripts/build_android.sh
    ;;
  "ios")
    ./scripts/build_ios.sh
    ;;
  "web")
    ./scripts/build_web.sh
    ;;
  "all")
    ./scripts/build_android.sh
    if [[ "$OSTYPE" == "darwin"* ]]; then
      ./scripts/build_ios.sh
    fi
    ./scripts/build_web.sh
    ;;
  *)
    echo "❌ 不支持的平台: $PLATFORM"
    echo "支持的平台: android, ios, web, all"
    exit 1
    ;;
esac

echo "🎉 构建完成!"
```

## 🚀 部署流程

### 🐳 Docker部署

#### Dockerfile
```dockerfile
# Dockerfile
FROM nginx:alpine

# 复制Web构建产物
COPY packages/financial_app_main/build/web /usr/share/nginx/html

# 复制Nginx配置
COPY docker/nginx.conf /etc/nginx/nginx.conf

# 暴露端口
EXPOSE 80

# 启动Nginx
CMD ["nginx", "-g", "daemon off;"]
```

#### Docker Compose
```yaml
# docker-compose.yml
version: '3.8'

services:
  financial-app:
    build: .
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./docker/ssl:/etc/nginx/ssl:ro
      - ./docker/logs:/var/log/nginx
    environment:
      - NODE_ENV=production
    restart: unless-stopped
    networks:
      - financial-network

  redis:
    image: redis:alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - financial-network

networks:
  financial-network:
    driver: bridge

volumes:
  redis_data:
```

#### 部署脚本
```bash
#!/bin/bash
# scripts/deploy_docker.sh

set -e

ENVIRONMENT=${1:-"production"}
VERSION=${2:-"latest"}

echo "🐳 开始Docker部署..."
echo "📋 环境: $ENVIRONMENT"
echo "📋 版本: $VERSION"

# 1. 构建Docker镜像
echo "🏗️ 构建Docker镜像..."
docker build -t financial-app:$VERSION .

# 2. 标记镜像
echo "🏷️ 标记镜像..."
docker tag financial-app:$VERSION financial-app:latest

# 3. 停止现有容器
echo "🛑 停止现有容器..."
docker-compose down

# 4. 启动新容器
echo "🚀 启动新容器..."
docker-compose up -d

# 5. 健康检查
echo "🔍 健康检查..."
sleep 10
curl -f http://localhost/health || exit 1

echo "✅ Docker部署完成!"
```

### 🌐 内部部署

#### 内部部署脚本
```bash
#!/bin/bash
# scripts/deploy_internal.sh

set -e

ENVIRONMENT=${1:-"production"}
SERVER=${2:-"app-server-01"}

echo "🏢 开始内部部署..."
echo "📋 环境: $ENVIRONMENT"
echo "📋 服务器: $SERVER"

# 1. 构建应用
echo "🏗️ 构建应用..."
./scripts/build_web.sh

# 2. 打包部署文件
echo "📦 打包部署文件..."
tar -czf deploy-$(date +%Y%m%d-%H%M%S).tar.gz \
  packages/financial_app_main/build/web \
  docker/nginx.conf \
  scripts/

# 3. 上传到服务器
echo "📤 上传到服务器..."
scp deploy-*.tar.gz deploy@$SERVER:/tmp/

# 4. 远程部署
echo "🚀 远程部署..."
ssh deploy@$SERVER << 'EOF'
  cd /tmp
  tar -xzf deploy-*.tar.gz
  
  # 备份当前版本
  sudo cp -r /var/www/financial-app /var/www/financial-app.backup.$(date +%Y%m%d-%H%M%S)
  
  # 部署新版本
  sudo cp -r packages/financial_app_main/build/web/* /var/www/financial-app/
  
  # 更新Nginx配置
  sudo cp docker/nginx.conf /etc/nginx/sites-available/financial-app
  
  # 重启服务
  sudo systemctl reload nginx
  
  # 清理临时文件
  rm -rf deploy-* packages docker scripts
EOF

# 5. 健康检查
echo "🔍 健康检查..."
sleep 5
curl -f https://$SERVER/health || exit 1

echo "✅ 内部部署完成!"
```

## 🔧 环境配置

### 📋 环境变量配置

#### 开发环境 (.env.development)
```bash
# 应用配置
APP_ENV=development
APP_DEBUG=true
APP_VERSION=1.0.0-dev

# API配置
API_BASE_URL=https://dev-api.financial-app.com
WS_BASE_URL=wss://dev-ws.financial-app.com

# 数据库配置
DB_HOST=localhost
DB_PORT=5432
DB_NAME=financial_app_dev
DB_USER=dev_user
DB_PASSWORD=dev_password

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# 日志配置
LOG_LEVEL=debug
LOG_FILE=/var/log/financial-app/app.log

# 安全配置
JWT_SECRET=dev_jwt_secret_key
ENCRYPTION_KEY=dev_encryption_key
```

#### 生产环境 (.env.production)
```bash
# 应用配置
APP_ENV=production
APP_DEBUG=false
APP_VERSION=1.0.0

# API配置
API_BASE_URL=https://api.financial-app.com
WS_BASE_URL=wss://ws.financial-app.com

# 数据库配置
DB_HOST=prod-db.financial-app.com
DB_PORT=5432
DB_NAME=financial_app_prod
DB_USER=prod_user
DB_PASSWORD=${DB_PASSWORD}

# Redis配置
REDIS_HOST=prod-redis.financial-app.com
REDIS_PORT=6379
REDIS_PASSWORD=${REDIS_PASSWORD}

# 日志配置
LOG_LEVEL=info
LOG_FILE=/var/log/financial-app/app.log

# 安全配置
JWT_SECRET=${JWT_SECRET}
ENCRYPTION_KEY=${ENCRYPTION_KEY}

# 监控配置
SENTRY_DSN=${SENTRY_DSN}
PROMETHEUS_ENDPOINT=http://prometheus:9090
```

### 🔧 Nginx配置
```nginx
# docker/nginx.conf
events {
    worker_connections 1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;
    
    # 日志格式
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';
    
    access_log /var/log/nginx/access.log main;
    error_log /var/log/nginx/error.log warn;
    
    # 基础配置
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    
    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/javascript
        application/xml+rss
        application/json;
    
    server {
        listen 80;
        server_name financial-app.com www.financial-app.com;
        root /usr/share/nginx/html;
        index index.html;
        
        # 安全头
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header X-XSS-Protection "1; mode=block" always;
        add_header Referrer-Policy "strict-origin-when-cross-origin" always;
        
        # Flutter Web路由支持
        location / {
            try_files $uri $uri/ /index.html;
        }
        
        # 静态资源缓存
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
        
        # API代理
        location /api/ {
            proxy_pass https://api.financial-app.com/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
        
        # WebSocket代理
        location /ws/ {
            proxy_pass https://ws.financial-app.com/;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
        
        # 健康检查
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }
    }
}
```

## 📊 CI/CD流水线

### 🔄 GitLab CI配置
```yaml
# .gitlab-ci.yml
stages:
  - test
  - build
  - deploy

variables:
  FLUTTER_VERSION: "3.16.0"
  DART_VERSION: "3.2.0"

# 测试阶段
test:
  stage: test
  image: cirrusci/flutter:$FLUTTER_VERSION
  before_script:
    - dart pub global activate melos
    - melos bootstrap
  script:
    - dart scripts/fix_code_quality.dart
    - flutter analyze
    - melos run test
  coverage: '/lines......: \d+\.\d+\%/'
  artifacts:
    reports:
      coverage_report:
        coverage_format: cobertura
        path: coverage/cobertura.xml

# 构建阶段
build:
  stage: build
  image: cirrusci/flutter:$FLUTTER_VERSION
  before_script:
    - dart pub global activate melos
    - melos bootstrap
  script:
    - ./scripts/build_web.sh
    - ./scripts/build_android.sh
  artifacts:
    paths:
      - packages/financial_app_main/build/web/
      - packages/financial_app_main/build/app/outputs/
    expire_in: 1 week

# 部署到测试环境
deploy_test:
  stage: deploy
  image: alpine:latest
  before_script:
    - apk add --no-cache curl openssh-client
  script:
    - ./scripts/deploy_internal.sh testing test-server
  environment:
    name: testing
    url: https://test.financial-app.com
  only:
    - develop

# 部署到生产环境
deploy_prod:
  stage: deploy
  image: alpine:latest
  before_script:
    - apk add --no-cache curl openssh-client
  script:
    - ./scripts/deploy_internal.sh production prod-server
  environment:
    name: production
    url: https://financial-app.com
  when: manual
  only:
    - main
```

## 🔍 部署验证

### ✅ 健康检查脚本
```bash
#!/bin/bash
# scripts/health_check.sh

set -e

URL=${1:-"https://financial-app.com"}
TIMEOUT=${2:-30}

echo "🔍 开始健康检查..."
echo "📋 URL: $URL"
echo "📋 超时: ${TIMEOUT}秒"

# 1. 基础连通性检查
echo "🌐 检查基础连通性..."
curl -f --max-time $TIMEOUT "$URL/health" || exit 1

# 2. 应用页面检查
echo "📱 检查应用页面..."
curl -f --max-time $TIMEOUT "$URL/" | grep -q "financial-app" || exit 1

# 3. API接口检查
echo "📡 检查API接口..."
curl -f --max-time $TIMEOUT "$URL/api/health" || exit 1

# 4. WebSocket连接检查
echo "🌐 检查WebSocket连接..."
# 这里可以添加WebSocket连接测试

echo "✅ 健康检查通过!"
```

### 📊 部署后验证清单
- [ ] 应用页面正常加载
- [ ] API接口响应正常
- [ ] WebSocket连接正常
- [ ] 用户登录功能正常
- [ ] 核心业务功能正常
- [ ] 性能指标符合预期
- [ ] 错误日志无异常
- [ ] 监控告警正常

---

**📦 完善的构建部署流程确保了金融应用能够稳定、安全、高效地部署到各种环境中，为用户提供可靠的服务！**
