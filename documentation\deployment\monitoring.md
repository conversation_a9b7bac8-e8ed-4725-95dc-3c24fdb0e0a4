# 📊 监控配置指南

本文档详细介绍了金融应用的监控体系配置，包括应用监控、性能监控、日志管理和告警配置。

## 🎯 监控架构概览

### 🏗️ 监控体系架构
```
┌─────────────────────────────────────────────────────────────┐
│                    监控体系架构                              │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐     │
│  │ Application │    │Performance  │    │   Logs      │     │
│  │ Monitoring  │    │ Monitoring  │    │ Management  │     │
│  │             │    │             │    │             │     │
│  │ - 错误追踪   │    │ - 响应时间   │    │ - 日志收集   │     │
│  │ - 用户行为   │    │ - 资源使用   │    │ - 日志分析   │     │
│  │ - 业务指标   │    │ - 系统负载   │    │ - 日志存储   │     │
│  └─────────────┘    └─────────────┘    └─────────────┘     │
│         │                   │                   │          │
│         └───────────────────┼───────────────────┘          │
│                             │                              │
│  ┌─────────────────────────▼─────────────────────────┐     │
│  │              Alert & Notification                 │     │
│  │  ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────┐ │     │
│  │  │  Email  │ │   SMS   │ │ Webhook │ │Dashboard│ │     │
│  │  │  邮件   │ │  短信   │ │  回调   │ │  仪表板 │ │     │
│  │  └─────────┘ └─────────┘ └─────────┘ └─────────┘ │     │
│  └─────────────────────────────────────────────────┘     │
└─────────────────────────────────────────────────────────────┘
```

### 📊 监控指标分类
| 类别 | 指标 | 阈值 | 告警级别 |
|------|------|------|----------|
| 应用性能 | 响应时间 | >2s | Warning |
| 应用性能 | 错误率 | >1% | Critical |
| 系统资源 | CPU使用率 | >80% | Warning |
| 系统资源 | 内存使用率 | >85% | Critical |
| 业务指标 | 登录成功率 | <95% | Warning |
| 业务指标 | 交易成功率 | <99% | Critical |

## 📱 应用监控配置

### 🔍 错误追踪 (Sentry)

#### Sentry配置
```dart
// lib/src/core/monitoring/sentry_config.dart
import 'package:sentry_flutter/sentry_flutter.dart';

class SentryConfig {
  static Future<void> init() async {
    await SentryFlutter.init(
      (options) {
        options.dsn = const String.fromEnvironment('SENTRY_DSN');
        options.environment = const String.fromEnvironment('APP_ENV');
        options.release = const String.fromEnvironment('APP_VERSION');
        
        // 性能监控
        options.tracesSampleRate = 0.1;
        options.profilesSampleRate = 0.1;
        
        // 错误过滤
        options.beforeSend = (event, hint) {
          // 过滤敏感信息
          if (event.user?.email != null) {
            event.user = event.user!.copyWith(email: '[FILTERED]');
          }
          return event;
        };
        
        // 面包屑配置
        options.maxBreadcrumbs = 100;
        
        // 附件配置
        options.attachStacktrace = true;
        options.attachThreads = true;
      },
    );
  }
  
  /// 记录自定义事件
  static void captureEvent({
    required String message,
    SentryLevel level = SentryLevel.info,
    Map<String, dynamic>? extra,
    Map<String, String>? tags,
  }) {
    Sentry.captureMessage(
      message,
      level: level,
      withScope: (scope) {
        if (extra != null) {
          scope.setExtras(extra);
        }
        if (tags != null) {
          scope.setTags(tags);
        }
      },
    );
  }
  
  /// 记录用户操作
  static void addBreadcrumb({
    required String message,
    String? category,
    SentryLevel level = SentryLevel.info,
    Map<String, dynamic>? data,
  }) {
    Sentry.addBreadcrumb(
      Breadcrumb(
        message: message,
        category: category,
        level: level,
        data: data,
        timestamp: DateTime.now(),
      ),
    );
  }
  
  /// 设置用户上下文
  static void setUser({
    required String id,
    String? email,
    String? username,
    Map<String, dynamic>? extras,
  }) {
    Sentry.configureScope((scope) {
      scope.setUser(SentryUser(
        id: id,
        email: email,
        username: username,
        extras: extras,
      ));
    });
  }
}
```

#### 错误监控集成
```dart
// lib/src/core/monitoring/error_monitor.dart
class ErrorMonitor {
  static final AppLogger _logger = AppLogger.logModule('ErrorMonitor');
  
  /// 初始化错误监控
  static void init() {
    // Flutter错误处理
    FlutterError.onError = (FlutterErrorDetails details) {
      _logger.error('Flutter错误: ${details.exception}');
      
      Sentry.captureException(
        details.exception,
        stackTrace: details.stack,
        withScope: (scope) {
          scope.setTag('error_type', 'flutter_error');
          scope.setContext('error_details', {
            'library': details.library,
            'context': details.context?.toString(),
          });
        },
      );
    };
    
    // 平台错误处理
    PlatformDispatcher.instance.onError = (error, stack) {
      _logger.error('平台错误: $error');
      
      Sentry.captureException(
        error,
        stackTrace: stack,
        withScope: (scope) {
          scope.setTag('error_type', 'platform_error');
        },
      );
      
      return true;
    };
  }
  
  /// 记录业务错误
  static void captureBusinessError({
    required String operation,
    required dynamic error,
    StackTrace? stackTrace,
    Map<String, dynamic>? context,
  }) {
    _logger.error('业务错误 [$operation]: $error');
    
    Sentry.captureException(
      error,
      stackTrace: stackTrace,
      withScope: (scope) {
        scope.setTag('error_type', 'business_error');
        scope.setTag('operation', operation);
        if (context != null) {
          scope.setContext('business_context', context);
        }
      },
    );
  }
  
  /// 记录网络错误
  static void captureNetworkError({
    required String url,
    required int statusCode,
    required String method,
    dynamic error,
    Map<String, dynamic>? requestData,
  }) {
    _logger.error('网络错误 [$method $url]: $statusCode - $error');
    
    Sentry.captureException(
      error ?? 'Network Error',
      withScope: (scope) {
        scope.setTag('error_type', 'network_error');
        scope.setContext('network_context', {
          'url': url,
          'method': method,
          'status_code': statusCode,
          'request_data': requestData,
        });
      },
    );
  }
}
```

### 📊 性能监控

#### 性能指标收集
```dart
// lib/src/core/monitoring/performance_monitor.dart
class PerformanceMonitor {
  static final Map<String, Stopwatch> _timers = {};
  static final AppLogger _logger = AppLogger.logModule('PerformanceMonitor');
  
  /// 开始性能计时
  static void startTimer(String operation) {
    _timers[operation] = Stopwatch()..start();
    
    SentryConfig.addBreadcrumb(
      message: 'Started operation: $operation',
      category: 'performance',
    );
  }
  
  /// 结束性能计时
  static void endTimer(String operation, {Map<String, dynamic>? context}) {
    final timer = _timers.remove(operation);
    if (timer == null) return;
    
    timer.stop();
    final duration = timer.elapsedMilliseconds;
    
    _logger.info('性能指标 [$operation]: ${duration}ms');
    
    // 记录到Sentry
    final transaction = Sentry.startTransaction(
      operation,
      'performance',
      description: 'Performance measurement for $operation',
    );
    
    transaction.setData('duration_ms', duration);
    if (context != null) {
      transaction.setData('context', context);
    }
    
    transaction.finish();
    
    // 性能告警
    if (duration > 2000) {
      SentryConfig.captureEvent(
        message: 'Slow operation detected: $operation (${duration}ms)',
        level: SentryLevel.warning,
        tags: {'performance': 'slow_operation'},
        extra: {'duration_ms': duration, 'operation': operation},
      );
    }
  }
  
  /// 记录内存使用情况
  static Future<void> recordMemoryUsage() async {
    final info = await DeviceInfoPlugin().androidInfo;
    final memoryInfo = await MemoryInfo.getMemoryInfo();
    
    SentryConfig.captureEvent(
      message: 'Memory usage report',
      level: SentryLevel.info,
      tags: {'monitoring': 'memory'},
      extra: {
        'total_memory': memoryInfo.totalMem,
        'available_memory': memoryInfo.availMem,
        'used_memory': memoryInfo.totalMem - memoryInfo.availMem,
        'memory_usage_percent': 
            ((memoryInfo.totalMem - memoryInfo.availMem) / memoryInfo.totalMem * 100).round(),
      },
    );
  }
  
  /// 记录网络性能
  static void recordNetworkPerformance({
    required String url,
    required String method,
    required int statusCode,
    required Duration duration,
    int? responseSize,
  }) {
    _logger.info('网络性能 [$method $url]: ${duration.inMilliseconds}ms');
    
    SentryConfig.captureEvent(
      message: 'Network performance',
      level: SentryLevel.info,
      tags: {'monitoring': 'network'},
      extra: {
        'url': url,
        'method': method,
        'status_code': statusCode,
        'duration_ms': duration.inMilliseconds,
        'response_size': responseSize,
      },
    );
  }
}
```

### 📈 业务指标监控

#### 业务事件追踪
```dart
// lib/src/core/monitoring/business_monitor.dart
class BusinessMonitor {
  static final AppLogger _logger = AppLogger.logModule('BusinessMonitor');
  
  /// 记录用户登录事件
  static void recordLogin({
    required String userId,
    required bool success,
    String? method,
    String? errorReason,
  }) {
    _logger.info('用户登录: $userId, 成功: $success');
    
    SentryConfig.captureEvent(
      message: 'User login attempt',
      level: success ? SentryLevel.info : SentryLevel.warning,
      tags: {
        'event_type': 'user_login',
        'success': success.toString(),
        'method': method ?? 'unknown',
      },
      extra: {
        'user_id': userId,
        'error_reason': errorReason,
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
  }
  
  /// 记录交易事件
  static void recordTrade({
    required String userId,
    required String symbol,
    required String side,
    required double quantity,
    required double price,
    required bool success,
    String? orderId,
    String? errorReason,
  }) {
    _logger.info('交易事件: $userId, $symbol, $side, 成功: $success');
    
    SentryConfig.captureEvent(
      message: 'Trade execution',
      level: success ? SentryLevel.info : SentryLevel.error,
      tags: {
        'event_type': 'trade',
        'symbol': symbol,
        'side': side,
        'success': success.toString(),
      },
      extra: {
        'user_id': userId,
        'order_id': orderId,
        'quantity': quantity,
        'price': price,
        'error_reason': errorReason,
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
  }
  
  /// 记录页面访问
  static void recordPageView({
    required String pageName,
    required String userId,
    Duration? loadTime,
  }) {
    _logger.debug('页面访问: $pageName, 用户: $userId');
    
    SentryConfig.addBreadcrumb(
      message: 'Page view: $pageName',
      category: 'navigation',
      data: {
        'page_name': pageName,
        'user_id': userId,
        'load_time_ms': loadTime?.inMilliseconds,
      },
    );
  }
  
  /// 记录功能使用
  static void recordFeatureUsage({
    required String feature,
    required String userId,
    Map<String, dynamic>? parameters,
  }) {
    _logger.info('功能使用: $feature, 用户: $userId');
    
    SentryConfig.captureEvent(
      message: 'Feature usage',
      level: SentryLevel.info,
      tags: {
        'event_type': 'feature_usage',
        'feature': feature,
      },
      extra: {
        'user_id': userId,
        'parameters': parameters,
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
  }
}
```

## 📊 系统监控配置

### 🖥️ Prometheus配置
```yaml
# docker/prometheus.yml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "alert_rules.yml"

scrape_configs:
  - job_name: 'financial-app'
    static_configs:
      - targets: ['financial-app:8080']
    metrics_path: '/metrics'
    scrape_interval: 10s

  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx-exporter:9113']

  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']

  - job_name: 'node'
    static_configs:
      - targets: ['node-exporter:9100']

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093
```

### 🚨 告警规则配置
```yaml
# docker/alert_rules.yml
groups:
  - name: financial_app_alerts
    rules:
      # 应用响应时间告警
      - alert: HighResponseTime
        expr: http_request_duration_seconds{quantile="0.95"} > 2
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "应用响应时间过高"
          description: "应用95%响应时间超过2秒，当前值: {{ $value }}秒"

      # 错误率告警
      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) / rate(http_requests_total[5m]) > 0.01
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "应用错误率过高"
          description: "应用错误率超过1%，当前值: {{ $value | humanizePercentage }}"

      # CPU使用率告警
      - alert: HighCPUUsage
        expr: 100 - (avg by(instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "CPU使用率过高"
          description: "实例 {{ $labels.instance }} CPU使用率超过80%，当前值: {{ $value }}%"

      # 内存使用率告警
      - alert: HighMemoryUsage
        expr: (1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100 > 85
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "内存使用率过高"
          description: "实例 {{ $labels.instance }} 内存使用率超过85%，当前值: {{ $value }}%"

      # 磁盘空间告警
      - alert: LowDiskSpace
        expr: (1 - (node_filesystem_avail_bytes / node_filesystem_size_bytes)) * 100 > 90
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "磁盘空间不足"
          description: "实例 {{ $labels.instance }} 磁盘使用率超过90%，当前值: {{ $value }}%"

      # 业务指标告警
      - alert: LowLoginSuccessRate
        expr: rate(user_login_total{status="success"}[5m]) / rate(user_login_total[5m]) < 0.95
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "登录成功率过低"
          description: "登录成功率低于95%，当前值: {{ $value | humanizePercentage }}"

      - alert: LowTradeSuccessRate
        expr: rate(trade_execution_total{status="success"}[5m]) / rate(trade_execution_total[5m]) < 0.99
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "交易成功率过低"
          description: "交易成功率低于99%，当前值: {{ $value | humanizePercentage }}"
```

### 📧 AlertManager配置
```yaml
# docker/alertmanager.yml
global:
  smtp_smarthost: 'smtp.company.com:587'
  smtp_from: '<EMAIL>'
  smtp_auth_username: '<EMAIL>'
  smtp_auth_password: 'smtp_password'

route:
  group_by: ['alertname']
  group_wait: 10s
  group_interval: 10s
  repeat_interval: 1h
  receiver: 'web.hook'
  routes:
    - match:
        severity: critical
      receiver: 'critical-alerts'
    - match:
        severity: warning
      receiver: 'warning-alerts'

receivers:
  - name: 'web.hook'
    webhook_configs:
      - url: 'http://webhook-service:8080/alerts'

  - name: 'critical-alerts'
    email_configs:
      - to: '<EMAIL>'
        subject: '🚨 Critical Alert: {{ .GroupLabels.alertname }}'
        body: |
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          {{ end }}
    webhook_configs:
      - url: 'http://sms-service:8080/send'
        send_resolved: true

  - name: 'warning-alerts'
    email_configs:
      - to: '<EMAIL>'
        subject: '⚠️ Warning Alert: {{ .GroupLabels.alertname }}'
        body: |
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          {{ end }}

inhibit_rules:
  - source_match:
      severity: 'critical'
    target_match:
      severity: 'warning'
    equal: ['alertname', 'instance']
```

## 📝 日志管理

### 📊 日志配置
```dart
// lib/src/core/logging/log_config.dart
class LogConfig {
  static void init() {
    Logger.root.level = _getLogLevel();
    Logger.root.onRecord.listen((record) {
      _handleLogRecord(record);
    });
  }
  
  static Level _getLogLevel() {
    const env = String.fromEnvironment('APP_ENV');
    switch (env) {
      case 'production':
        return Level.INFO;
      case 'testing':
        return Level.FINE;
      default:
        return Level.ALL;
    }
  }
  
  static void _handleLogRecord(LogRecord record) {
    // 控制台输出
    print(_formatLogRecord(record));
    
    // 文件输出
    _writeToFile(record);
    
    // 远程日志服务
    if (record.level >= Level.WARNING) {
      _sendToRemoteLogging(record);
    }
  }
  
  static String _formatLogRecord(LogRecord record) {
    final timestamp = record.time.toIso8601String();
    final level = record.level.name.padRight(7);
    final logger = record.loggerName.padRight(20);
    
    return '[$timestamp] [$level] [$logger] ${record.message}';
  }
  
  static void _writeToFile(LogRecord record) {
    // 实现文件日志写入
  }
  
  static void _sendToRemoteLogging(LogRecord record) {
    // 发送到远程日志服务
    SentryConfig.captureEvent(
      message: record.message,
      level: _mapLogLevel(record.level),
      tags: {'logger': record.loggerName},
      extra: {
        'timestamp': record.time.toIso8601String(),
        'level': record.level.name,
      },
    );
  }
  
  static SentryLevel _mapLogLevel(Level level) {
    if (level >= Level.SEVERE) return SentryLevel.error;
    if (level >= Level.WARNING) return SentryLevel.warning;
    if (level >= Level.INFO) return SentryLevel.info;
    return SentryLevel.debug;
  }
}
```

### 📊 Grafana仪表板配置
```json
{
  "dashboard": {
    "title": "金融应用监控仪表板",
    "panels": [
      {
        "title": "应用响应时间",
        "type": "graph",
        "targets": [
          {
            "expr": "http_request_duration_seconds{quantile=\"0.95\"}",
            "legendFormat": "95th percentile"
          }
        ]
      },
      {
        "title": "错误率",
        "type": "singlestat",
        "targets": [
          {
            "expr": "rate(http_requests_total{status=~\"5..\"}[5m]) / rate(http_requests_total[5m]) * 100",
            "legendFormat": "Error Rate %"
          }
        ]
      },
      {
        "title": "系统资源使用",
        "type": "graph",
        "targets": [
          {
            "expr": "100 - (avg(irate(node_cpu_seconds_total{mode=\"idle\"}[5m])) * 100)",
            "legendFormat": "CPU Usage %"
          },
          {
            "expr": "(1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100",
            "legendFormat": "Memory Usage %"
          }
        ]
      },
      {
        "title": "业务指标",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(user_login_total[5m])",
            "legendFormat": "Login Rate"
          },
          {
            "expr": "rate(trade_execution_total[5m])",
            "legendFormat": "Trade Rate"
          }
        ]
      }
    ]
  }
}
```

---

**📊 完善的监控体系确保了金融应用的稳定运行，通过实时监控和及时告警，保障了系统的高可用性和用户体验！**
