# 📋 代码规范与最佳实践

本文档定义了金融应用项目的代码规范和最佳实践，确保代码质量和团队协作效率。

## 🎯 总体原则

### 📐 设计原则
- **SOLID原则**: 单一职责、开闭原则、里氏替换、接口隔离、依赖倒置
- **DRY原则**: Don't Repeat Yourself，避免代码重复
- **KISS原则**: Keep It Simple, Stupid，保持简单
- **YAGNI原则**: You Aren't Gonna Need It，不要过度设计

### 🏗️ 架构原则
- **分层架构**: 严格遵循三层架构（Data、Domain、Presentation）
- **依赖注入**: 使用GetIt进行依赖管理
- **状态管理**: Provider用于简单UI状态，Bloc用于复杂业务逻辑
- **模块化**: 每个模块独立开发，通过接口通信

## 📝 Dart代码规范

### 🔤 命名规范

#### 类命名 (PascalCase)
```dart
// ✅ 正确
class UserService {}
class AuthBloc {}
class LoginResponseModel {}

// ❌ 错误
class userService {}
class auth_bloc {}
class loginresponsemodel {}
```

#### 方法和变量命名 (camelCase)
```dart
// ✅ 正确
void getUserData() {}
String userName = '';
bool isLoggedIn = false;

// ❌ 错误
void GetUserData() {}
String user_name = '';
bool IsLoggedIn = false;
```

#### 常量命名 (UPPER_SNAKE_CASE)
```dart
// ✅ 正确
const String API_BASE_URL = 'https://api.example.com';
const int MAX_RETRY_COUNT = 3;

// ❌ 错误
const String apiBaseUrl = 'https://api.example.com';
const int maxRetryCount = 3;
```

#### 私有成员命名 (下划线前缀)
```dart
// ✅ 正确
class UserService {
  String _apiKey = '';
  void _validateInput() {}
}

// ❌ 错误
class UserService {
  String apiKey = '';
  void validateInput() {}
}
```

### 📁 文件命名规范

#### 文件命名 (snake_case)
```
// ✅ 正确
user_service.dart
auth_bloc.dart
login_response_model.dart

// ❌ 错误
UserService.dart
AuthBloc.dart
LoginResponseModel.dart
```

#### 文件组织
```
lib/
├── src/
│   ├── data/
│   │   ├── datasources/
│   │   │   ├── local/
│   │   │   │   └── user_local_data_source.dart
│   │   │   └── remote/
│   │   │       └── user_remote_data_source.dart
│   │   ├── models/
│   │   │   └── user_model.dart
│   │   └── repositories/
│   │       └── user_repository_impl.dart
│   ├── domain/
│   │   ├── entities/
│   │   │   └── user.dart
│   │   ├── repositories/
│   │   │   └── user_repository.dart
│   │   └── usecases/
│   │       └── get_user.dart
│   └── presentation/
│       ├── bloc/
│       │   ├── user_bloc.dart
│       │   ├── user_event.dart
│       │   └── user_state.dart
│       ├── pages/
│       │   └── user_profile_page.dart
│       └── widgets/
│           └── user_avatar_widget.dart
```

### 💬 注释规范

#### 类注释
```dart
/// 用户服务类
/// 
/// 负责处理用户相关的业务逻辑，包括：
/// - 用户信息获取
/// - 用户状态管理
/// - 用户偏好设置
class UserService {
  // 实现代码...
}
```

#### 方法注释
```dart
/// 获取用户信息
/// 
/// [userId] 用户ID
/// 
/// 返回 [User] 对象，如果用户不存在则抛出 [UserNotFoundException]
/// 
/// 示例：
/// ```dart
/// final user = await getUserById('123');
/// print(user.name);
/// ```
Future<User> getUserById(String userId) async {
  // 实现代码...
}
```

#### 复杂逻辑注释
```dart
void calculateTradingFee(double amount) {
  // 计算基础手续费（万分之二点五）
  double baseFee = amount * 0.00025;
  
  // 最低手续费5元
  if (baseFee < 5.0) {
    baseFee = 5.0;
  }
  
  // 印花税（千分之一，仅卖出时收取）
  double stampTax = isSelling ? amount * 0.001 : 0.0;
  
  return baseFee + stampTax;
}
```

### 🔧 代码格式化

#### 使用dart format
```bash
# 格式化单个文件
dart format lib/main.dart

# 格式化整个项目
dart format .

# 检查格式但不修改
dart format --set-exit-if-changed .
```

#### 行长度限制
```dart
// ✅ 正确 - 每行不超过80字符
const String longMessage = 
    'This is a very long message that needs to be split '
    'across multiple lines for better readability.';

// ❌ 错误 - 行太长
const String longMessage = 'This is a very long message that needs to be split across multiple lines for better readability.';
```

## 🏗️ 架构规范

### 📦 模块结构规范

#### 依赖注入配置
```dart
// module_injection.dart
import 'package:get_it/get_it.dart';

/// 模块依赖注入配置
class ModuleInjection {
  static Future<void> init() async {
    final locator = GetIt.instance;
    
    // 数据源注册
    locator.registerLazySingleton<UserRemoteDataSource>(
      () => UserRemoteDataSourceImpl(locator()),
    );
    
    // 仓储注册
    locator.registerLazySingleton<UserRepository>(
      () => UserRepositoryImpl(locator()),
    );
    
    // 用例注册
    locator.registerLazySingleton(() => GetUser(locator()));
    
    // Bloc注册
    locator.registerFactory(() => UserBloc(locator()));
  }
}
```

#### 主导出文件
```dart
// module_name.dart
library module_name;

// 导出公共接口
export 'src/domain/entities/user.dart';
export 'src/domain/repositories/user_repository.dart';
export 'src/presentation/bloc/user_bloc.dart';

// 导出依赖注入
export 'module_injection.dart';
```

### 🔄 状态管理规范

#### Bloc规范
```dart
// 事件定义
abstract class UserEvent extends Equatable {
  const UserEvent();
  
  @override
  List<Object?> get props => [];
}

class GetUserEvent extends UserEvent {
  final String userId;
  
  const GetUserEvent(this.userId);
  
  @override
  List<Object?> get props => [userId];
}

// 状态定义
abstract class UserState extends Equatable {
  const UserState();
  
  @override
  List<Object?> get props => [];
}

class UserInitial extends UserState {}

class UserLoading extends UserState {}

class UserLoaded extends UserState {
  final User user;
  
  const UserLoaded(this.user);
  
  @override
  List<Object?> get props => [user];
}

class UserError extends UserState {
  final String message;
  
  const UserError(this.message);
  
  @override
  List<Object?> get props => [message];
}

// Bloc实现
class UserBloc extends Bloc<UserEvent, UserState> {
  final GetUser _getUser;
  
  UserBloc(this._getUser) : super(UserInitial()) {
    on<GetUserEvent>(_onGetUser);
  }
  
  Future<void> _onGetUser(
    GetUserEvent event,
    Emitter<UserState> emit,
  ) async {
    emit(UserLoading());
    
    try {
      final user = await _getUser(event.userId);
      emit(UserLoaded(user));
    } catch (e) {
      emit(UserError(e.toString()));
    }
  }
}
```

#### Provider规范
```dart
// 简单UI状态使用Provider
class ThemeProvider extends ChangeNotifier {
  ThemeMode _themeMode = ThemeMode.system;
  
  ThemeMode get themeMode => _themeMode;
  
  void setThemeMode(ThemeMode mode) {
    _themeMode = mode;
    notifyListeners();
  }
}
```

### 🌐 网络请求规范

#### API服务接口
```dart
abstract class ApiService {
  Future<Map<String, dynamic>> get(String path, {Map<String, dynamic>? params});
  Future<Map<String, dynamic>> post(String path, {Map<String, dynamic>? data});
  Future<Map<String, dynamic>> put(String path, {Map<String, dynamic>? data});
  Future<Map<String, dynamic>> delete(String path);
}
```

#### 错误处理
```dart
class ApiException implements Exception {
  final String message;
  final int? statusCode;
  
  const ApiException(this.message, [this.statusCode]);
  
  @override
  String toString() => 'ApiException: $message';
}

// 使用示例
try {
  final response = await apiService.get('/users/123');
  return User.fromJson(response);
} on ApiException catch (e) {
  throw UserNotFoundException('User not found: ${e.message}');
} catch (e) {
  throw UnknownException('Unknown error: $e');
}
```

## 🧪 测试规范

### 📝 测试文件组织
```
test/
├── unit/                    # 单元测试
│   ├── data/
│   ├── domain/
│   └── presentation/
├── widget/                  # Widget测试
└── integration/             # 集成测试
```

### 🔬 单元测试规范
```dart
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';

void main() {
  group('UserBloc', () {
    late UserBloc userBloc;
    late MockGetUser mockGetUser;
    
    setUp(() {
      mockGetUser = MockGetUser();
      userBloc = UserBloc(mockGetUser);
    });
    
    tearDown(() {
      userBloc.close();
    });
    
    test('初始状态应该是UserInitial', () {
      expect(userBloc.state, equals(UserInitial()));
    });
    
    test('获取用户成功时应该发出UserLoaded状态', () async {
      // Arrange
      const user = User(id: '1', name: 'Test User');
      when(mockGetUser.call('1')).thenAnswer((_) async => user);
      
      // Act
      userBloc.add(const GetUserEvent('1'));
      
      // Assert
      await expectLater(
        userBloc.stream,
        emitsInOrder([
          UserLoading(),
          const UserLoaded(user),
        ]),
      );
    });
  });
}
```

### 🎨 Widget测试规范
```dart
void main() {
  testWidgets('UserProfilePage应该显示用户信息', (WidgetTester tester) async {
    // Arrange
    const user = User(id: '1', name: 'Test User');
    
    // Act
    await tester.pumpWidget(
      MaterialApp(
        home: UserProfilePage(user: user),
      ),
    );
    
    // Assert
    expect(find.text('Test User'), findsOneWidget);
    expect(find.byType(CircleAvatar), findsOneWidget);
  });
}
```

## 📊 性能优化规范

### 🚀 Widget优化
```dart
// ✅ 使用const构造函数
class MyWidget extends StatelessWidget {
  const MyWidget({Key? key}) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return const Text('Hello World');
  }
}

// ✅ 避免在build方法中创建对象
class MyWidget extends StatelessWidget {
  static const _textStyle = TextStyle(fontSize: 16);
  
  @override
  Widget build(BuildContext context) {
    return Text('Hello', style: _textStyle);
  }
}
```

### 💾 内存优化
```dart
// ✅ 及时释放资源
class MyPage extends StatefulWidget {
  @override
  _MyPageState createState() => _MyPageState();
}

class _MyPageState extends State<MyPage> {
  StreamSubscription? _subscription;
  
  @override
  void initState() {
    super.initState();
    _subscription = someStream.listen((data) {
      // 处理数据
    });
  }
  
  @override
  void dispose() {
    _subscription?.cancel();
    super.dispose();
  }
}
```

## 🔐 安全规范

### 🛡️ 敏感信息处理
```dart
// ✅ 不要在代码中硬编码敏感信息
class ApiConfig {
  static const String baseUrl = String.fromEnvironment('API_BASE_URL');
  static const String apiKey = String.fromEnvironment('API_KEY');
}

// ❌ 错误示例
class ApiConfig {
  static const String apiKey = 'sk-1234567890abcdef';
}
```

### 🔒 数据验证
```dart
// ✅ 始终验证输入数据
String validateEmail(String email) {
  if (email.isEmpty) {
    throw ValidationException('邮箱不能为空');
  }
  
  if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email)) {
    throw ValidationException('邮箱格式不正确');
  }
  
  return email.trim().toLowerCase();
}
```

## 📋 代码审查清单

### ✅ 提交前检查
- [ ] 代码格式化 (`dart format`)
- [ ] 静态分析通过 (`flutter analyze`)
- [ ] 测试通过 (`flutter test`)
- [ ] 遵循命名规范
- [ ] 添加必要注释
- [ ] 没有硬编码的敏感信息
- [ ] 错误处理完善
- [ ] 性能考虑合理

### 🔍 审查要点
- [ ] 架构设计合理
- [ ] 代码逻辑清晰
- [ ] 异常处理完善
- [ ] 测试覆盖充分
- [ ] 文档更新及时
- [ ] 安全考虑周全

## 🛠️ 工具配置

### 📝 analysis_options.yaml
```yaml
include: package:flutter_lints/flutter.yaml

analyzer:
  exclude:
    - "**/*.g.dart"
    - "**/*.freezed.dart"
  
  strong-mode:
    implicit-casts: false
    implicit-dynamic: false

linter:
  rules:
    - prefer_const_constructors
    - prefer_const_literals_to_create_immutables
    - avoid_print
    - prefer_single_quotes
    - sort_constructors_first
```

### 🔧 IDE配置
#### VS Code settings.json
```json
{
  "dart.previewFlutterUiGuides": true,
  "dart.previewFlutterUiGuidesCustomTracking": true,
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll": true
  }
}
```

## 🚀 持续改进

### 📈 代码质量监控
- 定期运行代码质量检查工具
- 监控测试覆盖率变化
- 跟踪技术债务情况
- 收集团队反馈

### 🔄 规范更新
- 根据项目发展调整规范
- 学习业界最佳实践
- 团队讨论和共识
- 文档及时更新

---

**📚 持续学习和改进是保持代码质量的关键！请定期查看和更新这些规范。**
