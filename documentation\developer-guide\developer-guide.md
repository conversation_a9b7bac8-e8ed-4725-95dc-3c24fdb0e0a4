> **📋 文档迁移说明**
> 
> 本文档已从项目根目录迁移到统一的文档管理系统中。
> - **原文件名**: DEVELOPER_GUIDE.md
> - **迁移时间**: 2025-07-07T20:00:20.406017
> - **迁移工具**: scripts/migrate_docs.dart
> 
> 如需查看最新的文档结构，请参考 [文档中心](../README.md)。

---

# 👨‍💻 金融应用开发者指南

## 🎯 快速开始

### 环境准备
```bash
# 1. 安装 Flutter SDK (3.24.0+)
flutter --version

# 2. 克隆项目
git clone <repository-url>
cd financial_app_workspace

# 3. 安装依赖
make install

# 4. 运行项目
make run
```

### 项目结构概览
```
financial_app_workspace/
├── packages/
│   ├── financial_app_main/          # 主应用
│   ├── financial_app_core/          # 核心共享库
│   ├── financial_app_auth/          # 认证模块
│   ├── financial_app_market/        # 市场数据模块
│   ├── financial_app_trade/         # 交易模块
│   ├── financial_app_portfolio/     # 投资组合模块
│   └── financial_app_notification/  # 通知模块
├── tools/                           # 开发工具
├── scripts/                         # 构建脚本
└── docs/                           # 文档
```

## 🏗️ 架构概述

### 混合状态管理架构
我们采用 **Provider + Bloc** 的混合架构：

#### Provider 使用场景
```dart
// ✅ 简单的 UI 状态管理
class ThemeProvider extends ChangeNotifier {
  ThemeMode _themeMode = ThemeMode.system;
  
  void toggleTheme() {
    _themeMode = _themeMode == ThemeMode.light 
        ? ThemeMode.dark 
        : ThemeMode.light;
    notifyListeners();
  }
}

// 使用方式
Consumer<ThemeProvider>(
  builder: (context, themeProvider, child) {
    return Switch(
      value: themeProvider.themeMode == ThemeMode.dark,
      onChanged: (_) => themeProvider.toggleTheme(),
    );
  },
)
```

#### Bloc 使用场景
```dart
// ✅ 复杂的业务逻辑状态管理
class AuthBloc extends BaseBloc<AuthEvent, AuthState> {
  Future<void> _onLogin(LoginEvent event, Emitter<AuthState> emit) async {
    emit(const AuthLoading());
    
    final result = await _authRepository.login(
      username: event.username,
      password: event.password,
    );
    
    result.fold(
      (failure) => emit(AuthError(message: failure.message)),
      (user) => emit(AuthAuthenticated(user: user)),
    );
  }
}

// 使用方式
BlocBuilder<AuthBloc, AuthState>(
  builder: (context, state) {
    if (state is AuthLoading) {
      return const CircularProgressIndicator();
    } else if (state is AuthAuthenticated) {
      return HomeScreen(user: state.user);
    } else if (state is AuthError) {
      return ErrorWidget(message: state.message);
    }
    return LoginScreen();
  },
)
```

## 📝 开发规范

### 1. 代码风格

#### 命名规范
```dart
// ✅ 类名使用 PascalCase
class UserRepository {}
class AuthBloc {}

// ✅ 变量和方法名使用 camelCase
String userName = '';
void getUserInfo() {}

// ✅ 常量使用 lowerCamelCase
const String apiBaseUrl = 'https://api.example.com';

// ✅ 私有成员使用下划线前缀
String _privateField = '';
void _privateMethod() {}
```

#### 文件命名规范
```
// ✅ 文件名使用 snake_case
user_repository.dart
auth_bloc.dart
market_event.dart

// ✅ 测试文件添加 _test 后缀
user_repository_test.dart
auth_bloc_test.dart
```

### 2. Bloc 开发规范

#### 事件定义
```dart
// ✅ 使用 sealed class 定义事件基类
sealed class AuthEvent extends Equatable {
  const AuthEvent();
}

// ✅ 事件名使用动词 + Event 后缀
class LoginEvent extends AuthEvent {
  final String username;
  final String password;
  
  const LoginEvent({
    required this.username,
    required this.password,
  });
  
  @override
  List<Object> get props => [username, password];
}

class LogoutEvent extends AuthEvent {
  const LogoutEvent();
}
```

#### 状态定义
```dart
// ✅ 使用 sealed class 定义状态基类
sealed class AuthState extends BaseState {
  const AuthState();
}

// ✅ 状态名使用形容词或过去分词
class AuthInitial extends AuthState {
  const AuthInitial();
}

class AuthLoading extends AuthState {
  const AuthLoading();
}

class AuthAuthenticated extends AuthState {
  final User user;
  final String token;
  
  const AuthAuthenticated({
    required this.user,
    required this.token,
  });
  
  @override
  List<Object> get props => [user, token];
}
```

#### Bloc 实现
```dart
class AuthBloc extends BaseBloc<AuthEvent, AuthState> {
  final AuthRepository _authRepository;
  final Logger _logger;
  
  AuthBloc({
    required AuthRepository authRepository,
    required Logger logger,
  }) : _authRepository = authRepository,
       _logger = logger,
       super(const AuthInitial()) {
    
    // 注册事件处理器
    on<LoginEvent>(_onLogin);
    on<LogoutEvent>(_onLogout);
  }

  Future<void> _onLogin(LoginEvent event, Emitter<AuthState> emit) async {
    _logger.info('用户登录: ${event.username}');
    
    emit(const AuthLoading());
    
    try {
      final result = await _authRepository.login(
        username: event.username,
        password: event.password,
      );
      
      result.fold(
        (failure) => emit(AuthError(message: failure.message)),
        (user) => emit(AuthAuthenticated(user: user, token: user.token)),
      );
    } catch (e) {
      _logger.error('登录异常: $e');
      emit(const AuthError(message: '登录失败，请稍后重试'));
    }
  }
}
```

### 3. 错误处理规范

#### 使用 Either 模式
```dart
// ✅ Repository 层返回 Either<Failure, Success>
abstract class AuthRepository {
  Future<Either<Failure, User>> login({
    required String username,
    required String password,
  });
}

class AuthRepositoryImpl implements AuthRepository {
  @override
  Future<Either<Failure, User>> login({
    required String username,
    required String password,
  }) async {
    try {
      final response = await _apiService.post('/auth/login', {
        'username': username,
        'password': password,
      });
      
      final user = User.fromJson(response.data);
      return Right(user);
    } on DioException catch (e) {
      if (e.response?.statusCode == 401) {
        return const Left(AuthenticationFailure('用户名或密码错误'));
      }
      return Left(NetworkFailure(e.message ?? '网络错误'));
    } catch (e) {
      return Left(UnknownFailure(e.toString()));
    }
  }
}
```

#### 自定义 Failure 类型
```dart
// ✅ 定义具体的失败类型
abstract class Failure extends Equatable {
  final String message;
  final String? code;
  
  const Failure(this.message, {this.code});
  
  @override
  List<Object?> get props => [message, code];
}

class NetworkFailure extends Failure {
  const NetworkFailure(String message) : super(message);
}

class AuthenticationFailure extends Failure {
  const AuthenticationFailure(String message) : super(message);
}

class ValidationFailure extends Failure {
  const ValidationFailure(String message) : super(message);
}
```

### 4. 测试规范

#### 单元测试
```dart
// ✅ Bloc 测试示例
class AuthBlocTest {
  group('AuthBloc', () {
    late AuthBloc authBloc;
    late MockAuthRepository mockRepository;
    
    setUp(() {
      mockRepository = MockAuthRepository();
      authBloc = AuthBloc(repository: mockRepository);
    });
    
    tearDown(() {
      authBloc.close();
    });
    
    blocTest<AuthBloc, AuthState>(
      '登录成功时应该发送 AuthAuthenticated 状态',
      build: () => authBloc,
      setUp: () {
        when(() => mockRepository.login(
          username: any(named: 'username'),
          password: any(named: 'password'),
        )).thenAnswer((_) async => const Right(mockUser));
      },
      act: (bloc) => bloc.add(const LoginEvent(
        username: '<EMAIL>',
        password: 'password123',
      )),
      expect: () => [
        const AuthLoading(),
        isA<AuthAuthenticated>()
          .having((state) => state.user.email, 'user email', '<EMAIL>'),
      ],
    );
  });
}
```

#### Widget 测试
```dart
// ✅ Widget 测试示例
class LoginScreenTest {
  testWidgets('应该显示登录表单', (WidgetTester tester) async {
    await tester.pumpWidget(
      MaterialApp(
        home: BlocProvider(
          create: (_) => MockAuthBloc(),
          child: const LoginScreen(),
        ),
      ),
    );
    
    // 验证 UI 元素
    expect(find.byType(TextField), findsNWidgets(2)); // 用户名和密码输入框
    expect(find.byType(ElevatedButton), findsOneWidget); // 登录按钮
    expect(find.text('登录'), findsOneWidget);
  });
  
  testWidgets('点击登录按钮应该触发登录事件', (WidgetTester tester) async {
    final mockBloc = MockAuthBloc();
    
    await tester.pumpWidget(
      MaterialApp(
        home: BlocProvider.value(
          value: mockBloc,
          child: const LoginScreen(),
        ),
      ),
    );
    
    // 输入用户名和密码
    await tester.enterText(find.byKey(const Key('username_field')), '<EMAIL>');
    await tester.enterText(find.byKey(const Key('password_field')), 'password123');
    
    // 点击登录按钮
    await tester.tap(find.byKey(const Key('login_button')));
    
    // 验证事件被触发
    verify(() => mockBloc.add(const LoginEvent(
      username: '<EMAIL>',
      password: 'password123',
    ))).called(1);
  });
}
```

## 🛠️ 开发工具

### 1. Make 命令
```bash
# 安装依赖
make install

# 运行应用
make run

# 运行测试
make test

# 代码分析
make analyze

# 代码格式化
make format

# 生成代码
make generate

# 构建应用
make build

# 清理项目
make clean
```

### 2. 代码生成工具

#### 生成 Bloc
```bash
# 生成完整的 Bloc 文件
make generate-bloc MODULE=user EVENTS=login,logout,register

# 这会生成：
# - user_event.dart
# - user_state.dart
# - user_bloc.dart
# - user_bloc_test.dart
```

#### 生成模块
```bash
# 生成完整的模块结构
make generate-module MODULE=notification

# 这会生成完整的模块目录结构和基础文件
```

### 3. 调试工具

#### 性能监控
```dart
// 在调试模式下启用性能监控
PerformanceOverlay(
  showOverlay: kDebugMode,
  child: MyApp(),
)
```

#### Bloc 调试
```dart
// 启用 Bloc 日志
class SimpleBlocObserver extends BlocObserver {
  @override
  void onEvent(BlocBase bloc, Object? event) {
    super.onEvent(bloc, event);
    print('${bloc.runtimeType} $event');
  }

  @override
  void onTransition(BlocBase bloc, Transition transition) {
    super.onTransition(bloc, transition);
    print('${bloc.runtimeType} $transition');
  }

  @override
  void onError(BlocBase bloc, Object error, StackTrace stackTrace) {
    super.onError(bloc, error, stackTrace);
    print('${bloc.runtimeType} $error $stackTrace');
  }
}

// 在 main.dart 中注册
void main() {
  Bloc.observer = SimpleBlocObserver();
  runApp(MyApp());
}
```

## 🚀 部署指南

### 1. 构建配置

#### Android 构建
```bash
# Debug 构建
flutter build apk --debug

# Release 构建
flutter build apk --release

# App Bundle 构建
flutter build appbundle --release
```

#### iOS 构建
```bash
# Debug 构建
flutter build ios --debug

# Release 构建
flutter build ios --release
```

### 2. 环境配置

#### 开发环境
```dart
// config/dev.dart
class DevConfig {
  static const String apiBaseUrl = 'https://dev-api.example.com';
  static const bool enableLogging = true;
  static const bool enablePerformanceMonitoring = true;
}
```

#### 生产环境
```dart
// config/prod.dart
class ProdConfig {
  static const String apiBaseUrl = 'https://api.example.com';
  static const bool enableLogging = false;
  static const bool enablePerformanceMonitoring = false;
}
```

## 📚 学习资源

### 官方文档
- [Flutter 官方文档](https://flutter.dev/docs)
- [Bloc 官方文档](https://bloclibrary.dev)
- [Provider 官方文档](https://pub.dev/packages/provider)

### 项目文档
- [详细架构指南](./DETAILED_ARCHITECTURE_GUIDE.md)
- [架构决策记录](./ARCHITECTURE_DECISION_RECORDS.md)
- [API 文档](./API_DOCUMENTATION.md)

### 最佳实践
- [Bloc 最佳实践](https://bloclibrary.dev/architecture/)
- [Flutter 性能优化](https://flutter.dev/docs/perf)
- [Dart 代码风格](https://dart.dev/guides/language/effective-dart)

## 🤝 贡献指南

### 1. 开发流程
1. 从 `main` 分支创建功能分支
2. 按照代码规范进行开发
3. 编写相应的测试
4. 提交 Pull Request
5. 代码审查通过后合并

### 2. 提交规范
```bash
# 功能开发
git commit -m "feat: 添加用户登录功能"

# Bug 修复
git commit -m "fix: 修复登录页面崩溃问题"

# 文档更新
git commit -m "docs: 更新 API 文档"

# 重构
git commit -m "refactor: 重构认证模块"

# 测试
git commit -m "test: 添加登录功能测试"
```

### 3. 代码审查清单
- [ ] 代码符合项目规范
- [ ] 包含相应的测试
- [ ] 文档已更新
- [ ] 性能影响已评估
- [ ] 安全性已考虑

---

*这份指南将帮助您快速上手项目开发，如有疑问请参考详细文档或联系团队成员。*
