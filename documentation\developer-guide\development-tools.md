> **📋 文档迁移说明**
> 
> 本文档已从项目根目录迁移到统一的文档管理系统中。
> - **原文件名**: DEVELOPMENT_TOOLS_GUIDE.md
> - **迁移时间**: 2025-07-07T20:00:20.410900
> - **迁移工具**: scripts/migrate_docs.dart
> 
> 如需查看最新的文档结构，请参考 [文档中心](../README.md)。

---

# 🛠️ 开发工具完整指南

## 📋 工具概览

本项目提供了完整的开发工具链，帮助提升开发效率和代码质量。

### 🧰 **开发者工具箱** (推荐入口)
```bash
dart scripts/dev_toolbox.dart
```
**功能**: 交互式工具选择界面，包含所有开发工具的快速入口

---

## 🏗️ **代码生成工具**

### 📦 **模块生成器**
```bash
# 生成完整的标准化模块
dart scripts/code_generator.dart module user_profile

# 生成业务实体
dart scripts/code_generator.dart entity product

# 生成用例
dart scripts/code_generator.dart usecase create_order

# 生成页面
dart scripts/code_generator.dart page settings

# 生成组件
dart scripts/code_generator.dart widget user_card
```

**特点**:
- ✅ 自动创建标准三层架构
- ✅ 生成完整的依赖注入配置
- ✅ 包含测试模板和文档
- ✅ 遵循项目命名规范

---

## 🔍 **分析和优化工具**

### 📊 **模块结构分析器**
```bash
# 分析所有模块结构质量
dart scripts/module_structure_optimizer.dart analyze

# 自动优化模块结构
dart scripts/module_structure_optimizer.dart optimize

# 验证模块结构规范
dart scripts/module_structure_optimizer.dart validate

# 创建新的标准模块
dart scripts/module_structure_optimizer.dart create new_module
```

**输出**:
- 📊 模块质量评分 (0-100分)
- 📈 质量分布统计
- 📋 问题和建议列表
- 📄 详细分析报告 (JSON格式)

### 🔧 **自动化重构工具**
```bash
# 运行所有重构操作
dart scripts/auto_refactor.dart all

# 代码格式化
dart scripts/auto_refactor.dart format

# 整理导入语句
dart scripts/auto_refactor.dart imports

# 移除未使用的代码
dart scripts/auto_refactor.dart unused

# 检查命名规范
dart scripts/auto_refactor.dart naming

# 运行代码分析
dart scripts/auto_refactor.dart analyze
```

**功能**:
- 🎨 自动代码格式化
- 📦 智能导入语句排序
- 🧹 清理未使用的代码
- 📏 命名规范检查
- 🔍 静态代码分析

---

## ⚙️ **环境配置工具**

### 🔧 **开发环境设置**
```bash
# 完整的开发环境设置
dart scripts/dev_environment_setup.dart setup

# 检查开发环境
dart scripts/dev_environment_setup.dart check

# 安装开发工具
dart scripts/dev_environment_setup.dart tools

# 设置 Git hooks
dart scripts/dev_environment_setup.dart hooks

# 清理开发环境
dart scripts/dev_environment_setup.dart clean
```

**设置内容**:
- 🔍 环境检查 (Flutter, Dart, Git, Melos)
- 📦 开发工具安装
- 🪝 Git hooks 配置
- 💻 IDE 配置 (VS Code, IntelliJ)
- 📝 开发配置文件

---

## 🎯 **快速命令参考**

### 📋 **日常开发命令**
```bash
# 开发环境启动
melos bootstrap && melos dev

# 快速质量检查
dart scripts/auto_refactor.dart all && melos test_unit

# 生成新模块
dart scripts/code_generator.dart module <module_name>

# 分析项目状态
dart scripts/module_structure_optimizer.dart analyze
```

### 🔧 **维护命令**
```bash
# 清理和重新安装
melos clean && melos bootstrap

# 更新依赖
melos upgrade

# 完整测试
melos test

# 性能分析
dart scripts/performance_analyzer.dart
```

---

## 💡 **使用技巧**

### 🚀 **提升效率的技巧**

1. **使用工具箱**: 优先使用 `dart scripts/dev_toolbox.dart` 作为入口
2. **批量操作**: 使用 `melos` 命令对所有包进行批量操作
3. **自动化**: 设置 Git hooks 自动运行质量检查
4. **模板生成**: 使用代码生成器保持代码一致性

### 📊 **质量监控**

1. **定期分析**: 每周运行模块结构分析
2. **覆盖率监控**: 保持测试覆盖率在 80% 以上
3. **性能监控**: 定期运行性能分析
4. **代码质量**: 使用自动重构工具保持代码质量

### 🔄 **工作流建议**

```bash
# 1. 开始新功能开发
git checkout -b feature/new-feature
dart scripts/code_generator.dart module new_feature

# 2. 开发过程中
dart scripts/auto_refactor.dart format  # 格式化代码
melos test_unit                         # 运行单元测试

# 3. 提交前检查
dart scripts/auto_refactor.dart all     # 全面重构
melos test                              # 完整测试
dart scripts/module_structure_optimizer.dart analyze  # 结构分析

# 4. 提交代码
git add .
git commit -m "feat(module): add new feature"
git push origin feature/new-feature
```

---

## 🆘 **故障排除**

### ❌ **常见问题**

1. **工具执行失败**
   ```bash
   # 检查 Dart 环境
   dart --version
   
   # 重新安装依赖
   melos clean && melos bootstrap
   ```

2. **权限问题** (Linux/macOS)
   ```bash
   # 给脚本添加执行权限
   chmod +x scripts/*.dart
   ```

3. **路径问题**
   ```bash
   # 确保在项目根目录执行
   pwd  # 应该显示项目根目录
   ```

### 🔧 **重置环境**
```bash
# 完全重置开发环境
dart scripts/dev_environment_setup.dart clean
dart scripts/dev_environment_setup.dart setup
```

---

## 📞 **获取帮助**

- 🧰 **工具箱**: `dart scripts/dev_toolbox.dart`
- 📚 **开发指南**: `DEVELOPER_GUIDE.md`
- 🏗️ **架构标准**: `ARCHITECTURE_STANDARDS.md`
- ⚡ **快速参考**: `QUICK_REFERENCE.md`

**记住**: 所有工具都支持 `--help` 参数查看详细帮助信息！
