# 🚀 开发者快速开始指南

欢迎加入金融应用项目的开发团队！本指南将帮助您快速搭建开发环境并开始开发工作。

## 📋 环境要求

### 🖥️ 系统要求
- **操作系统**: Windows 10+, macOS 10.14+, 或 Ubuntu 18.04+
- **内存**: 8GB RAM (推荐16GB)
- **存储**: 至少10GB可用空间
- **网络**: 稳定的互联网连接

### 🛠️ 必需工具

#### 1. Flutter SDK
```bash
# 下载并安装 Flutter 3.0+
# 访问 https://flutter.dev/docs/get-started/install

# 验证安装
flutter doctor
```

#### 2. Dart SDK
```bash
# Flutter 已包含 Dart SDK
# 确保 Dart 3.0+
dart --version
```

#### 3. 开发IDE
推荐使用以下IDE之一：
- **Android Studio** (推荐)
- **Visual Studio Code** + Flutter插件
- **IntelliJ IDEA** + Flutter插件

#### 4. Git版本控制
```bash
# 安装 Git
# Windows: https://git-scm.com/download/win
# macOS: brew install git
# Ubuntu: sudo apt install git

# 验证安装
git --version
```

#### 5. Melos (多包管理)
```bash
# 安装 Melos
dart pub global activate melos

# 验证安装
melos --version
```

## 📥 项目获取

### 1. 克隆项目
```bash
# 克隆主仓库
git clone http://*************:89/app_team/financial_app_workspace.git
cd financial_app_workspace
```

### 2. 配置Git
```bash
# 设置用户信息
git config user.name "您的姓名"
git config user.email "您的邮箱"

# 设置默认分支
git config init.defaultBranch main
```

## 🔧 环境配置

### 1. 自动化环境设置
```bash
# 运行自动化环境设置脚本
dart scripts/dev_environment_setup.dart

# 这个脚本会自动：
# - 检查所有依赖
# - 安装缺失的工具
# - 配置开发环境
# - 验证环境完整性
```

### 2. 手动安装依赖
如果自动化脚本失败，可以手动执行：

```bash
# 1. 安装项目依赖
melos bootstrap

# 2. 生成必要文件
flutter packages pub run build_runner build

# 3. 验证环境
flutter doctor
dart scripts/dev_toolbox.dart --health-check
```

### 3. IDE配置

#### Android Studio配置
1. 安装Flutter和Dart插件
2. 配置Flutter SDK路径
3. 导入项目：File → Open → 选择项目根目录
4. 等待索引完成

#### VS Code配置
1. 安装Flutter扩展
2. 打开项目文件夹
3. 按Ctrl+Shift+P，运行"Flutter: Select Device"

## 🏃‍♂️ 运行项目

### 1. 选择运行设备
```bash
# 查看可用设备
flutter devices

# 启动模拟器（如果需要）
flutter emulators --launch <emulator_id>
```

### 2. 运行主应用
```bash
# 进入主应用目录
cd packages/financial_app_main

# 运行调试版本
flutter run

# 或指定设备运行
flutter run -d <device_id>
```

### 3. 热重载
- 在应用运行时，修改代码后按 `r` 进行热重载
- 按 `R` 进行热重启
- 按 `q` 退出运行

## 🧪 验证安装

### 1. 运行测试
```bash
# 运行所有测试
melos run test

# 运行特定模块测试
cd packages/financial_app_core
flutter test
```

### 2. 代码质量检查
```bash
# 运行代码质量检查
dart scripts/fix_code_quality.dart

# 检查代码格式
dart format --set-exit-if-changed .

# 静态分析
flutter analyze
```

### 3. 构建验证
```bash
# 构建Android APK
flutter build apk --debug

# 构建iOS (仅macOS)
flutter build ios --debug --no-codesign
```

## 📁 项目结构了解

### 🏗️ 整体架构
```
financial_app_workspace/
├── packages/                    # 所有模块包
│   ├── financial_app_main/      # 主应用模块
│   ├── financial_app_core/      # 核心共享模块
│   ├── financial_app_auth/      # 认证模块
│   ├── financial_app_market/    # 市场数据模块
│   ├── financial_app_trade/     # 交易模块
│   ├── financial_app_portfolio/ # 投资组合模块
│   ├── financial_app_notification/ # 通知模块
│   ├── financial_ws_client/     # WebSocket客户端
│   ├── financial_trading_chart/ # 交易图表模块
│   └── financial_app_assets/    # 资源模块
├── scripts/                     # 开发脚本
├── docs/                        # 文档
├── config/                      # 配置文件
└── melos.yaml                   # Melos配置
```

### 📦 模块结构
每个业务模块都遵循标准三层架构：
```
packages/[module_name]/
├── lib/
│   ├── [module_name].dart           # 主导出文件
│   ├── [module_name]_injection.dart # 依赖注入
│   └── src/
│       ├── data/                    # 数据层
│       ├── domain/                  # 业务逻辑层
│       ├── presentation/            # 表现层
│       └── shared/                  # 共享组件
├── test/                            # 测试文件
└── README.md                        # 模块文档
```

## 🔧 开发工具使用

### 1. 开发者工具箱
```bash
# 启动交互式工具箱
dart scripts/dev_toolbox.dart

# 工具箱包含：
# - 代码生成器
# - 质量检查工具
# - 性能分析工具
# - 测试工具
# - 构建工具
```

### 2. 代码生成
```bash
# 生成新模块
dart scripts/code_generator.dart module user_profile

# 生成Bloc
dart scripts/code_generator.dart bloc user

# 生成页面
dart scripts/code_generator.dart page settings

# 生成组件
dart scripts/code_generator.dart widget user_card
```

### 3. 质量检查
```bash
# 全面代码质量检查
dart scripts/fix_code_quality.dart

# 测试覆盖率分析
dart scripts/test_coverage_analyzer.dart

# 性能分析
dart scripts/performance_analyzer.dart
```

## 🐛 调试技巧

### 1. Flutter Inspector
- 在IDE中打开Flutter Inspector
- 查看Widget树结构
- 分析布局和性能问题

### 2. 日志调试
```dart
// 使用项目的日志系统
import 'package:financial_app_core/financial_app_core.dart';

// 在代码中添加日志
AppLogger.logModule('ModuleName').info('调试信息');
AppLogger.logModule('ModuleName').error('错误信息');
```

### 3. 断点调试
- 在IDE中设置断点
- 使用调试模式运行应用
- 逐步执行代码分析问题

## 📝 开发规范

### 1. 代码风格
- 遵循Dart官方代码风格
- 使用`dart format`格式化代码
- 遵循项目命名规范

### 2. 提交规范
```bash
# 提交信息格式
git commit -m "type(scope): description"

# 类型说明：
# feat: 新功能
# fix: 修复bug
# docs: 文档更新
# style: 代码格式调整
# refactor: 重构
# test: 测试相关
# chore: 构建过程或辅助工具的变动
```

### 3. 分支管理
```bash
# 创建功能分支
git checkout -b feature/new-feature

# 创建修复分支
git checkout -b fix/bug-description

# 创建发布分支
git checkout -b release/v1.0.0
```

## 🚀 开发流程

### 1. 开始新功能开发
```bash
# 1. 更新主分支
git checkout main
git pull origin main

# 2. 创建功能分支
git checkout -b feature/your-feature

# 3. 开始开发
# 编写代码...

# 4. 运行测试
melos run test

# 5. 代码质量检查
dart scripts/fix_code_quality.dart

# 6. 提交代码
git add .
git commit -m "feat: add new feature"
git push origin feature/your-feature
```

### 2. 代码审查流程
1. 创建Merge Request
2. 等待自动化检查通过
3. 请求代码审查
4. 根据反馈修改代码
5. 审查通过后合并

## 📚 学习资源

### 📖 项目文档
- [系统架构设计](../system-design/architecture-overview.md)
- [API参考文档](../api-reference/rest-api.md)
- [开发规范](coding-standards.md)
- [测试指南](testing-guide.md)

### 🌐 外部资源
- [Flutter官方文档](https://flutter.dev/docs)
- [Dart语言指南](https://dart.dev/guides)
- [Bloc状态管理](https://bloclibrary.dev/)
- [Provider状态管理](https://pub.dev/packages/provider)

## 🆘 获取帮助

### 💬 团队沟通
- **技术讨论群**: 加入内部技术讨论群
- **代码审查**: 通过GitLab进行代码审查
- **技术分享**: 参加定期的技术分享会议

### 📞 技术支持
- **导师制度**: 新人会分配技术导师
- **文档支持**: 查阅完整的技术文档
- **问题反馈**: 通过Issue系统反馈问题

## ✅ 检查清单

在开始开发前，请确认以下项目：

### 环境检查
- [ ] Flutter SDK 3.0+ 已安装
- [ ] Dart SDK 3.0+ 已安装
- [ ] IDE已配置Flutter插件
- [ ] Git已配置用户信息
- [ ] Melos已安装并可用

### 项目检查
- [ ] 项目已成功克隆
- [ ] 依赖已安装 (`melos bootstrap`)
- [ ] 应用可以成功运行
- [ ] 测试可以正常执行
- [ ] 代码质量检查通过

### 工具检查
- [ ] 开发者工具箱可以启动
- [ ] 代码生成器可以使用
- [ ] 日志系统正常工作
- [ ] 调试功能正常

---

**🎉 恭喜！您已经完成了开发环境的搭建，可以开始愉快的开发工作了！**

如果在环境搭建过程中遇到任何问题，请随时联系团队成员或查阅相关文档。
