# 🧪 测试指南

本指南介绍了金融应用项目的测试策略、测试类型和最佳实践，帮助开发者编写高质量的测试代码。

## 🎯 测试策略

### 📊 测试金字塔
```
        /\
       /  \
      / UI \     <- 少量UI测试 (10%)
     /______\
    /        \
   / Widget   \   <- 适量Widget测试 (20%)
  /____________\
 /              \
/ Unit Tests     \ <- 大量单元测试 (70%)
/________________\
```

### 🎯 测试目标
- **代码覆盖率**: 目标85%以上
- **关键路径**: 100%覆盖业务关键路径
- **边界条件**: 充分测试边界和异常情况
- **回归测试**: 防止新代码破坏现有功能

## 🔬 单元测试

### 📝 测试结构
```dart
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';

// 生成Mock类
@GenerateMocks([UserRepository, ApiService])
import 'user_service_test.mocks.dart';

void main() {
  group('UserService', () {
    late UserService userService;
    late MockUserRepository mockRepository;
    
    setUp(() {
      mockRepository = MockUserRepository();
      userService = UserService(mockRepository);
    });
    
    group('getUserById', () {
      test('应该返回用户信息当用户存在时', () async {
        // Arrange
        const userId = '123';
        const expectedUser = User(id: userId, name: 'Test User');
        when(mockRepository.getUserById(userId))
            .thenAnswer((_) async => expectedUser);
        
        // Act
        final result = await userService.getUserById(userId);
        
        // Assert
        expect(result, equals(expectedUser));
        verify(mockRepository.getUserById(userId)).called(1);
      });
      
      test('应该抛出异常当用户不存在时', () async {
        // Arrange
        const userId = '999';
        when(mockRepository.getUserById(userId))
            .thenThrow(UserNotFoundException('User not found'));
        
        // Act & Assert
        expect(
          () => userService.getUserById(userId),
          throwsA(isA<UserNotFoundException>()),
        );
      });
    });
  });
}
```

### 🎭 Mock和Stub

#### 使用Mockito创建Mock
```dart
// 1. 添加注解
@GenerateMocks([ApiService, LocalStorage])
import 'test_file.mocks.dart';

// 2. 生成Mock文件
// flutter packages pub run build_runner build

// 3. 使用Mock
void main() {
  late MockApiService mockApiService;
  
  setUp(() {
    mockApiService = MockApiService();
  });
  
  test('测试API调用', () async {
    // 设置Mock行为
    when(mockApiService.get('/users/123'))
        .thenAnswer((_) async => {'id': '123', 'name': 'Test'});
    
    // 执行测试
    final result = await mockApiService.get('/users/123');
    
    // 验证结果
    expect(result['name'], equals('Test'));
    verify(mockApiService.get('/users/123')).called(1);
  });
}
```

#### 测试异步操作
```dart
test('测试异步操作', () async {
  // 使用completer控制异步流程
  final completer = Completer<String>();
  
  // 模拟异步操作
  when(mockService.fetchData())
      .thenAnswer((_) => completer.future);
  
  // 开始异步操作
  final future = service.processData();
  
  // 完成异步操作
  completer.complete('test data');
  
  // 验证结果
  final result = await future;
  expect(result, equals('processed: test data'));
});
```

### 🔄 Bloc测试

#### 测试Bloc状态变化
```dart
void main() {
  group('AuthBloc', () {
    late AuthBloc authBloc;
    late MockAuthRepository mockRepository;
    
    setUp(() {
      mockRepository = MockAuthRepository();
      authBloc = AuthBloc(mockRepository);
    });
    
    tearDown(() {
      authBloc.close();
    });
    
    test('初始状态应该是AuthInitial', () {
      expect(authBloc.state, equals(AuthInitial()));
    });
    
    blocTest<AuthBloc, AuthState>(
      '登录成功时应该发出正确的状态序列',
      build: () {
        when(mockRepository.login('user', 'pass'))
            .thenAnswer((_) async => const User(id: '1', name: 'Test'));
        return authBloc;
      },
      act: (bloc) => bloc.add(const LoginEvent('user', 'pass')),
      expect: () => [
        AuthLoading(),
        const AuthSuccess(User(id: '1', name: 'Test')),
      ],
      verify: (_) {
        verify(mockRepository.login('user', 'pass')).called(1);
      },
    );
    
    blocTest<AuthBloc, AuthState>(
      '登录失败时应该发出错误状态',
      build: () {
        when(mockRepository.login('user', 'wrong'))
            .thenThrow(AuthException('Invalid credentials'));
        return authBloc;
      },
      act: (bloc) => bloc.add(const LoginEvent('user', 'wrong')),
      expect: () => [
        AuthLoading(),
        const AuthError('Invalid credentials'),
      ],
    );
  });
}
```

## 🎨 Widget测试

### 📱 基础Widget测试
```dart
void main() {
  group('UserProfileWidget', () {
    testWidgets('应该显示用户信息', (WidgetTester tester) async {
      // Arrange
      const user = User(id: '1', name: 'Test User', email: '<EMAIL>');
      
      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: UserProfileWidget(user: user),
          ),
        ),
      );
      
      // Assert
      expect(find.text('Test User'), findsOneWidget);
      expect(find.text('<EMAIL>'), findsOneWidget);
      expect(find.byType(CircleAvatar), findsOneWidget);
    });
    
    testWidgets('点击编辑按钮应该触发回调', (WidgetTester tester) async {
      // Arrange
      bool callbackTriggered = false;
      const user = User(id: '1', name: 'Test User');
      
      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: UserProfileWidget(
              user: user,
              onEdit: () => callbackTriggered = true,
            ),
          ),
        ),
      );
      
      await tester.tap(find.byIcon(Icons.edit));
      await tester.pump();
      
      // Assert
      expect(callbackTriggered, isTrue);
    });
  });
}
```

### 🔄 状态管理Widget测试
```dart
void main() {
  group('LoginPage', () {
    late MockAuthBloc mockAuthBloc;
    
    setUp(() {
      mockAuthBloc = MockAuthBloc();
    });
    
    testWidgets('应该显示登录表单', (WidgetTester tester) async {
      // Arrange
      when(() => mockAuthBloc.state).thenReturn(AuthInitial());
      
      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: BlocProvider<AuthBloc>.value(
            value: mockAuthBloc,
            child: const LoginPage(),
          ),
        ),
      );
      
      // Assert
      expect(find.byType(TextFormField), findsNWidgets(2));
      expect(find.text('用户名'), findsOneWidget);
      expect(find.text('密码'), findsOneWidget);
      expect(find.byType(ElevatedButton), findsOneWidget);
    });
    
    testWidgets('提交表单应该触发登录事件', (WidgetTester tester) async {
      // Arrange
      when(() => mockAuthBloc.state).thenReturn(AuthInitial());
      
      await tester.pumpWidget(
        MaterialApp(
          home: BlocProvider<AuthBloc>.value(
            value: mockAuthBloc,
            child: const LoginPage(),
          ),
        ),
      );
      
      // Act
      await tester.enterText(find.byKey(const Key('username')), 'testuser');
      await tester.enterText(find.byKey(const Key('password')), 'testpass');
      await tester.tap(find.byType(ElevatedButton));
      await tester.pump();
      
      // Assert
      verify(() => mockAuthBloc.add(
        const LoginEvent('testuser', 'testpass')
      )).called(1);
    });
  });
}
```

### 📊 复杂交互测试
```dart
testWidgets('滑动刷新应该重新加载数据', (WidgetTester tester) async {
  // Arrange
  when(() => mockBloc.state).thenReturn(DataLoaded(mockData));
  
  await tester.pumpWidget(
    MaterialApp(
      home: BlocProvider<DataBloc>.value(
        value: mockBloc,
        child: const DataListPage(),
      ),
    ),
  );
  
  // Act - 执行下拉刷新
  await tester.fling(
    find.byType(ListView),
    const Offset(0, 300),
    1000,
  );
  await tester.pump();
  await tester.pump(const Duration(seconds: 1));
  
  // Assert
  verify(() => mockBloc.add(RefreshDataEvent())).called(1);
});
```

## 🔗 集成测试

### 📱 端到端测试
```dart
// integration_test/app_test.dart
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:financial_app_main/main.dart' as app;

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();
  
  group('完整应用测试', () {
    testWidgets('用户登录流程测试', (WidgetTester tester) async {
      // 启动应用
      app.main();
      await tester.pumpAndSettle();
      
      // 验证启动页面
      expect(find.text('金融交易应用'), findsOneWidget);
      
      // 导航到登录页面
      await tester.tap(find.text('登录'));
      await tester.pumpAndSettle();
      
      // 输入登录信息
      await tester.enterText(find.byKey(const Key('username')), 'testuser');
      await tester.enterText(find.byKey(const Key('password')), 'testpass');
      
      // 提交登录
      await tester.tap(find.text('登录'));
      await tester.pumpAndSettle();
      
      // 验证登录成功
      expect(find.text('欢迎回来'), findsOneWidget);
    });
    
    testWidgets('交易流程测试', (WidgetTester tester) async {
      // 前置条件：用户已登录
      await loginUser(tester);
      
      // 导航到交易页面
      await tester.tap(find.byIcon(Icons.trending_up));
      await tester.pumpAndSettle();
      
      // 选择股票
      await tester.tap(find.text('AAPL'));
      await tester.pumpAndSettle();
      
      // 下单
      await tester.tap(find.text('买入'));
      await tester.enterText(find.byKey(const Key('quantity')), '100');
      await tester.tap(find.text('确认下单'));
      await tester.pumpAndSettle();
      
      // 验证订单提交成功
      expect(find.text('订单提交成功'), findsOneWidget);
    });
  });
}

Future<void> loginUser(WidgetTester tester) async {
  // 登录辅助方法
  await tester.enterText(find.byKey(const Key('username')), 'testuser');
  await tester.enterText(find.byKey(const Key('password')), 'testpass');
  await tester.tap(find.text('登录'));
  await tester.pumpAndSettle();
}
```

## 📊 测试覆盖率

### 📈 生成覆盖率报告
```bash
# 运行测试并生成覆盖率
flutter test --coverage

# 生成HTML报告
genhtml coverage/lcov.info -o coverage/html

# 查看报告
open coverage/html/index.html
```

### 🎯 覆盖率目标
- **整体覆盖率**: ≥85%
- **业务逻辑层**: ≥90%
- **数据层**: ≥80%
- **表现层**: ≥70%

### 📊 使用工具分析
```bash
# 使用项目内置工具
dart scripts/test_coverage_analyzer.dart

# 查看详细报告
dart scripts/test_coverage_analyzer.dart --detailed

# 生成覆盖率趋势
dart scripts/test_coverage_analyzer.dart --trend
```

## 🔧 测试工具和配置

### 📦 依赖配置
```yaml
# pubspec.yaml
dev_dependencies:
  flutter_test:
    sdk: flutter
  mockito: ^5.4.0
  build_runner: ^2.3.0
  bloc_test: ^9.1.0
  integration_test:
    sdk: flutter
```

### ⚙️ 测试配置
```dart
// test/test_config.dart
class TestConfig {
  static void setupTests() {
    // 设置测试环境
    TestWidgetsFlutterBinding.ensureInitialized();
    
    // 配置HTTP客户端
    HttpOverrides.global = MockHttpOverrides();
    
    // 设置时区
    setUpAll(() {
      // 全局测试设置
    });
    
    tearDownAll(() {
      // 全局清理
    });
  }
}
```

## 🚀 测试最佳实践

### ✅ 好的测试实践
```dart
// ✅ 测试名称清晰描述行为
test('当用户名为空时应该返回验证错误', () {});

// ✅ 使用AAA模式 (Arrange, Act, Assert)
test('测试示例', () {
  // Arrange - 准备测试数据
  final service = UserService();
  const input = '<EMAIL>';
  
  // Act - 执行被测试的操作
  final result = service.validateEmail(input);
  
  // Assert - 验证结果
  expect(result, isTrue);
});

// ✅ 测试边界条件
test('当输入为null时应该抛出异常', () {
  expect(() => service.process(null), throwsArgumentError);
});
```

### ❌ 避免的测试反模式
```dart
// ❌ 测试名称不清晰
test('测试用户服务', () {});

// ❌ 测试过于复杂
test('复杂测试', () {
  // 100行测试代码...
});

// ❌ 测试依赖外部状态
test('依赖全局状态的测试', () {
  GlobalState.value = 'test';
  // 这样的测试不稳定
});
```

## 📋 测试检查清单

### ✅ 提交前检查
- [ ] 所有测试通过
- [ ] 新代码有对应测试
- [ ] 测试覆盖率达标
- [ ] 测试名称清晰
- [ ] 没有跳过的测试
- [ ] 集成测试通过

### 🔍 代码审查检查
- [ ] 测试逻辑正确
- [ ] Mock使用合理
- [ ] 边界条件覆盖
- [ ] 异常情况测试
- [ ] 测试可维护性

---

**🧪 好的测试是代码质量的保障，也是重构的安全网！**
