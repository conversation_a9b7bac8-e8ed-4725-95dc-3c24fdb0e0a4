> **📋 文档迁移说明**
> 
> 本文档已从项目根目录迁移到统一的文档管理系统中。
> - **原文件名**: TOOLS_AND_SCRIPTS_GUIDE.md
> - **迁移时间**: 2025-07-07T20:00:20.435468
> - **迁移工具**: scripts/migrate_docs.dart
> 
> 如需查看最新的文档结构，请参考 [文档中心](../README.md)。

---

# 工具和脚本详细操作说明

## 📋 目录

1. [性能分析工具](#性能分析工具)
2. [测试覆盖率工具](#测试覆盖率工具)
3. [构建优化工具](#构建优化工具)
4. [部署工具](#部署工具)
5. [Melos 脚本命令](#melos-脚本命令)
6. [Makefile 命令](#makefile-命令)
7. [故障排除](#故障排除)

---

## 🔍 性能分析工具

### 1. 性能分析器 (`scripts/performance_analyzer.dart`)

**功能**：全面分析项目性能，包括构建大小、依赖管理、代码复杂度等。

#### 使用方法：
```bash
# 基本使用
dart scripts/performance_analyzer.dart

# 查看帮助
dart scripts/performance_analyzer.dart --help
```

#### 输出文件：
- `performance_report.json` - 详细性能报告
- `.analysis_cache/` - 分析缓存目录

#### 报告内容：
- 📦 构建产物大小分析
- 📚 依赖包数量和重复度
- 🧮 代码复杂度评估
- 🌐 网络配置检查
- 📊 性能评分 (0-100)

#### 示例输出：
```json
{
  "performance_score": 100,
  "build_sizes": {
    "Android": {"total_size": 73654272, "formatted_size": "70.2MB"}
  },
  "dependencies": {
    "total_unique_dependencies": 45
  }
}
```

---

## 🧪 测试覆盖率工具

### 1. 测试覆盖率分析器 (`scripts/test_coverage_analyzer.dart`)

**功能**：分析现有测试文件，计算覆盖率，生成测试建议。

#### 使用方法：
```bash
# 分析测试覆盖率
dart scripts/test_coverage_analyzer.dart

# 查看详细输出
dart scripts/test_coverage_analyzer.dart --verbose
```

#### 输出文件：
- `test_coverage_report.json` - 测试覆盖率报告
- `.test_analysis_cache/` - 测试分析缓存

#### 分析内容：
- 📁 现有测试文件统计
- 📊 源代码文件分析
- 📈 测试覆盖率计算
- 💡 测试改进建议

### 2. 测试模板生成器 (`scripts/test_template_generator.dart`)

**功能**：为源文件自动生成测试模板。

#### 使用方法：
```bash
# 为单个文件生成测试模板
dart scripts/test_template_generator.dart packages/financial_app_core/lib/src/services/user_service.dart

# 为多个文件生成（使用脚本）
find packages -name "*.dart" -path "*/lib/*" | while read file; do
  dart scripts/test_template_generator.dart "$file"
done
```

#### 生成内容：
- 🧪 基础测试结构
- 🔧 类和方法测试模板
- 📝 测试用例占位符
- 🔗 Mock 对象配置

#### 示例生成的测试文件：
```dart
import 'package:flutter_test/flutter_test.dart';
import 'package:financial_app_core/src/services/user_service.dart';

void main() {
  group('UserService Tests', () {
    late UserService userService;
    
    setUp(() {
      userService = UserService();
    });
    
    group('getUser', () {
      test('should return correct data', () async {
        // Arrange
        // TODO: 设置测试数据
        
        // Act
        // final result = await userService.getUser();
        
        // Assert
        // expect(result, expectedValue);
      });
    });
  });
}
```

---

## 🔨 构建优化工具

### 1. 构建优化器 (`scripts/build_optimization.dart`)

**功能**：执行优化的构建流程，包括预检查、依赖优化、代码生成等。

#### 使用方法：
```bash
# 构建所有平台
dart scripts/build_optimization.dart all

# 构建特定平台
dart scripts/build_optimization.dart android
dart scripts/build_optimization.dart ios
dart scripts/build_optimization.dart web

# 查看帮助
dart scripts/build_optimization.dart --help
```

#### 构建流程：
1. 🔍 预构建检查
2. 📦 依赖优化
3. ⚙️ 代码生成
4. 🔨 应用构建
5. 🔧 构建后处理

#### 输出文件：
- `build_report.json` - 构建报告
- `deploy_<platform>.sh` - 部署脚本

---

## 🚀 部署工具

### 1. 内部部署脚本 (`scripts/deploy_internal.sh`)

**功能**：适用于内部 GitLab 环境的自动化部署工具。

#### 使用方法：
```bash
# 赋予执行权限
chmod +x scripts/deploy_internal.sh

# 构建 Docker 镜像
./scripts/deploy_internal.sh build

# 部署到测试环境
./scripts/deploy_internal.sh deploy staging

# 部署到生产环境
./scripts/deploy_internal.sh deploy production

# 使用 Kubernetes 部署
./scripts/deploy_internal.sh deploy staging --k8s
./scripts/deploy_internal.sh deploy production --k8s

# 回滚部署
./scripts/deploy_internal.sh rollback staging
./scripts/deploy_internal.sh rollback production

# 健康检查
./scripts/deploy_internal.sh health https://financial-app-staging.company.com

# 清理资源
./scripts/deploy_internal.sh clean
```

#### 环境变量配置：
```bash
# Docker 仓库配置
export DOCKER_REGISTRY="registry.company.com"
export DOCKER_REGISTRY_USER="your-username"
export DOCKER_REGISTRY_PASSWORD="your-password"

# 服务器配置
export STAGING_SERVER="staging.company.com"
export PRODUCTION_SERVER="production.company.com"

# Kubernetes 配置
export NAMESPACE="financial-app"
```

#### 支持的部署方式：
- 🐳 **Docker Compose**：传统容器部署
- ☸️ **Kubernetes**：云原生部署
- 🔄 **滚动更新**：零停机部署
- 📊 **健康检查**：自动状态监控

---

## 📜 Melos 脚本命令

### 基础命令
```bash
# 项目初始化
melos bootstrap          # 初始化所有包依赖
melos clean             # 清理所有构建文件
melos pub_get           # 获取所有依赖

# 代码质量
melos format            # 格式化所有代码
melos format_check      # 检查代码格式
melos analyze           # 静态代码分析
melos outdated          # 检查过时依赖
```

### 代码生成
```bash
melos build_runner      # 运行代码生成
melos build_runner_watch # 监听模式代码生成
```

### 测试命令
```bash
melos test              # 运行所有测试
melos test_coverage     # 运行测试并生成覆盖率
melos test_unit         # 运行单元测试
melos test_widget       # 运行 Widget 测试
melos test_integration  # 运行集成测试
melos test_coverage_analyze # 分析测试覆盖率
melos test_report       # 生成完整测试报告
```

### 构建命令
```bash
# Android 构建
melos build_android_debug    # Android Debug 构建
melos build_android_release  # Android Release 构建

# iOS 构建
melos build_ios_debug       # iOS Debug 构建
melos build_ios_release     # iOS Release 构建

# Web 构建
melos build_web             # Web 应用构建

# 全平台构建
melos build_all             # 构建所有平台
```

### Docker 命令
```bash
melos docker_build      # 构建 Docker 镜像
melos docker_run        # 启动 Docker 容器
melos docker_stop       # 停止 Docker 容器
```

### 部署命令
```bash
melos deploy_staging     # 部署到测试环境
melos deploy_production  # 部署到生产环境
```

### 完整流程
```bash
melos ci                # 完整 CI 检查流程
melos release           # 完整发布流程
```

---

## 🛠️ Makefile 命令

### 环境管理
```bash
make setup              # 初始化开发环境
make clean              # 清理构建文件
make clean-deep         # 深度清理（包括依赖）
make dev-setup          # 完整开发环境设置
make dev-reset          # 重置开发环境
```

### 依赖管理
```bash
make deps-get           # 获取所有依赖
make deps-upgrade       # 升级所有依赖
make deps-outdated      # 检查过时依赖
```

### 代码质量
```bash
make format             # 格式化代码
make format-check       # 检查代码格式
make analyze            # 静态代码分析
make lint               # 运行所有代码检查
make quick-check        # 快速代码检查
```

### 代码生成
```bash
make generate           # 运行代码生成
make generate-watch     # 监听模式代码生成
```

### 测试
```bash
make test               # 运行所有测试
make test-coverage      # 运行测试覆盖率
```

### 构建
```bash
make build-android-debug    # Android Debug 构建
make build-android-release  # Android Release 构建
make build-ios-debug        # iOS Debug 构建
make build-ios-release      # iOS Release 构建
make build-web              # Web 应用构建
make build-all              # 构建所有平台
make build-optimized        # 运行优化构建
make quick-build            # 快速构建
```

### Docker
```bash
make docker-build       # 构建 Docker 镜像
make docker-run         # 启动 Docker 容器
make docker-stop        # 停止 Docker 容器
make docker-logs        # 查看 Docker 日志
make docker-clean       # 清理 Docker 资源
```

### 部署
```bash
make deploy-staging         # 部署到测试环境
make deploy-production      # 部署到生产环境
make deploy-k8s-staging     # K8s 测试环境部署
make deploy-k8s-production  # K8s 生产环境部署
make rollback-staging       # 回滚测试环境
make rollback-production    # 回滚生产环境
```

### 监控
```bash
make monitor-start      # 启动监控服务
make monitor-stop       # 停止监控服务
```

### 性能分析
```bash
make performance-analyze    # 运行性能分析
make performance-report     # 查看性能报告
make profile-web           # Web 性能分析构建
make profile-android       # Android 性能分析构建
make memory-test           # 内存使用测试
make benchmark             # 运行性能基准测试
```

### 状态检查
```bash
make status             # 显示项目状态
```

---

## 🔧 故障排除

### 常见问题和解决方案

#### 1. 权限问题
```bash
# Linux/macOS 赋予脚本执行权限
chmod +x scripts/*.sh
chmod +x scripts/*.dart

# Windows 使用 PowerShell 执行
powershell -ExecutionPolicy Bypass -File scripts/script.ps1
```

#### 2. 依赖问题
```bash
# 清理并重新安装依赖
melos clean
melos bootstrap

# 检查 Flutter 环境
flutter doctor -v

# 检查 Dart 环境
dart --version
```

#### 3. 构建问题
```bash
# 清理构建缓存
flutter clean
dart pub cache clean

# 重新生成代码
melos build_runner

# 检查构建配置
flutter config
```

#### 4. 测试问题
```bash
# 清理测试缓存
rm -rf .test_analysis_cache/

# 重新运行测试
melos test --no-cache

# 检查测试依赖
flutter packages test
```

#### 5. Docker 问题
```bash
# 检查 Docker 状态
docker info

# 清理 Docker 资源
docker system prune -f

# 重新构建镜像
docker build --no-cache -t financial-app:latest .
```

#### 6. 部署问题
```bash
# 检查网络连接
ping staging.company.com

# 检查 SSH 连接
ssh -T **********************

# 检查环境变量
echo $DOCKER_REGISTRY
echo $STAGING_SERVER
```

### 日志和调试

#### 启用详细日志
```bash
# Melos 详细输出
melos --verbose <command>

# Flutter 详细输出
flutter --verbose <command>

# Docker 详细输出
docker --debug <command>
```

#### 查看日志文件
```bash
# 应用日志
tail -f logs/app.log

# Docker 日志
docker logs financial-app-web

# 系统日志
journalctl -u financial-app
```

### 性能优化建议

#### 1. 构建性能
- 使用 `--cache` 选项
- 启用并行构建
- 使用增量构建

#### 2. 测试性能
- 使用 `--parallel` 运行测试
- 缓存测试依赖
- 分组运行测试

#### 3. 部署性能
- 使用镜像缓存
- 启用压缩传输
- 并行部署多个服务

---

## 📞 技术支持

如果遇到问题，请按以下顺序排查：

1. 📖 查看本文档的故障排除部分
2. 🔍 检查相关日志文件
3. 🧪 在测试环境中复现问题
4. 📝 记录详细的错误信息和复现步骤
5. 🔧 尝试清理缓存和重新构建

### 有用的调试命令
```bash
# 系统信息
flutter doctor -v
dart --version
docker --version
git --version

# 项目状态
melos list
flutter packages get
docker ps

# 网络检查
ping google.com
nslookup registry.company.com
curl -I https://api.company.com/health
```

---

**📝 注意**：请根据实际的公司环境和配置调整相关的 URL、服务器地址和认证信息。
