> **📋 文档迁移说明**
> 
> 本文档已从项目根目录迁移到统一的文档管理系统中。
> - **原文件名**: ARCHITECTURE_STANDARDS.md
> - **迁移时间**: 2025-07-07T20:00:20.392458
> - **迁移工具**: scripts/migrate_docs.dart
> 
> 如需查看最新的文档结构，请参考 [文档中心](../README.md)。

---

# 🏗️ 代码架构标准化文档

## 📊 **架构标准化成果总结**

### 🎉 **整体项目提升**
- ✅ **平均评分**: 71分 → 82分 (+11分)
- ✅ **优秀模块**: 5个 → 6个 (+1个)
- ✅ **良好模块**: 1个 → 2个 (+1个)
- ✅ **需改进模块**: 4个 → 2个 (-2个)

### 🏆 **重大改进模块**
1. **financial_app_main**: 30分 → 70分 (+40分)
2. **financial_ws_client**: 15分 → 85分 (+70分)

---

## 🏛️ **标准架构模式**

### 📋 **三层架构标准**

所有业务模块都应遵循以下标准三层架构：

```
packages/[module_name]/
├── lib/
│   ├── [module_name].dart              # 主导出文件
│   ├── [module_name]_injection.dart    # 依赖注入配置
│   └── src/
│       ├── data/                       # 数据层
│       │   ├── datasources/            # 数据源
│       │   ├── models/                 # 数据模型
│       │   └── repositories/           # 仓储实现
│       ├── domain/                     # 业务逻辑层
│       │   ├── entities/               # 业务实体
│       │   ├── repositories/           # 仓储接口
│       │   └── usecases/               # 用例
│       ├── presentation/               # 表现层
│       │   ├── bloc/                   # 状态管理
│       │   ├── pages/                  # 页面
│       │   ├── widgets/                # 组件
│       │   └── providers/              # 提供者
│       └── shared/                     # 模块内共享
│           ├── constants/              # 常量
│           ├── enums/                  # 枚举
│           ├── exceptions/             # 异常
│           ├── extensions/             # 扩展
│           └── utils/                  # 工具
├── test/                               # 测试文件
│   ├── unit/                           # 单元测试
│   ├── widget/                         # Widget 测试
│   └── integration/                    # 集成测试
└── README.md                           # 模块文档
```

### 🎯 **模块分类标准**

#### 1. **主应用模块** (`financial_app_main`)
- **职责**: 应用架构层，模块集成和协调
- **不包含**: 具体业务逻辑
- **包含**: 路由管理、依赖注入协调、配置管理

#### 2. **业务模块** (`financial_app_*`)
- **职责**: 独立的业务功能
- **架构**: 标准三层架构
- **依赖注入**: 各自维护独立的依赖注入

#### 3. **工具模块** (`financial_ws_client`, `financial_trading_chart`)
- **职责**: 专门的技术功能
- **架构**: 根据功能特点调整的三层架构
- **复用性**: 高度可复用

#### 4. **核心模块** (`financial_app_core`)
- **职责**: 共享服务和组件
- **架构**: 服务导向架构
- **依赖**: 被其他模块依赖

---

## 📝 **代码组织规范**

### 🔤 **命名规范**

#### 文件命名
- **实体**: `user.dart`, `order.dart`
- **模型**: `user_model.dart`, `order_model.dart`
- **仓储**: `user_repository.dart`, `user_repository_impl.dart`
- **用例**: `get_user.dart`, `create_order.dart`
- **页面**: `user_profile_page.dart`, `order_list_page.dart`
- **组件**: `user_avatar_widget.dart`, `order_card_widget.dart`

#### 类命名
- **实体**: `User`, `Order`
- **模型**: `UserModel`, `OrderModel`
- **仓储**: `UserRepository`, `UserRepositoryImpl`
- **用例**: `GetUser`, `CreateOrder`
- **页面**: `UserProfilePage`, `OrderListPage`
- **组件**: `UserAvatarWidget`, `OrderCardWidget`

### 📁 **目录组织规范**

#### 按功能分组
```dart
// ✅ 推荐
lib/src/domain/usecases/
├── user/
│   ├── get_user.dart
│   ├── update_user.dart
│   └── delete_user.dart
└── order/
    ├── create_order.dart
    ├── cancel_order.dart
    └── get_order_history.dart
```

#### 按类型分组
```dart
// ❌ 不推荐（文件过多时难以管理）
lib/src/domain/usecases/
├── get_user.dart
├── update_user.dart
├── delete_user.dart
├── create_order.dart
├── cancel_order.dart
└── get_order_history.dart
```

### 🔗 **依赖注入规范**

#### 模块依赖注入文件结构
```dart
// [module_name]_injection.dart
import 'package:get_it/get_it.dart';

/// 初始化 [ModuleName] 模块的依赖注入
Future<void> init[ModuleName]Dependencies() async {
  final getIt = GetIt.instance;
  
  // 1. 注册数据源
  getIt.registerLazySingleton<[ModuleName]DataSource>(
    () => [ModuleName]DataSourceImpl(),
  );
  
  // 2. 注册仓储
  getIt.registerLazySingleton<[ModuleName]Repository>(
    () => [ModuleName]RepositoryImpl(
      dataSource: getIt<[ModuleName]DataSource>(),
    ),
  );
  
  // 3. 注册用例
  getIt.registerLazySingleton<[UseCase]>(
    () => [UseCase](getIt<[ModuleName]Repository>()),
  );
  
  // 4. 注册表现层服务
  getIt.registerFactory<[ModuleName]Bloc>(
    () => [ModuleName]Bloc(
      useCase: getIt<[UseCase]>(),
    ),
  );
}
```

---

## 🛠️ **最佳实践指南**

### 1. **实体设计**
```dart
// ✅ 推荐：使用 Equatable 进行值比较
class User extends Equatable {
  final String id;
  final String name;
  final String email;

  const User({
    required this.id,
    required this.name,
    required this.email,
  });

  @override
  List<Object?> get props => [id, name, email];
}
```

### 2. **用例设计**
```dart
// ✅ 推荐：清晰的用例接口
class GetUser {
  final UserRepository _repository;

  GetUser(this._repository);

  Future<Either<Failure, User>> execute(String userId) async {
    try {
      final user = await _repository.getUser(userId);
      return Right(user);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }
}
```

### 3. **仓储设计**
```dart
// ✅ 推荐：接口与实现分离
abstract class UserRepository {
  Future<User> getUser(String id);
  Future<void> saveUser(User user);
}

class UserRepositoryImpl implements UserRepository {
  final UserDataSource _dataSource;

  UserRepositoryImpl({required UserDataSource dataSource})
      : _dataSource = dataSource;

  @override
  Future<User> getUser(String id) async {
    final userModel = await _dataSource.getUser(id);
    return userModel.toEntity();
  }
}
```

### 4. **状态管理**
```dart
// ✅ 推荐：使用 BLoC 模式
class UserBloc extends Bloc<UserEvent, UserState> {
  final GetUser _getUser;

  UserBloc({required GetUser getUser})
      : _getUser = getUser,
        super(UserInitial()) {
    on<GetUserEvent>(_onGetUser);
  }

  Future<void> _onGetUser(
    GetUserEvent event,
    Emitter<UserState> emit,
  ) async {
    emit(UserLoading());
    
    final result = await _getUser.execute(event.userId);
    
    result.fold(
      (failure) => emit(UserError(failure.message)),
      (user) => emit(UserLoaded(user)),
    );
  }
}
```

---

## 📊 **质量标准**

### 🎯 **模块评分标准**

- **90-100分**: 优秀 (Excellent)
  - 完整的三层架构
  - 标准化目录结构
  - 完善的测试覆盖
  - 清晰的文档

- **70-89分**: 良好 (Good)
  - 基本的三层架构
  - 规范的目录结构
  - 部分测试覆盖

- **50-69分**: 一般 (Fair)
  - 简单的分层结构
  - 基本的目录组织

- **0-49分**: 需改进 (Poor)
  - 缺少标准结构
  - 需要重构

### 📋 **检查清单**

#### 模块结构检查
- [ ] 是否有主导出文件
- [ ] 是否有依赖注入文件
- [ ] 是否有标准的 src 目录
- [ ] 是否有三层架构分层
- [ ] 是否有测试目录
- [ ] 是否有 README 文档

#### 代码质量检查
- [ ] 是否遵循命名规范
- [ ] 是否有适当的注释
- [ ] 是否有错误处理
- [ ] 是否有类型安全
- [ ] 是否有单元测试

---

## 🔧 **工具支持**

### 📊 **模块结构分析器**
```bash
# 分析所有模块结构
dart scripts/module_structure_optimizer.dart analyze

# 优化模块结构
dart scripts/module_structure_optimizer.dart optimize

# 创建标准模块
dart scripts/module_structure_optimizer.dart create new_module
```

### 🧪 **测试工具**
```bash
# 生成测试模板
dart scripts/test_template_generator.dart <source_file>

# 分析测试覆盖率
dart scripts/test_coverage_analyzer.dart
```

### 📈 **性能分析**
```bash
# 性能分析
dart scripts/performance_analyzer.dart

# 构建优化
dart scripts/build_optimization.dart
```

---

## 🎯 **持续改进**

### 📈 **定期检查**
- **每周**: 运行模块结构分析
- **每月**: 检查架构一致性
- **每季度**: 评估架构演进

### 🔄 **架构演进**
- 根据业务需求调整架构
- 保持向后兼容性
- 渐进式重构

### 📚 **知识分享**
- 定期架构评审
- 最佳实践分享
- 新人培训指南

---

**🎉 通过标准化架构，项目的整体质量得到了显著提升，为后续的开发和维护奠定了坚实的基础！**
