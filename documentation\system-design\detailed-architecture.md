> **📋 文档迁移说明**
> 
> 本文档已从项目根目录迁移到统一的文档管理系统中。
> - **原文件名**: DETAILED_ARCHITECTURE_GUIDE.md
> - **迁移时间**: 2025-07-07T20:00:20.403024
> - **迁移工具**: scripts/migrate_docs.dart
> 
> 如需查看最新的文档结构，请参考 [文档中心](../README.md)。

---

# 🏗️ 金融应用新架构详细指南

## 📋 目录
1. [架构概览](#架构概览)
2. [混合状态管理架构](#混合状态管理架构)
3. [模块详细设计](#模块详细设计)
4. [核心基础设施](#核心基础设施)
5. [数据流设计](#数据流设计)
6. [安全架构](#安全架构)
7. [性能优化策略](#性能优化策略)
8. [开发工作流](#开发工作流)

## 🎯 架构概览

### 整体架构图
```
┌─────────────────────────────────────────────────────────────┐
│                    金融应用新架构                              │
├─────────────────────────────────────────────────────────────┤
│  UI Layer (表现层)                                           │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐            │
│  │   Screens   │ │   Widgets   │ │   Dialogs   │            │
│  └─────────────┘ └─────────────┘ └─────────────┘            │
├─────────────────────────────────────────────────────────────┤
│  State Management Layer (状态管理层)                         │
│  ┌─────────────────────┐ ┌─────────────────────┐            │
│  │   Provider          │ │      Bloc           │            │
│  │  ┌─────────────┐    │ │  ┌─────────────┐    │            │
│  │  │ ThemeProvider│    │ │  │  AuthBloc   │    │            │
│  │  │ LocaleProvider│   │ │  │ MarketBloc  │    │            │
│  │  └─────────────┘    │ │  │ TradeBloc   │    │            │
│  │                     │ │  └─────────────┘    │            │
│  └─────────────────────┘ └─────────────────────┘            │
├─────────────────────────────────────────────────────────────┤
│  Business Logic Layer (业务逻辑层)                           │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐            │
│  │  UseCases   │ │ Repositories│ │   Services  │            │
│  └─────────────┘ └─────────────┘ └─────────────┘            │
├─────────────────────────────────────────────────────────────┤
│  Data Layer (数据层)                                         │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐            │
│  │ DataSources │ │    Cache    │ │  WebSocket  │            │
│  └─────────────┘ └─────────────┘ └─────────────┘            │
├─────────────────────────────────────────────────────────────┤
│  Infrastructure Layer (基础设施层)                           │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐            │
│  │ErrorHandler │ │ Performance │ │ Lifecycle   │            │
│  │             │ │  Monitor    │ │  Manager    │            │
│  └─────────────┘ └─────────────┘ └─────────────┘            │
└─────────────────────────────────────────────────────────────┘
```

### 架构设计原则

#### 1. **关注点分离 (Separation of Concerns)**
- **UI 层**: 只负责界面展示和用户交互
- **状态管理层**: 负责应用状态的管理和分发
- **业务逻辑层**: 处理具体的业务规则和流程
- **数据层**: 负责数据的获取、存储和同步

#### 2. **依赖倒置 (Dependency Inversion)**
- 高层模块不依赖低层模块，都依赖抽象
- 使用接口和抽象类定义契约
- 通过依赖注入实现解耦

#### 3. **单一职责 (Single Responsibility)**
- 每个类和模块只有一个变化的理由
- 明确的职责边界和接口定义

#### 4. **开闭原则 (Open/Closed Principle)**
- 对扩展开放，对修改关闭
- 通过抽象和多态实现功能扩展

## 🔄 混合状态管理架构

### 架构决策理由

#### **为什么选择混合架构？**

1. **Provider 的优势**:
   - 简单易用，学习成本低
   - 适合简单的 UI 状态管理
   - Flutter 官方推荐
   - 轻量级，性能开销小

2. **Bloc 的优势**:
   - 强类型安全
   - 事件驱动，逻辑清晰
   - 优秀的测试支持
   - 适合复杂业务逻辑

3. **混合架构的好处**:
   - 各取所长，避免过度工程化
   - 渐进式迁移，风险可控
   - 团队学习成本分散
   - 适应不同复杂度的场景

### 状态管理分工

#### **Provider 负责的状态**
```dart
// 1. 主题状态管理
class ThemeProvider extends ChangeNotifier {
  ThemeMode _themeMode = ThemeMode.system;
  
  ThemeMode get themeMode => _themeMode;
  
  void setTheme(ThemeMode mode) {
    _themeMode = mode;
    notifyListeners();
    // 持久化主题设置
    _saveThemePreference(mode);
  }
  
  void toggleTheme() {
    setTheme(_themeMode == ThemeMode.light 
        ? ThemeMode.dark 
        : ThemeMode.light);
  }
}

// 2. 语言状态管理
class LocaleProvider extends ChangeNotifier {
  Locale _locale = const Locale('zh', 'CN');
  
  Locale get locale => _locale;
  
  void setLocale(Locale locale) {
    _locale = locale;
    notifyListeners();
    // 持久化语言设置
    _saveLocalePreference(locale);
  }
}
```

#### **Bloc 负责的状态**
```dart
// 1. 认证状态管理
class AuthBloc extends BaseBloc<AuthEvent, AuthState> {
  // 复杂的认证逻辑
  // 令牌管理
  // 双因素认证
  // 会话管理
}

// 2. 市场数据状态管理
class MarketBloc extends BaseBloc<MarketEvent, MarketState> {
  // 实时行情订阅
  // 数据缓存管理
  // WebSocket 连接管理
  // 自选股管理
}

// 3. 交易状态管理
class TradeBloc extends BaseBloc<TradeEvent, TradeState> {
  // 订单管理
  // 持仓管理
  // 风险控制
  // 交易验证
}
```

### 状态管理配置

#### **HybridStateManager 实现**
```dart
class HybridStateManager {
  /// 创建混合状态管理的 Widget 树
  static Widget createStateManagement({required Widget child}) {
    return MultiBlocProvider(
      providers: [
        // Bloc Providers - 业务逻辑状态
        BlocProvider<AuthBloc>(
          create: (context) => GetIt.instance<AuthBloc>(),
          lazy: false, // 认证状态需要立即初始化
        ),
        BlocProvider<MarketBloc>(
          create: (context) => GetIt.instance<MarketBloc>(),
          lazy: true, // 懒加载，用到时再初始化
        ),
        BlocProvider<TradeBloc>(
          create: (context) => GetIt.instance<TradeBloc>(),
          lazy: true,
        ),
      ],
      child: MultiProvider(
        providers: [
          // Provider - UI 状态
          ChangeNotifierProvider<ThemeProvider>(
            create: (_) => ThemeProvider(),
            lazy: false, // 主题需要立即初始化
          ),
          ChangeNotifierProvider<LocaleProvider>(
            create: (_) => LocaleProvider(),
            lazy: false, // 语言需要立即初始化
          ),
        ],
        child: StateManagementListener(child: child),
      ),
    );
  }
}
```

#### **跨模块状态监听**
```dart
class StateManagementListener extends StatefulWidget {
  final Widget child;
  
  @override
  Widget build(BuildContext context) {
    return MultiBlocListener(
      listeners: [
        // 监听认证状态变化
        BlocListener<AuthBloc, AuthState>(
          listener: (context, state) {
            if (state is AuthUnauthenticated) {
              // 用户登出，清理其他模块数据
              context.read<MarketBloc>().add(ClearMarketDataEvent());
              context.read<TradeBloc>().add(ClearTradeDataEvent());
            } else if (state is AuthAuthenticated) {
              // 用户登录，预加载数据
              context.read<MarketBloc>().add(GetWatchlistEvent());
              context.read<TradeBloc>().add(GetAccountInfoEvent());
            }
          },
        ),
        
        // 监听市场数据错误
        BlocListener<MarketBloc, MarketState>(
          listener: (context, state) {
            if (state is MarketError) {
              _showErrorSnackBar(context, '市场数据错误: ${state.message}');
            }
          },
        ),
        
        // 监听交易状态变化
        BlocListener<TradeBloc, TradeState>(
          listener: (context, state) {
            if (state is OrderPlaced) {
              _showSuccessSnackBar(context, state.message);
            } else if (state is RiskWarning) {
              _showRiskWarningDialog(context, state);
            }
          },
        ),
      ],
      child: widget.child,
    );
  }
}
```

## 📦 模块详细设计

### Auth 模块架构

#### **事件设计 (AuthEvent)**
```dart
sealed class AuthEvent extends Equatable {
  const AuthEvent();
}

// 基础认证事件
class LoginEvent extends AuthEvent {
  final String username;
  final String password;
  final bool rememberMe;
  
  const LoginEvent({
    required this.username,
    required this.password,
    this.rememberMe = false,
  });
}

class LogoutEvent extends AuthEvent {
  final String? reason; // 登出原因
  const LogoutEvent({this.reason});
}

// 高级认证事件
class EnableTwoFactorEvent extends AuthEvent {
  final String phoneNumber;
  final String email;
  
  const EnableTwoFactorEvent({
    required this.phoneNumber,
    required this.email,
  });
}

class VerifyTwoFactorEvent extends AuthEvent {
  final String code;
  final TwoFactorType type; // SMS, EMAIL, TOTP
  
  const VerifyTwoFactorEvent({
    required this.code,
    required this.type,
  });
}

// 令牌管理事件
class RefreshTokenEvent extends AuthEvent {
  const RefreshTokenEvent();
}

class CheckAuthStatusEvent extends AuthEvent {
  const CheckAuthStatusEvent();
}
```

#### **状态设计 (AuthState)**
```dart
sealed class AuthState extends BaseState {
  const AuthState();
}

// 基础状态
class AuthInitial extends AuthState {
  const AuthInitial();
}

class AuthLoading extends AuthState {
  final String? operation; // 当前执行的操作
  const AuthLoading({this.operation});
}

// 认证状态
class AuthAuthenticated extends AuthState {
  final UserModel user;
  final String accessToken;
  final DateTime tokenExpiry;
  final List<String> permissions;
  
  const AuthAuthenticated({
    required this.user,
    required this.accessToken,
    required this.tokenExpiry,
    required this.permissions,
  });
  
  // 检查令牌是否即将过期
  bool get isTokenExpiringSoon {
    final now = DateTime.now();
    final timeUntilExpiry = tokenExpiry.difference(now);
    return timeUntilExpiry.inMinutes < 5; // 5分钟内过期
  }
  
  // 检查用户权限
  bool hasPermission(String permission) {
    return permissions.contains(permission);
  }
}

class AuthUnauthenticated extends AuthState {
  final String? message;
  const AuthUnauthenticated({this.message});
}

// 双因素认证状态
class AuthTwoFactorRequired extends AuthState {
  final String sessionId;
  final List<TwoFactorType> availableTypes;
  final Duration timeRemaining;
  
  const AuthTwoFactorRequired({
    required this.sessionId,
    required this.availableTypes,
    required this.timeRemaining,
  });
}

class AuthTwoFactorEnabled extends AuthState {
  final String backupCodes;
  final String qrCode; // TOTP QR码
  
  const AuthTwoFactorEnabled({
    required this.backupCodes,
    required this.qrCode,
  });
}

// 错误状态
class AuthError extends AuthState {
  final String message;
  final String? errorCode;
  final AuthErrorType errorType;
  final int? retryAfterSeconds;
  
  const AuthError({
    required this.message,
    this.errorCode,
    this.errorType = AuthErrorType.general,
    this.retryAfterSeconds,
  });
}
```

#### **业务逻辑实现 (AuthBloc)**
```dart
class AuthBloc extends BaseBloc<AuthEvent, AuthState> {
  final AuthRepository _authRepository;
  final TokenManager _tokenManager;
  final BiometricService _biometricService;
  final EventBus _eventBus;
  final Logger _logger;
  
  // 自动令牌刷新定时器
  Timer? _tokenRefreshTimer;
  
  AuthBloc({
    required AuthRepository authRepository,
    required TokenManager tokenManager,
    required BiometricService biometricService,
    required EventBus eventBus,
    required Logger logger,
  }) : _authRepository = authRepository,
       _tokenManager = tokenManager,
       _biometricService = biometricService,
       _eventBus = eventBus,
       _logger = logger,
       super(const AuthInitial()) {
    
    // 注册事件处理器
    on<LoginEvent>(_onLogin);
    on<LogoutEvent>(_onLogout);
    on<RefreshTokenEvent>(_onRefreshToken);
    on<EnableTwoFactorEvent>(_onEnableTwoFactor);
    on<VerifyTwoFactorEvent>(_onVerifyTwoFactor);
    on<CheckAuthStatusEvent>(_onCheckAuthStatus);
    
    // 启动时检查认证状态
    add(const CheckAuthStatusEvent());
  }

  /// 处理登录事件
  Future<void> _onLogin(LoginEvent event, Emitter<AuthState> emit) async {
    _logger.info('🔐 用户登录: ${event.username}');
    
    emit(const AuthLoading(operation: '登录中'));
    
    try {
      // 1. 输入验证
      final validationResult = await _validateLoginInput(event);
      if (!validationResult.isValid) {
        emit(AuthError(
          message: validationResult.message,
          errorType: AuthErrorType.validation,
        ));
        return;
      }
      
      // 2. 执行登录
      final loginResult = await _authRepository.login(
        username: event.username,
        password: event.password,
      );
      
      await loginResult.fold(
        (failure) async {
          _logger.error('登录失败: ${failure.message}');
          emit(AuthError(
            message: failure.message,
            errorCode: failure.code,
            errorType: _mapFailureToErrorType(failure),
          ));
        },
        (authData) async {
          // 3. 检查是否需要双因素认证
          if (authData.requiresTwoFactor) {
            emit(AuthTwoFactorRequired(
              sessionId: authData.sessionId!,
              availableTypes: authData.availableTwoFactorTypes,
              timeRemaining: const Duration(minutes: 5),
            ));
            return;
          }
          
          // 4. 保存认证信息
          await _tokenManager.saveTokens(
            accessToken: authData.accessToken,
            refreshToken: authData.refreshToken,
          );
          
          // 5. 设置自动刷新
          _setupTokenRefresh(authData.tokenExpiry);
          
          // 6. 发送登录成功事件
          _eventBus.fire(UserLoggedInEvent(authData.user));
          
          emit(AuthAuthenticated(
            user: authData.user,
            accessToken: authData.accessToken,
            tokenExpiry: authData.tokenExpiry,
            permissions: authData.permissions,
          ));
          
          _logger.info('✅ 用户登录成功');
        },
      );
      
    } catch (e) {
      _logger.error('登录异常: $e');
      emit(AuthError(
        message: '登录失败，请稍后重试',
        errorType: AuthErrorType.network,
      ));
    }
  }

  /// 设置令牌自动刷新
  void _setupTokenRefresh(DateTime tokenExpiry) {
    _tokenRefreshTimer?.cancel();
    
    // 在令牌过期前5分钟刷新
    final refreshTime = tokenExpiry.subtract(const Duration(minutes: 5));
    final delay = refreshTime.difference(DateTime.now());
    
    if (delay.isNegative) {
      // 令牌已经过期，立即刷新
      add(const RefreshTokenEvent());
    } else {
      _tokenRefreshTimer = Timer(delay, () {
        add(const RefreshTokenEvent());
      });
    }
  }

  /// 输入验证
  Future<ValidationResult> _validateLoginInput(LoginEvent event) async {
    // 用户名验证
    if (event.username.trim().isEmpty) {
      return ValidationResult.invalid('请输入用户名');
    }
    
    // 密码验证
    if (event.password.isEmpty) {
      return ValidationResult.invalid('请输入密码');
    }
    
    if (event.password.length < 6) {
      return ValidationResult.invalid('密码长度不能少于6位');
    }
    
    // 检查是否包含特殊字符（根据安全策略）
    if (!_isPasswordStrong(event.password)) {
      return ValidationResult.invalid('密码强度不足，请包含大小写字母和数字');
    }
    
    return ValidationResult.valid();
  }

  /// 密码强度检查
  bool _isPasswordStrong(String password) {
    // 至少包含一个大写字母、一个小写字母和一个数字
    final hasUppercase = password.contains(RegExp(r'[A-Z]'));
    final hasLowercase = password.contains(RegExp(r'[a-z]'));
    final hasDigits = password.contains(RegExp(r'[0-9]'));
    
    return hasUppercase && hasLowercase && hasDigits;
  }

  @override
  Future<void> close() {
    _tokenRefreshTimer?.cancel();
    return super.close();
  }
}
```

### Market 模块架构

#### **实时数据管理**
```dart
class MarketBloc extends BaseBloc<MarketEvent, MarketState> {
  final MarketRepository _marketRepository;
  final WebSocketService _webSocketService;
  final CacheManager _cacheManager;
  
  // 订阅管理
  final Map<String, StreamSubscription> _subscriptions = {};
  final Set<String> _subscribedSymbols = {};
  
  // 数据缓存
  final Map<String, StockData> _stockCache = {};
  final Map<String, QuoteData> _quoteCache = {};
  
  /// 订阅实时行情
  Future<void> _onSubscribeQuote(
    SubscribeQuoteEvent event,
    Emitter<MarketState> emit,
  ) async {
    if (_subscribedSymbols.contains(event.symbol)) {
      _logger.warning('已经订阅了 ${event.symbol}');
      return;
    }
    
    try {
      // 建立 WebSocket 连接
      final stream = _webSocketService.subscribeToQuote(event.symbol);
      
      final subscription = stream.listen(
        (quoteData) {
          // 更新缓存
          final previousQuote = _quoteCache[event.symbol];
          _quoteCache[event.symbol] = quoteData;
          
          // 发送状态更新
          if (!isClosed) {
            emit(QuoteUpdated(
              symbol: event.symbol,
              quote: quoteData,
              timestamp: DateTime.now(),
              previousQuote: previousQuote,
            ));
          }
        },
        onError: (error) {
          _logger.error('实时行情订阅错误: $error');
          if (!isClosed) {
            emit(MarketError(
              message: '实时行情连接失败: $error',
              errorType: MarketErrorType.subscription,
            ));
          }
        },
      );
      
      _subscriptions[event.symbol] = subscription;
      _subscribedSymbols.add(event.symbol);
      
      emit(SubscriptionUpdated(
        symbol: event.symbol,
        isSubscribed: true,
      ));
      
    } catch (e) {
      _logger.error('订阅实时行情失败: $e');
      emit(MarketError(
        message: '订阅失败: $e',
        errorType: MarketErrorType.subscription,
      ));
    }
  }
}
```

### Trade 模块架构

#### **交易风险控制**
```dart
class TradeBloc extends BaseBloc<TradeEvent, TradeState> {
  /// 下单前风险检查
  Future<RiskCheckResult> _performRiskCheck(PlaceOrderEvent event) async {
    final checks = <RiskCheck>[
      // 1. 资金充足性检查
      InsufficientFundsCheck(),
      
      // 2. 持仓集中度检查
      ConcentrationRiskCheck(),
      
      // 3. 单笔交易限额检查
      SingleTradeAmountCheck(),
      
      // 4. 日交易次数检查
      DailyTradeCountCheck(),
      
      // 5. 价格合理性检查
      PriceReasonabilityCheck(),
    ];
    
    for (final check in checks) {
      final result = await check.perform(event);
      if (result.hasRisk) {
        return result;
      }
    }
    
    return RiskCheckResult.passed();
  }

  /// 订单验证
  Future<ValidationResult> _validateOrder(PlaceOrderEvent event) async {
    // 基础验证
    if (event.quantity <= 0) {
      return ValidationResult.invalid('数量必须大于0');
    }
    
    if (event.quantity % 100 != 0) {
      return ValidationResult.invalid('股票数量必须是100的整数倍');
    }
    
    // 限价单价格验证
    if (event.orderType == 'limit') {
      if (event.price == null || event.price! <= 0) {
        return ValidationResult.invalid('限价单必须设置有效价格');
      }
      
      // 价格精度验证（保留2位小数）
      if ((event.price! * 100) % 1 != 0) {
        return ValidationResult.invalid('价格精度不能超过2位小数');
      }
    }
    
    // 交易时间验证
    if (!_isMarketOpen()) {
      return ValidationResult.invalid('当前不在交易时间');
    }
    
    return ValidationResult.valid();
  }
}

## 🛡️ 核心基础设施

### 全局错误处理系统

#### **多层错误捕获架构**
```dart
class GlobalErrorHandler {
  /// 错误捕获层级
  void initialize() {
    // 1. Flutter 框架错误
    FlutterError.onError = (FlutterErrorDetails details) {
      _handleFlutterError(details);
    };

    // 2. 平台异步错误
    PlatformDispatcher.instance.onError = (error, stack) {
      _handlePlatformError(error, stack);
      return true;
    };

    // 3. Isolate 错误
    Isolate.current.addErrorListener(
      RawReceivePort((pair) async {
        final List<dynamic> errorAndStacktrace = pair;
        await _handleIsolateError(
          errorAndStacktrace.first,
          errorAndStacktrace.last,
        );
      }).sendPort,
    );
  }

  /// 智能错误分类
  ErrorType _classifyError(Object error) {
    final errorString = error.toString().toLowerCase();

    // 网络相关错误
    if (errorString.contains('socket') ||
        errorString.contains('network') ||
        errorString.contains('timeout') ||
        errorString.contains('connection')) {
      return ErrorType.network;
    }

    // 认证相关错误
    if (errorString.contains('unauthorized') ||
        errorString.contains('forbidden') ||
        errorString.contains('token')) {
      return ErrorType.authentication;
    }

    // 业务逻辑错误
    if (errorString.contains('validation') ||
        errorString.contains('business') ||
        errorString.contains('rule')) {
      return ErrorType.business;
    }

    // 状态相关错误
    if (errorString.contains('state') ||
        errorString.contains('bloc') ||
        errorString.contains('provider')) {
      return ErrorType.state;
    }

    return ErrorType.general;
  }

  /// 用户友好的错误消息
  String _getUserFriendlyMessage(ErrorType type, String originalMessage) {
    switch (type) {
      case ErrorType.network:
        return '网络连接异常，请检查网络设置后重试';
      case ErrorType.authentication:
        return '登录状态已过期，请重新登录';
      case ErrorType.business:
        return '操作失败，请检查输入信息';
      case ErrorType.state:
        return '应用状态异常，请重启应用';
      default:
        return '系统异常，请稍后重试';
    }
  }
}
```

#### **错误恢复策略**
```dart
class ErrorRecoveryManager {
  /// 自动恢复策略
  Future<bool> attemptRecovery(ErrorReport error) async {
    switch (error.type) {
      case ErrorType.network:
        return await _recoverFromNetworkError(error);
      case ErrorType.authentication:
        return await _recoverFromAuthError(error);
      case ErrorType.state:
        return await _recoverFromStateError(error);
      default:
        return false;
    }
  }

  /// 网络错误恢复
  Future<bool> _recoverFromNetworkError(ErrorReport error) async {
    // 1. 检查网络连接
    final isConnected = await _checkNetworkConnectivity();
    if (!isConnected) {
      return false;
    }

    // 2. 重试网络请求
    try {
      await _retryLastNetworkRequest();
      return true;
    } catch (e) {
      return false;
    }
  }

  /// 认证错误恢复
  Future<bool> _recoverFromAuthError(ErrorReport error) async {
    try {
      // 尝试刷新令牌
      final authBloc = GetIt.instance<AuthBloc>();
      authBloc.add(const RefreshTokenEvent());

      // 等待刷新结果
      await authBloc.stream
          .where((state) => state is! AuthLoading)
          .first;

      return authBloc.state is AuthAuthenticated;
    } catch (e) {
      return false;
    }
  }
}
```

### 性能监控系统

#### **实时性能指标收集**
```dart
class PerformanceMonitor {
  /// 性能指标收集
  void _collectMetrics() {
    final metric = PerformanceMetric(
      timestamp: DateTime.now(),
      memoryUsage: _getMemoryUsage(),
      frameRate: _getCurrentFrameRate(),
      cpuUsage: _getCpuUsage(),
      networkLatency: _getNetworkLatency(),
      batteryLevel: _getBatteryLevel(),
    );

    _metrics.add(metric);
    _analyzePerformanceTrends(metric);
  }

  /// 性能趋势分析
  void _analyzePerformanceTrends(PerformanceMetric metric) {
    // 内存泄漏检测
    if (_detectMemoryLeak()) {
      _logger.warning('🚨 检测到可能的内存泄漏');
      _eventBus.fire(MemoryLeakDetectedEvent());
    }

    // 性能下降检测
    if (_detectPerformanceDegradation()) {
      _logger.warning('📉 检测到性能下降');
      _eventBus.fire(PerformanceDegradationEvent());
    }

    // 电池消耗异常检测
    if (_detectAbnormalBatteryUsage()) {
      _logger.warning('🔋 检测到异常电池消耗');
      _eventBus.fire(AbnormalBatteryUsageEvent());
    }
  }

  /// 内存泄漏检测算法
  bool _detectMemoryLeak() {
    if (_metrics.length < 10) return false;

    // 获取最近10个指标
    final recentMetrics = _metrics.takeLast(10).toList();

    // 计算内存使用趋势
    double totalIncrease = 0;
    for (int i = 1; i < recentMetrics.length; i++) {
      final increase = recentMetrics[i].memoryUsage - recentMetrics[i-1].memoryUsage;
      if (increase > 0) {
        totalIncrease += increase;
      }
    }

    // 如果内存持续增长超过50MB，认为可能存在内存泄漏
    return totalIncrease > 50 * 1024 * 1024;
  }
}
```

#### **性能优化建议引擎**
```dart
class PerformanceOptimizer {
  /// 生成优化建议
  List<OptimizationSuggestion> generateSuggestions(PerformanceReport report) {
    final suggestions = <OptimizationSuggestion>[];

    // 内存优化建议
    if (report.averageMemoryUsage > 100 * 1024 * 1024) {
      suggestions.add(OptimizationSuggestion(
        type: OptimizationType.memory,
        priority: Priority.high,
        title: '内存使用过高',
        description: '当前平均内存使用${(report.averageMemoryUsage / 1024 / 1024).toStringAsFixed(1)}MB，建议优化',
        actions: [
          '清理不必要的缓存',
          '检查是否存在内存泄漏',
          '优化图片加载策略',
          '减少同时加载的数据量',
        ],
      ));
    }

    // 帧率优化建议
    if (report.averageFrameRate < 45) {
      suggestions.add(OptimizationSuggestion(
        type: OptimizationType.performance,
        priority: Priority.high,
        title: '帧率过低',
        description: '当前平均帧率${report.averageFrameRate.toStringAsFixed(1)}fps，影响用户体验',
        actions: [
          '减少复杂的UI动画',
          '优化列表渲染性能',
          '使用RepaintBoundary减少重绘',
          '检查是否有阻塞主线程的操作',
        ],
      ));
    }

    return suggestions;
  }
}
```

### 应用生命周期管理

#### **智能资源管理**
```dart
class AppLifecycleManager extends WidgetsBindingObserver {
  /// 后台资源管理策略
  void _onAppPaused() {
    _logger.info('⏸️ 应用进入后台，开始资源管理');

    // 1. 暂停非必要的网络请求
    _pauseNonEssentialNetworkRequests();

    // 2. 清理敏感数据
    _clearSensitiveDataFromMemory();

    // 3. 暂停实时数据订阅
    _pauseRealTimeSubscriptions();

    // 4. 保存应用状态
    _saveApplicationState();

    // 5. 设置后台清理定时器
    _scheduleBackgroundCleanup();
  }

  /// 前台恢复策略
  void _onAppResumed(AppLifecycleState? previousState) {
    _logger.info('🌟 应用恢复前台，开始数据刷新');

    final backgroundDuration = _getBackgroundDuration();

    // 根据后台时长决定刷新策略
    if (backgroundDuration != null) {
      if (backgroundDuration.inMinutes > 30) {
        // 长时间后台，全量刷新
        _performFullDataRefresh();
      } else if (backgroundDuration.inMinutes > 5) {
        // 中等时间后台，增量刷新
        _performIncrementalDataRefresh();
      } else {
        // 短时间后台，恢复订阅即可
        _resumeRealTimeSubscriptions();
      }
    }

    // 恢复性能监控
    _resumePerformanceMonitoring();
  }

  /// 智能数据刷新策略
  void _performIncrementalDataRefresh() {
    // 1. 检查认证状态
    final authBloc = GetIt.instance<AuthBloc>();
    if (authBloc.state is AuthAuthenticated) {
      final authState = authBloc.state as AuthAuthenticated;

      // 检查令牌是否即将过期
      if (authState.isTokenExpiringSoon) {
        authBloc.add(const RefreshTokenEvent());
      }
    }

    // 2. 刷新关键数据
    final marketBloc = GetIt.instance<MarketBloc>();
    marketBloc.add(const RefreshMarketDataEvent());

    // 3. 刷新用户相关数据
    final tradeBloc = GetIt.instance<TradeBloc>();
    tradeBloc.add(const GetAccountInfoEvent());
  }
}
```

## 🔄 数据流设计

### 单向数据流架构

#### **数据流向图**
```
用户操作 → Event → Bloc → State → UI 更新
    ↑                              ↓
    └── 用户反馈 ←── UI 响应 ←── State 变化
```

#### **具体数据流示例：用户登录**
```dart
// 1. 用户点击登录按钮
onPressed: () {
  context.read<AuthBloc>().add(LoginEvent(
    username: _usernameController.text,
    password: _passwordController.text,
  ));
}

// 2. AuthBloc 处理登录事件
Future<void> _onLogin(LoginEvent event, Emitter<AuthState> emit) async {
  // 2.1 发送加载状态
  emit(const AuthLoading(operation: '登录中'));

  // 2.2 调用业务逻辑
  final result = await _loginUseCase.execute(
    username: event.username,
    password: event.password,
  );

  // 2.3 根据结果发送相应状态
  result.fold(
    (failure) => emit(AuthError(message: failure.message)),
    (user) => emit(AuthAuthenticated(user: user)),
  );
}

// 3. UI 监听状态变化
BlocBuilder<AuthBloc, AuthState>(
  builder: (context, state) {
    if (state is AuthLoading) {
      return const CircularProgressIndicator();
    } else if (state is AuthAuthenticated) {
      return HomeScreen(user: state.user);
    } else if (state is AuthError) {
      return ErrorWidget(message: state.message);
    }
    return LoginForm();
  },
)
```

### 跨模块数据同步

#### **事件总线模式**
```dart
class CrossModuleEventBus {
  static final EventBus _eventBus = EventBus();

  /// 发送跨模块事件
  static void fire(dynamic event) {
    _eventBus.fire(event);
  }

  /// 监听跨模块事件
  static StreamSubscription<T> on<T>() {
    return _eventBus.on<T>();
  }
}

// 使用示例：用户登录后同步数据
class AuthBloc extends BaseBloc<AuthEvent, AuthState> {
  Future<void> _onLogin(LoginEvent event, Emitter<AuthState> emit) async {
    // ... 登录逻辑

    if (loginSuccess) {
      // 发送用户登录事件
      CrossModuleEventBus.fire(UserLoggedInEvent(user));

      emit(AuthAuthenticated(user: user));
    }
  }
}

// 其他模块监听登录事件
class MarketBloc extends BaseBloc<MarketEvent, MarketState> {
  MarketBloc() : super(MarketInitial()) {
    // 监听用户登录事件
    CrossModuleEventBus.on<UserLoggedInEvent>().listen((event) {
      // 用户登录后，加载自选股
      add(GetWatchlistEvent());
    });
  }
}
```

## 🔒 安全架构

### 数据安全策略

#### **敏感数据保护**
```dart
class SecurityManager {
  /// 敏感数据加密存储
  Future<void> storeSecureData(String key, String value) async {
    // 1. 使用设备密钥加密
    final encryptedValue = await _encryptWithDeviceKey(value);

    // 2. 存储到安全存储
    await _secureStorage.write(key: key, value: encryptedValue);

    // 3. 记录访问日志
    _auditLogger.logDataAccess(key, 'WRITE');
  }

  /// 生物识别验证
  Future<bool> authenticateWithBiometrics() async {
    try {
      final isAvailable = await _localAuth.canCheckBiometrics;
      if (!isAvailable) return false;

      final isAuthenticated = await _localAuth.authenticate(
        localizedReason: '请验证身份以继续操作',
        options: const AuthenticationOptions(
          biometricOnly: true,
          stickyAuth: true,
        ),
      );

      if (isAuthenticated) {
        _auditLogger.logBiometricAuth('SUCCESS');
      } else {
        _auditLogger.logBiometricAuth('FAILED');
      }

      return isAuthenticated;
    } catch (e) {
      _auditLogger.logBiometricAuth('ERROR', error: e.toString());
      return false;
    }
  }
}
```

#### **网络安全**
```dart
class NetworkSecurityInterceptor extends Interceptor {
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    // 1. 添加安全头
    options.headers.addAll({
      'X-Request-ID': _generateRequestId(),
      'X-Client-Version': _getAppVersion(),
      'X-Device-ID': _getDeviceId(),
    });

    // 2. 请求签名
    final signature = _generateRequestSignature(options);
    options.headers['X-Signature'] = signature;

    // 3. 请求加密（敏感接口）
    if (_isSensitiveEndpoint(options.path)) {
      options.data = _encryptRequestData(options.data);
    }

    super.onRequest(options, handler);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    // 1. 验证响应签名
    if (!_verifyResponseSignature(response)) {
      handler.reject(DioException(
        requestOptions: response.requestOptions,
        message: '响应签名验证失败',
        type: DioExceptionType.badResponse,
      ));
      return;
    }

    // 2. 解密响应数据
    if (_isEncryptedResponse(response)) {
      response.data = _decryptResponseData(response.data);
    }

    super.onResponse(response, handler);
  }
}
```

### 审计日志系统

#### **操作审计**
```dart
class AuditLogger {
  /// 记录用户操作
  void logUserOperation(String operation, Map<String, dynamic> details) {
    final auditLog = AuditLog(
      timestamp: DateTime.now(),
      userId: _getCurrentUserId(),
      operation: operation,
      details: details,
      ipAddress: _getClientIpAddress(),
      userAgent: _getUserAgent(),
      sessionId: _getSessionId(),
    );

    // 异步写入日志
    _writeAuditLog(auditLog);
  }

  /// 记录安全事件
  void logSecurityEvent(SecurityEventType type, String description) {
    final securityLog = SecurityLog(
      timestamp: DateTime.now(),
      eventType: type,
      description: description,
      severity: _getSeverityLevel(type),
      sourceIp: _getClientIpAddress(),
      userId: _getCurrentUserId(),
    );

    // 立即写入安全日志
    _writeSecurityLog(securityLog);

    // 高危事件立即告警
    if (securityLog.severity == SecuritySeverity.critical) {
      _triggerSecurityAlert(securityLog);
    }
  }
}

## ⚡ 性能优化策略

### 启动性能优化

#### **分阶段启动策略**
```dart
class AppStartupOptimizer {
  /// 优化的启动流程
  Future<void> optimizedInitialization() async {
    // 阶段1：关键服务初始化（并行）
    await Future.wait([
      _initializeLogging(),
      _initializeErrorHandling(),
      _initializeCrashReporting(),
    ]);

    // 阶段2：核心服务初始化
    await _initializeDependencyInjection();
    await _initializeDatabase();

    // 阶段3：业务服务初始化（延迟）
    _scheduleDelayedInitialization();
  }

  /// 延迟初始化非关键服务
  void _scheduleDelayedInitialization() {
    // 使用微任务延迟初始化
    scheduleMicrotask(() async {
      await Future.wait([
        _initializeAnalytics(),
        _initializePushNotifications(),
        _initializeLocationServices(),
      ]);
    });
  }
}
```

#### **资源预加载策略**
```dart
class ResourcePreloader {
  /// 智能预加载
  Future<void> preloadCriticalResources() async {
    // 1. 预加载关键图片
    await _preloadImages([
      'assets/images/logo.png',
      'assets/images/splash.png',
      'assets/images/icons/common_icons.png',
    ]);

    // 2. 预编译关键页面
    await _precompileRoutes([
      '/login',
      '/home',
      '/market',
    ]);

    // 3. 预加载用户数据
    if (await _isUserLoggedIn()) {
      _preloadUserData();
    }
  }

  /// 预加载用户数据
  Future<void> _preloadUserData() async {
    final futures = <Future>[];

    // 并行预加载
    futures.add(_preloadWatchlist());
    futures.add(_preloadAccountInfo());
    futures.add(_preloadRecentTrades());

    // 等待所有预加载完成，但不阻塞启动
    unawaited(Future.wait(futures));
  }
}
```

### 内存优化策略

#### **智能缓存管理**
```dart
class IntelligentCacheManager {
  final Map<String, CacheEntry> _cache = {};
  final int _maxCacheSize = 100 * 1024 * 1024; // 100MB
  int _currentCacheSize = 0;

  /// LRU缓存策略
  void put(String key, dynamic data, {Duration? ttl}) {
    final entry = CacheEntry(
      data: data,
      timestamp: DateTime.now(),
      ttl: ttl,
      size: _calculateDataSize(data),
      accessCount: 0,
    );

    // 检查是否需要清理缓存
    if (_currentCacheSize + entry.size > _maxCacheSize) {
      _evictLeastRecentlyUsed(entry.size);
    }

    _cache[key] = entry;
    _currentCacheSize += entry.size;
  }

  /// 智能缓存清理
  void _evictLeastRecentlyUsed(int requiredSpace) {
    // 按访问时间和频率排序
    final entries = _cache.entries.toList()
      ..sort((a, b) {
        final aScore = _calculateEvictionScore(a.value);
        final bScore = _calculateEvictionScore(b.value);
        return aScore.compareTo(bScore);
      });

    int freedSpace = 0;
    for (final entry in entries) {
      if (freedSpace >= requiredSpace) break;

      _cache.remove(entry.key);
      freedSpace += entry.value.size;
      _currentCacheSize -= entry.value.size;
    }
  }

  /// 计算驱逐分数（分数越低越容易被驱逐）
  double _calculateEvictionScore(CacheEntry entry) {
    final age = DateTime.now().difference(entry.timestamp).inMinutes;
    final accessFrequency = entry.accessCount / (age + 1);
    final sizeWeight = entry.size / (1024 * 1024); // MB

    // 综合考虑访问频率、数据年龄和大小
    return accessFrequency / (age * 0.1 + sizeWeight * 0.1 + 1);
  }
}
```

#### **图片内存优化**
```dart
class ImageMemoryOptimizer {
  /// 自适应图片加载
  Widget buildOptimizedImage(String imageUrl, {
    required double width,
    required double height,
  }) {
    return CachedNetworkImage(
      imageUrl: imageUrl,
      width: width,
      height: height,
      // 根据设备像素密度调整图片质量
      memCacheWidth: (width * MediaQuery.of(context).devicePixelRatio).round(),
      memCacheHeight: (height * MediaQuery.of(context).devicePixelRatio).round(),
      placeholder: (context, url) => _buildShimmerPlaceholder(width, height),
      errorWidget: (context, url, error) => _buildErrorPlaceholder(),
      // 使用渐进式JPEG
      progressIndicatorBuilder: (context, url, progress) {
        return _buildProgressIndicator(progress.progress);
      },
    );
  }

  /// 图片预处理
  Future<void> preprocessImages() async {
    // 1. 压缩大图片
    await _compressLargeImages();

    // 2. 生成缩略图
    await _generateThumbnails();

    // 3. 转换为WebP格式
    await _convertToWebP();
  }
}
```

### 网络性能优化

#### **智能请求管理**
```dart
class NetworkOptimizer {
  final Map<String, Completer> _pendingRequests = {};
  final RequestQueue _requestQueue = RequestQueue();

  /// 请求去重
  Future<T> deduplicatedRequest<T>(String key, Future<T> Function() request) async {
    // 如果已有相同请求在进行中，等待其结果
    if (_pendingRequests.containsKey(key)) {
      return await _pendingRequests[key]!.future as T;
    }

    final completer = Completer<T>();
    _pendingRequests[key] = completer;

    try {
      final result = await request();
      completer.complete(result);
      return result;
    } catch (e) {
      completer.completeError(e);
      rethrow;
    } finally {
      _pendingRequests.remove(key);
    }
  }

  /// 请求优先级管理
  Future<T> prioritizedRequest<T>(
    Future<T> Function() request, {
    RequestPriority priority = RequestPriority.normal,
  }) async {
    return await _requestQueue.add(request, priority: priority);
  }

  /// 批量请求优化
  Future<List<T>> batchRequests<T>(
    List<Future<T> Function()> requests, {
    int maxConcurrency = 3,
  }) async {
    final results = <T>[];
    final semaphore = Semaphore(maxConcurrency);

    final futures = requests.map((request) async {
      await semaphore.acquire();
      try {
        return await request();
      } finally {
        semaphore.release();
      }
    });

    return await Future.wait(futures);
  }
}
```

#### **连接池管理**
```dart
class ConnectionPoolManager {
  final Map<String, ConnectionPool> _pools = {};

  /// 获取连接池
  ConnectionPool getPool(String host) {
    return _pools.putIfAbsent(host, () => ConnectionPool(
      maxConnections: 5,
      keepAliveTimeout: const Duration(seconds: 30),
      connectionTimeout: const Duration(seconds: 10),
    ));
  }

  /// 连接健康检查
  Future<void> healthCheck() async {
    for (final pool in _pools.values) {
      await pool.healthCheck();
    }
  }

  /// 清理空闲连接
  void cleanupIdleConnections() {
    for (final pool in _pools.values) {
      pool.cleanupIdleConnections();
    }
  }
}
```

## 🔧 开发工作流

### 代码生成工具

#### **Bloc 代码生成器**
```dart
class BlocGenerator {
  /// 生成完整的 Bloc 文件
  Future<void> generateBloc(String moduleName, List<String> events) async {
    final templates = BlocTemplates();

    // 1. 生成事件文件
    final eventContent = templates.generateEvents(moduleName, events);
    await _writeFile('${moduleName}_event.dart', eventContent);

    // 2. 生成状态文件
    final stateContent = templates.generateStates(moduleName, events);
    await _writeFile('${moduleName}_state.dart', stateContent);

    // 3. 生成 Bloc 文件
    final blocContent = templates.generateBloc(moduleName, events);
    await _writeFile('${moduleName}_bloc.dart', blocContent);

    // 4. 生成测试文件
    final testContent = templates.generateTests(moduleName, events);
    await _writeFile('${moduleName}_bloc_test.dart', testContent);

    _logger.info('✅ ${moduleName} Bloc 代码生成完成');
  }
}
```

#### **模块脚手架生成器**
```dart
class ModuleScaffoldGenerator {
  /// 生成完整模块结构
  Future<void> generateModule(String moduleName) async {
    final moduleStructure = ModuleStructure(moduleName);

    // 创建目录结构
    await _createDirectoryStructure(moduleStructure);

    // 生成核心文件
    await Future.wait([
      _generatePubspecYaml(moduleStructure),
      _generateModuleExports(moduleStructure),
      _generateDependencyInjection(moduleStructure),
      _generateRepository(moduleStructure),
      _generateDataSources(moduleStructure),
      _generateModels(moduleStructure),
      _generateUseCases(moduleStructure),
    ]);

    _logger.info('🏗️ ${moduleName} 模块脚手架生成完成');
  }
}
```

### 自动化测试策略

#### **测试金字塔实现**
```dart
// 1. 单元测试 (70%)
class AuthBlocTest {
  group('AuthBloc', () {
    late AuthBloc authBloc;
    late MockAuthRepository mockRepository;

    setUp(() {
      mockRepository = MockAuthRepository();
      authBloc = AuthBloc(repository: mockRepository);
    });

    blocTest<AuthBloc, AuthState>(
      '登录成功时应该发送 AuthAuthenticated 状态',
      build: () => authBloc,
      act: (bloc) => bloc.add(LoginEvent(
        username: '<EMAIL>',
        password: 'password123',
      )),
      expect: () => [
        const AuthLoading(operation: '登录中'),
        isA<AuthAuthenticated>(),
      ],
    );
  });
}

// 2. 集成测试 (20%)
class MarketIntegrationTest {
  testWidgets('市场数据页面集成测试', (WidgetTester tester) async {
    // 构建完整的应用环境
    await tester.pumpWidget(TestApp(
      child: MarketPage(),
    ));

    // 验证初始状态
    expect(find.byType(CircularProgressIndicator), findsOneWidget);

    // 等待数据加载
    await tester.pumpAndSettle();

    // 验证数据显示
    expect(find.byType(StockListItem), findsWidgets);

    // 测试搜索功能
    await tester.enterText(find.byType(TextField), 'AAPL');
    await tester.testTextInput.receiveAction(TextInputAction.search);
    await tester.pumpAndSettle();

    // 验证搜索结果
    expect(find.text('AAPL'), findsOneWidget);
  });
}

// 3. 端到端测试 (10%)
class E2ETest {
  testWidgets('完整交易流程测试', (WidgetTester tester) async {
    // 1. 启动应用
    await tester.pumpWidget(MyApp());
    await tester.pumpAndSettle();

    // 2. 用户登录
    await _performLogin(tester);

    // 3. 浏览市场数据
    await _browseMarketData(tester);

    // 4. 执行交易
    await _performTrade(tester);

    // 5. 验证交易结果
    await _verifyTradeResult(tester);
  });
}
```

### CI/CD 流水线

#### **自动化构建流程**
```yaml
# .gitlab-ci.yml
stages:
  - analyze
  - test
  - build
  - deploy

variables:
  FLUTTER_VERSION: "3.24.0"

# 代码分析
analyze:
  stage: analyze
  script:
    - flutter analyze
    - dart format --set-exit-if-changed .
    - flutter pub run import_sorter:main --exit-if-changed
  only:
    - merge_requests
    - main

# 单元测试
unit_test:
  stage: test
  script:
    - flutter test --coverage
    - genhtml coverage/lcov.info -o coverage/html
  coverage: '/lines......: \d+\.\d+%/'
  artifacts:
    reports:
      coverage_report:
        coverage_format: cobertura
        path: coverage/cobertura.xml
    paths:
      - coverage/

# 集成测试
integration_test:
  stage: test
  script:
    - flutter drive --target=test_driver/app.dart
  artifacts:
    when: always
    paths:
      - test_driver/screenshots/

# 构建应用
build_android:
  stage: build
  script:
    - flutter build apk --release
    - flutter build appbundle --release
  artifacts:
    paths:
      - build/app/outputs/
    expire_in: 1 week
  only:
    - main
    - tags

# 部署到测试环境
deploy_staging:
  stage: deploy
  script:
    - ./scripts/deploy_to_staging.sh
  environment:
    name: staging
    url: https://staging.financial-app.com
  only:
    - main

# 部署到生产环境
deploy_production:
  stage: deploy
  script:
    - ./scripts/deploy_to_production.sh
  environment:
    name: production
    url: https://financial-app.com
  when: manual
  only:
    - tags
```

### 代码质量保证

#### **静态代码分析配置**
```yaml
# analysis_options.yaml
include: package:flutter_lints/flutter.yaml

analyzer:
  exclude:
    - "**/*.g.dart"
    - "**/*.freezed.dart"

  strong-mode:
    implicit-casts: false
    implicit-dynamic: false

linter:
  rules:
    # 性能相关
    - avoid_function_literals_in_foreach_calls
    - prefer_const_constructors
    - prefer_const_literals_to_create_immutables

    # 安全相关
    - avoid_web_libraries_in_flutter
    - secure_pubspec_urls

    # 可读性相关
    - prefer_single_quotes
    - require_trailing_commas
    - sort_child_properties_last

    # 架构相关
    - avoid_classes_with_only_static_members
    - prefer_mixin
    - use_super_parameters

custom_lint:
  rules:
    # 自定义规则
    - bloc_naming_convention
    - provider_naming_convention
    - file_naming_convention
```

## 📚 最佳实践指南

### Bloc 开发最佳实践

#### **1. 事件命名规范**
```dart
// ✅ 好的命名
class LoadUserProfileEvent extends UserEvent {}
class UpdateUserEmailEvent extends UserEvent {}
class DeleteUserAccountEvent extends UserEvent {}

// ❌ 避免的命名
class UserEvent1 extends UserEvent {}
class DoSomething extends UserEvent {}
class Update extends UserEvent {}
```

#### **2. 状态设计原则**
```dart
// ✅ 使用 sealed class 确保状态完整性
sealed class UserState extends Equatable {
  const UserState();
}

// ✅ 状态应该是不可变的
class UserLoaded extends UserState {
  final User user;
  final List<Permission> permissions;

  const UserLoaded({
    required this.user,
    required this.permissions,
  });

  @override
  List<Object> get props => [user, permissions];
}

// ✅ 提供便利方法
class UserAuthenticated extends UserState {
  final User user;
  final String token;

  const UserAuthenticated({
    required this.user,
    required this.token,
  });

  // 便利方法
  bool get isAdmin => user.role == UserRole.admin;
  bool get hasValidToken => token.isNotEmpty;
}
```

#### **3. Bloc 测试策略**
```dart
class UserBlocTest {
  group('UserBloc', () {
    // 测试每个事件的所有可能状态转换
    blocTest<UserBloc, UserState>(
      '加载用户成功',
      build: () => userBloc,
      act: (bloc) => bloc.add(LoadUserEvent(userId: '123')),
      expect: () => [
        const UserLoading(),
        isA<UserLoaded>()
          .having((state) => state.user.id, 'user id', '123'),
      ],
    );

    // 测试错误情况
    blocTest<UserBloc, UserState>(
      '加载用户失败',
      setUp: () {
        when(() => mockRepository.getUser(any()))
          .thenThrow(NetworkException('网络错误'));
      },
      build: () => userBloc,
      act: (bloc) => bloc.add(LoadUserEvent(userId: '123')),
      expect: () => [
        const UserLoading(),
        isA<UserError>()
          .having((state) => state.message, 'error message', contains('网络错误')),
      ],
    );
  });
}
```

### Provider 使用最佳实践

#### **1. 简单状态管理**
```dart
// ✅ 适合 Provider 的场景
class ThemeProvider extends ChangeNotifier {
  ThemeMode _themeMode = ThemeMode.system;

  ThemeMode get themeMode => _themeMode;

  void setTheme(ThemeMode mode) {
    if (_themeMode != mode) {
      _themeMode = mode;
      notifyListeners();
      _persistTheme(mode);
    }
  }
}

// ✅ 使用 Consumer 进行局部更新
Consumer<ThemeProvider>(
  builder: (context, themeProvider, child) {
    return MaterialApp(
      theme: ThemeData.light(),
      darkTheme: ThemeData.dark(),
      themeMode: themeProvider.themeMode,
      // child 不会重建
      home: child,
    );
  },
  child: const HomePage(), // 这个 widget 不会重建
)
```

### 性能优化最佳实践

#### **1. Widget 重建优化**
```dart
// ✅ 使用 const 构造函数
class MyWidget extends StatelessWidget {
  const MyWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return const Text('Hello World');
  }
}

// ✅ 使用 RepaintBoundary 隔离重绘
RepaintBoundary(
  child: ExpensiveWidget(),
)

// ✅ 使用 BlocBuilder 的 buildWhen 减少重建
BlocBuilder<CounterBloc, CounterState>(
  buildWhen: (previous, current) {
    // 只有计数值变化时才重建
    return previous.count != current.count;
  },
  builder: (context, state) {
    return Text('${state.count}');
  },
)
```

#### **2. 内存管理最佳实践**
```dart
// ✅ 及时释放资源
class MyWidget extends StatefulWidget {
  @override
  State<MyWidget> createState() => _MyWidgetState();
}

class _MyWidgetState extends State<MyWidget> {
  late StreamSubscription _subscription;
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(vsync: this);
    _subscription = someStream.listen((_) {});
  }

  @override
  void dispose() {
    _controller.dispose();
    _subscription.cancel();
    super.dispose();
  }
}
```

---

## 🎯 总结

这套新架构为您的金融应用提供了：

### 🏗️ **现代化的技术栈**
- **混合状态管理** - Provider + Bloc 的最佳实践
- **类型安全** - 编译时错误检查
- **事件驱动** - 清晰的业务逻辑流程

### 🛡️ **企业级的质量保证**
- **全局错误处理** - 完善的错误捕获和恢复
- **性能监控** - 实时的性能监控和优化
- **安全架构** - 多层安全防护机制

### 🚀 **优秀的开发体验**
- **自动化工具** - 完整的代码生成和迁移工具
- **完善的测试** - 全面的测试策略和工具
- **详细的文档** - 完整的架构指南和最佳实践

这套架构将为您的项目长期发展提供强有力的技术支撑，确保应用的稳定性、可维护性和扩展性。
```
```
