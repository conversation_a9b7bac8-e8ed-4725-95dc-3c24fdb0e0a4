# 📦 模块设计架构

本文档详细介绍了金融应用的模块化架构设计，包括模块划分原则、依赖关系和交互机制。

## 🎯 模块化架构概览

### 🏗️ 整体模块结构
```
financial_app_workspace/
├── packages/
│   ├── financial_app_main/          # 主应用模块 (架构层)
│   ├── financial_app_core/          # 核心共享模块 (基础设施层)
│   ├── financial_app_auth/          # 认证模块 (业务层)
│   ├── financial_app_market/        # 市场数据模块 (业务层)
│   ├── financial_app_trade/         # 交易模块 (业务层)
│   ├── financial_app_portfolio/     # 投资组合模块 (业务层)
│   ├── financial_app_notification/  # 通知模块 (业务层)
│   ├── financial_ws_client/         # WebSocket客户端 (技术层)
│   ├── financial_trading_chart/     # 交易图表模块 (技术层)
│   └── financial_app_assets/        # 资源模块 (支持层)
```

### 📊 模块分层架构
```
┌─────────────────────────────────────────────────────────────┐
│                    应用架构层                                │
│              financial_app_main                             │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                    业务模块层                                │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │    Auth     │ │   Market    │ │    Trade    │ │Portfolio│ │
│  │             │ │             │ │             │ │         │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
│  ┌─────────────┐                                             │
│  │Notification │                                             │
│  │             │                                             │
│  └─────────────┘                                             │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                    技术模块层                                │
│  ┌─────────────┐ ┌─────────────┐                             │
│  │ WebSocket   │ │Trading Chart│                             │
│  │   Client    │ │             │                             │
│  └─────────────┘ └─────────────┘                             │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                  基础设施层                                  │
│              financial_app_core                             │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                    支持层                                    │
│              financial_app_assets                           │
└─────────────────────────────────────────────────────────────┘
```

## 🏛️ 核心模块设计

### 📱 主应用模块 (financial_app_main)

#### 🎯 职责定义
- **应用启动和初始化**
- **模块集成和协调**
- **路由管理和导航**
- **全局状态管理配置**
- **依赖注入容器管理**

#### 🏗️ 架构设计
```dart
// 主应用模块结构
packages/financial_app_main/
├── lib/
│   ├── financial_app_main.dart      # 主导出文件
│   ├── app.dart                     # 应用入口
│   ├── main.dart                    # 程序入口
│   ├── di/                          # 依赖注入
│   │   └── injection_container.dart
│   ├── router/                      # 路由管理
│   │   └── app_router.dart
│   ├── providers/                   # Provider状态管理
│   │   ├── theme_provider.dart
│   │   └── locale_provider.dart
│   ├── src/
│   │   ├── core/                    # 核心架构
│   │   │   ├── app_bootstrap.dart
│   │   │   ├── module_manager.dart
│   │   │   └── hybrid_state_manager.dart
│   │   ├── di/                      # 模块依赖注入
│   │   │   └── module_injection.dart
│   │   └── performance/             # 性能优化
│   │       └── app_performance_monitor.dart
│   └── view/                        # 页面视图
│       ├── splash_page.dart
│       ├── home_page.dart
│       └── main_page.dart
```

#### 🔧 核心功能实现
```dart
/// 应用启动器
class AppBootstrap {
  static Future<void> initialize() async {
    // 1. Flutter绑定初始化
    WidgetsFlutterBinding.ensureInitialized();
    
    // 2. 全局错误处理
    FlutterError.onError = (details) {
      AppLogger.logModule('App').error('Flutter错误: ${details.exception}');
    };
    
    // 3. 依赖注入初始化
    await InjectionContainer.init();
    
    // 4. 模块管理器初始化
    await ModuleManager.initializeModules();
    
    // 5. 性能监控启动
    AppPerformanceMonitor.start();
  }
}

/// 模块管理器
class ModuleManager {
  static final List<String> _modules = [
    'core',
    'auth',
    'market',
    'trade',
    'portfolio',
    'notification',
    'websocket',
  ];
  
  static Future<void> initializeModules() async {
    for (final module in _modules) {
      await _initializeModule(module);
    }
  }
  
  static Future<void> _initializeModule(String moduleName) async {
    try {
      switch (moduleName) {
        case 'core':
          await CoreInjection.init();
          break;
        case 'auth':
          await AuthInjection.init();
          break;
        case 'market':
          await MarketInjection.init();
          break;
        // ... 其他模块
      }
      
      AppLogger.logModule('ModuleManager').info('✅ 模块初始化成功: $moduleName');
    } catch (e) {
      AppLogger.logModule('ModuleManager').error('❌ 模块初始化失败: $moduleName, 错误: $e');
      rethrow;
    }
  }
}
```

### 🛡️ 核心模块 (financial_app_core)

#### 🎯 职责定义
- **基础数据模型和状态**
- **网络服务和API客户端**
- **本地存储和缓存服务**
- **日志系统和工具类**
- **安全服务和加密**

#### 🏗️ 架构设计
```dart
// 核心模块结构
packages/financial_app_core/
├── lib/
│   ├── financial_app_core.dart      # 主导出文件
│   ├── core_injection.dart          # 依赖注入配置
│   └── src/
│       ├── config/                  # 配置管理
│       │   └── app_config.dart
│       ├── data/                    # 数据层
│       │   ├── datasources/
│       │   │   ├── local/
│       │   │   └── remote/
│       │   └── models/
│       ├── models/                  # 基础模型
│       │   ├── base_state.dart
│       │   ├── user_model.dart
│       │   └── trade_pair_model.dart
│       ├── services/                # 核心服务
│       │   ├── api_service.dart
│       │   ├── storage_service.dart
│       │   └── security_service.dart
│       ├── utils/                   # 工具类
│       │   ├── date_utils.dart
│       │   ├── format_utils.dart
│       │   └── validation_utils.dart
│       └── logger/                  # 日志系统
│           ├── app_logger.dart
│           └── logger_config.dart
```

#### 🔧 核心服务实现
```dart
/// API服务接口
abstract class ApiService {
  Future<Map<String, dynamic>> get(String path, {Map<String, dynamic>? params});
  Future<Map<String, dynamic>> post(String path, {Map<String, dynamic>? data});
  Future<Map<String, dynamic>> put(String path, {Map<String, dynamic>? data});
  Future<Map<String, dynamic>> delete(String path);
}

/// API服务实现
class ApiServiceImpl implements ApiService {
  final Dio _dio;
  final AppLogger _logger = AppLogger.logModule('ApiService');
  
  ApiServiceImpl(this._dio) {
    _setupInterceptors();
  }
  
  void _setupInterceptors() {
    _dio.interceptors.addAll([
      LogInterceptor(
        requestBody: true,
        responseBody: true,
        logPrint: (obj) => _logger.debug(obj.toString()),
      ),
      AuthInterceptor(),
      ErrorInterceptor(),
    ]);
  }
  
  @override
  Future<Map<String, dynamic>> get(String path, {Map<String, dynamic>? params}) async {
    try {
      final response = await _dio.get(path, queryParameters: params);
      return _handleResponse(response);
    } catch (e) {
      throw _handleError(e);
    }
  }
  
  Map<String, dynamic> _handleResponse(Response response) {
    if (response.statusCode == 200) {
      return response.data as Map<String, dynamic>;
    } else {
      throw ApiException('请求失败: ${response.statusCode}');
    }
  }
  
  Exception _handleError(dynamic error) {
    if (error is DioException) {
      switch (error.type) {
        case DioExceptionType.connectionTimeout:
          return NetworkException('连接超时');
        case DioExceptionType.receiveTimeout:
          return NetworkException('接收超时');
        case DioExceptionType.badResponse:
          return ApiException('服务器错误: ${error.response?.statusCode}');
        default:
          return NetworkException('网络错误');
      }
    }
    return UnknownException('未知错误: $error');
  }
}
```

## 🔐 业务模块设计

### 👤 认证模块 (financial_app_auth)

#### 🎯 功能职责
- **用户登录和注册**
- **身份验证和授权**
- **用户信息管理**
- **安全认证流程**

#### 🏗️ 标准三层架构
```dart
// 认证模块结构
packages/financial_app_auth/
├── lib/
│   ├── financial_app_auth.dart      # 主导出文件
│   ├── auth_injection.dart          # 依赖注入配置
│   └── src/
│       ├── data/                    # 数据层
│       │   ├── datasources/
│       │   │   ├── local/
│       │   │   │   └── auth_local_data_source.dart
│       │   │   └── remote/
│       │   │       └── auth_remote_data_source.dart
│       │   ├── models/
│       │   │   ├── login_request_model.dart
│       │   │   └── login_response_model.dart
│       │   └── repositories/
│       │       └── auth_repository_impl.dart
│       ├── domain/                  # 业务逻辑层
│       │   ├── entities/
│       │   │   └── user.dart
│       │   ├── repositories/
│       │   │   └── auth_repository.dart
│       │   └── usecases/
│       │       ├── login_user.dart
│       │       └── logout_user.dart
│       ├── presentation/            # 表现层
│       │   ├── bloc/
│       │   │   ├── auth_bloc.dart
│       │   │   ├── auth_event.dart
│       │   │   └── auth_state.dart
│       │   ├── pages/
│       │   │   ├── login_page.dart
│       │   │   └── register_page.dart
│       │   └── widgets/
│       │       └── auth_form_widget.dart
│       └── shared/                  # 共享组件
│           ├── constants/
│           ├── exceptions/
│           └── utils/
```

### 📊 市场数据模块 (financial_app_market)

#### 🎯 功能职责
- **实时行情数据获取**
- **K线图表数据处理**
- **市场分析和指标计算**
- **自选股管理**

#### 🔧 核心功能实现
```dart
/// 市场数据用例
class GetMarketData {
  final MarketRepository _repository;
  
  GetMarketData(this._repository);
  
  Future<List<MarketData>> call({
    required String symbol,
    String? interval,
    int? limit,
  }) async {
    return await _repository.getMarketData(
      symbol: symbol,
      interval: interval ?? '1m',
      limit: limit ?? 100,
    );
  }
}

/// 市场数据Bloc
class MarketBloc extends Bloc<MarketEvent, MarketState> {
  final GetMarketData _getMarketData;
  final AppLogger _logger = AppLogger.logModule('MarketBloc');
  
  MarketBloc(this._getMarketData) : super(MarketInitial()) {
    on<GetMarketDataEvent>(_onGetMarketData);
    on<SubscribeMarketDataEvent>(_onSubscribeMarketData);
  }
  
  Future<void> _onGetMarketData(
    GetMarketDataEvent event,
    Emitter<MarketState> emit,
  ) async {
    emit(MarketLoading());
    
    try {
      final data = await _getMarketData(
        symbol: event.symbol,
        interval: event.interval,
        limit: event.limit,
      );
      
      emit(MarketDataLoaded(data));
    } catch (e) {
      _logger.error('获取市场数据失败: $e');
      emit(MarketError('获取数据失败: $e'));
    }
  }
}
```

### 💰 交易模块 (financial_app_trade)

#### 🎯 功能职责
- **交易下单和撤单**
- **订单状态管理**
- **交易历史查询**
- **风险控制和验证**

#### 🔧 核心功能实现
```dart
/// 下单用例
class PlaceOrder {
  final TradeRepository _repository;
  final RiskManager _riskManager;
  
  PlaceOrder(this._repository, this._riskManager);
  
  Future<Order> call({
    required String symbol,
    required OrderSide side,
    required OrderType type,
    required double quantity,
    double? price,
  }) async {
    // 风险检查
    await _riskManager.validateOrder(
      symbol: symbol,
      side: side,
      quantity: quantity,
      price: price,
    );
    
    // 执行下单
    return await _repository.placeOrder(
      symbol: symbol,
      side: side,
      type: type,
      quantity: quantity,
      price: price,
    );
  }
}
```

## 🔗 模块依赖关系

### 📊 依赖关系图
```
┌─────────────────────────────────────────────────────────────┐
│                    模块依赖关系                              │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  financial_app_main                                         │
│         │                                                   │
│         ├─── financial_app_core (基础依赖)                  │
│         ├─── financial_app_auth                             │
│         ├─── financial_app_market                           │
│         ├─── financial_app_trade                            │
│         ├─── financial_app_portfolio                        │
│         ├─── financial_app_notification                     │
│         ├─── financial_ws_client                            │
│         └─── financial_trading_chart                        │
│                                                             │
│  业务模块依赖:                                              │
│  ├─ financial_app_auth → financial_app_core                │
│  ├─ financial_app_market → financial_app_core              │
│  │                      → financial_ws_client              │
│  ├─ financial_app_trade → financial_app_core               │
│  │                     → financial_app_auth                │
│  │                     → financial_app_market              │
│  ├─ financial_app_portfolio → financial_app_core           │
│  │                          → financial_app_auth           │
│  │                          → financial_app_market         │
│  └─ financial_app_notification → financial_app_core        │
│                                → financial_app_auth        │
└─────────────────────────────────────────────────────────────┘
```

### 🔧 依赖注入配置
```dart
/// 模块依赖注入管理
class ModuleInjection {
  static final Map<String, bool> _initializedModules = {};
  
  static Future<void> initializeModulesInOrder() async {
    // 按依赖顺序初始化模块
    final initOrder = [
      ModuleInfo('core', '核心模块', CoreInjection.init),
      ModuleInfo('websocket', 'WebSocket客户端', WebSocketInjection.init),
      ModuleInfo('auth', '认证模块', AuthInjection.init),
      ModuleInfo('market', '市场数据模块', MarketInjection.init),
      ModuleInfo('trade', '交易模块', TradeInjection.init),
      ModuleInfo('portfolio', '投资组合模块', PortfolioInjection.init),
      ModuleInfo('notification', '通知模块', NotificationInjection.init),
    ];
    
    for (final moduleInfo in initOrder) {
      await _initializeModule(moduleInfo);
    }
  }
  
  static Future<void> _initializeModule(ModuleInfo moduleInfo) async {
    if (_initializedModules[moduleInfo.name] == true) {
      return; // 已初始化，跳过
    }
    
    try {
      await moduleInfo.initFunction();
      _initializedModules[moduleInfo.name] = true;
      
      AppLogger.logModule('ModuleInjection')
          .info('✅ ${moduleInfo.description} 初始化成功');
    } catch (e) {
      AppLogger.logModule('ModuleInjection')
          .error('❌ ${moduleInfo.description} 初始化失败: $e');
      rethrow;
    }
  }
}

class ModuleInfo {
  final String name;
  final String description;
  final Future<void> Function() initFunction;
  
  const ModuleInfo(this.name, this.description, this.initFunction);
}
```

## 📈 模块通信机制

### 🔄 事件总线通信
```dart
/// 模块间事件通信
class ModuleEventBus {
  static final EventBus _eventBus = EventBus();
  
  /// 发布事件
  static void publish<T>(T event) {
    _eventBus.fire(event);
  }
  
  /// 订阅事件
  static StreamSubscription<T> subscribe<T>(void Function(T) handler) {
    return _eventBus.on<T>().listen(handler);
  }
}

/// 用户登录事件
class UserLoggedInEvent {
  final String userId;
  final DateTime timestamp;
  
  UserLoggedInEvent(this.userId) : timestamp = DateTime.now();
}

/// 交易完成事件
class TradeCompletedEvent {
  final String orderId;
  final String symbol;
  final double quantity;
  final DateTime timestamp;
  
  TradeCompletedEvent({
    required this.orderId,
    required this.symbol,
    required this.quantity,
  }) : timestamp = DateTime.now();
}
```

### 📡 WebSocket数据共享
```dart
/// WebSocket数据共享服务
class WebSocketDataService {
  static final GlobalWebSocketManager _wsManager = GlobalWebSocketManager();
  
  /// 订阅市场数据
  static Stream<MarketData> subscribeMarketData(String symbol) {
    return _wsManager
        .getChannelStream('market.$symbol')
        .where((message) => message.type == 'market_data')
        .map((message) => MarketData.fromJson(message.data));
  }
  
  /// 订阅交易更新
  static Stream<TradeUpdate> subscribeTradeUpdates(String userId) {
    return _wsManager
        .getChannelStream('trade.$userId')
        .where((message) => message.type == 'trade_update')
        .map((message) => TradeUpdate.fromJson(message.data));
  }
}
```

---

**📦 模块化架构设计确保了系统的可维护性、可扩展性和团队协作效率，为金融应用的长期发展奠定了坚实基础！**
