# 🔄 状态管理架构设计

本文档详细介绍了金融应用的混合状态管理架构，包括Provider和Bloc的使用策略、数据流设计和最佳实践。

## 🎯 架构概览

### 🏗️ 混合状态管理策略
金融应用采用Provider + Bloc混合状态管理架构，根据状态复杂度和业务需求选择合适的状态管理方案：

```
┌─────────────────────────────────────────────────────────────┐
│                    混合状态管理架构                          │
├─────────────────────────────────────────────────────────────┤
│  Provider (简单UI状态)     │  Bloc (复杂业务逻辑)          │
│  ├─ ThemeProvider          │  ├─ AuthBloc                  │
│  ├─ LocaleProvider         │  ├─ MarketBloc                │
│  └─ SettingsProvider       │  ├─ TradeBloc                 │
│                             │  ├─ PortfolioBloc             │
│                             │  ├─ NotificationBloc          │
│                             │  └─ WebSocketBloc             │
└─────────────────────────────────────────────────────────────┘
```

### 📊 选择标准
| 状态类型 | 复杂度 | 推荐方案 | 使用场景 |
|----------|--------|----------|----------|
| UI状态 | 简单 | Provider | 主题切换、语言设置、简单表单 |
| 业务状态 | 复杂 | Bloc | 用户认证、交易流程、数据获取 |
| 全局状态 | 中等 | Provider | 应用配置、用户偏好 |
| 异步状态 | 复杂 | Bloc | API调用、WebSocket连接 |

## 🎨 Provider状态管理

### 🎯 使用场景
Provider主要用于管理简单的UI状态和全局配置：
- 主题切换 (ThemeProvider)
- 多语言切换 (LocaleProvider)
- 用户偏好设置 (SettingsProvider)
- 简单的表单状态

### 📝 实现示例

#### 主题管理Provider
```dart
class ThemeProvider extends ChangeNotifier {
  ThemeMode _themeMode = ThemeMode.system;
  
  ThemeMode get themeMode => _themeMode;
  
  bool get isDarkMode {
    switch (_themeMode) {
      case ThemeMode.dark:
        return true;
      case ThemeMode.light:
        return false;
      case ThemeMode.system:
        return WidgetsBinding.instance.window.platformBrightness == Brightness.dark;
    }
  }
  
  void setThemeMode(ThemeMode mode) {
    if (_themeMode != mode) {
      _themeMode = mode;
      _saveThemePreference(mode);
      notifyListeners();
    }
  }
  
  void toggleTheme() {
    setThemeMode(isDarkMode ? ThemeMode.light : ThemeMode.dark);
  }
  
  Future<void> _saveThemePreference(ThemeMode mode) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('theme_mode', mode.toString());
  }
  
  Future<void> loadThemePreference() async {
    final prefs = await SharedPreferences.getInstance();
    final themeModeString = prefs.getString('theme_mode');
    if (themeModeString != null) {
      _themeMode = ThemeMode.values.firstWhere(
        (mode) => mode.toString() == themeModeString,
        orElse: () => ThemeMode.system,
      );
      notifyListeners();
    }
  }
}
```

#### 多语言管理Provider
```dart
class LocaleProvider extends ChangeNotifier {
  Locale _locale = const Locale('zh', 'CN');
  
  Locale get locale => _locale;
  
  List<Locale> get supportedLocales => const [
    Locale('zh', 'CN'), // 简体中文
    Locale('zh', 'TW'), // 繁体中文
    Locale('en', 'US'), // 英语
  ];
  
  void setLocale(Locale locale) {
    if (_locale != locale && supportedLocales.contains(locale)) {
      _locale = locale;
      _saveLocalePreference(locale);
      notifyListeners();
    }
  }
  
  Future<void> _saveLocalePreference(Locale locale) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('locale', '${locale.languageCode}_${locale.countryCode}');
  }
  
  Future<void> loadLocalePreference() async {
    final prefs = await SharedPreferences.getInstance();
    final localeString = prefs.getString('locale');
    if (localeString != null) {
      final parts = localeString.split('_');
      if (parts.length == 2) {
        final locale = Locale(parts[0], parts[1]);
        if (supportedLocales.contains(locale)) {
          _locale = locale;
          notifyListeners();
        }
      }
    }
  }
}
```

### 🔧 Provider配置
```dart
class HybridStateManager {
  static Widget createStateManagement({required Widget child}) {
    return MultiBlocProvider(
      providers: _createBlocProviders(),
      child: MultiProvider(
        providers: _createProviders(),
        child: child,
      ),
    );
  }
  
  static List<ChangeNotifierProvider> _createProviders() {
    return [
      ChangeNotifierProvider(
        create: (_) => ThemeProvider()..loadThemePreference(),
      ),
      ChangeNotifierProvider(
        create: (_) => LocaleProvider()..loadLocalePreference(),
      ),
      ChangeNotifierProvider(
        create: (_) => SettingsProvider()..loadSettings(),
      ),
    ];
  }
}
```

## 🧩 Bloc状态管理

### 🎯 使用场景
Bloc用于管理复杂的业务逻辑和异步状态：
- 用户认证流程 (AuthBloc)
- 市场数据管理 (MarketBloc)
- 交易流程管理 (TradeBloc)
- 投资组合管理 (PortfolioBloc)
- WebSocket连接管理 (WebSocketBloc)

### 📝 基础架构

#### 基础状态和事件
```dart
// 基础状态类
abstract class BaseState extends Equatable {
  const BaseState();
  
  @override
  List<Object?> get props => [];
}

class LoadingState extends BaseState {
  const LoadingState();
}

class SuccessState<T> extends BaseState {
  final T data;
  
  const SuccessState(this.data);
  
  @override
  List<Object?> get props => [data];
}

class ErrorState extends BaseState {
  final String message;
  final String? errorCode;
  
  const ErrorState({required this.message, this.errorCode});
  
  @override
  List<Object?> get props => [message, errorCode];
}

// 基础事件类
abstract class BaseEvent extends Equatable {
  const BaseEvent();
  
  @override
  List<Object?> get props => [];
}
```

#### 认证Bloc示例
```dart
// 认证事件
abstract class AuthEvent extends BaseEvent {}

class LoginEvent extends AuthEvent {
  final String username;
  final String password;
  
  const LoginEvent(this.username, this.password);
  
  @override
  List<Object?> get props => [username, password];
}

class LogoutEvent extends AuthEvent {}

class CheckAuthStatusEvent extends AuthEvent {}

// 认证状态
abstract class AuthState extends BaseState {}

class AuthInitial extends AuthState {}

class AuthLoading extends AuthState {}

class AuthSuccess extends AuthState {
  final User user;
  
  const AuthSuccess(this.user);
  
  @override
  List<Object?> get props => [user];
}

class AuthError extends AuthState {
  final String message;
  
  const AuthError(this.message);
  
  @override
  List<Object?> get props => [message];
}

// 认证Bloc
class AuthBloc extends Bloc<AuthEvent, AuthState> {
  final AuthRepository _authRepository;
  final AppLogger _logger = AppLogger.logModule('AuthBloc');
  
  AuthBloc(this._authRepository) : super(AuthInitial()) {
    on<LoginEvent>(_onLogin);
    on<LogoutEvent>(_onLogout);
    on<CheckAuthStatusEvent>(_onCheckAuthStatus);
  }
  
  Future<void> _onLogin(
    LoginEvent event,
    Emitter<AuthState> emit,
  ) async {
    _logger.info('开始登录流程: ${event.username}');
    emit(AuthLoading());
    
    try {
      final user = await _authRepository.login(
        event.username,
        event.password,
      );
      
      _logger.info('登录成功: ${user.id}');
      emit(AuthSuccess(user));
    } catch (e) {
      _logger.error('登录失败: $e');
      emit(AuthError(_getErrorMessage(e)));
    }
  }
  
  Future<void> _onLogout(
    LogoutEvent event,
    Emitter<AuthState> emit,
  ) async {
    _logger.info('开始登出流程');
    emit(AuthLoading());
    
    try {
      await _authRepository.logout();
      _logger.info('登出成功');
      emit(AuthInitial());
    } catch (e) {
      _logger.error('登出失败: $e');
      emit(AuthError(_getErrorMessage(e)));
    }
  }
  
  String _getErrorMessage(dynamic error) {
    if (error is AuthException) {
      return error.message;
    }
    return '发生未知错误，请稍后重试';
  }
}
```

### 🌐 WebSocket状态管理
```dart
// WebSocket事件
abstract class WebSocketEvent extends BaseEvent {}

class ConnectWebSocketEvent extends WebSocketEvent {}

class DisconnectWebSocketEvent extends WebSocketEvent {}

class SubscribeChannelEvent extends WebSocketEvent {
  final String channel;
  
  const SubscribeChannelEvent(this.channel);
  
  @override
  List<Object?> get props => [channel];
}

class WebSocketMessageReceivedEvent extends WebSocketEvent {
  final WebSocketMessage message;
  
  const WebSocketMessageReceivedEvent(this.message);
  
  @override
  List<Object?> get props => [message];
}

// WebSocket状态
abstract class WebSocketState extends BaseState {}

class WebSocketDisconnected extends WebSocketState {}

class WebSocketConnecting extends WebSocketState {}

class WebSocketConnected extends WebSocketState {
  final List<String> subscribedChannels;
  
  const WebSocketConnected(this.subscribedChannels);
  
  @override
  List<Object?> get props => [subscribedChannels];
}

class WebSocketError extends WebSocketState {
  final String message;
  
  const WebSocketError(this.message);
  
  @override
  List<Object?> get props => [message];
}

class WebSocketMessageReceived extends WebSocketState {
  final WebSocketMessage message;
  final List<String> subscribedChannels;
  
  const WebSocketMessageReceived(this.message, this.subscribedChannels);
  
  @override
  List<Object?> get props => [message, subscribedChannels];
}

// WebSocket Bloc
class WebSocketBloc extends Bloc<WebSocketEvent, WebSocketState> {
  final WebSocketService _webSocketService;
  final AppLogger _logger = AppLogger.logModule('WebSocketBloc');
  StreamSubscription? _messageSubscription;
  
  WebSocketBloc(this._webSocketService) : super(WebSocketDisconnected()) {
    on<ConnectWebSocketEvent>(_onConnect);
    on<DisconnectWebSocketEvent>(_onDisconnect);
    on<SubscribeChannelEvent>(_onSubscribeChannel);
    on<WebSocketMessageReceivedEvent>(_onMessageReceived);
  }
  
  Future<void> _onConnect(
    ConnectWebSocketEvent event,
    Emitter<WebSocketState> emit,
  ) async {
    _logger.info('开始WebSocket连接');
    emit(WebSocketConnecting());
    
    try {
      await _webSocketService.connect();
      
      // 监听消息
      _messageSubscription = _webSocketService.messageStream.listen(
        (message) => add(WebSocketMessageReceivedEvent(message)),
      );
      
      _logger.info('WebSocket连接成功');
      emit(const WebSocketConnected([]));
    } catch (e) {
      _logger.error('WebSocket连接失败: $e');
      emit(WebSocketError('连接失败: $e'));
    }
  }
  
  @override
  Future<void> close() {
    _messageSubscription?.cancel();
    return super.close();
  }
}
```

## 🔄 数据流设计

### 📊 单向数据流
```
┌─────────────┐    Event    ┌─────────────┐    State    ┌─────────────┐
│    UI       │ ─────────→  │    Bloc     │ ─────────→  │    UI       │
│  (Widget)   │             │ (Business   │             │  (Widget)   │
│             │             │   Logic)    │             │             │
└─────────────┘             └─────────────┘             └─────────────┘
                                   │
                                   ▼
                            ┌─────────────┐
                            │ Repository  │
                            │   (Data)    │
                            └─────────────┘
```

### 🌊 状态传播机制
```dart
// 状态监听和响应
class TradePage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return BlocListener<AuthBloc, AuthState>(
      listener: (context, state) {
        if (state is AuthError) {
          // 认证错误时跳转到登录页
          Navigator.pushReplacementNamed(context, '/login');
        }
      },
      child: BlocBuilder<TradeBloc, TradeState>(
        builder: (context, state) {
          if (state is TradeLoading) {
            return const LoadingWidget();
          } else if (state is TradeLoaded) {
            return TradeContentWidget(data: state.data);
          } else if (state is TradeError) {
            return ErrorWidget(message: state.message);
          }
          return const EmptyWidget();
        },
      ),
    );
  }
}
```

## 🔧 状态管理配置

### 🏗️ 依赖注入配置
```dart
class StateManagementInjection {
  static Future<void> init() async {
    final locator = GetIt.instance;
    
    // Provider注册
    locator.registerLazySingleton(() => ThemeProvider());
    locator.registerLazySingleton(() => LocaleProvider());
    
    // Bloc注册
    locator.registerFactory(() => AuthBloc(locator()));
    locator.registerFactory(() => MarketBloc(locator()));
    locator.registerFactory(() => TradeBloc(locator()));
    locator.registerFactory(() => PortfolioBloc(locator()));
    locator.registerFactory(() => WebSocketBloc(locator()));
  }
}
```

### 🎯 状态管理器初始化
```dart
class HybridStateManager {
  static Widget createStateManagement({required Widget child}) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(create: (_) => locator<AuthBloc>()),
        BlocProvider(create: (_) => locator<MarketBloc>()),
        BlocProvider(create: (_) => locator<TradeBloc>()),
        BlocProvider(create: (_) => locator<PortfolioBloc>()),
        BlocProvider(create: (_) => locator<WebSocketBloc>()),
      ],
      child: MultiProvider(
        providers: [
          ChangeNotifierProvider.value(value: locator<ThemeProvider>()),
          ChangeNotifierProvider.value(value: locator<LocaleProvider>()),
        ],
        child: child,
      ),
    );
  }
}
```

## 📈 性能优化

### 🚀 状态优化策略
1. **状态分割**: 将大状态拆分为小状态，减少不必要的重建
2. **选择性监听**: 使用BlocSelector只监听需要的状态变化
3. **状态缓存**: 缓存计算结果，避免重复计算
4. **懒加载**: 按需创建Bloc实例

### 💡 优化示例
```dart
// 使用BlocSelector优化性能
class UserNameWidget extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return BlocSelector<UserBloc, UserState, String>(
      selector: (state) {
        if (state is UserLoaded) {
          return state.user.name;
        }
        return '';
      },
      builder: (context, userName) {
        return Text(userName);
      },
    );
  }
}

// 使用Equatable优化状态比较
class UserState extends Equatable {
  final User user;
  final bool isLoading;
  
  const UserState({required this.user, required this.isLoading});
  
  @override
  List<Object?> get props => [user, isLoading];
  
  UserState copyWith({User? user, bool? isLoading}) {
    return UserState(
      user: user ?? this.user,
      isLoading: isLoading ?? this.isLoading,
    );
  }
}
```

## 🧪 测试策略

### 🔬 Bloc测试
```dart
void main() {
  group('AuthBloc', () {
    late AuthBloc authBloc;
    late MockAuthRepository mockRepository;
    
    setUp(() {
      mockRepository = MockAuthRepository();
      authBloc = AuthBloc(mockRepository);
    });
    
    blocTest<AuthBloc, AuthState>(
      '登录成功时应该发出正确的状态序列',
      build: () => authBloc,
      act: (bloc) => bloc.add(const LoginEvent('user', 'pass')),
      expect: () => [
        AuthLoading(),
        const AuthSuccess(User(id: '1', name: 'Test')),
      ],
    );
  });
}
```

### 🎨 Provider测试
```dart
void main() {
  group('ThemeProvider', () {
    late ThemeProvider themeProvider;
    
    setUp(() {
      themeProvider = ThemeProvider();
    });
    
    test('初始主题应该是系统主题', () {
      expect(themeProvider.themeMode, equals(ThemeMode.system));
    });
    
    test('切换主题应该通知监听者', () {
      bool notified = false;
      themeProvider.addListener(() => notified = true);
      
      themeProvider.setThemeMode(ThemeMode.dark);
      
      expect(themeProvider.themeMode, equals(ThemeMode.dark));
      expect(notified, isTrue);
    });
  });
}
```

---

**🔄 混合状态管理架构为应用提供了灵活、高效的状态管理解决方案，确保了良好的用户体验和开发体验！**
