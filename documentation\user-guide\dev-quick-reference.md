> **📋 文档迁移说明**
> 
> 本文档已从项目根目录迁移到统一的文档管理系统中。
> - **原文件名**: DEV_QUICK_REFERENCE.md
> - **迁移时间**: 2025-07-07T20:00:20.412854
> - **迁移工具**: scripts/migrate_docs.dart
> 
> 如需查看最新的文档结构，请参考 [文档中心](../README.md)。

---

# ⚡ 开发快速参考

## 🚀 **一键启动**

```bash
# 开发者工具箱 (推荐入口)
dart scripts/dev_toolbox.dart

# 快速环境设置
dart scripts/dev_environment_setup.dart setup
```

---

## 📦 **模块操作**

```bash
# 创建新模块
dart scripts/code_generator.dart module <module_name>

# 分析模块质量
dart scripts/module_structure_optimizer.dart analyze

# 优化模块结构
dart scripts/module_structure_optimizer.dart optimize
```

---

## 🧹 **代码质量**

```bash
# 全面重构
dart scripts/auto_refactor.dart all

# 仅格式化
dart scripts/auto_refactor.dart format

# 静态分析
dart analyze
```

---

## 🧪 **测试**

```bash
# 所有测试
melos test

# 单元测试
melos test_unit

# 测试覆盖率
melos test_coverage
```

---

## 📦 **包管理**

```bash
# 安装依赖
melos bootstrap

# 清理项目
melos clean

# 更新依赖
melos upgrade
```

---

## 🎯 **常用工作流**

### 新功能开发
```bash
git checkout -b feature/new-feature
dart scripts/code_generator.dart module new_feature
# 开发...
dart scripts/auto_refactor.dart all
melos test
git commit -m "feat: add new feature"
```

### Bug 修复
```bash
git checkout -b fix/bug-name
# 修复...
melos test_unit
git commit -m "fix: resolve bug"
```

---

## 🆘 **紧急修复**

```bash
# 环境问题
flutter doctor
melos clean && melos bootstrap

# 重置环境
dart scripts/dev_environment_setup.dart clean
dart scripts/dev_environment_setup.dart setup
```

---

## 📚 **文档**

- 📖 [开发指南](DEVELOPER_GUIDE.md)
- 🏗️ [架构标准](ARCHITECTURE_STANDARDS.md)  
- 🛠️ [工具指南](DEVELOPMENT_TOOLS_GUIDE.md)

**记住**: 不确定时先运行 `dart scripts/dev_toolbox.dart` ！
