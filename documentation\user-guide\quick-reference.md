> **📋 文档迁移说明**
> 
> 本文档已从项目根目录迁移到统一的文档管理系统中。
> - **原文件名**: QUICK_REFERENCE.md
> - **迁移时间**: 2025-07-07T20:00:20.430775
> - **迁移工具**: scripts/migrate_docs.dart
> 
> 如需查看最新的文档结构，请参考 [文档中心](../README.md)。

---

# 🚀 快速参考卡片

## 📋 日常开发常用命令

### 🔧 环境初始化
```bash
# 首次设置（仅需一次）
make setup                    # 或 melos bootstrap

# 日常开发启动
make dev-setup               # 完整开发环境
make generate-watch          # 启动代码生成监听
```

### 📝 代码开发流程
```bash
# 1. 代码检查
make quick-check             # 快速检查格式和分析

# 2. 运行测试
make test                    # 运行所有测试

# 3. 构建应用
make quick-build             # 快速构建（Web）
make build-all               # 构建所有平台
```

### 🧪 测试相关
```bash
# 生成测试模板
dart scripts/test_template_generator.dart packages/your_package/lib/src/your_file.dart

# 运行特定测试
melos test_unit              # 单元测试
melos test_widget            # Widget 测试
melos test_coverage          # 测试覆盖率

# 分析测试覆盖率
dart scripts/test_coverage_analyzer.dart
```

### 📊 性能分析
```bash
# 性能分析
make performance-analyze     # 或 dart scripts/performance_analyzer.dart

# 查看性能报告
make performance-report      # 显示性能评分

# 内存监控
make memory-test             # 启动内存监控
```

### 🚀 部署流程
```bash
# 测试环境部署
make deploy-staging          # 或 ./scripts/deploy_internal.sh deploy staging

# 生产环境部署
make deploy-production       # 或 ./scripts/deploy_internal.sh deploy production

# 回滚部署
make rollback-production     # 紧急回滚
```

---

## 🔍 故障排除快速指南

### ❌ 构建失败
```bash
make clean-deep              # 深度清理
make setup                   # 重新初始化
make generate                # 重新生成代码
```

### ❌ 测试失败
```bash
rm -rf .test_analysis_cache/ # 清理测试缓存
melos clean                  # 清理构建
melos test --no-cache        # 重新运行测试
```

### ❌ 依赖问题
```bash
make deps-outdated           # 检查过时依赖
make deps-upgrade            # 升级依赖
flutter doctor -v            # 检查环境
```

### ❌ Docker 问题
```bash
make docker-clean            # 清理 Docker 资源
docker system prune -f       # 清理系统资源
make docker-build            # 重新构建镜像
```

---

## 📊 文件结构快速导航

### 🛠️ 工具脚本位置
```
scripts/
├── performance_analyzer.dart      # 性能分析工具
├── test_coverage_analyzer.dart    # 测试覆盖率分析
├── test_template_generator.dart   # 测试模板生成器
├── build_optimization.dart        # 构建优化工具
└── deploy_internal.sh             # 内部部署脚本
```

### 📋 配置文件位置
```
├── melos.yaml                     # Melos 配置
├── Makefile                       # Make 命令配置
├── .gitlab-ci.yml                 # GitLab CI/CD 配置
├── docker-compose.yml             # Docker Compose 配置
└── config/environments.yml        # 环境配置
```

### 📊 报告文件位置
```
├── performance_report.json        # 性能分析报告
├── test_coverage_report.json      # 测试覆盖率报告
├── build_report.json             # 构建报告
└── .analysis_cache/               # 分析缓存目录
```

---

## 🎯 性能指标快速检查

### 📊 当前项目指标
```bash
# 性能评分
cat performance_report.json | grep "performance_score"

# 测试覆盖率
cat test_coverage_report.json | grep "coverage_percentage"

# 构建大小
ls -lh packages/financial_app_main/build/app/outputs/flutter-apk/
```

### 🎯 目标指标
- 📊 **性能评分**：> 90/100
- 🧪 **测试覆盖率**：> 70%
- 📦 **APK 大小**：< 100MB
- ⚡ **构建时间**：< 10分钟

---

## 🔗 重要链接

### 📚 文档
- [完整工具说明](TOOLS_AND_SCRIPTS_GUIDE.md)
- [性能优化报告](PERFORMANCE_OPTIMIZATION_REPORT.md)
- [测试覆盖率报告](TEST_COVERAGE_REPORT.md)
- [构建优化报告](BUILD_OPTIMIZATION_REPORT.md)

### 🌐 内部链接（需要根据实际环境调整）
- GitLab: `https://gitlab.company.com/financial-app`
- 测试环境: `https://financial-app-staging.company.com`
- 生产环境: `https://financial-app.company.com`
- 监控面板: `https://grafana.company.com`

---

## 💡 开发技巧

### 🚀 提高开发效率
1. **使用监听模式**：`make generate-watch` 自动代码生成
2. **快速检查**：`make quick-check` 提交前快速验证
3. **增量构建**：使用缓存加速构建过程
4. **并行测试**：`melos test --parallel` 加速测试

### 🧪 测试最佳实践
1. **先写测试**：使用 `test_template_generator.dart` 快速生成
2. **保持覆盖率**：每次提交检查 `test_coverage_analyzer.dart`
3. **分层测试**：单元测试 → Widget 测试 → 集成测试
4. **Mock 外部依赖**：使用 Mockito 隔离测试

### 🚀 部署最佳实践
1. **测试先行**：先部署到测试环境验证
2. **健康检查**：部署后执行健康检查
3. **回滚准备**：保持回滚脚本可用
4. **监控告警**：关注部署后的监控指标

---

## 📞 紧急情况处理

### 🚨 生产环境问题
```bash
# 1. 立即回滚
make rollback-production

# 2. 检查健康状态
./scripts/deploy_internal.sh health https://financial-app.company.com

# 3. 查看日志
make docker-logs

# 4. 重新部署（修复后）
make deploy-production
```

### 🚨 构建失败
```bash
# 1. 清理环境
make clean-deep

# 2. 重新初始化
make setup

# 3. 检查依赖
flutter doctor -v

# 4. 重新构建
make build-all
```

### 🚨 测试失败
```bash
# 1. 清理缓存
rm -rf .test_analysis_cache/

# 2. 重新运行
melos test --no-cache

# 3. 分析覆盖率
dart scripts/test_coverage_analyzer.dart

# 4. 生成缺失测试
dart scripts/test_template_generator.dart <file_path>
```

---

## 📝 备忘录

### ✅ 每日检查清单
- [ ] 运行 `make quick-check` 检查代码质量
- [ ] 运行 `make test` 确保测试通过
- [ ] 检查 `performance_report.json` 性能指标
- [ ] 提交前运行 `melos ci` 完整检查

### ✅ 每周检查清单
- [ ] 运行 `make deps-outdated` 检查依赖更新
- [ ] 运行 `dart scripts/test_coverage_analyzer.dart` 分析测试覆盖率
- [ ] 运行 `make performance-analyze` 性能分析
- [ ] 清理 `make docker-clean` Docker 资源

### ✅ 发布前检查清单
- [ ] 运行 `melos release` 完整发布流程
- [ ] 检查所有测试通过
- [ ] 验证性能指标达标
- [ ] 确认部署脚本可用
- [ ] 准备回滚方案

---

**💡 提示**：将此文档加入书签，方便随时查阅！
