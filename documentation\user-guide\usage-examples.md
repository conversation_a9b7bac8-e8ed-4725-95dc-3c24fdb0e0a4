> **📋 文档迁移说明**
> 
> 本文档已从项目根目录迁移到统一的文档管理系统中。
> - **原文件名**: USAGE_EXAMPLES.md
> - **迁移时间**: 2025-07-07T20:00:20.438737
> - **迁移工具**: scripts/migrate_docs.dart
> 
> 如需查看最新的文档结构，请参考 [文档中心](../README.md)。

---

# 📚 工具和脚本使用示例

## 🎯 实际使用场景和示例

### 场景 1：新功能开发完整流程

#### 背景
开发一个新的用户认证功能，需要添加 `AuthService` 类。

#### 步骤演示

**1. 创建源文件**
```bash
# 创建新的服务文件
touch packages/financial_app_auth/lib/src/services/auth_service.dart
```

**2. 生成测试模板**
```bash
# 为新文件生成测试模板
dart scripts/test_template_generator.dart packages/financial_app_auth/lib/src/services/auth_service.dart

# 输出示例：
# 🔍 分析源文件: packages/financial_app_auth/lib/src/services/auth_service.dart
# ✅ 测试模板已生成: packages/financial_app_auth/test/src/services/auth_service_test.dart
# 📝 包含 1 个类的测试模板
```

**3. 开发过程中的检查**
```bash
# 启动代码生成监听（在开发过程中保持运行）
make generate-watch

# 快速代码检查
make quick-check

# 输出示例：
# 🎨 格式化代码...
# 🔍 静态代码分析...
# ✅ 快速检查完成
```

**4. 测试开发**
```bash
# 运行单元测试
melos test_unit

# 检查测试覆盖率
dart scripts/test_coverage_analyzer.dart

# 输出示例：
# 🧪 开始测试覆盖率分析...
# 🔍 分析现有测试文件...
#    发现 16 个测试文件
#    有效测试文件: 6
#    总测试用例: 28
# ✅ 测试覆盖率分析完成！
```

**5. 提交前完整检查**
```bash
# 运行完整 CI 流程
melos ci

# 输出示例：
# 📦 Bootstrap 项目...
# 🎨 检查代码格式...
# 🔍 静态代码分析...
# 🧪 运行测试...
# ✅ CI 检查完成
```

---

### 场景 2：性能问题排查

#### 背景
应用启动变慢，需要分析性能瓶颈。

#### 步骤演示

**1. 运行性能分析**
```bash
# 执行全面性能分析
dart scripts/performance_analyzer.dart

# 输出示例：
# 🔍 开始性能分析...
# 📦 分析构建产物大小...
#    Android: 70.2MB
#    Web: 15.3MB
# 📚 分析依赖包大小...
#    发现 15 个包
#    总共 45 个唯一依赖
# 🧮 分析代码复杂度...
#    总文件数: 506
#    总代码行数: 66,643
#    平均每文件行数: 132
# ✅ 性能分析完成！
# 📊 详细报告已保存到 performance_report.json
```

**2. 查看性能报告**
```bash
# 快速查看性能评分
make performance-report

# 输出示例：
# 性能评分: 100/100

# 查看详细报告
cat performance_report.json | jq '.recommendations'

# 输出示例：
# [
#   {
#     "category": "Build Size",
#     "priority": "Medium",
#     "issue": "Web 构建产物较大 (15.3MB)",
#     "solution": "启用代码分割、压缩资源"
#   }
# ]
```

**3. 内存监控测试**
```bash
# 启动内存监控测试
make memory-test

# 在应用代码中启用内存监控
# MemoryOptimizer().startMonitoring();

# 查看内存使用报告
# final report = MemoryOptimizer().getMemoryReport();
# print('当前内存使用: ${report['current_usage']}');
```

---

### 场景 3：测试覆盖率提升

#### 背景
项目测试覆盖率较低，需要系统性提升。

#### 步骤演示

**1. 分析当前测试状况**
```bash
# 分析测试覆盖率
dart scripts/test_coverage_analyzer.dart

# 输出示例：
# 🧪 开始测试覆盖率分析...
# 🔍 分析现有测试文件...
#    发现 15 个测试文件
#    有效测试文件: 3
#    空测试文件: 12
#    总测试用例: 8
# 📁 分析源代码文件...
#    发现 506 个源文件
#    总类数: 156
#    总方法数: 892
#    总函数数: 234
# 📊 计算测试覆盖率...
#    可测试项目: 1282
#    现有测试: 8
#    覆盖率: 1%
#    覆盖等级: critical
# 💡 生成测试建议...
#    生成 158 个测试建议
# ✅ 测试覆盖率分析完成！
```

**2. 批量生成测试模板**
```bash
# 为所有核心文件生成测试模板
find packages/financial_app_core/lib/src -name "*.dart" -not -path "*/generated/*" | while read file; do
  echo "生成测试模板: $file"
  dart scripts/test_template_generator.dart "$file"
done

# 输出示例：
# 生成测试模板: packages/financial_app_core/lib/src/services/user_service.dart
# ✅ 测试模板已生成: packages/financial_app_core/test/src/services/user_service_test.dart
# 📝 包含 1 个类的测试模板
```

**3. 运行测试并检查覆盖率**
```bash
# 运行测试覆盖率
melos test_coverage

# 生成测试报告
melos test_report

# 输出示例：
# 🧪 运行测试...
# 📊 生成覆盖率报告...
# 🔍 分析测试覆盖率...
# 📊 测试报告已生成
```

---

### 场景 4：生产环境部署

#### 背景
新版本开发完成，需要部署到生产环境。

#### 步骤演示

**1. 部署前检查**
```bash
# 运行完整发布流程检查
melos release

# 输出示例：
# 🔄 运行 CI 流程...
# 📊 生成测试报告...
# 🔨 构建所有平台...
# 🎉 发布流程完成
```

**2. 构建优化**
```bash
# 运行优化构建
dart scripts/build_optimization.dart all

# 输出示例：
# 🚀 开始构建优化流程...
# 📱 目标平台: all
# 🔍 执行预构建检查...
#    检查 Flutter 环境...
#    检查 Melos 安装...
#    检查项目结构...
#    ✅ 预构建检查完成
# 📦 优化依赖管理...
#    执行 Melos Bootstrap...
#    清理构建缓存...
#    重新获取依赖...
#    ✅ 依赖优化完成
# ⚙️ 执行代码生成...
#    运行 build_runner...
#    生成国际化文件...
#    ✅ 代码生成完成
# 🔨 构建应用...
#    🤖 构建 Android 应用...
#       ✅ Android 构建完成
#    🌐 构建 Web 应用...
#       ✅ Web 构建完成
#    ✅ 应用构建完成
# 🔧 执行构建后处理...
#    📊 生成构建报告...
#       构建报告已保存到: build_report.json
#    🗜️ 优化构建产物...
#    📝 生成部署脚本...
#       部署脚本已生成: deploy_all.sh
#    ✅ 构建后处理完成
# ✅ 构建优化完成！
```

**3. 部署到测试环境**
```bash
# 部署到测试环境
./scripts/deploy_internal.sh deploy staging

# 输出示例：
# 🔍 检查必要工具...
# ✅ docker 已安装
# ✅ docker-compose 已安装
# ✅ kubectl 已安装
# 🚀 部署到 staging 环境 (staging.company.com)...
# 📦 部署构建产物...
# ✅ staging 环境部署完成
```

**4. 健康检查**
```bash
# 检查部署状态
./scripts/deploy_internal.sh health https://financial-app-staging.company.com

# 输出示例：
# 🏥 执行健康检查: https://financial-app-staging.company.com
# ✅ 健康检查通过
```

**5. 部署到生产环境**
```bash
# 部署到生产环境
./scripts/deploy_internal.sh deploy production

# 输出示例：
# 🚀 部署到 production 环境 (production.company.com)...
# 📦 部署构建产物...
# ✅ production 环境部署完成
```

---

### 场景 5：紧急回滚

#### 背景
生产环境出现问题，需要紧急回滚。

#### 步骤演示

**1. 立即回滚**
```bash
# 紧急回滚生产环境
make rollback-production

# 或者使用部署脚本
./scripts/deploy_internal.sh rollback production

# 输出示例：
# 🔄 回滚 production 环境...
# ✅ 回滚完成
```

**2. 验证回滚结果**
```bash
# 健康检查
./scripts/deploy_internal.sh health https://financial-app.company.com

# 查看应用日志
make docker-logs

# 输出示例：
# 🏥 执行健康检查: https://financial-app.company.com
# ✅ 健康检查通过
```

---

### 场景 6：日常维护

#### 背景
定期维护项目，检查依赖更新和性能状况。

#### 步骤演示

**1. 检查项目状态**
```bash
# 查看项目整体状态
make status

# 输出示例：
# 📊 项目状态:
# Flutter 版本: Flutter 3.32.4 • channel stable
# Dart 版本: Dart 3.5.4
# Melos 版本: 6.1.0
# Docker 版本: Docker version 24.0.5
# 
# 📦 包数量:
#   包总数: 15
```

**2. 检查依赖更新**
```bash
# 检查过时的依赖
make deps-outdated

# 输出示例：
# 🔍 检查过时依赖...
# Package 'dio' can be upgraded from 5.3.2 to 5.4.0
# Package 'provider' can be upgraded from 6.0.5 to 6.1.0
```

**3. 性能基准测试**
```bash
# 运行性能基准测试
make benchmark

# 输出示例：
# ⚡ 运行性能基准测试...
# 🔍 运行性能分析...
# 📊 Web 性能分析构建...
# ✅ 性能基准测试完成
```

**4. 清理资源**
```bash
# 清理 Docker 资源
make docker-clean

# 深度清理项目
make clean-deep

# 输出示例：
# 🧹 清理 Docker 资源...
# 🧹 深度清理...
# ✅ 清理完成
```

---

## 💡 高级使用技巧

### 1. 自动化脚本组合
```bash
# 创建自定义工作流脚本
cat > scripts/daily_check.sh << 'EOF'
#!/bin/bash
echo "🔍 执行每日检查..."
make quick-check
dart scripts/performance_analyzer.dart
dart scripts/test_coverage_analyzer.dart
echo "✅ 每日检查完成"
EOF

chmod +x scripts/daily_check.sh
./scripts/daily_check.sh
```

### 2. 监控集成
```bash
# 设置性能监控告警
cat > scripts/performance_monitor.sh << 'EOF'
#!/bin/bash
SCORE=$(dart scripts/performance_analyzer.dart | grep "性能评分" | cut -d: -f2 | tr -d ' ')
if [ "$SCORE" -lt 80 ]; then
  echo "⚠️ 性能评分过低: $SCORE"
  # 发送告警通知
fi
EOF
```

### 3. CI/CD 集成示例
```yaml
# .gitlab-ci.yml 中的使用示例
performance-check:
  stage: test
  script:
    - dart scripts/performance_analyzer.dart
    - SCORE=$(cat performance_report.json | jq '.performance_score')
    - if [ "$SCORE" -lt 80 ]; then exit 1; fi
  artifacts:
    reports:
      performance: performance_report.json
```

---

**📝 注意**：这些示例基于实际的工具和脚本，可以直接在项目中使用。根据具体需求调整参数和配置。
