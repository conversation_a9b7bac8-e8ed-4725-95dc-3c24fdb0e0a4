# Financial App Assets - 资源管理 Makefile
# 提供便捷的资源管理命令

.PHONY: help validate optimize manifest stats sync clean install test build

# 默认目标
help:
	@echo "📦 Financial App Assets - 资源管理工具"
	@echo ""
	@echo "可用命令:"
	@echo "  make validate     - 验证所有资源文件"
	@echo "  make optimize     - 优化资源文件"
	@echo "  make manifest     - 生成资源清单"
	@echo "  make stats        - 生成统计报告"
	@echo "  make sync         - 同步资源文件"
	@echo "  make clean        - 清理临时文件"
	@echo "  make install      - 安装依赖"
	@echo "  make test         - 运行测试"
	@echo "  make build        - 构建包"
	@echo "  make check-all    - 执行完整检查"
	@echo ""
	@echo "示例:"
	@echo "  make validate VERBOSE=1    - 详细验证"
	@echo "  make optimize DRY_RUN=1    - 预览优化"
	@echo "  make stats FORMAT=csv      - CSV 格式统计"

# 变量定义
DART := dart
SCRIPT_DIR := scripts
ASSET_MANAGER := $(SCRIPT_DIR)/asset_manager.dart
VERBOSE_FLAG := $(if $(VERBOSE),--verbose,)
DRY_RUN_FLAG := $(if $(DRY_RUN),--dry-run,)
FORMAT_FLAG := $(if $(FORMAT),--format $(FORMAT),)

# 验证资源文件
validate:
	@echo "🔍 验证资源文件..."
	$(DART) $(ASSET_MANAGER) validate $(VERBOSE_FLAG)
	@echo "✅ 资源验证完成"

# 优化资源文件
optimize:
	@echo "🚀 优化资源文件..."
	$(DART) $(ASSET_MANAGER) optimize $(VERBOSE_FLAG) $(DRY_RUN_FLAG)
	@echo "✅ 资源优化完成"

# 生成资源清单
manifest:
	@echo "📋 生成资源清单..."
	$(DART) $(ASSET_MANAGER) generate-manifest $(VERBOSE_FLAG)
	@echo "✅ 资源清单生成完成"

# 生成统计报告
stats:
	@echo "📊 生成统计报告..."
	$(DART) $(ASSET_MANAGER) stats $(FORMAT_FLAG)

# 同步资源文件
sync:
	@echo "🔄 同步资源文件..."
	$(DART) $(ASSET_MANAGER) sync $(VERBOSE_FLAG)
	@echo "✅ 资源同步完成"

# 清理临时文件
clean:
	@echo "🧹 清理临时文件..."
	$(DART) $(ASSET_MANAGER) clean $(VERBOSE_FLAG)
	@echo "✅ 清理完成"

# 安装依赖
install:
	@echo "📦 安装依赖..."
	flutter pub get
	@echo "✅ 依赖安装完成"

# 运行测试
test:
	@echo "🧪 运行测试..."
	flutter test
	@echo "✅ 测试完成"

# 构建包
build:
	@echo "🔨 构建包..."
	flutter packages pub publish --dry-run
	@echo "✅ 构建完成"

# 执行完整检查
check-all: install validate test stats
	@echo "🎯 完整检查完成"

# 开发环境设置
dev-setup: install
	@echo "🛠️ 设置开发环境..."
	@echo "创建示例资源..."
	@mkdir -p assets/images/logos
	@mkdir -p assets/images/icons
	@mkdir -p assets/images/backgrounds
	@mkdir -p assets/images/coins
	@mkdir -p assets/fonts
	@mkdir -p assets/configs
	@echo "✅ 开发环境设置完成"

# 生成代码
generate:
	@echo "⚙️ 生成代码..."
	flutter packages pub run build_runner build
	@echo "✅ 代码生成完成"

# 格式化代码
format:
	@echo "🎨 格式化代码..."
	dart format lib/ test/
	@echo "✅ 代码格式化完成"

# 分析代码
analyze:
	@echo "🔍 分析代码..."
	flutter analyze
	@echo "✅ 代码分析完成"

# 更新依赖
upgrade:
	@echo "⬆️ 更新依赖..."
	flutter pub upgrade
	@echo "✅ 依赖更新完成"

# 发布准备
publish-check: clean install analyze test validate
	@echo "📦 发布前检查..."
	flutter packages pub publish --dry-run
	@echo "✅ 发布检查完成"

# 监控资源使用
monitor:
	@echo "📊 监控资源使用..."
	@while true; do \
		clear; \
		echo "=== 资源使用监控 ==="; \
		$(DART) $(ASSET_MANAGER) stats --format table; \
		echo ""; \
		echo "按 Ctrl+C 退出监控"; \
		sleep 5; \
	done

# 备份资源
backup:
	@echo "💾 备份资源..."
	@BACKUP_DIR="backup_$$(date +%Y%m%d_%H%M%S)"; \
	mkdir -p $$BACKUP_DIR; \
	cp -r assets/ $$BACKUP_DIR/; \
	tar -czf $$BACKUP_DIR.tar.gz $$BACKUP_DIR/; \
	rm -rf $$BACKUP_DIR; \
	echo "✅ 资源备份完成: $$BACKUP_DIR.tar.gz"

# 恢复资源
restore:
	@echo "🔄 恢复资源..."
	@if [ -z "$(BACKUP)" ]; then \
		echo "❌ 请指定备份文件: make restore BACKUP=backup_20240101_120000.tar.gz"; \
		exit 1; \
	fi
	@if [ ! -f "$(BACKUP)" ]; then \
		echo "❌ 备份文件不存在: $(BACKUP)"; \
		exit 1; \
	fi
	tar -xzf $(BACKUP)
	@BACKUP_DIR=$$(basename $(BACKUP) .tar.gz); \
	cp -r $$BACKUP_DIR/assets/ .; \
	rm -rf $$BACKUP_DIR
	@echo "✅ 资源恢复完成"

# 资源大小检查
size-check:
	@echo "📏 检查资源大小..."
	@find assets -type f -exec ls -lh {} \; | \
	awk '{print $$5 " " $$9}' | \
	sort -hr | \
	head -20
	@echo ""
	@echo "总大小:"
	@du -sh assets/

# 重复文件检查
duplicate-check:
	@echo "🔍 检查重复文件..."
	@find assets -type f -exec md5sum {} \; | \
	sort | \
	uniq -d -w 32 | \
	cut -c 35-

# 资源完整性检查
integrity-check:
	@echo "🔒 检查资源完整性..."
	@find assets -type f -name "*.png" -o -name "*.jpg" -o -name "*.jpeg" | \
	while read file; do \
		if ! file "$$file" | grep -q "image"; then \
			echo "❌ 损坏的图片文件: $$file"; \
		fi; \
	done
	@echo "✅ 完整性检查完成"

# 性能测试
perf-test:
	@echo "⚡ 性能测试..."
	@echo "测试资源加载性能..."
	@time $(DART) -c "import 'lib/financial_app_assets.dart'; void main() async { await AssetManager.instance.initialize(); }"
	@echo "✅ 性能测试完成"

# 文档生成
docs:
	@echo "📚 生成文档..."
	dart doc .
	@echo "✅ 文档生成完成"

# 版本信息
version:
	@echo "📋 版本信息:"
	@grep "version:" pubspec.yaml
	@echo "Flutter: $$(flutter --version | head -1)"
	@echo "Dart: $$(dart --version)"

# 项目信息
info:
	@echo "📦 项目信息:"
	@echo "名称: $$(grep "name:" pubspec.yaml | cut -d' ' -f2)"
	@echo "描述: $$(grep "description:" pubspec.yaml | cut -d'"' -f2)"
	@echo "版本: $$(grep "version:" pubspec.yaml | cut -d' ' -f2)"
	@echo "资源文件数: $$(find assets -type f | wc -l)"
	@echo "总大小: $$(du -sh assets | cut -f1)"

# 快速开始
quick-start: dev-setup validate manifest
	@echo "🚀 快速开始完成！"
	@echo ""
	@echo "下一步:"
	@echo "1. 添加您的资源文件到 assets/ 目录"
	@echo "2. 运行 'make validate' 验证资源"
	@echo "3. 运行 'make optimize' 优化资源"
	@echo "4. 在您的应用中使用资源"
