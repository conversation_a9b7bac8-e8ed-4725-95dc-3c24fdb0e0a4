# 📦 Financial App Assets

企业级共享资源模块 - 统一管理图片、字体、图标等静态资源

## 🌟 特性

### 🚀 核心功能
- **智能资源管理** - 自动缓存、预加载、优化
- **多格式支持** - PNG、JPG、SVG、WebP、字体、动画
- **性能优化** - 内存和磁盘双重缓存，懒加载
- **错误处理** - 完善的错误处理和回退机制
- **团队协作** - 统一的资源规范和管理工具

### 🛠️ 高级特性
- **资源预加载** - 关键资源智能预加载
- **缓存策略** - 多层缓存机制，自动清理
- **资源验证** - 完整性检查和格式验证
- **统计分析** - 资源使用情况统计和监控
- **自动化工具** - 资源优化、验证、同步脚本

## 🚀 快速开始

### 1. 添加依赖

在您的 `pubspec.yaml` 中添加：

```yaml
dependencies:
  financial_app_assets:
    path: ../financial_app_assets
```

### 2. 初始化资源管理器

```dart
import 'package:financial_app_assets/financial_app_assets.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // 初始化资源管理器
  await AssetManager.instance.initialize();

  // 预加载关键资源
  await ImageAssets.preloadCriticalImages();

  runApp(MyApp());
}
```

### 3. 使用图片资源

```dart
// 方式 1: 使用 AppImages 常量
Image.asset(
  AppImages.appLogo,
  package: AppImages.packageName,
  width: 100,
  height: 100,
)

// 方式 2: 使用便捷方法
AppImages.image(
  AppImages.coinBtc,
  width: 32,
  height: 32,
)

// 方式 3: 使用优化组件
AssetImage(
  imagePath: AppImages.splashBackground,
  width: double.infinity,
  height: 200,
  fit: BoxFit.cover,
  enableCache: true,
  fallbackImage: AppImages.errorState,
)
```

### 4. 使用字体

```dart
Text(
  '¥1,234.56',
  style: AppFonts.priceStyle.copyWith(
    color: Colors.green,
    fontSize: 24,
  ),
)

Text(
  '投资组合',
  style: AppFonts.titleStyle,
)
```

### 5. 使用图标

```dart
Icon(
  AppIcons.home,
  size: 24,
  color: Colors.blue,
)

// 或使用便捷方法
AppIcons.createIcon(
  AppIcons.trade,
  size: 32,
  color: Theme.of(context).primaryColor,
)
```

## 🛠️ 管理工具

### 资源验证

```bash
# 验证所有资源
dart scripts/asset_manager.dart validate --verbose

# 优化资源
dart scripts/asset_manager.dart optimize --dry-run

# 生成清单
dart scripts/asset_manager.dart generate-manifest

# 统计分析
dart scripts/asset_manager.dart stats --format csv
```

## 📊 性能监控

### 缓存统计

```dart
// 获取缓存统计
final stats = AssetManager.instance.getUsageStats();
print('最常用资源: ${stats.entries.first.key}');

// 获取缓存大小
final cacheSize = await AssetManager.instance.getCacheSize();
print('缓存大小: ${AssetUtils.formatFileSize(cacheSize)}');
```

## 🎨 最佳实践

### 1. 资源命名规范

```
- 使用描述性名称: `app_logo.png` 而不是 `img1.png`
- 使用下划线分隔: `coin_btc.png`
- 包含尺寸信息: `icon_home_24x24.png`
- 使用一致的前缀: `bg_`, `icon_`, `coin_`
```

### 2. 性能优化

```dart
// 预加载关键资源
await ImageAssets.preloadCriticalImages();

// 使用适当的图片格式
- PNG: 透明图片、图标
- JPG: 照片、复杂图像
- WebP: 现代浏览器优化
- SVG: 矢量图标、简单图形
```

### 3. 错误处理

```dart
AssetImage(
  imagePath: AppImages.userAvatar,
  fallbackImage: AppImages.defaultAvatar,
  errorWidget: Container(
    child: Icon(Icons.person),
  ),
)
```

## 📈 版本历史

### v1.0.0 (2024-01-01)
- ✨ 初始版本发布
- 🚀 核心资源管理功能
- 📦 图片、字体、图标支持
- 🛠️ 管理工具和脚本

---

**让资源管理变得简单高效！** 🚀✨
