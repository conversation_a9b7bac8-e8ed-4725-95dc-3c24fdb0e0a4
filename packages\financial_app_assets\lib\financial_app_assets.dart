/// 企业级共享资源模块
///
/// 提供统一的静态资源管理，包括：
/// - 图片资源管理和优化
/// - 字体资源配置
/// - 图标资源系统
/// - 动画资源管理
/// - 资源缓存和预加载
library;

// 核心资源管理
export 'src/core/asset_manager.dart';
export 'src/core/asset_config.dart';
export 'src/core/asset_cache.dart';

// 图片资源
export 'src/images/app_images.dart';
export 'src/images/image_assets.dart';
export 'src/images/optimized_image.dart';

// 图标资源
export 'src/icons/app_icons.dart';
export 'src/icons/icon_assets.dart';
export 'src/icons/financial_icons.dart';

// 字体资源
export 'src/fonts/app_fonts.dart' hide FontConfig;
export 'src/fonts/font_assets.dart' hide FontConfig;

// 动画资源
export 'src/animations/animation_assets.dart' hide AnimationConfig;
export 'src/animations/lottie_assets.dart';

// 工具类
export 'src/utils/asset_utils.dart';
export 'src/utils/image_utils.dart';
export 'src/utils/size_utils.dart';

// 组件
export 'src/widgets/asset_image.dart';
export 'src/widgets/asset_icon.dart';
export 'src/widgets/cached_asset_image.dart' hide CacheConfig;

// 常量
export 'src/constants/asset_constants.dart';
export 'src/constants/image_constants.dart';
export 'src/constants/icon_constants.dart';

// 日志管理器
export 'src/utils/assets_logger.dart';
