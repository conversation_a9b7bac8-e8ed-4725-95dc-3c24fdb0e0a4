/// 动画资源路径管理
/// 
/// 管理所有动画文件的路径和配置
class AnimationAssets {
  AnimationAssets._();

  // 基础路径
  static const String _basePath = 'assets/animations';
  static const String _lottieBasePath = '$_basePath/lottie';
  static const String _rifBasePath = '$_basePath/rive';

  // ==================== Lottie 动画 ====================
  
  // 加载动画
  static const String loadingDots = '$_lottieBasePath/loading/loading_dots.json';
  static const String loadingSpinner = '$_lottieBasePath/loading/loading_spinner.json';
  static const String loadingPulse = '$_lottieBasePath/loading/loading_pulse.json';
  static const String loadingWave = '$_lottieBasePath/loading/loading_wave.json';
  
  // 成功动画
  static const String successCheck = '$_lottieBasePath/success/success_check.json';
  static const String successConfetti = '$_lottieBasePath/success/success_confetti.json';
  static const String successTick = '$_lottieBasePath/success/success_tick.json';
  
  // 错误动画
  static const String errorCross = '$_lottieBasePath/error/error_cross.json';
  static const String errorShake = '$_lottieBasePath/error/error_shake.json';
  static const String errorAlert = '$_lottieBasePath/error/error_alert.json';
  
  // 空状态动画
  static const String emptyBox = '$_lottieBasePath/empty/empty_box.json';
  static const String emptySearch = '$_lottieBasePath/empty/empty_search.json';
  static const String emptyData = '$_lottieBasePath/empty/empty_data.json';
  static const String emptyNetwork = '$_lottieBasePath/empty/empty_network.json';
  
  // 金融相关动画
  static const String coinFlip = '$_lottieBasePath/finance/coin_flip.json';
  static const String moneyGrow = '$_lottieBasePath/finance/money_grow.json';
  static const String chartUp = '$_lottieBasePath/finance/chart_up.json';
  static const String chartDown = '$_lottieBasePath/finance/chart_down.json';
  static const String tradingSignal = '$_lottieBasePath/finance/trading_signal.json';
  static const String portfolioGrowth = '$_lottieBasePath/finance/portfolio_growth.json';
  
  // 交互动画
  static const String buttonPress = '$_lottieBasePath/interaction/button_press.json';
  static const String swipeLeft = '$_lottieBasePath/interaction/swipe_left.json';
  static const String swipeRight = '$_lottieBasePath/interaction/swipe_right.json';
  static const String pullToRefresh = '$_lottieBasePath/interaction/pull_to_refresh.json';
  
  // 引导动画
  static const String onboardingStep1 = '$_lottieBasePath/onboarding/step1.json';
  static const String onboardingStep2 = '$_lottieBasePath/onboarding/step2.json';
  static const String onboardingStep3 = '$_lottieBasePath/onboarding/step3.json';
  static const String onboardingWelcome = '$_lottieBasePath/onboarding/welcome.json';
  
  // 通知动画
  static const String notificationBell = '$_lottieBasePath/notification/bell.json';
  static const String notificationPop = '$_lottieBasePath/notification/pop.json';
  static const String notificationSlide = '$_lottieBasePath/notification/slide.json';

  // ==================== Rive 动画 ====================
  
  // 交互式图标
  static const String menuToggle = '$_rifBasePath/icons/menu_toggle.riv';
  static const String heartLike = '$_rifBasePath/icons/heart_like.riv';
  static const String starFavorite = '$_rifBasePath/icons/star_favorite.riv';
  static const String playPause = '$_rifBasePath/icons/play_pause.riv';
  
  // 复杂交互
  static const String tradingDashboard = '$_rifBasePath/trading/dashboard.riv';
  static const String portfolioChart = '$_rifBasePath/portfolio/chart.riv';
  static const String marketTicker = '$_rifBasePath/market/ticker.riv';

  // ==================== 动画配置 ====================
  
  /// 动画配置信息
  static const Map<String, AnimationConfig> animationConfigs = {
    // 加载动画配置
    'loading_dots': AnimationConfig(
      path: loadingDots,
      type: AnimationType.lottie,
      duration: Duration(seconds: 2),
      repeat: true,
      description: '点状加载动画',
    ),
    'loading_spinner': AnimationConfig(
      path: loadingSpinner,
      type: AnimationType.lottie,
      duration: Duration(seconds: 1),
      repeat: true,
      description: '旋转加载动画',
    ),
    'loading_pulse': AnimationConfig(
      path: loadingPulse,
      type: AnimationType.lottie,
      duration: Duration(milliseconds: 1500),
      repeat: true,
      description: '脉冲加载动画',
    ),
    
    // 成功动画配置
    'success_check': AnimationConfig(
      path: successCheck,
      type: AnimationType.lottie,
      duration: Duration(milliseconds: 800),
      repeat: false,
      description: '成功勾选动画',
    ),
    'success_confetti': AnimationConfig(
      path: successConfetti,
      type: AnimationType.lottie,
      duration: Duration(seconds: 3),
      repeat: false,
      description: '成功庆祝动画',
    ),
    
    // 错误动画配置
    'error_cross': AnimationConfig(
      path: errorCross,
      type: AnimationType.lottie,
      duration: Duration(milliseconds: 600),
      repeat: false,
      description: '错误叉号动画',
    ),
    'error_shake': AnimationConfig(
      path: errorShake,
      type: AnimationType.lottie,
      duration: Duration(milliseconds: 500),
      repeat: false,
      description: '错误摇摆动画',
    ),
    
    // 金融动画配置
    'coin_flip': AnimationConfig(
      path: coinFlip,
      type: AnimationType.lottie,
      duration: Duration(seconds: 2),
      repeat: true,
      description: '硬币翻转动画',
    ),
    'chart_up': AnimationConfig(
      path: chartUp,
      type: AnimationType.lottie,
      duration: Duration(seconds: 2),
      repeat: false,
      description: '图表上涨动画',
    ),
    'chart_down': AnimationConfig(
      path: chartDown,
      type: AnimationType.lottie,
      duration: Duration(seconds: 2),
      repeat: false,
      description: '图表下跌动画',
    ),
    
    // 交互动画配置
    'menu_toggle': AnimationConfig(
      path: menuToggle,
      type: AnimationType.rive,
      duration: Duration(milliseconds: 300),
      repeat: false,
      description: '菜单切换动画',
    ),
    'heart_like': AnimationConfig(
      path: heartLike,
      type: AnimationType.rive,
      duration: Duration(milliseconds: 400),
      repeat: false,
      description: '点赞心形动画',
    ),
  };

  // ==================== 工具方法 ====================
  
  /// 获取所有Lottie动画路径
  static List<String> getAllLottieAnimations() {
    return [
      // 加载动画
      loadingDots, loadingSpinner, loadingPulse, loadingWave,
      // 成功动画
      successCheck, successConfetti, successTick,
      // 错误动画
      errorCross, errorShake, errorAlert,
      // 空状态动画
      emptyBox, emptySearch, emptyData, emptyNetwork,
      // 金融动画
      coinFlip, moneyGrow, chartUp, chartDown, tradingSignal, portfolioGrowth,
      // 交互动画
      buttonPress, swipeLeft, swipeRight, pullToRefresh,
      // 引导动画
      onboardingStep1, onboardingStep2, onboardingStep3, onboardingWelcome,
      // 通知动画
      notificationBell, notificationPop, notificationSlide,
    ];
  }
  
  /// 获取所有Rive动画路径
  static List<String> getAllRiveAnimations() {
    return [
      // 交互式图标
      menuToggle, heartLike, starFavorite, playPause,
      // 复杂交互
      tradingDashboard, portfolioChart, marketTicker,
    ];
  }
  
  /// 根据名称获取动画路径
  static String? getAnimationByName(String name) {
    final config = animationConfigs[name];
    return config?.path;
  }
  
  /// 获取动画配置
  static AnimationConfig? getAnimationConfig(String name) {
    return animationConfigs[name];
  }
  
  /// 根据类型获取动画列表
  static List<String> getAnimationsByType(AnimationType type) {
    final animations = <String>[];
    for (final config in animationConfigs.values) {
      if (config.type == type) {
        animations.add(config.path);
      }
    }
    return animations;
  }
  
  /// 获取加载动画列表
  static List<String> getLoadingAnimations() {
    return [loadingDots, loadingSpinner, loadingPulse, loadingWave];
  }
  
  /// 获取成功动画列表
  static List<String> getSuccessAnimations() {
    return [successCheck, successConfetti, successTick];
  }
  
  /// 获取错误动画列表
  static List<String> getErrorAnimations() {
    return [errorCross, errorShake, errorAlert];
  }
  
  /// 获取金融相关动画列表
  static List<String> getFinanceAnimations() {
    return [coinFlip, moneyGrow, chartUp, chartDown, tradingSignal, portfolioGrowth];
  }
  
  /// 检查动画是否存在
  static bool animationExists(String animationPath) {
    return getAllLottieAnimations().contains(animationPath) || 
           getAllRiveAnimations().contains(animationPath);
  }
}

/// 动画类型枚举
enum AnimationType {
  lottie,
  rive,
  gif,
  custom,
}

/// 动画配置类
class AnimationConfig {
  /// 动画文件路径
  final String path;
  
  /// 动画类型
  final AnimationType type;
  
  /// 动画时长
  final Duration duration;
  
  /// 是否重复播放
  final bool repeat;
  
  /// 动画描述
  final String description;
  
  /// 自动播放
  final bool autoPlay;
  
  /// 播放速度
  final double speed;

  const AnimationConfig({
    required this.path,
    required this.type,
    required this.duration,
    required this.repeat,
    required this.description,
    this.autoPlay = true,
    this.speed = 1.0,
  });

  @override
  String toString() {
    return 'AnimationConfig(path: $path, type: $type, duration: $duration, '
           'repeat: $repeat, description: $description)';
  }
}
