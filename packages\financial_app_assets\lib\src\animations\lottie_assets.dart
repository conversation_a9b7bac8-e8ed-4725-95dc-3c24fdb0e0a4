import 'package:flutter/material.dart';

/// Lottie动画资源管理
/// 
/// 专门管理Lottie动画的播放、缓存和配置
class LottieAssets {
  LottieAssets._();

  // ==================== Lottie动画路径 ====================
  
  // 基础路径
  static const String _basePath = 'assets/animations/lottie';
  
  // 加载动画
  static const String loadingDots = '$_basePath/loading/loading_dots.json';
  static const String loadingSpinner = '$_basePath/loading/loading_spinner.json';
  static const String loadingPulse = '$_basePath/loading/loading_pulse.json';
  static const String loadingWave = '$_basePath/loading/loading_wave.json';
  static const String loadingBounce = '$_basePath/loading/loading_bounce.json';
  
  // 状态动画
  static const String successCheck = '$_basePath/status/success_check.json';
  static const String successConfetti = '$_basePath/status/success_confetti.json';
  static const String errorCross = '$_basePath/status/error_cross.json';
  static const String errorShake = '$_basePath/status/error_shake.json';
  static const String warningAlert = '$_basePath/status/warning_alert.json';
  static const String infoNotice = '$_basePath/status/info_notice.json';
  
  // 空状态动画
  static const String emptyBox = '$_basePath/empty/empty_box.json';
  static const String emptySearch = '$_basePath/empty/empty_search.json';
  static const String emptyData = '$_basePath/empty/empty_data.json';
  static const String emptyNetwork = '$_basePath/empty/empty_network.json';
  static const String emptyFavorites = '$_basePath/empty/empty_favorites.json';
  static const String emptyNotifications = '$_basePath/empty/empty_notifications.json';
  
  // 金融动画
  static const String coinFlip = '$_basePath/finance/coin_flip.json';
  static const String moneyGrow = '$_basePath/finance/money_grow.json';
  static const String chartRise = '$_basePath/finance/chart_rise.json';
  static const String chartFall = '$_basePath/finance/chart_fall.json';
  static const String tradingSignal = '$_basePath/finance/trading_signal.json';
  static const String portfolioGrowth = '$_basePath/finance/portfolio_growth.json';
  static const String stockTicker = '$_basePath/finance/stock_ticker.json';
  static const String cryptoMining = '$_basePath/finance/crypto_mining.json';
  
  // 交互动画
  static const String buttonTap = '$_basePath/interaction/button_tap.json';
  static const String swipeGesture = '$_basePath/interaction/swipe_gesture.json';
  static const String pullRefresh = '$_basePath/interaction/pull_refresh.json';
  static const String scrollIndicator = '$_basePath/interaction/scroll_indicator.json';
  static const String dragDrop = '$_basePath/interaction/drag_drop.json';
  
  // 引导动画
  static const String welcomeIntro = '$_basePath/onboarding/welcome_intro.json';
  static const String featureHighlight = '$_basePath/onboarding/feature_highlight.json';
  static const String tutorialStep = '$_basePath/onboarding/tutorial_step.json';
  static const String getStarted = '$_basePath/onboarding/get_started.json';
  
  // 通知动画
  static const String notificationBell = '$_basePath/notification/bell.json';
  static const String notificationPop = '$_basePath/notification/pop.json';
  static const String messageReceived = '$_basePath/notification/message_received.json';
  static const String alertBadge = '$_basePath/notification/alert_badge.json';
  
  // 网络状态动画
  static const String connecting = '$_basePath/network/connecting.json';
  static const String connected = '$_basePath/network/connected.json';
  static const String disconnected = '$_basePath/network/disconnected.json';
  static const String syncing = '$_basePath/network/syncing.json';

  // ==================== 预设动画配置 ====================
  
  /// 加载动画配置
  static const Map<String, LottieConfig> loadingConfigs = {
    'dots': LottieConfig(
      path: loadingDots,
      repeat: true,
      duration: Duration(seconds: 2),
      size: Size(50, 50),
    ),
    'spinner': LottieConfig(
      path: loadingSpinner,
      repeat: true,
      duration: Duration(seconds: 1),
      size: Size(40, 40),
    ),
    'pulse': LottieConfig(
      path: loadingPulse,
      repeat: true,
      duration: Duration(milliseconds: 1500),
      size: Size(60, 60),
    ),
    'wave': LottieConfig(
      path: loadingWave,
      repeat: true,
      duration: Duration(seconds: 3),
      size: Size(80, 30),
    ),
  };
  
  /// 状态动画配置
  static const Map<String, LottieConfig> statusConfigs = {
    'success': LottieConfig(
      path: successCheck,
      repeat: false,
      duration: Duration(milliseconds: 800),
      size: Size(60, 60),
    ),
    'error': LottieConfig(
      path: errorCross,
      repeat: false,
      duration: Duration(milliseconds: 600),
      size: Size(60, 60),
    ),
    'warning': LottieConfig(
      path: warningAlert,
      repeat: false,
      duration: Duration(milliseconds: 700),
      size: Size(60, 60),
    ),
    'info': LottieConfig(
      path: infoNotice,
      repeat: false,
      duration: Duration(milliseconds: 500),
      size: Size(60, 60),
    ),
  };
  
  /// 空状态动画配置
  static const Map<String, LottieConfig> emptyConfigs = {
    'general': LottieConfig(
      path: emptyBox,
      repeat: true,
      duration: Duration(seconds: 4),
      size: Size(120, 120),
    ),
    'search': LottieConfig(
      path: emptySearch,
      repeat: true,
      duration: Duration(seconds: 3),
      size: Size(100, 100),
    ),
    'data': LottieConfig(
      path: emptyData,
      repeat: true,
      duration: Duration(seconds: 5),
      size: Size(150, 150),
    ),
    'network': LottieConfig(
      path: emptyNetwork,
      repeat: true,
      duration: Duration(seconds: 3),
      size: Size(120, 120),
    ),
  };
  
  /// 金融动画配置
  static const Map<String, LottieConfig> financeConfigs = {
    'coin_flip': LottieConfig(
      path: coinFlip,
      repeat: true,
      duration: Duration(seconds: 2),
      size: Size(80, 80),
    ),
    'money_grow': LottieConfig(
      path: moneyGrow,
      repeat: false,
      duration: Duration(seconds: 3),
      size: Size(100, 100),
    ),
    'chart_rise': LottieConfig(
      path: chartRise,
      repeat: false,
      duration: Duration(seconds: 2),
      size: Size(120, 80),
    ),
    'chart_fall': LottieConfig(
      path: chartFall,
      repeat: false,
      duration: Duration(seconds: 2),
      size: Size(120, 80),
    ),
    'trading_signal': LottieConfig(
      path: tradingSignal,
      repeat: true,
      duration: Duration(seconds: 1),
      size: Size(40, 40),
    ),
  };

  // ==================== 工具方法 ====================
  
  /// 获取加载动画配置
  static LottieConfig? getLoadingConfig(String type) {
    return loadingConfigs[type];
  }
  
  /// 获取状态动画配置
  static LottieConfig? getStatusConfig(String type) {
    return statusConfigs[type];
  }
  
  /// 获取空状态动画配置
  static LottieConfig? getEmptyConfig(String type) {
    return emptyConfigs[type];
  }
  
  /// 获取金融动画配置
  static LottieConfig? getFinanceConfig(String type) {
    return financeConfigs[type];
  }
  
  /// 获取所有Lottie动画路径
  static List<String> getAllAnimations() {
    return [
      // 加载动画
      ...loadingConfigs.values.map((config) => config.path),
      // 状态动画
      ...statusConfigs.values.map((config) => config.path),
      // 空状态动画
      ...emptyConfigs.values.map((config) => config.path),
      // 金融动画
      ...financeConfigs.values.map((config) => config.path),
      // 其他动画
      buttonTap, swipeGesture, pullRefresh, scrollIndicator, dragDrop,
      welcomeIntro, featureHighlight, tutorialStep, getStarted,
      notificationBell, notificationPop, messageReceived, alertBadge,
      connecting, connected, disconnected, syncing,
    ];
  }
  
  /// 根据用途获取推荐动画
  static String getRecommendedAnimation(AnimationPurpose purpose) {
    switch (purpose) {
      case AnimationPurpose.loading:
        return loadingSpinner;
      case AnimationPurpose.success:
        return successCheck;
      case AnimationPurpose.error:
        return errorCross;
      case AnimationPurpose.empty:
        return emptyBox;
      case AnimationPurpose.finance:
        return chartRise;
      case AnimationPurpose.notification:
        return notificationBell;
      case AnimationPurpose.onboarding:
        return welcomeIntro;
    }
  }
  
  /// 预加载关键动画
  static Future<void> preloadCriticalAnimations() async {
    final criticalAnimations = [
      loadingSpinner,
      successCheck,
      errorCross,
      emptyBox,
    ];
    
    // 这里可以添加实际的预加载逻辑
    // 例如使用 Lottie.asset() 预加载动画
  }
  
  /// 检查动画文件是否存在
  static bool animationExists(String path) {
    return getAllAnimations().contains(path);
  }
}

/// Lottie动画配置类
class LottieConfig {
  /// 动画文件路径
  final String path;
  
  /// 是否重复播放
  final bool repeat;
  
  /// 动画时长
  final Duration duration;
  
  /// 动画尺寸
  final Size size;
  
  /// 播放速度
  final double speed;
  
  /// 是否自动播放
  final bool autoPlay;

  const LottieConfig({
    required this.path,
    this.repeat = false,
    required this.duration,
    required this.size,
    this.speed = 1.0,
    this.autoPlay = true,
  });

  @override
  String toString() {
    return 'LottieConfig(path: $path, repeat: $repeat, duration: $duration, size: $size)';
  }
}

/// 动画用途枚举
enum AnimationPurpose {
  loading,
  success,
  error,
  empty,
  finance,
  notification,
  onboarding,
}
