/// 资源常量定义
/// 
/// 统一管理所有资源相关的常量
class AssetConstants {
  AssetConstants._();

  // ==================== 路径常量 ====================
  
  /// 资源根路径
  static const String assetsRoot = 'assets';
  
  /// 图片根路径
  static const String imagesRoot = '$assetsRoot/images';
  
  /// 图标根路径
  static const String iconsRoot = '$assetsRoot/icons';
  
  /// 字体根路径
  static const String fontsRoot = '$assetsRoot/fonts';
  
  /// 动画根路径
  static const String animationsRoot = '$assetsRoot/animations';
  
  /// 数据根路径
  static const String dataRoot = '$assetsRoot/data';

  // ==================== 图片子目录 ====================
  
  /// Logo图片目录
  static const String logosPath = '$imagesRoot/logos';
  
  /// 背景图片目录
  static const String backgroundsPath = '$imagesRoot/backgrounds';
  
  /// 插图目录
  static const String illustrationsPath = '$imagesRoot/illustrations';
  
  /// 头像目录
  static const String avatarsPath = '$imagesRoot/avatars';
  
  /// 货币图标目录
  static const String coinsPath = '$imagesRoot/coins';
  
  /// 图表图片目录
  static const String chartsPath = '$imagesRoot/charts';
  
  /// 占位符图片目录
  static const String placeholdersPath = '$imagesRoot/placeholders';

  // ==================== 图标子目录 ====================
  
  /// 导航图标目录
  static const String navigationIconsPath = '$iconsRoot/navigation';
  
  /// 操作图标目录
  static const String actionIconsPath = '$iconsRoot/actions';
  
  /// 状态图标目录
  static const String statusIconsPath = '$iconsRoot/status';
  
  /// 金融图标目录
  static const String financialIconsPath = '$iconsRoot/financial';
  
  /// SVG图标目录
  static const String svgIconsPath = '$iconsRoot/svg';
  
  /// PNG图标目录
  static const String pngIconsPath = '$iconsRoot/png';

  // ==================== 字体子目录 ====================
  
  /// 中文字体目录
  static const String chineseFontsPath = '$fontsRoot/chinese';
  
  /// 英文字体目录
  static const String englishFontsPath = '$fontsRoot/english';
  
  /// 数字字体目录
  static const String numberFontsPath = '$fontsRoot/numbers';
  
  /// 图标字体目录
  static const String iconFontsPath = '$fontsRoot/icons';

  // ==================== 动画子目录 ====================
  
  /// Lottie动画目录
  static const String lottiePath = '$animationsRoot/lottie';
  
  /// Rive动画目录
  static const String rivePath = '$animationsRoot/rive';
  
  /// GIF动画目录
  static const String gifPath = '$animationsRoot/gif';

  // ==================== 文件扩展名 ====================
  
  /// 图片文件扩展名
  static const List<String> imageExtensions = [
    '.jpg',
    '.jpeg',
    '.png',
    '.gif',
    '.webp',
    '.bmp',
  ];
  
  /// 矢量图文件扩展名
  static const List<String> vectorExtensions = [
    '.svg',
  ];
  
  /// 字体文件扩展名
  static const List<String> fontExtensions = [
    '.ttf',
    '.otf',
    '.woff',
    '.woff2',
  ];
  
  /// 动画文件扩展名
  static const List<String> animationExtensions = [
    '.json',  // Lottie
    '.riv',   // Rive
    '.gif',   // GIF
  ];

  // ==================== 尺寸常量 ====================
  
  /// 图标尺寸
  static const double iconSizeSmall = 16.0;
  static const double iconSizeMedium = 24.0;
  static const double iconSizeLarge = 32.0;
  static const double iconSizeXLarge = 48.0;
  
  /// 头像尺寸
  static const double avatarSizeSmall = 32.0;
  static const double avatarSizeMedium = 48.0;
  static const double avatarSizeLarge = 64.0;
  static const double avatarSizeXLarge = 96.0;
  
  /// Logo尺寸
  static const double logoSizeSmall = 48.0;
  static const double logoSizeMedium = 72.0;
  static const double logoSizeLarge = 96.0;
  static const double logoSizeXLarge = 128.0;

  // ==================== 缓存常量 ====================
  
  /// 默认缓存时间（7天）
  static const int defaultCacheAge = 7 * 24 * 60 * 60 * 1000;
  
  /// 最大内存缓存大小（100MB）
  static const int maxMemoryCacheSize = 100 * 1024 * 1024;
  
  /// 最大磁盘缓存大小（500MB）
  static const int maxDiskCacheSize = 500 * 1024 * 1024;
  
  /// 缓存键前缀
  static const String cacheKeyPrefix = 'financial_app_assets_';

  // ==================== 质量常量 ====================
  
  /// 图片质量设置
  static const int imageQualityLow = 50;
  static const int imageQualityMedium = 75;
  static const int imageQualityHigh = 90;
  static const int imageQualityOriginal = 100;
  
  /// 压缩比例
  static const double compressionRatioLow = 0.3;
  static const double compressionRatioMedium = 0.5;
  static const double compressionRatioHigh = 0.7;
  static const double compressionRatioOriginal = 1.0;

  // ==================== 动画常量 ====================
  
  /// 动画时长
  static const int animationDurationFast = 200;
  static const int animationDurationMedium = 300;
  static const int animationDurationSlow = 500;
  
  /// 动画曲线
  static const String animationCurveLinear = 'linear';
  static const String animationCurveEaseIn = 'easeIn';
  static const String animationCurveEaseOut = 'easeOut';
  static const String animationCurveEaseInOut = 'easeInOut';

  // ==================== 错误常量 ====================
  
  /// 错误消息
  static const String errorImageNotFound = 'Image not found';
  static const String errorImageLoadFailed = 'Failed to load image';
  static const String errorImageCorrupted = 'Image file is corrupted';
  static const String errorNetworkTimeout = 'Network timeout';
  static const String errorInsufficientMemory = 'Insufficient memory';
  
  /// 错误代码
  static const int errorCodeNotFound = 404;
  static const int errorCodeLoadFailed = 500;
  static const int errorCodeCorrupted = 422;
  static const int errorCodeTimeout = 408;
  static const int errorCodeMemory = 507;

  // ==================== 配置常量 ====================
  
  /// 默认重试次数
  static const int defaultRetryCount = 3;
  
  /// 默认重试延迟（毫秒）
  static const int defaultRetryDelay = 1000;
  
  /// 默认超时时间（毫秒）
  static const int defaultTimeout = 30000;
  
  /// 最大并发加载数
  static const int maxConcurrentLoads = 5;

  // ==================== 包信息常量 ====================
  
  /// 包名
  static const String packageName = 'financial_app_assets';
  
  /// 版本号
  static const String version = '1.0.0';
  
  /// 作者
  static const String author = 'Financial App Team';
  
  /// 描述
  static const String description = 'Financial app assets management package';

  // ==================== 工具方法 ====================
  
  /// 检查是否为图片文件
  static bool isImageFile(String path) {
    final extension = path.toLowerCase().split('.').last;
    return imageExtensions.contains('.$extension') || 
           vectorExtensions.contains('.$extension');
  }
  
  /// 检查是否为矢量图
  static bool isVectorFile(String path) {
    final extension = path.toLowerCase().split('.').last;
    return vectorExtensions.contains('.$extension');
  }
  
  /// 检查是否为字体文件
  static bool isFontFile(String path) {
    final extension = path.toLowerCase().split('.').last;
    return fontExtensions.contains('.$extension');
  }
  
  /// 检查是否为动画文件
  static bool isAnimationFile(String path) {
    final extension = path.toLowerCase().split('.').last;
    return animationExtensions.contains('.$extension');
  }
  
  /// 获取文件扩展名
  static String getFileExtension(String path) {
    final parts = path.split('.');
    return parts.isNotEmpty ? '.${parts.last.toLowerCase()}' : '';
  }
  
  /// 生成缓存键
  static String generateCacheKey(String path, {String? suffix}) {
    final key = '$cacheKeyPrefix${path.hashCode}';
    return suffix != null ? '${key}_$suffix' : key;
  }
  
  /// 获取资源完整路径
  static String getAssetPath(String relativePath) {
    if (relativePath.startsWith(assetsRoot)) {
      return relativePath;
    }
    return '$assetsRoot/$relativePath';
  }
  
  /// 获取图片完整路径
  static String getImagePath(String imageName) {
    return '$imagesRoot/$imageName';
  }
  
  /// 获取图标完整路径
  static String getIconPath(String iconName) {
    return '$iconsRoot/$iconName';
  }
  
  /// 获取字体完整路径
  static String getFontPath(String fontName) {
    return '$fontsRoot/$fontName';
  }
  
  /// 获取动画完整路径
  static String getAnimationPath(String animationName) {
    return '$animationsRoot/$animationName';
  }
  
  /// 根据质量获取压缩比例
  static double getCompressionRatio(int quality) {
    if (quality <= imageQualityLow) {
      return compressionRatioLow;
    } else if (quality <= imageQualityMedium) {
      return compressionRatioMedium;
    } else if (quality <= imageQualityHigh) {
      return compressionRatioHigh;
    } else {
      return compressionRatioOriginal;
    }
  }
  
  /// 根据尺寸类型获取图标大小
  static double getIconSize(String sizeType) {
    switch (sizeType.toLowerCase()) {
      case 'small':
        return iconSizeSmall;
      case 'medium':
        return iconSizeMedium;
      case 'large':
        return iconSizeLarge;
      case 'xlarge':
        return iconSizeXLarge;
      default:
        return iconSizeMedium;
    }
  }
  
  /// 根据尺寸类型获取头像大小
  static double getAvatarSize(String sizeType) {
    switch (sizeType.toLowerCase()) {
      case 'small':
        return avatarSizeSmall;
      case 'medium':
        return avatarSizeMedium;
      case 'large':
        return avatarSizeLarge;
      case 'xlarge':
        return avatarSizeXLarge;
      default:
        return avatarSizeMedium;
    }
  }
}
