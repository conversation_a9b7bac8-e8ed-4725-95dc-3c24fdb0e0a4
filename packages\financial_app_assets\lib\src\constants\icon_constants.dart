/// 图标常量定义
/// 
/// 统一管理所有图标资源的路径和配置
class IconConstants {
  IconConstants._();

  // ==================== 基础路径 ====================
  
  static const String _basePath = 'assets/icons';
  static const String _svgPath = '$_basePath/svg';
  static const String _pngPath = '$_basePath/png';

  // ==================== SVG 图标路径 ====================
  
  // 导航图标
  static const String homeIconSvg = '$_svgPath/navigation/home.svg';
  static const String marketIconSvg = '$_svgPath/navigation/market.svg';
  static const String tradeIconSvg = '$_svgPath/navigation/trade.svg';
  static const String portfolioIconSvg = '$_svgPath/navigation/portfolio.svg';
  static const String profileIconSvg = '$_svgPath/navigation/profile.svg';
  static const String settingsIconSvg = '$_svgPath/navigation/settings.svg';
  
  // 操作图标
  static const String addIconSvg = '$_svgPath/actions/add.svg';
  static const String deleteIconSvg = '$_svgPath/actions/delete.svg';
  static const String editIconSvg = '$_svgPath/actions/edit.svg';
  static const String searchIconSvg = '$_svgPath/actions/search.svg';
  static const String filterIconSvg = '$_svgPath/actions/filter.svg';
  static const String sortIconSvg = '$_svgPath/actions/sort.svg';
  static const String refreshIconSvg = '$_svgPath/actions/refresh.svg';
  static const String shareIconSvg = '$_svgPath/actions/share.svg';
  static const String copyIconSvg = '$_svgPath/actions/copy.svg';
  static const String downloadIconSvg = '$_svgPath/actions/download.svg';
  static const String uploadIconSvg = '$_svgPath/actions/upload.svg';
  static const String printIconSvg = '$_svgPath/actions/print.svg';
  
  // 状态图标
  static const String successIconSvg = '$_svgPath/status/success.svg';
  static const String errorIconSvg = '$_svgPath/status/error.svg';
  static const String warningIconSvg = '$_svgPath/status/warning.svg';
  static const String infoIconSvg = '$_svgPath/status/info.svg';
  static const String loadingIconSvg = '$_svgPath/status/loading.svg';
  static const String onlineIconSvg = '$_svgPath/status/online.svg';
  static const String offlineIconSvg = '$_svgPath/status/offline.svg';
  static const String connectingIconSvg = '$_svgPath/status/connecting.svg';
  
  // 金融图标
  static const String buyIconSvg = '$_svgPath/financial/buy.svg';
  static const String sellIconSvg = '$_svgPath/financial/sell.svg';
  static const String trendUpIconSvg = '$_svgPath/financial/trend_up.svg';
  static const String trendDownIconSvg = '$_svgPath/financial/trend_down.svg';
  static const String trendFlatIconSvg = '$_svgPath/financial/trend_flat.svg';
  static const String chartIconSvg = '$_svgPath/financial/chart.svg';
  static const String candlestickIconSvg = '$_svgPath/financial/candlestick.svg';
  static const String walletIconSvg = '$_svgPath/financial/wallet.svg';
  static const String bankIconSvg = '$_svgPath/financial/bank.svg';
  static const String creditCardIconSvg = '$_svgPath/financial/credit_card.svg';
  static const String transferIconSvg = '$_svgPath/financial/transfer.svg';
  static const String investmentIconSvg = '$_svgPath/financial/investment.svg';
  static const String savingsIconSvg = '$_svgPath/financial/savings.svg';
  static const String loanIconSvg = '$_svgPath/financial/loan.svg';
  static const String insuranceIconSvg = '$_svgPath/financial/insurance.svg';
  
  // 货币图标
  static const String cnyIconSvg = '$_svgPath/financial/currencies/cny.svg';
  static const String usdIconSvg = '$_svgPath/financial/currencies/usd.svg';
  static const String eurIconSvg = '$_svgPath/financial/currencies/eur.svg';
  static const String jpyIconSvg = '$_svgPath/financial/currencies/jpy.svg';
  static const String gbpIconSvg = '$_svgPath/financial/currencies/gbp.svg';
  static const String btcIconSvg = '$_svgPath/financial/currencies/btc.svg';
  static const String ethIconSvg = '$_svgPath/financial/currencies/eth.svg';
  
  // 通用图标
  static const String heartIconSvg = '$_svgPath/general/heart.svg';
  static const String starIconSvg = '$_svgPath/general/star.svg';
  static const String bookmarkIconSvg = '$_svgPath/general/bookmark.svg';
  static const String notificationIconSvg = '$_svgPath/general/notification.svg';
  static const String messageIconSvg = '$_svgPath/general/message.svg';
  static const String phoneIconSvg = '$_svgPath/general/phone.svg';
  static const String emailIconSvg = '$_svgPath/general/email.svg';
  static const String locationIconSvg = '$_svgPath/general/location.svg';
  static const String calendarIconSvg = '$_svgPath/general/calendar.svg';
  static const String clockIconSvg = '$_svgPath/general/clock.svg';

  // ==================== PNG 图标路径 ====================
  
  // 应用图标
  static const String appIconPng = '$_pngPath/app/app_icon.png';
  static const String appIcon2xPng = '$_pngPath/app/<EMAIL>';
  static const String appIcon3xPng = '$_pngPath/app/<EMAIL>';
  
  // 启动图标
  static const String launchIconPng = '$_pngPath/launch/launch_icon.png';
  static const String launchIcon2xPng = '$_pngPath/launch/<EMAIL>';
  static const String launchIcon3xPng = '$_pngPath/launch/<EMAIL>';
  
  // 通知图标
  static const String notificationIconPng = '$_pngPath/notification/notification.png';
  static const String notificationIcon2xPng = '$_pngPath/notification/<EMAIL>';
  static const String notificationIcon3xPng = '$_pngPath/notification/<EMAIL>';

  // ==================== 图标尺寸常量 ====================
  
  /// 小图标尺寸
  static const double smallIconSize = 16.0;
  
  /// 中等图标尺寸
  static const double mediumIconSize = 24.0;
  
  /// 大图标尺寸
  static const double largeIconSize = 32.0;
  
  /// 超大图标尺寸
  static const double extraLargeIconSize = 48.0;
  
  /// 导航栏图标尺寸
  static const double navigationIconSize = 24.0;
  
  /// 工具栏图标尺寸
  static const double toolbarIconSize = 20.0;
  
  /// 按钮图标尺寸
  static const double buttonIconSize = 18.0;
  
  /// 列表项图标尺寸
  static const double listItemIconSize = 20.0;

  // ==================== 图标颜色常量 ====================
  
  /// 主要图标颜色
  static const String primaryIconColor = '#1976D2';
  
  /// 次要图标颜色
  static const String secondaryIconColor = '#757575';
  
  /// 成功图标颜色
  static const String successIconColor = '#4CAF50';
  
  /// 错误图标颜色
  static const String errorIconColor = '#F44336';
  
  /// 警告图标颜色
  static const String warningIconColor = '#FF9800';
  
  /// 信息图标颜色
  static const String infoIconColor = '#2196F3';
  
  /// 禁用图标颜色
  static const String disabledIconColor = '#BDBDBD';
  
  /// 上涨图标颜色
  static const String bullishIconColor = '#4CAF50';
  
  /// 下跌图标颜色
  static const String bearishIconColor = '#F44336';
  
  /// 持平图标颜色
  static const String neutralIconColor = '#757575';

  // ==================== 工具方法 ====================
  
  /// 获取所有SVG图标路径
  static List<String> getAllSvgIcons() {
    return [
      // 导航图标
      homeIconSvg, marketIconSvg, tradeIconSvg, portfolioIconSvg, profileIconSvg, settingsIconSvg,
      // 操作图标
      addIconSvg, deleteIconSvg, editIconSvg, searchIconSvg, filterIconSvg, sortIconSvg,
      refreshIconSvg, shareIconSvg, copyIconSvg, downloadIconSvg, uploadIconSvg, printIconSvg,
      // 状态图标
      successIconSvg, errorIconSvg, warningIconSvg, infoIconSvg, loadingIconSvg,
      onlineIconSvg, offlineIconSvg, connectingIconSvg,
      // 金融图标
      buyIconSvg, sellIconSvg, trendUpIconSvg, trendDownIconSvg, trendFlatIconSvg,
      chartIconSvg, candlestickIconSvg, walletIconSvg, bankIconSvg, creditCardIconSvg,
      transferIconSvg, investmentIconSvg, savingsIconSvg, loanIconSvg, insuranceIconSvg,
      // 货币图标
      cnyIconSvg, usdIconSvg, eurIconSvg, jpyIconSvg, gbpIconSvg, btcIconSvg, ethIconSvg,
      // 通用图标
      heartIconSvg, starIconSvg, bookmarkIconSvg, notificationIconSvg, messageIconSvg,
      phoneIconSvg, emailIconSvg, locationIconSvg, calendarIconSvg, clockIconSvg,
    ];
  }
  
  /// 获取所有PNG图标路径
  static List<String> getAllPngIcons() {
    return [
      // 应用图标
      appIconPng, appIcon2xPng, appIcon3xPng,
      // 启动图标
      launchIconPng, launchIcon2xPng, launchIcon3xPng,
      // 通知图标
      notificationIconPng, notificationIcon2xPng, notificationIcon3xPng,
    ];
  }
  
  /// 根据类型获取图标列表
  static List<String> getIconsByType(IconType type) {
    switch (type) {
      case IconType.navigation:
        return [homeIconSvg, marketIconSvg, tradeIconSvg, portfolioIconSvg, profileIconSvg, settingsIconSvg];
      case IconType.action:
        return [addIconSvg, deleteIconSvg, editIconSvg, searchIconSvg, filterIconSvg, sortIconSvg,
                refreshIconSvg, shareIconSvg, copyIconSvg, downloadIconSvg, uploadIconSvg, printIconSvg];
      case IconType.status:
        return [successIconSvg, errorIconSvg, warningIconSvg, infoIconSvg, loadingIconSvg,
                onlineIconSvg, offlineIconSvg, connectingIconSvg];
      case IconType.financial:
        return [buyIconSvg, sellIconSvg, trendUpIconSvg, trendDownIconSvg, trendFlatIconSvg,
                chartIconSvg, candlestickIconSvg, walletIconSvg, bankIconSvg, creditCardIconSvg,
                transferIconSvg, investmentIconSvg, savingsIconSvg, loanIconSvg, insuranceIconSvg];
      case IconType.currency:
        return [cnyIconSvg, usdIconSvg, eurIconSvg, jpyIconSvg, gbpIconSvg, btcIconSvg, ethIconSvg];
      case IconType.general:
        return [heartIconSvg, starIconSvg, bookmarkIconSvg, notificationIconSvg, messageIconSvg,
                phoneIconSvg, emailIconSvg, locationIconSvg, calendarIconSvg, clockIconSvg];
    }
  }
  
  /// 根据名称获取SVG图标路径
  static String? getSvgIconByName(String name) {
    switch (name.toLowerCase()) {
      // 导航图标
      case 'home':
        return homeIconSvg;
      case 'market':
        return marketIconSvg;
      case 'trade':
        return tradeIconSvg;
      case 'portfolio':
        return portfolioIconSvg;
      case 'profile':
        return profileIconSvg;
      case 'settings':
        return settingsIconSvg;
      
      // 操作图标
      case 'add':
        return addIconSvg;
      case 'delete':
        return deleteIconSvg;
      case 'edit':
        return editIconSvg;
      case 'search':
        return searchIconSvg;
      case 'filter':
        return filterIconSvg;
      case 'sort':
        return sortIconSvg;
      case 'refresh':
        return refreshIconSvg;
      case 'share':
        return shareIconSvg;
      case 'copy':
        return copyIconSvg;
      
      // 状态图标
      case 'success':
        return successIconSvg;
      case 'error':
        return errorIconSvg;
      case 'warning':
        return warningIconSvg;
      case 'info':
        return infoIconSvg;
      case 'loading':
        return loadingIconSvg;
      
      // 金融图标
      case 'buy':
        return buyIconSvg;
      case 'sell':
        return sellIconSvg;
      case 'chart':
        return chartIconSvg;
      case 'wallet':
        return walletIconSvg;
      case 'bank':
        return bankIconSvg;
      
      // 货币图标
      case 'cny':
        return cnyIconSvg;
      case 'usd':
        return usdIconSvg;
      case 'eur':
        return eurIconSvg;
      case 'jpy':
        return jpyIconSvg;
      case 'gbp':
        return gbpIconSvg;
      case 'btc':
        return btcIconSvg;
      case 'eth':
        return ethIconSvg;
      
      default:
        return null;
    }
  }
  
  /// 根据设备像素比获取合适的PNG图标路径
  static String getPngIconForDevicePixelRatio(String basePath, double devicePixelRatio) {
    if (devicePixelRatio >= 3.0) {
      return '${basePath.replaceAll('.png', '')}@3x.png';
    } else if (devicePixelRatio >= 2.0) {
      return '${basePath.replaceAll('.png', '')}@2x.png';
    } else {
      return basePath;
    }
  }
  
  /// 根据尺寸类型获取图标大小
  static double getIconSize(IconSizeType sizeType) {
    switch (sizeType) {
      case IconSizeType.small:
        return smallIconSize;
      case IconSizeType.medium:
        return mediumIconSize;
      case IconSizeType.large:
        return largeIconSize;
      case IconSizeType.extraLarge:
        return extraLargeIconSize;
      case IconSizeType.navigation:
        return navigationIconSize;
      case IconSizeType.toolbar:
        return toolbarIconSize;
      case IconSizeType.button:
        return buttonIconSize;
      case IconSizeType.listItem:
        return listItemIconSize;
    }
  }
  
  /// 检查图标是否存在
  static bool iconExists(String iconPath) {
    return getAllSvgIcons().contains(iconPath) || getAllPngIcons().contains(iconPath);
  }
  
  /// 获取所有图标路径
  static List<String> getAllIcons() {
    return [...getAllSvgIcons(), ...getAllPngIcons()];
  }
}

/// 图标类型枚举
enum IconType {
  navigation,
  action,
  status,
  financial,
  currency,
  general,
}

/// 图标尺寸类型枚举
enum IconSizeType {
  small,
  medium,
  large,
  extraLarge,
  navigation,
  toolbar,
  button,
  listItem,
}
