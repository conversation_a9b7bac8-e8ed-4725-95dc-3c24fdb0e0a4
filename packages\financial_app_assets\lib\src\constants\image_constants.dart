/// 图片常量定义
/// 
/// 统一管理所有图片资源的路径和配置
class ImageConstants {
  ImageConstants._();

  // ==================== 基础路径 ====================
  
  static const String _basePath = 'assets/images';

  // ==================== Logo 图片 ====================
  
  /// 应用主Logo
  static const String appLogo = '$_basePath/logos/app_logo.png';
  static const String appLogo2x = '$_basePath/logos/<EMAIL>';
  static const String appLogo3x = '$_basePath/logos/<EMAIL>';
  
  /// 应用Logo（深色版本）
  static const String appLogoDark = '$_basePath/logos/app_logo_dark.png';
  static const String appLogoDark2x = '$_basePath/logos/<EMAIL>';
  static const String appLogoDark3x = '$_basePath/logos/<EMAIL>';
  
  /// 应用Logo（白色版本）
  static const String appLogoWhite = '$_basePath/logos/app_logo_white.png';
  static const String appLogoWhite2x = '$_basePath/logos/<EMAIL>';
  static const String appLogoWhite3x = '$_basePath/logos/<EMAIL>';
  
  /// 启动页Logo
  static const String splashLogo = '$_basePath/logos/splash_logo.png';
  static const String splashLogo2x = '$_basePath/logos/<EMAIL>';
  static const String splashLogo3x = '$_basePath/logos/<EMAIL>';

  // ==================== 背景图片 ====================
  
  /// 登录背景
  static const String loginBackground = '$_basePath/backgrounds/login_bg.png';
  static const String loginBackground2x = '$_basePath/backgrounds/<EMAIL>';
  static const String loginBackground3x = '$_basePath/backgrounds/<EMAIL>';
  
  /// 注册背景
  static const String registerBackground = '$_basePath/backgrounds/register_bg.png';
  static const String registerBackground2x = '$_basePath/backgrounds/<EMAIL>';
  static const String registerBackground3x = '$_basePath/backgrounds/<EMAIL>';
  
  /// 主页背景
  static const String homeBackground = '$_basePath/backgrounds/home_bg.png';
  static const String homeBackground2x = '$_basePath/backgrounds/<EMAIL>';
  static const String homeBackground3x = '$_basePath/backgrounds/<EMAIL>';
  
  /// 市场背景
  static const String marketBackground = '$_basePath/backgrounds/market_bg.png';
  static const String marketBackground2x = '$_basePath/backgrounds/<EMAIL>';
  static const String marketBackground3x = '$_basePath/backgrounds/<EMAIL>';
  
  /// 交易背景
  static const String tradingBackground = '$_basePath/backgrounds/trading_bg.png';
  static const String tradingBackground2x = '$_basePath/backgrounds/<EMAIL>';
  static const String tradingBackground3x = '$_basePath/backgrounds/<EMAIL>';
  
  /// 投资组合背景
  static const String portfolioBackground = '$_basePath/backgrounds/portfolio_bg.png';
  static const String portfolioBackground2x = '$_basePath/backgrounds/<EMAIL>';
  static const String portfolioBackground3x = '$_basePath/backgrounds/<EMAIL>';

  // ==================== 插图 ====================
  
  /// 空状态插图
  static const String emptyState = '$_basePath/illustrations/empty_state.png';
  static const String emptyState2x = '$_basePath/illustrations/<EMAIL>';
  static const String emptyState3x = '$_basePath/illustrations/<EMAIL>';
  
  /// 网络错误插图
  static const String networkError = '$_basePath/illustrations/network_error.png';
  static const String networkError2x = '$_basePath/illustrations/<EMAIL>';
  static const String networkError3x = '$_basePath/illustrations/<EMAIL>';
  
  /// 服务器错误插图
  static const String serverError = '$_basePath/illustrations/server_error.png';
  static const String serverError2x = '$_basePath/illustrations/<EMAIL>';
  static const String serverError3x = '$_basePath/illustrations/<EMAIL>';
  
  /// 成功插图
  static const String success = '$_basePath/illustrations/success.png';
  static const String success2x = '$_basePath/illustrations/<EMAIL>';
  static const String success3x = '$_basePath/illustrations/<EMAIL>';
  
  /// 引导页插图
  static const String onboarding1 = '$_basePath/illustrations/onboarding_1.png';
  static const String onboarding1_2x = '$_basePath/illustrations/<EMAIL>';
  static const String onboarding1_3x = '$_basePath/illustrations/<EMAIL>';
  
  static const String onboarding2 = '$_basePath/illustrations/onboarding_2.png';
  static const String onboarding2_2x = '$_basePath/illustrations/<EMAIL>';
  static const String onboarding2_3x = '$_basePath/illustrations/<EMAIL>';
  
  static const String onboarding3 = '$_basePath/illustrations/onboarding_3.png';
  static const String onboarding3_2x = '$_basePath/illustrations/<EMAIL>';
  static const String onboarding3_3x = '$_basePath/illustrations/<EMAIL>';

  // ==================== 头像 ====================
  
  /// 默认头像
  static const String defaultAvatar = '$_basePath/avatars/default_avatar.png';
  static const String defaultAvatar2x = '$_basePath/avatars/<EMAIL>';
  static const String defaultAvatar3x = '$_basePath/avatars/<EMAIL>';
  
  /// 男性默认头像
  static const String maleAvatar = '$_basePath/avatars/male_avatar.png';
  static const String maleAvatar2x = '$_basePath/avatars/<EMAIL>';
  static const String maleAvatar3x = '$_basePath/avatars/<EMAIL>';
  
  /// 女性默认头像
  static const String femaleAvatar = '$_basePath/avatars/female_avatar.png';
  static const String femaleAvatar2x = '$_basePath/avatars/<EMAIL>';
  static const String femaleAvatar3x = '$_basePath/avatars/<EMAIL>';

  // ==================== 货币图标 ====================
  
  /// 人民币
  static const String cnyIcon = '$_basePath/coins/cny.png';
  static const String cnyIcon2x = '$_basePath/coins/<EMAIL>';
  static const String cnyIcon3x = '$_basePath/coins/<EMAIL>';
  
  /// 美元
  static const String usdIcon = '$_basePath/coins/usd.png';
  static const String usdIcon2x = '$_basePath/coins/<EMAIL>';
  static const String usdIcon3x = '$_basePath/coins/<EMAIL>';
  
  /// 欧元
  static const String eurIcon = '$_basePath/coins/eur.png';
  static const String eurIcon2x = '$_basePath/coins/<EMAIL>';
  static const String eurIcon3x = '$_basePath/coins/<EMAIL>';
  
  /// 日元
  static const String jpyIcon = '$_basePath/coins/jpy.png';
  static const String jpyIcon2x = '$_basePath/coins/<EMAIL>';
  static const String jpyIcon3x = '$_basePath/coins/<EMAIL>';
  
  /// 英镑
  static const String gbpIcon = '$_basePath/coins/gbp.png';
  static const String gbpIcon2x = '$_basePath/coins/<EMAIL>';
  static const String gbpIcon3x = '$_basePath/coins/<EMAIL>';
  
  /// 比特币
  static const String btcIcon = '$_basePath/coins/btc.png';
  static const String btcIcon2x = '$_basePath/coins/<EMAIL>';
  static const String btcIcon3x = '$_basePath/coins/<EMAIL>';
  
  /// 以太坊
  static const String ethIcon = '$_basePath/coins/eth.png';
  static const String ethIcon2x = '$_basePath/coins/<EMAIL>';
  static const String ethIcon3x = '$_basePath/coins/<EMAIL>';

  // ==================== 图表图片 ====================
  
  /// 上涨趋势图
  static const String chartUp = '$_basePath/charts/chart_up.png';
  static const String chartUp2x = '$_basePath/charts/<EMAIL>';
  static const String chartUp3x = '$_basePath/charts/<EMAIL>';
  
  /// 下跌趋势图
  static const String chartDown = '$_basePath/charts/chart_down.png';
  static const String chartDown2x = '$_basePath/charts/<EMAIL>';
  static const String chartDown3x = '$_basePath/charts/<EMAIL>';
  
  /// 平稳趋势图
  static const String chartFlat = '$_basePath/charts/chart_flat.png';
  static const String chartFlat2x = '$_basePath/charts/<EMAIL>';
  static const String chartFlat3x = '$_basePath/charts/<EMAIL>';
  
  /// K线图示例
  static const String candlestickSample = '$_basePath/charts/candlestick_sample.png';
  static const String candlestickSample2x = '$_basePath/charts/<EMAIL>';
  static const String candlestickSample3x = '$_basePath/charts/<EMAIL>';

  // ==================== 占位符图片 ====================
  
  /// 图片占位符
  static const String imagePlaceholder = '$_basePath/placeholders/image_placeholder.png';
  static const String imagePlaceholder2x = '$_basePath/placeholders/<EMAIL>';
  static const String imagePlaceholder3x = '$_basePath/placeholders/<EMAIL>';
  
  /// 头像占位符
  static const String avatarPlaceholder = '$_basePath/placeholders/avatar_placeholder.png';
  static const String avatarPlaceholder2x = '$_basePath/placeholders/<EMAIL>';
  static const String avatarPlaceholder3x = '$_basePath/placeholders/<EMAIL>';
  
  /// Logo占位符
  static const String logoPlaceholder = '$_basePath/placeholders/logo_placeholder.png';
  static const String logoPlaceholder2x = '$_basePath/placeholders/<EMAIL>';
  static const String logoPlaceholder3x = '$_basePath/placeholders/<EMAIL>';

  // ==================== 工具方法 ====================
  
  /// 根据设备像素比获取合适的图片路径
  static String getImageForDevicePixelRatio(String basePath, double devicePixelRatio) {
    if (devicePixelRatio >= 3.0) {
      return '${basePath.replaceAll('.png', '')}@3x.png';
    } else if (devicePixelRatio >= 2.0) {
      return '${basePath.replaceAll('.png', '')}@2x.png';
    } else {
      return basePath;
    }
  }
  
  /// 获取所有Logo图片
  static List<String> getAllLogos() {
    return [
      appLogo, appLogo2x, appLogo3x,
      appLogoDark, appLogoDark2x, appLogoDark3x,
      appLogoWhite, appLogoWhite2x, appLogoWhite3x,
      splashLogo, splashLogo2x, splashLogo3x,
    ];
  }
  
  /// 获取所有背景图片
  static List<String> getAllBackgrounds() {
    return [
      loginBackground, loginBackground2x, loginBackground3x,
      registerBackground, registerBackground2x, registerBackground3x,
      homeBackground, homeBackground2x, homeBackground3x,
      marketBackground, marketBackground2x, marketBackground3x,
      tradingBackground, tradingBackground2x, tradingBackground3x,
      portfolioBackground, portfolioBackground2x, portfolioBackground3x,
    ];
  }
  
  /// 获取所有插图
  static List<String> getAllIllustrations() {
    return [
      emptyState, emptyState2x, emptyState3x,
      networkError, networkError2x, networkError3x,
      serverError, serverError2x, serverError3x,
      success, success2x, success3x,
      onboarding1, onboarding1_2x, onboarding1_3x,
      onboarding2, onboarding2_2x, onboarding2_3x,
      onboarding3, onboarding3_2x, onboarding3_3x,
    ];
  }
  
  /// 获取所有头像
  static List<String> getAllAvatars() {
    return [
      defaultAvatar, defaultAvatar2x, defaultAvatar3x,
      maleAvatar, maleAvatar2x, maleAvatar3x,
      femaleAvatar, femaleAvatar2x, femaleAvatar3x,
    ];
  }
  
  /// 获取所有货币图标
  static List<String> getAllCoinIcons() {
    return [
      cnyIcon, cnyIcon2x, cnyIcon3x,
      usdIcon, usdIcon2x, usdIcon3x,
      eurIcon, eurIcon2x, eurIcon3x,
      jpyIcon, jpyIcon2x, jpyIcon3x,
      gbpIcon, gbpIcon2x, gbpIcon3x,
      btcIcon, btcIcon2x, btcIcon3x,
      ethIcon, ethIcon2x, ethIcon3x,
    ];
  }
  
  /// 获取所有图表图片
  static List<String> getAllChartImages() {
    return [
      chartUp, chartUp2x, chartUp3x,
      chartDown, chartDown2x, chartDown3x,
      chartFlat, chartFlat2x, chartFlat3x,
      candlestickSample, candlestickSample2x, candlestickSample3x,
    ];
  }
  
  /// 获取所有占位符图片
  static List<String> getAllPlaceholders() {
    return [
      imagePlaceholder, imagePlaceholder2x, imagePlaceholder3x,
      avatarPlaceholder, avatarPlaceholder2x, avatarPlaceholder3x,
      logoPlaceholder, logoPlaceholder2x, logoPlaceholder3x,
    ];
  }
  
  /// 获取所有图片路径
  static List<String> getAllImages() {
    return [
      ...getAllLogos(),
      ...getAllBackgrounds(),
      ...getAllIllustrations(),
      ...getAllAvatars(),
      ...getAllCoinIcons(),
      ...getAllChartImages(),
      ...getAllPlaceholders(),
    ];
  }
  
  /// 根据类型获取图片列表
  static List<String> getImagesByType(ImageType type) {
    switch (type) {
      case ImageType.logo:
        return getAllLogos();
      case ImageType.background:
        return getAllBackgrounds();
      case ImageType.illustration:
        return getAllIllustrations();
      case ImageType.avatar:
        return getAllAvatars();
      case ImageType.coin:
        return getAllCoinIcons();
      case ImageType.chart:
        return getAllChartImages();
      case ImageType.placeholder:
        return getAllPlaceholders();
    }
  }
  
  /// 检查图片是否存在
  static bool imageExists(String imagePath) {
    return getAllImages().contains(imagePath);
  }
}

/// 图片类型枚举
enum ImageType {
  logo,
  background,
  illustration,
  avatar,
  coin,
  chart,
  placeholder,
}
