import 'dart:async';
import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/widgets.dart';
import 'package:path_provider/path_provider.dart';
import 'package:crypto/crypto.dart';

import 'asset_config.dart';

/// 智能资源缓存系统
/// 
/// 提供多层缓存机制：
/// - 内存缓存 (快速访问)
/// - 磁盘缓存 (持久化存储)
/// - 自动清理机制
/// - 缓存统计和监控
class AssetCache {
  static const String _cacheDir = 'asset_cache';
  static const String _metadataFile = 'cache_metadata.json';
  
  // 内存缓存
  final Map<String, ImageProvider> _imageCache = {};
  final Map<String, Uint8List> _bytesCache = {};
  final Map<String, String> _stringCache = {};
  
  // 缓存元数据
  final Map<String, CacheEntry> _metadata = {};
  
  // 配置
  late CacheConfig _config;
  Directory? _cacheDirectory;
  bool _initialized = false;
  
  Timer? _cleanupTimer;
  
  /// 初始化缓存系统
  Future<void> initialize() async {
    if (_initialized) return;
    
    final assetConfig = AssetConfig();
    await assetConfig.load();
    _config = assetConfig.getCacheConfig();
    
    if (_config.enableDiskCache) {
      await _initializeDiskCache();
    }
    
    // 启动定期清理
    _startPeriodicCleanup();
    
    _initialized = true;
  }
  
  /// 缓存图片
  Future<void> cacheImage(String key, ImageProvider imageProvider) async {
    if (_config.enableMemoryCache) {
      _imageCache[key] = imageProvider;
    }
    
    _updateMetadata(key, CacheEntryType.image);
  }
  
  /// 获取缓存的图片
  Future<ImageProvider?> getImage(String key) async {
    if (_config.enableMemoryCache && _imageCache.containsKey(key)) {
      _updateAccessTime(key);
      return _imageCache[key];
    }
    
    return null;
  }
  
  /// 缓存字节数据
  Future<void> cacheBytes(String key, Uint8List bytes) async {
    if (_config.enableMemoryCache) {
      _bytesCache[key] = bytes;
    }
    
    if (_config.enableDiskCache && _cacheDirectory != null) {
      await _writeToDisk(key, bytes);
    }
    
    _updateMetadata(key, CacheEntryType.bytes, bytes.length);
  }
  
  /// 获取缓存的字节数据
  Future<Uint8List?> getBytes(String key) async {
    // 先检查内存缓存
    if (_config.enableMemoryCache && _bytesCache.containsKey(key)) {
      _updateAccessTime(key);
      return _bytesCache[key];
    }
    
    // 再检查磁盘缓存
    if (_config.enableDiskCache && _cacheDirectory != null) {
      final bytes = await _readFromDisk(key);
      if (bytes != null) {
        // 加载到内存缓存
        if (_config.enableMemoryCache) {
          _bytesCache[key] = bytes;
        }
        _updateAccessTime(key);
        return bytes;
      }
    }
    
    return null;
  }
  
  /// 缓存字符串
  Future<void> cacheString(String key, String content) async {
    if (_config.enableMemoryCache) {
      _stringCache[key] = content;
    }
    
    if (_config.enableDiskCache && _cacheDirectory != null) {
      final bytes = Uint8List.fromList(content.codeUnits);
      await _writeToDisk(key, bytes);
    }
    
    _updateMetadata(key, CacheEntryType.string, content.length);
  }
  
  /// 获取缓存的字符串
  Future<String?> getString(String key) async {
    // 先检查内存缓存
    if (_config.enableMemoryCache && _stringCache.containsKey(key)) {
      _updateAccessTime(key);
      return _stringCache[key];
    }
    
    // 再检查磁盘缓存
    if (_config.enableDiskCache && _cacheDirectory != null) {
      final bytes = await _readFromDisk(key);
      if (bytes != null) {
        final content = String.fromCharCodes(bytes);
        // 加载到内存缓存
        if (_config.enableMemoryCache) {
          _stringCache[key] = content;
        }
        _updateAccessTime(key);
        return content;
      }
    }
    
    return null;
  }
  
  /// 清理缓存
  Future<void> clear() async {
    _imageCache.clear();
    _bytesCache.clear();
    _stringCache.clear();
    _metadata.clear();
    
    if (_cacheDirectory != null && await _cacheDirectory!.exists()) {
      await _cacheDirectory!.delete(recursive: true);
    }
  }
  
  /// 获取缓存大小
  Future<int> getSize() async {
    int totalSize = 0;
    
    // 内存缓存大小估算
    for (final bytes in _bytesCache.values) {
      totalSize += bytes.length;
    }
    
    for (final content in _stringCache.values) {
      totalSize += content.length * 2; // UTF-16 编码
    }
    
    // 磁盘缓存大小
    if (_cacheDirectory != null && await _cacheDirectory!.exists()) {
      await for (final entity in _cacheDirectory!.list(recursive: true)) {
        if (entity is File) {
          final stat = await entity.stat();
          totalSize += stat.size;
        }
      }
    }
    
    return totalSize;
  }
  
  /// 获取缓存统计
  CacheStats getStats() {
    return CacheStats(
      memoryImageCount: _imageCache.length,
      memoryBytesCount: _bytesCache.length,
      memoryStringCount: _stringCache.length,
      totalEntries: _metadata.length,
      hitCount: _metadata.values.fold(0, (sum, entry) => sum + entry.hitCount),
    );
  }
  
  /// 初始化磁盘缓存
  Future<void> _initializeDiskCache() async {
    final appDir = await getApplicationDocumentsDirectory();
    _cacheDirectory = Directory('${appDir.path}/$_cacheDir');
    
    if (!await _cacheDirectory!.exists()) {
      await _cacheDirectory!.create(recursive: true);
    }
  }
  
  /// 写入磁盘
  Future<void> _writeToDisk(String key, Uint8List bytes) async {
    if (_cacheDirectory == null) return;
    
    final hash = _generateHash(key);
    final file = File('${_cacheDirectory!.path}/$hash');
    await file.writeAsBytes(bytes);
  }
  
  /// 从磁盘读取
  Future<Uint8List?> _readFromDisk(String key) async {
    if (_cacheDirectory == null) return null;
    
    final hash = _generateHash(key);
    final file = File('${_cacheDirectory!.path}/$hash');
    
    if (await file.exists()) {
      return await file.readAsBytes();
    }
    
    return null;
  }
  
  /// 生成缓存键的哈希值
  String _generateHash(String key) {
    final bytes = key.codeUnits;
    final digest = sha256.convert(bytes);
    return digest.toString();
  }
  
  /// 更新元数据
  void _updateMetadata(String key, CacheEntryType type, [int? size]) {
    final now = DateTime.now();
    final existing = _metadata[key];
    
    if (existing != null) {
      _metadata[key] = existing.copyWith(
        lastAccessed: now,
        hitCount: existing.hitCount + 1,
      );
    } else {
      _metadata[key] = CacheEntry(
        key: key,
        type: type,
        size: size ?? 0,
        createdAt: now,
        lastAccessed: now,
        hitCount: 1,
      );
    }
  }
  
  /// 更新访问时间
  void _updateAccessTime(String key) {
    final entry = _metadata[key];
    if (entry != null) {
      _metadata[key] = entry.copyWith(
        lastAccessed: DateTime.now(),
        hitCount: entry.hitCount + 1,
      );
    }
  }
  
  /// 启动定期清理
  void _startPeriodicCleanup() {
    _cleanupTimer = Timer.periodic(const Duration(hours: 1), (_) {
      _performCleanup();
    });
  }
  
  /// 执行清理
  Future<void> _performCleanup() async {
    final now = DateTime.now();
    final maxAge = Duration(days: _config.maxAgeDays);
    
    // 清理过期的内存缓存
    final expiredKeys = <String>[];
    for (final entry in _metadata.entries) {
      if (now.difference(entry.value.lastAccessed) > maxAge) {
        expiredKeys.add(entry.key);
      }
    }
    
    for (final key in expiredKeys) {
      _imageCache.remove(key);
      _bytesCache.remove(key);
      _stringCache.remove(key);
      _metadata.remove(key);
    }
    
    // 清理磁盘缓存
    if (_cacheDirectory != null && await _cacheDirectory!.exists()) {
      await for (final entity in _cacheDirectory!.list()) {
        if (entity is File) {
          final stat = await entity.stat();
          if (now.difference(stat.accessed) > maxAge) {
            await entity.delete();
          }
        }
      }
    }
  }
  
  /// 释放资源
  void dispose() {
    _cleanupTimer?.cancel();
    _imageCache.clear();
    _bytesCache.clear();
    _stringCache.clear();
    _metadata.clear();
  }
}

/// 缓存条目类型
enum CacheEntryType {
  image,
  bytes,
  string,
}

/// 缓存条目
class CacheEntry {
  final String key;
  final CacheEntryType type;
  final int size;
  final DateTime createdAt;
  final DateTime lastAccessed;
  final int hitCount;
  
  const CacheEntry({
    required this.key,
    required this.type,
    required this.size,
    required this.createdAt,
    required this.lastAccessed,
    required this.hitCount,
  });
  
  CacheEntry copyWith({
    String? key,
    CacheEntryType? type,
    int? size,
    DateTime? createdAt,
    DateTime? lastAccessed,
    int? hitCount,
  }) {
    return CacheEntry(
      key: key ?? this.key,
      type: type ?? this.type,
      size: size ?? this.size,
      createdAt: createdAt ?? this.createdAt,
      lastAccessed: lastAccessed ?? this.lastAccessed,
      hitCount: hitCount ?? this.hitCount,
    );
  }
}

/// 缓存统计
class CacheStats {
  final int memoryImageCount;
  final int memoryBytesCount;
  final int memoryStringCount;
  final int totalEntries;
  final int hitCount;
  
  const CacheStats({
    required this.memoryImageCount,
    required this.memoryBytesCount,
    required this.memoryStringCount,
    required this.totalEntries,
    required this.hitCount,
  });
  
  Map<String, dynamic> toJson() {
    return {
      'memoryImageCount': memoryImageCount,
      'memoryBytesCount': memoryBytesCount,
      'memoryStringCount': memoryStringCount,
      'totalEntries': totalEntries,
      'hitCount': hitCount,
    };
  }
}
