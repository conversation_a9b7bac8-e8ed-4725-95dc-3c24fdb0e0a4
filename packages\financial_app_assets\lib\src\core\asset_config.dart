import 'dart:convert';
import 'package:flutter/services.dart';
import 'package:meta/meta.dart';

/// 资源配置管理器
/// 
/// 管理资源的配置信息，包括：
/// - 关键资源列表
/// - 缓存策略配置
/// - 预加载策略
/// - 资源优化配置
class AssetConfig {
  static const String _configPath = 'assets/configs/asset_config.json';
  
  Map<String, dynamic> _config = {};
  bool _loaded = false;
  
  /// 加载配置
  Future<void> load() async {
    if (_loaded) return;
    
    try {
      final configString = await rootBundle.loadString(
        'packages/financial_app_assets/$_configPath',
      );
      _config = json.decode(configString) as Map<String, dynamic>;
    } catch (e) {
      // 如果配置文件不存在，使用默认配置
      _config = _getDefaultConfig();
    }
    
    _loaded = true;
  }
  
  /// 获取关键资源列表
  List<String> getCriticalAssets() {
    final critical = _config['critical_assets'] as List<dynamic>?;
    return critical?.cast<String>() ?? [];
  }
  
  /// 获取预加载资源列表
  List<String> getPreloadAssets() {
    final preload = _config['preload_assets'] as List<dynamic>?;
    return preload?.cast<String>() ?? [];
  }
  
  /// 获取缓存配置
  CacheConfig getCacheConfig() {
    final cache = _config['cache'] as Map<String, dynamic>? ?? {};
    return CacheConfig.fromJson(cache);
  }
  
  /// 获取图片优化配置
  ImageOptimizationConfig getImageOptimizationConfig() {
    final optimization = _config['image_optimization'] as Map<String, dynamic>? ?? {};
    return ImageOptimizationConfig.fromJson(optimization);
  }
  
  /// 获取字体配置
  FontConfig getFontConfig() {
    final fonts = _config['fonts'] as Map<String, dynamic>? ?? {};
    return FontConfig.fromJson(fonts);
  }
  
  /// 获取图标配置
  IconConfig getIconConfig() {
    final icons = _config['icons'] as Map<String, dynamic>? ?? {};
    return IconConfig.fromJson(icons);
  }
  
  /// 获取动画配置
  AnimationConfig getAnimationConfig() {
    final animations = _config['animations'] as Map<String, dynamic>? ?? {};
    return AnimationConfig.fromJson(animations);
  }
  
  /// 检查是否启用某个功能
  bool isFeatureEnabled(String feature) {
    final features = _config['features'] as Map<String, dynamic>? ?? {};
    return features[feature] as bool? ?? false;
  }
  
  /// 获取配置值
  T? getValue<T>(String key, [T? defaultValue]) {
    return _config[key] as T? ?? defaultValue;
  }
  
  /// 获取默认配置
  Map<String, dynamic> _getDefaultConfig() {
    return {
      'critical_assets': [
        'assets/images/logos/app_logo.png',
        'assets/images/icons/loading.png',
        'assets/images/backgrounds/splash_bg.png',
      ],
      'preload_assets': [
        'assets/images/icons/home.png',
        'assets/images/icons/market.png',
        'assets/images/icons/trade.png',
        'assets/images/icons/portfolio.png',
        'assets/images/icons/profile.png',
      ],
      'cache': {
        'max_size_mb': 100,
        'max_age_days': 7,
        'cleanup_threshold': 0.8,
        'enable_disk_cache': true,
        'enable_memory_cache': true,
      },
      'image_optimization': {
        'enable_compression': true,
        'quality': 85,
        'enable_webp': true,
        'enable_progressive': true,
        'max_width': 1920,
        'max_height': 1080,
      },
      'fonts': {
        'primary_family': 'FinancialApp',
        'number_family': 'FinancialNumbers',
        'icon_family': 'FinancialIcons',
        'fallback_families': ['Roboto', 'Arial', 'sans-serif'],
      },
      'icons': {
        'default_size': 24.0,
        'default_color': '#333333',
        'enable_svg': true,
        'enable_icon_font': true,
      },
      'animations': {
        'enable_lottie': true,
        'default_duration_ms': 300,
        'enable_hero_animations': true,
      },
      'features': {
        'lazy_loading': true,
        'progressive_loading': true,
        'error_fallback': true,
        'usage_analytics': true,
      },
    };
  }
}

/// 缓存配置
@immutable
class CacheConfig {
  final int maxSizeMB;
  final int maxAgeDays;
  final double cleanupThreshold;
  final bool enableDiskCache;
  final bool enableMemoryCache;
  
  const CacheConfig({
    required this.maxSizeMB,
    required this.maxAgeDays,
    required this.cleanupThreshold,
    required this.enableDiskCache,
    required this.enableMemoryCache,
  });
  
  factory CacheConfig.fromJson(Map<String, dynamic> json) {
    return CacheConfig(
      maxSizeMB: json['max_size_mb'] as int? ?? 100,
      maxAgeDays: json['max_age_days'] as int? ?? 7,
      cleanupThreshold: (json['cleanup_threshold'] as num?)?.toDouble() ?? 0.8,
      enableDiskCache: json['enable_disk_cache'] as bool? ?? true,
      enableMemoryCache: json['enable_memory_cache'] as bool? ?? true,
    );
  }
}

/// 图片优化配置
@immutable
class ImageOptimizationConfig {
  final bool enableCompression;
  final int quality;
  final bool enableWebP;
  final bool enableProgressive;
  final int maxWidth;
  final int maxHeight;
  
  const ImageOptimizationConfig({
    required this.enableCompression,
    required this.quality,
    required this.enableWebP,
    required this.enableProgressive,
    required this.maxWidth,
    required this.maxHeight,
  });
  
  factory ImageOptimizationConfig.fromJson(Map<String, dynamic> json) {
    return ImageOptimizationConfig(
      enableCompression: json['enable_compression'] as bool? ?? true,
      quality: json['quality'] as int? ?? 85,
      enableWebP: json['enable_webp'] as bool? ?? true,
      enableProgressive: json['enable_progressive'] as bool? ?? true,
      maxWidth: json['max_width'] as int? ?? 1920,
      maxHeight: json['max_height'] as int? ?? 1080,
    );
  }
}

/// 字体配置
@immutable
class FontConfig {
  final String primaryFamily;
  final String numberFamily;
  final String iconFamily;
  final List<String> fallbackFamilies;
  
  const FontConfig({
    required this.primaryFamily,
    required this.numberFamily,
    required this.iconFamily,
    required this.fallbackFamilies,
  });
  
  factory FontConfig.fromJson(Map<String, dynamic> json) {
    return FontConfig(
      primaryFamily: json['primary_family'] as String? ?? 'FinancialApp',
      numberFamily: json['number_family'] as String? ?? 'FinancialNumbers',
      iconFamily: json['icon_family'] as String? ?? 'FinancialIcons',
      fallbackFamilies: (json['fallback_families'] as List<dynamic>?)
          ?.cast<String>() ?? ['Roboto', 'Arial', 'sans-serif'],
    );
  }
}

/// 图标配置
@immutable
class IconConfig {
  final double defaultSize;
  final String defaultColor;
  final bool enableSvg;
  final bool enableIconFont;
  
  const IconConfig({
    required this.defaultSize,
    required this.defaultColor,
    required this.enableSvg,
    required this.enableIconFont,
  });
  
  factory IconConfig.fromJson(Map<String, dynamic> json) {
    return IconConfig(
      defaultSize: (json['default_size'] as num?)?.toDouble() ?? 24.0,
      defaultColor: json['default_color'] as String? ?? '#333333',
      enableSvg: json['enable_svg'] as bool? ?? true,
      enableIconFont: json['enable_icon_font'] as bool? ?? true,
    );
  }
}

/// 动画配置
@immutable
class AnimationConfig {
  final bool enableLottie;
  final int defaultDurationMs;
  final bool enableHeroAnimations;
  
  const AnimationConfig({
    required this.enableLottie,
    required this.defaultDurationMs,
    required this.enableHeroAnimations,
  });
  
  factory AnimationConfig.fromJson(Map<String, dynamic> json) {
    return AnimationConfig(
      enableLottie: json['enable_lottie'] as bool? ?? true,
      defaultDurationMs: json['default_duration_ms'] as int? ?? 300,
      enableHeroAnimations: json['enable_hero_animations'] as bool? ?? true,
    );
  }
}
