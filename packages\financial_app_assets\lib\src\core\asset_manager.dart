import 'dart:async';
import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import 'package:crypto/crypto.dart';

import 'asset_cache.dart';
import 'asset_config.dart';

/// 企业级资源管理器
///
/// 提供统一的资源管理功能，包括：
/// - 资源加载和缓存
/// - 资源预加载
/// - 资源优化
/// - 资源监控
class AssetManager {
  static AssetManager? _instance;
  static AssetManager get instance => _instance ??= AssetManager._();

  AssetManager._();

  final AssetCache _cache = AssetCache();
  final AssetConfig _config = AssetConfig();

  bool _initialized = false;
  final Map<String, Completer<void>> _preloadingAssets = {};
  final Map<String, int> _assetUsageCount = {};

  /// 初始化资源管理器
  Future<void> initialize() async {
    if (_initialized) return;

    await _cache.initialize();
    await _config.load();

    // 预加载关键资源
    await _preloadCriticalAssets();

    _initialized = true;
  }

  /// 获取图片资源
  Future<ImageProvider> getImage(
    String assetPath, {
    String? package = 'financial_app_assets',
    bool useCache = true,
    ImageConfiguration? configuration,
  }) async {
    _trackAssetUsage(assetPath);

    if (useCache) {
      final cached = await _cache.getImage(assetPath);
      if (cached != null) {
        return cached;
      }
    }

    final imageProvider = AssetImage(assetPath, package: package);

    if (useCache) {
      await _cache.cacheImage(assetPath, imageProvider);
    }

    return imageProvider;
  }

  /// 获取字节数据
  Future<Uint8List> getBytes(
    String assetPath, {
    String? package = 'financial_app_assets',
    bool useCache = true,
  }) async {
    _trackAssetUsage(assetPath);

    if (useCache) {
      final cached = await _cache.getBytes(assetPath);
      if (cached != null) {
        return cached;
      }
    }

    final fullPath = package != null
        ? 'packages/$package/$assetPath'
        : assetPath;
    final bytes = await rootBundle.load(fullPath);
    final uint8List = bytes.buffer.asUint8List();

    if (useCache) {
      await _cache.cacheBytes(assetPath, uint8List);
    }

    return uint8List;
  }

  /// 获取字符串内容
  Future<String> getString(
    String assetPath, {
    String? package = 'financial_app_assets',
    bool useCache = true,
  }) async {
    _trackAssetUsage(assetPath);

    if (useCache) {
      final cached = await _cache.getString(assetPath);
      if (cached != null) {
        return cached;
      }
    }

    final fullPath = package != null
        ? 'packages/$package/$assetPath'
        : assetPath;
    final content = await rootBundle.loadString(fullPath);

    if (useCache) {
      await _cache.cacheString(assetPath, content);
    }

    return content;
  }

  /// 预加载资源
  Future<void> preloadAsset(
    String assetPath, {
    String? package = 'financial_app_assets',
    AssetType type = AssetType.image,
  }) async {
    final key = '$package:$assetPath';

    if (_preloadingAssets.containsKey(key)) {
      return _preloadingAssets[key]!.future;
    }

    final completer = Completer<void>();
    _preloadingAssets[key] = completer;

    try {
      switch (type) {
        case AssetType.image:
          await getImage(assetPath, package: package);
          break;
        case AssetType.bytes:
          await getBytes(assetPath, package: package);
          break;
        case AssetType.string:
          await getString(assetPath, package: package);
          break;
      }
      completer.complete();
    } catch (e) {
      completer.completeError(e);
    } finally {
      _preloadingAssets.remove(key);
    }
  }

  /// 批量预加载资源
  Future<void> preloadAssets(
    List<String> assetPaths, {
    String? package = 'financial_app_assets',
    AssetType type = AssetType.image,
    int concurrency = 3,
  }) async {
    final futures = <Future<void>>[];

    for (int i = 0; i < assetPaths.length; i += concurrency) {
      final batch = assetPaths.skip(i).take(concurrency);
      final batchFutures = batch.map(
        (path) => preloadAsset(path, package: package, type: type),
      );

      futures.addAll(batchFutures);

      // 等待当前批次完成再继续
      await Future.wait(batchFutures);
    }
  }

  /// 清理缓存
  Future<void> clearCache() async {
    await _cache.clear();
    _assetUsageCount.clear();
  }

  /// 获取缓存大小
  Future<int> getCacheSize() async {
    return await _cache.getSize();
  }

  /// 获取资源使用统计
  Map<String, int> getUsageStats() {
    return Map.unmodifiable(_assetUsageCount);
  }

  /// 检查资源是否存在
  Future<bool> assetExists(
    String assetPath, {
    String? package = 'financial_app_assets',
  }) async {
    try {
      final fullPath = package != null
          ? 'packages/$package/$assetPath'
          : assetPath;
      await rootBundle.load(fullPath);
      return true;
    } catch (e) {
      return false;
    }
  }

  /// 获取资源信息
  Future<AssetInfo> getAssetInfo(
    String assetPath, {
    String? package = 'financial_app_assets',
  }) async {
    final bytes = await getBytes(assetPath, package: package);
    final hash = sha256.convert(bytes).toString();

    return AssetInfo(
      path: assetPath,
      package: package,
      size: bytes.length,
      hash: hash,
      lastAccessed: DateTime.now(),
      usageCount: _assetUsageCount[assetPath] ?? 0,
    );
  }

  /// 预加载关键资源
  Future<void> _preloadCriticalAssets() async {
    final criticalAssets = _config.getCriticalAssets();
    if (criticalAssets.isNotEmpty) {
      await preloadAssets(criticalAssets);
    }
  }

  /// 跟踪资源使用情况
  void _trackAssetUsage(String assetPath) {
    _assetUsageCount[assetPath] = (_assetUsageCount[assetPath] ?? 0) + 1;
  }

  /// 释放资源
  void dispose() {
    _cache.dispose();
    _assetUsageCount.clear();
    _preloadingAssets.clear();
  }
}

/// 资源类型枚举
enum AssetType { image, bytes, string }

/// 资源信息
class AssetInfo {
  final String path;
  final String? package;
  final int size;
  final String hash;
  final DateTime lastAccessed;
  final int usageCount;

  const AssetInfo({
    required this.path,
    this.package,
    required this.size,
    required this.hash,
    required this.lastAccessed,
    required this.usageCount,
  });

  Map<String, dynamic> toJson() {
    return {
      'path': path,
      'package': package,
      'size': size,
      'hash': hash,
      'lastAccessed': lastAccessed.toIso8601String(),
      'usageCount': usageCount,
    };
  }
}
