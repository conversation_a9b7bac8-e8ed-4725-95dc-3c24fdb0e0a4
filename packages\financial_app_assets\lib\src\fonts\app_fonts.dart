import 'package:flutter/material.dart';

/// 应用字体资源管理
///
/// 统一管理应用中使用的所有字体，包括：
/// - 主要字体系列
/// - 数字专用字体
/// - 图标字体
/// - 字体权重和样式
class AppFonts {
  // 字体包名
  static const String packageName = 'financial_app_assets';

  // ==================== 字体系列 ====================

  /// 主要字体系列 - 用于一般文本
  static const String primaryFamily = 'FinancialApp';

  /// 数字字体系列 - 用于金融数据显示
  static const String numberFamily = 'FinancialNumbers';

  /// 图标字体系列 - 用于图标显示
  static const String iconFamily = 'FinancialIcons';

  // ==================== 字体权重 ====================

  /// 常规字重
  static const FontWeight regular = FontWeight.w400;

  /// 中等字重
  static const FontWeight medium = FontWeight.w500;

  /// 半粗字重
  static const FontWeight semiBold = FontWeight.w600;

  /// 粗体字重
  static const FontWeight bold = FontWeight.w700;

  // ==================== 预定义文本样式 ====================

  /// 标题样式
  static const TextStyle titleStyle = TextStyle(
    fontFamily: primaryFamily,
    fontWeight: bold,
    fontSize: 24,
    height: 1.2,
    package: packageName,
  );

  /// 副标题样式
  static const TextStyle subtitleStyle = TextStyle(
    fontFamily: primaryFamily,
    fontWeight: semiBold,
    fontSize: 18,
    height: 1.3,
    package: packageName,
  );

  /// 正文样式
  static const TextStyle bodyStyle = TextStyle(
    fontFamily: primaryFamily,
    fontWeight: regular,
    fontSize: 16,
    height: 1.4,
    package: packageName,
  );

  /// 小字体样式
  static const TextStyle captionStyle = TextStyle(
    fontFamily: primaryFamily,
    fontWeight: regular,
    fontSize: 12,
    height: 1.3,
    package: packageName,
  );

  /// 按钮文字样式
  static const TextStyle buttonStyle = TextStyle(
    fontFamily: primaryFamily,
    fontWeight: semiBold,
    fontSize: 16,
    height: 1.2,
    package: packageName,
  );

  // ==================== 数字显示样式 ====================

  /// 大数字样式 - 用于价格、金额等重要数字
  static const TextStyle largeNumberStyle = TextStyle(
    fontFamily: numberFamily,
    fontWeight: bold,
    fontSize: 32,
    height: 1.1,
    package: packageName,
  );

  /// 中等数字样式 - 用于一般数字显示
  static const TextStyle mediumNumberStyle = TextStyle(
    fontFamily: numberFamily,
    fontWeight: medium,
    fontSize: 20,
    height: 1.2,
    package: packageName,
  );

  /// 小数字样式 - 用于次要数字信息
  static const TextStyle smallNumberStyle = TextStyle(
    fontFamily: numberFamily,
    fontWeight: regular,
    fontSize: 14,
    height: 1.3,
    package: packageName,
  );

  /// 价格样式 - 专门用于价格显示
  static const TextStyle priceStyle = TextStyle(
    fontFamily: numberFamily,
    fontWeight: bold,
    fontSize: 24,
    height: 1.1,
    package: packageName,
  );

  /// 百分比样式 - 用于涨跌幅显示
  static const TextStyle percentageStyle = TextStyle(
    fontFamily: numberFamily,
    fontWeight: semiBold,
    fontSize: 16,
    height: 1.2,
    package: packageName,
  );

  // ==================== 工具方法 ====================

  /// 创建自定义文本样式
  static TextStyle createStyle({
    String fontFamily = primaryFamily,
    FontWeight fontWeight = regular,
    double fontSize = 16,
    double? height,
    Color? color,
    double? letterSpacing,
    double? wordSpacing,
    TextDecoration? decoration,
    Color? decorationColor,
    TextDecorationStyle? decorationStyle,
    double? decorationThickness,
  }) {
    return TextStyle(
      fontFamily: fontFamily,
      fontWeight: fontWeight,
      fontSize: fontSize,
      height: height,
      color: color,
      letterSpacing: letterSpacing,
      wordSpacing: wordSpacing,
      decoration: decoration,
      decorationColor: decorationColor,
      decorationStyle: decorationStyle,
      decorationThickness: decorationThickness,
      package: packageName,
    );
  }

  /// 创建数字样式
  static TextStyle createNumberStyle({
    FontWeight fontWeight = regular,
    double fontSize = 16,
    double? height,
    Color? color,
  }) {
    return createStyle(
      fontFamily: numberFamily,
      fontWeight: fontWeight,
      fontSize: fontSize,
      height: height,
      color: color,
    );
  }

  /// 获取响应式字体大小
  static double getResponsiveFontSize(
    BuildContext context,
    double baseFontSize,
  ) {
    final screenWidth = MediaQuery.of(context).size.width;
    final scaleFactor = screenWidth / 375.0; // 基于 iPhone X 宽度
    return baseFontSize * scaleFactor.clamp(0.8, 1.2);
  }

  /// 获取主题相关的文本样式
  static TextStyle getThemedStyle(BuildContext context, TextStyle baseStyle) {
    final theme = Theme.of(context);
    return baseStyle.copyWith(
      color: baseStyle.color ?? theme.textTheme.bodyLarge?.color,
    );
  }
}

/// 字体配置类
class FontConfig {
  /// 字体系列列表
  static const List<String> fontFamilies = [
    AppFonts.primaryFamily,
    AppFonts.numberFamily,
    AppFonts.iconFamily,
  ];

  /// 支持的字体权重
  static const List<FontWeight> supportedWeights = [
    AppFonts.regular,
    AppFonts.medium,
    AppFonts.semiBold,
    AppFonts.bold,
  ];

  /// 预定义字体大小
  static const Map<String, double> fontSizes = {
    'xs': 10,
    'sm': 12,
    'base': 14,
    'lg': 16,
    'xl': 18,
    '2xl': 20,
    '3xl': 24,
    '4xl': 32,
    '5xl': 40,
    '6xl': 48,
  };

  /// 行高比例
  static const Map<String, double> lineHeights = {
    'tight': 1.1,
    'normal': 1.2,
    'relaxed': 1.4,
    'loose': 1.6,
  };
}
