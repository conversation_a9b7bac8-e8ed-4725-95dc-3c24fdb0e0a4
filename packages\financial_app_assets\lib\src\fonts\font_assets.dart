/// 字体资源路径管理
/// 
/// 管理所有字体文件的路径和配置
class FontAssets {
  FontAssets._();

  // 基础路径
  static const String _basePath = 'assets/fonts';

  // ==================== 字体文件路径 ====================
  
  // PingFang SC 字体系列
  static const String pingFangSCThin = '$_basePath/PingFangSC/PingFangSC-Thin.ttf';
  static const String pingFangSCLight = '$_basePath/PingFangSC/PingFangSC-Light.ttf';
  static const String pingFangSCRegular = '$_basePath/PingFangSC/PingFangSC-Regular.ttf';
  static const String pingFangSCMedium = '$_basePath/PingFangSC/PingFangSC-Medium.ttf';
  static const String pingFangSCSemibold = '$_basePath/PingFangSC/PingFangSC-Semibold.ttf';
  
  // SF Pro Text 字体系列
  static const String sfProTextThin = '$_basePath/SFProText/SFProText-Thin.ttf';
  static const String sfProTextLight = '$_basePath/SFProText/SFProText-Light.ttf';
  static const String sfProTextRegular = '$_basePath/SFProText/SFProText-Regular.ttf';
  static const String sfProTextMedium = '$_basePath/SFProText/SFProText-Medium.ttf';
  static const String sfProTextSemibold = '$_basePath/SFProText/SFProText-Semibold.ttf';
  static const String sfProTextBold = '$_basePath/SFProText/SFProText-Bold.ttf';
  
  // SF Mono 字体系列（等宽字体）
  static const String sfMonoLight = '$_basePath/SFMono/SFMono-Light.ttf';
  static const String sfMonoRegular = '$_basePath/SFMono/SFMono-Regular.ttf';
  static const String sfMonoMedium = '$_basePath/SFMono/SFMono-Medium.ttf';
  static const String sfMonoSemibold = '$_basePath/SFMono/SFMono-Semibold.ttf';
  static const String sfMonoBold = '$_basePath/SFMono/SFMono-Bold.ttf';
  
  // 自定义图标字体
  static const String financialIcons = '$_basePath/FinancialIcons/FinancialIcons.ttf';
  
  // 数字专用字体
  static const String robotoMono = '$_basePath/RobotoMono/RobotoMono-Regular.ttf';
  static const String robotoMonoMedium = '$_basePath/RobotoMono/RobotoMono-Medium.ttf';
  static const String robotoMonoBold = '$_basePath/RobotoMono/RobotoMono-Bold.ttf';

  // ==================== 字体配置 ====================
  
  /// 字体配置信息
  static const Map<String, FontConfig> fontConfigs = {
    'PingFang SC': FontConfig(
      family: 'PingFang SC',
      weights: {
        100: pingFangSCThin,
        300: pingFangSCLight,
        400: pingFangSCRegular,
        500: pingFangSCMedium,
        600: pingFangSCSemibold,
      },
      description: '苹果中文字体，适合中文显示',
      supportedLanguages: ['zh-CN', 'zh-TW'],
    ),
    'SF Pro Text': FontConfig(
      family: 'SF Pro Text',
      weights: {
        100: sfProTextThin,
        300: sfProTextLight,
        400: sfProTextRegular,
        500: sfProTextMedium,
        600: sfProTextSemibold,
        700: sfProTextBold,
      },
      description: '苹果英文字体，适合英文显示',
      supportedLanguages: ['en'],
    ),
    'SF Mono': FontConfig(
      family: 'SF Mono',
      weights: {
        300: sfMonoLight,
        400: sfMonoRegular,
        500: sfMonoMedium,
        600: sfMonoSemibold,
        700: sfMonoBold,
      },
      description: '等宽字体，适合数字和代码显示',
      supportedLanguages: ['en'],
      isMonospace: true,
    ),
    'FinancialIcons': FontConfig(
      family: 'FinancialIcons',
      weights: {
        400: financialIcons,
      },
      description: '金融应用专用图标字体',
      supportedLanguages: [],
      isIconFont: true,
    ),
    'Roboto Mono': FontConfig(
      family: 'Roboto Mono',
      weights: {
        400: robotoMono,
        500: robotoMonoMedium,
        700: robotoMonoBold,
      },
      description: 'Google等宽字体，数字显示备选',
      supportedLanguages: ['en'],
      isMonospace: true,
    ),
  };

  // ==================== 工具方法 ====================
  
  /// 获取字体文件路径
  static String? getFontPath(String family, int weight) {
    final config = fontConfigs[family];
    if (config == null) return null;
    
    // 查找最接近的字重
    final availableWeights = config.weights.keys.toList()..sort();
    int closestWeight = availableWeights.first;
    
    for (final availableWeight in availableWeights) {
      if ((availableWeight - weight).abs() < (closestWeight - weight).abs()) {
        closestWeight = availableWeight;
      }
    }
    
    return config.weights[closestWeight];
  }
  
  /// 获取所有字体文件路径
  static List<String> getAllFontPaths() {
    final paths = <String>[];
    for (final config in fontConfigs.values) {
      paths.addAll(config.weights.values);
    }
    return paths;
  }
  
  /// 获取支持指定语言的字体
  static List<String> getFontsForLanguage(String languageCode) {
    final fonts = <String>[];
    for (final entry in fontConfigs.entries) {
      if (entry.value.supportedLanguages.contains(languageCode)) {
        fonts.add(entry.key);
      }
    }
    return fonts;
  }
  
  /// 获取等宽字体
  static List<String> getMonospaceFonts() {
    final fonts = <String>[];
    for (final entry in fontConfigs.entries) {
      if (entry.value.isMonospace) {
        fonts.add(entry.key);
      }
    }
    return fonts;
  }
  
  /// 获取图标字体
  static List<String> getIconFonts() {
    final fonts = <String>[];
    for (final entry in fontConfigs.entries) {
      if (entry.value.isIconFont) {
        fonts.add(entry.key);
      }
    }
    return fonts;
  }
  
  /// 检查字体是否存在
  static bool fontExists(String family) {
    return fontConfigs.containsKey(family);
  }
  
  /// 获取字体配置
  static FontConfig? getFontConfig(String family) {
    return fontConfigs[family];
  }
}

/// 字体配置类
class FontConfig {
  /// 字体家族名称
  final String family;
  
  /// 字重对应的文件路径
  final Map<int, String> weights;
  
  /// 字体描述
  final String description;
  
  /// 支持的语言代码
  final List<String> supportedLanguages;
  
  /// 是否为等宽字体
  final bool isMonospace;
  
  /// 是否为图标字体
  final bool isIconFont;

  const FontConfig({
    required this.family,
    required this.weights,
    required this.description,
    this.supportedLanguages = const [],
    this.isMonospace = false,
    this.isIconFont = false,
  });

  /// 获取可用的字重列表
  List<int> get availableWeights => weights.keys.toList()..sort();
  
  /// 获取默认字重
  int get defaultWeight => availableWeights.contains(400) ? 400 : availableWeights.first;
  
  /// 获取默认字体文件路径
  String get defaultPath => weights[defaultWeight]!;
  
  /// 是否支持指定语言
  bool supportsLanguage(String languageCode) {
    return supportedLanguages.contains(languageCode);
  }
  
  /// 是否有指定字重
  bool hasWeight(int weight) {
    return weights.containsKey(weight);
  }
  
  /// 获取最接近的字重
  int getClosestWeight(int targetWeight) {
    final availableWeights = weights.keys.toList()..sort();
    int closestWeight = availableWeights.first;
    
    for (final weight in availableWeights) {
      if ((weight - targetWeight).abs() < (closestWeight - targetWeight).abs()) {
        closestWeight = weight;
      }
    }
    
    return closestWeight;
  }
  
  @override
  String toString() {
    return 'FontConfig(family: $family, weights: ${weights.keys.toList()}, '
           'description: $description, isMonospace: $isMonospace, isIconFont: $isIconFont)';
  }
}
