import 'package:flutter/widgets.dart';

/// 应用图标资源管理
/// 
/// 统一管理应用中使用的所有图标，包括：
/// - 导航图标
/// - 功能图标
/// - 状态图标
/// - 金融专用图标
class AppIcons {
  // 图标包名
  static const String packageName = 'financial_app_assets';
  
  // ==================== 导航图标 ====================
  
  /// 首页图标
  static const IconData home = IconData(0xe900, fontFamily: 'FinancialIcons', fontPackage: packageName);
  
  /// 市场图标
  static const IconData market = IconData(0xe901, fontFamily: 'FinancialIcons', fontPackage: packageName);
  
  /// 交易图标
  static const IconData trade = IconData(0xe902, fontFamily: 'FinancialIcons', fontPackage: packageName);
  
  /// 投资组合图标
  static const IconData portfolio = IconData(0xe903, fontFamily: 'FinancialIcons', fontPackage: packageName);
  
  /// 个人资料图标
  static const IconData profile = IconData(0xe904, fontFamily: 'FinancialIcons', fontPackage: packageName);
  
  // ==================== 功能图标 ====================
  
  /// 搜索图标
  static const IconData search = IconData(0xe905, fontFamily: 'FinancialIcons', fontPackage: packageName);
  
  /// 设置图标
  static const IconData settings = IconData(0xe906, fontFamily: 'FinancialIcons', fontPackage: packageName);
  
  /// 通知图标
  static const IconData notification = IconData(0xe907, fontFamily: 'FinancialIcons', fontPackage: packageName);
  
  /// 收藏图标
  static const IconData favorite = IconData(0xe908, fontFamily: 'FinancialIcons', fontPackage: packageName);
  
  /// 分享图标
  static const IconData share = IconData(0xe909, fontFamily: 'FinancialIcons', fontPackage: packageName);
  
  /// 下载图标
  static const IconData download = IconData(0xe90a, fontFamily: 'FinancialIcons', fontPackage: packageName);
  
  /// 上传图标
  static const IconData upload = IconData(0xe90b, fontFamily: 'FinancialIcons', fontPackage: packageName);
  
  /// 编辑图标
  static const IconData edit = IconData(0xe90c, fontFamily: 'FinancialIcons', fontPackage: packageName);
  
  /// 删除图标
  static const IconData delete = IconData(0xe90d, fontFamily: 'FinancialIcons', fontPackage: packageName);
  
  /// 添加图标
  static const IconData add = IconData(0xe90e, fontFamily: 'FinancialIcons', fontPackage: packageName);
  
  /// 减少图标
  static const IconData remove = IconData(0xe90f, fontFamily: 'FinancialIcons', fontPackage: packageName);
  
  // ==================== 状态图标 ====================
  
  /// 成功图标
  static const IconData success = IconData(0xe910, fontFamily: 'FinancialIcons', fontPackage: packageName);
  
  /// 错误图标
  static const IconData error = IconData(0xe911, fontFamily: 'FinancialIcons', fontPackage: packageName);
  
  /// 警告图标
  static const IconData warning = IconData(0xe912, fontFamily: 'FinancialIcons', fontPackage: packageName);
  
  /// 信息图标
  static const IconData info = IconData(0xe913, fontFamily: 'FinancialIcons', fontPackage: packageName);
  
  /// 加载图标
  static const IconData loading = IconData(0xe914, fontFamily: 'FinancialIcons', fontPackage: packageName);
  
  /// 刷新图标
  static const IconData refresh = IconData(0xe915, fontFamily: 'FinancialIcons', fontPackage: packageName);
  
  // ==================== 金融专用图标 ====================
  
  /// 钱包图标
  static const IconData wallet = IconData(0xe920, fontFamily: 'FinancialIcons', fontPackage: packageName);
  
  /// 银行卡图标
  static const IconData creditCard = IconData(0xe921, fontFamily: 'FinancialIcons', fontPackage: packageName);
  
  /// 货币图标
  static const IconData currency = IconData(0xe922, fontFamily: 'FinancialIcons', fontPackage: packageName);
  
  /// 图表上涨图标
  static const IconData chartUp = IconData(0xe923, fontFamily: 'FinancialIcons', fontPackage: packageName);
  
  /// 图表下跌图标
  static const IconData chartDown = IconData(0xe924, fontFamily: 'FinancialIcons', fontPackage: packageName);
  
  /// 趋势图标
  static const IconData trend = IconData(0xe925, fontFamily: 'FinancialIcons', fontPackage: packageName);
  
  /// 计算器图标
  static const IconData calculator = IconData(0xe926, fontFamily: 'FinancialIcons', fontPackage: packageName);
  
  /// 报告图标
  static const IconData report = IconData(0xe927, fontFamily: 'FinancialIcons', fontPackage: packageName);
  
  /// 分析图标
  static const IconData analytics = IconData(0xe928, fontFamily: 'FinancialIcons', fontPackage: packageName);
  
  /// 投资图标
  static const IconData investment = IconData(0xe929, fontFamily: 'FinancialIcons', fontPackage: packageName);
  
  /// 收益图标
  static const IconData profit = IconData(0xe92a, fontFamily: 'FinancialIcons', fontPackage: packageName);
  
  /// 损失图标
  static const IconData loss = IconData(0xe92b, fontFamily: 'FinancialIcons', fontPackage: packageName);
  
  /// 风险图标
  static const IconData risk = IconData(0xe92c, fontFamily: 'FinancialIcons', fontPackage: packageName);
  
  /// 安全图标
  static const IconData security = IconData(0xe92d, fontFamily: 'FinancialIcons', fontPackage: packageName);
  
  // ==================== 交易相关图标 ====================
  
  /// 买入图标
  static const IconData buy = IconData(0xe930, fontFamily: 'FinancialIcons', fontPackage: packageName);
  
  /// 卖出图标
  static const IconData sell = IconData(0xe931, fontFamily: 'FinancialIcons', fontPackage: packageName);
  
  /// 订单图标
  static const IconData order = IconData(0xe932, fontFamily: 'FinancialIcons', fontPackage: packageName);
  
  /// 历史图标
  static const IconData history = IconData(0xe933, fontFamily: 'FinancialIcons', fontPackage: packageName);
  
  /// 持仓图标
  static const IconData position = IconData(0xe934, fontFamily: 'FinancialIcons', fontPackage: packageName);
  
  /// 余额图标
  static const IconData balance = IconData(0xe935, fontFamily: 'FinancialIcons', fontPackage: packageName);
  
  // ==================== 工具方法 ====================
  
  /// 获取图标集合
  static Map<String, IconData> get navigationIcons => {
    'home': home,
    'market': market,
    'trade': trade,
    'portfolio': portfolio,
    'profile': profile,
  };
  
  static Map<String, IconData> get functionIcons => {
    'search': search,
    'settings': settings,
    'notification': notification,
    'favorite': favorite,
    'share': share,
    'download': download,
    'upload': upload,
    'edit': edit,
    'delete': delete,
    'add': add,
    'remove': remove,
  };
  
  static Map<String, IconData> get statusIcons => {
    'success': success,
    'error': error,
    'warning': warning,
    'info': info,
    'loading': loading,
    'refresh': refresh,
  };
  
  static Map<String, IconData> get financialIcons => {
    'wallet': wallet,
    'creditCard': creditCard,
    'currency': currency,
    'chartUp': chartUp,
    'chartDown': chartDown,
    'trend': trend,
    'calculator': calculator,
    'report': report,
    'analytics': analytics,
    'investment': investment,
    'profit': profit,
    'loss': loss,
    'risk': risk,
    'security': security,
  };
  
  static Map<String, IconData> get tradeIcons => {
    'buy': buy,
    'sell': sell,
    'order': order,
    'history': history,
    'position': position,
    'balance': balance,
  };
  
  /// 根据名称获取图标
  static IconData? getIcon(String name) {
    final allIcons = {
      ...navigationIcons,
      ...functionIcons,
      ...statusIcons,
      ...financialIcons,
      ...tradeIcons,
    };
    return allIcons[name];
  }
  
  /// 获取所有图标
  static Map<String, IconData> get allIcons => {
    ...navigationIcons,
    ...functionIcons,
    ...statusIcons,
    ...financialIcons,
    ...tradeIcons,
  };
  
  /// 创建图标组件
  static Icon createIcon(
    IconData iconData, {
    double? size,
    Color? color,
    String? semanticLabel,
    TextDirection? textDirection,
  }) {
    return Icon(
      iconData,
      size: size,
      color: color,
      semanticLabel: semanticLabel,
      textDirection: textDirection,
    );
  }
}
