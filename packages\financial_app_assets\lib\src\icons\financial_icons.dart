import 'package:flutter/widgets.dart';

/// 金融专用图标字体
/// 
/// 包含金融应用特有的图标，如货币符号、交易图标等
class FinancialIcons {
  FinancialIcons._();

  // 图标包名
  static const String packageName = 'financial_app_assets';
  static const String fontFamily = 'FinancialIcons';

  // ==================== 货币符号 ====================
  
  /// 人民币符号
  static const IconData cny = IconData(0xe100, fontFamily: fontFamily, fontPackage: packageName);
  
  /// 美元符号
  static const IconData usd = IconData(0xe101, fontFamily: fontFamily, fontPackage: packageName);
  
  /// 欧元符号
  static const IconData eur = IconData(0xe102, fontFamily: fontFamily, fontPackage: packageName);
  
  /// 日元符号
  static const IconData jpy = IconData(0xe103, fontFamily: fontFamily, fontPackage: packageName);
  
  /// 英镑符号
  static const IconData gbp = IconData(0xe104, fontFamily: fontFamily, fontPackage: packageName);
  
  /// 比特币符号
  static const IconData btc = IconData(0xe105, fontFamily: fontFamily, fontPackage: packageName);
  
  /// 以太坊符号
  static const IconData eth = IconData(0xe106, fontFamily: fontFamily, fontPackage: packageName);

  // ==================== 交易图标 ====================
  
  /// 买入箭头
  static const IconData buyArrow = IconData(0xe200, fontFamily: fontFamily, fontPackage: packageName);
  
  /// 卖出箭头
  static const IconData sellArrow = IconData(0xe201, fontFamily: fontFamily, fontPackage: packageName);
  
  /// 涨幅三角
  static const IconData riseTriangle = IconData(0xe202, fontFamily: fontFamily, fontPackage: packageName);
  
  /// 跌幅三角
  static const IconData fallTriangle = IconData(0xe203, fontFamily: fontFamily, fontPackage: packageName);
  
  /// 持平线
  static const IconData flatLine = IconData(0xe204, fontFamily: fontFamily, fontPackage: packageName);
  
  /// 交易信号
  static const IconData tradeSignal = IconData(0xe205, fontFamily: fontFamily, fontPackage: packageName);
  
  /// 止损
  static const IconData stopLoss = IconData(0xe206, fontFamily: fontFamily, fontPackage: packageName);
  
  /// 止盈
  static const IconData takeProfit = IconData(0xe207, fontFamily: fontFamily, fontPackage: packageName);

  // ==================== 图表图标 ====================
  
  /// K线图
  static const IconData candlestick = IconData(0xe300, fontFamily: fontFamily, fontPackage: packageName);
  
  /// 折线图
  static const IconData lineChart = IconData(0xe301, fontFamily: fontFamily, fontPackage: packageName);
  
  /// 柱状图
  static const IconData barChart = IconData(0xe302, fontFamily: fontFamily, fontPackage: packageName);
  
  /// 面积图
  static const IconData areaChart = IconData(0xe303, fontFamily: fontFamily, fontPackage: packageName);
  
  /// 深度图
  static const IconData depthChart = IconData(0xe304, fontFamily: fontFamily, fontPackage: packageName);
  
  /// 技术指标
  static const IconData indicator = IconData(0xe305, fontFamily: fontFamily, fontPackage: packageName);
  
  /// 成交量
  static const IconData volume = IconData(0xe306, fontFamily: fontFamily, fontPackage: packageName);

  // ==================== 投资组合图标 ====================
  
  /// 投资组合
  static const IconData portfolio = IconData(0xe400, fontFamily: fontFamily, fontPackage: packageName);
  
  /// 资产配置
  static const IconData allocation = IconData(0xe401, fontFamily: fontFamily, fontPackage: packageName);
  
  /// 收益率
  static const IconData returns = IconData(0xe402, fontFamily: fontFamily, fontPackage: packageName);
  
  /// 风险评估
  static const IconData risk = IconData(0xe403, fontFamily: fontFamily, fontPackage: packageName);
  
  /// 分散投资
  static const IconData diversification = IconData(0xe404, fontFamily: fontFamily, fontPackage: packageName);
  
  /// 再平衡
  static const IconData rebalance = IconData(0xe405, fontFamily: fontFamily, fontPackage: packageName);

  // ==================== 市场图标 ====================
  
  /// 股票
  static const IconData stock = IconData(0xe500, fontFamily: fontFamily, fontPackage: packageName);
  
  /// 基金
  static const IconData fund = IconData(0xe501, fontFamily: fontFamily, fontPackage: packageName);
  
  /// 债券
  static const IconData bond = IconData(0xe502, fontFamily: fontFamily, fontPackage: packageName);
  
  /// 期货
  static const IconData futures = IconData(0xe503, fontFamily: fontFamily, fontPackage: packageName);
  
  /// 期权
  static const IconData options = IconData(0xe504, fontFamily: fontFamily, fontPackage: packageName);
  
  /// 外汇
  static const IconData forex = IconData(0xe505, fontFamily: fontFamily, fontPackage: packageName);
  
  /// 加密货币
  static const IconData crypto = IconData(0xe506, fontFamily: fontFamily, fontPackage: packageName);
  
  /// 商品
  static const IconData commodity = IconData(0xe507, fontFamily: fontFamily, fontPackage: packageName);

  // ==================== 银行图标 ====================
  
  /// 银行
  static const IconData bank = IconData(0xe600, fontFamily: fontFamily, fontPackage: packageName);
  
  /// 信用卡
  static const IconData creditCard = IconData(0xe601, fontFamily: fontFamily, fontPackage: packageName);
  
  /// 借记卡
  static const IconData debitCard = IconData(0xe602, fontFamily: fontFamily, fontPackage: packageName);
  
  /// 转账
  static const IconData transfer = IconData(0xe603, fontFamily: fontFamily, fontPackage: packageName);
  
  /// 存款
  static const IconData deposit = IconData(0xe604, fontFamily: fontFamily, fontPackage: packageName);
  
  /// 取款
  static const IconData withdrawal = IconData(0xe605, fontFamily: fontFamily, fontPackage: packageName);
  
  /// 贷款
  static const IconData loan = IconData(0xe606, fontFamily: fontFamily, fontPackage: packageName);

  // ==================== 分析图标 ====================
  
  /// 趋势分析
  static const IconData trendAnalysis = IconData(0xe700, fontFamily: fontFamily, fontPackage: packageName);
  
  /// 技术分析
  static const IconData technicalAnalysis = IconData(0xe701, fontFamily: fontFamily, fontPackage: packageName);
  
  /// 基本面分析
  static const IconData fundamentalAnalysis = IconData(0xe702, fontFamily: fontFamily, fontPackage: packageName);
  
  /// 量化分析
  static const IconData quantAnalysis = IconData(0xe703, fontFamily: fontFamily, fontPackage: packageName);
  
  /// 风险分析
  static const IconData riskAnalysis = IconData(0xe704, fontFamily: fontFamily, fontPackage: packageName);
  
  /// 回测
  static const IconData backtest = IconData(0xe705, fontFamily: fontFamily, fontPackage: packageName);

  // ==================== 状态图标 ====================
  
  /// 开市
  static const IconData marketOpen = IconData(0xe800, fontFamily: fontFamily, fontPackage: packageName);
  
  /// 闭市
  static const IconData marketClosed = IconData(0xe801, fontFamily: fontFamily, fontPackage: packageName);
  
  /// 暂停交易
  static const IconData tradingHalted = IconData(0xe802, fontFamily: fontFamily, fontPackage: packageName);
  
  /// 连接状态
  static const IconData connected = IconData(0xe803, fontFamily: fontFamily, fontPackage: packageName);
  
  /// 断开连接
  static const IconData disconnected = IconData(0xe804, fontFamily: fontFamily, fontPackage: packageName);
  
  /// 同步中
  static const IconData syncing = IconData(0xe805, fontFamily: fontFamily, fontPackage: packageName);

  // ==================== 工具方法 ====================
  
  /// 根据名称获取图标
  static IconData? getIconByName(String name) {
    switch (name.toLowerCase()) {
      // 货币符号
      case 'cny':
        return cny;
      case 'usd':
        return usd;
      case 'eur':
        return eur;
      case 'jpy':
        return jpy;
      case 'gbp':
        return gbp;
      case 'btc':
        return btc;
      case 'eth':
        return eth;
      
      // 交易图标
      case 'buy':
      case 'buyarrow':
        return buyArrow;
      case 'sell':
      case 'sellarrow':
        return sellArrow;
      case 'rise':
      case 'risetriangle':
        return riseTriangle;
      case 'fall':
      case 'falltriangle':
        return fallTriangle;
      case 'flat':
      case 'flatline':
        return flatLine;
      
      // 图表图标
      case 'candlestick':
        return candlestick;
      case 'linechart':
        return lineChart;
      case 'barchart':
        return barChart;
      case 'areachart':
        return areaChart;
      case 'depthchart':
        return depthChart;
      
      // 市场图标
      case 'stock':
        return stock;
      case 'fund':
        return fund;
      case 'bond':
        return bond;
      case 'futures':
        return futures;
      case 'options':
        return options;
      case 'forex':
        return forex;
      case 'crypto':
        return crypto;
      
      default:
        return null;
    }
  }
  
  /// 获取所有货币图标
  static Map<String, IconData> getCurrencyIcons() {
    return {
      'CNY': cny,
      'USD': usd,
      'EUR': eur,
      'JPY': jpy,
      'GBP': gbp,
      'BTC': btc,
      'ETH': eth,
    };
  }
  
  /// 获取所有交易图标
  static Map<String, IconData> getTradeIcons() {
    return {
      'buy': buyArrow,
      'sell': sellArrow,
      'rise': riseTriangle,
      'fall': fallTriangle,
      'flat': flatLine,
      'signal': tradeSignal,
      'stopLoss': stopLoss,
      'takeProfit': takeProfit,
    };
  }
  
  /// 获取所有图表图标
  static Map<String, IconData> getChartIcons() {
    return {
      'candlestick': candlestick,
      'line': lineChart,
      'bar': barChart,
      'area': areaChart,
      'depth': depthChart,
      'indicator': indicator,
      'volume': volume,
    };
  }
  
  /// 获取所有市场图标
  static Map<String, IconData> getMarketIcons() {
    return {
      'stock': stock,
      'fund': fund,
      'bond': bond,
      'futures': futures,
      'options': options,
      'forex': forex,
      'crypto': crypto,
      'commodity': commodity,
    };
  }
}
