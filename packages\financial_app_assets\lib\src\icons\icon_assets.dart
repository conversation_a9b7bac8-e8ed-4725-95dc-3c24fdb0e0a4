/// 图标资源路径管理
/// 
/// 管理所有SVG图标和PNG图标的路径
class IconAssets {
  IconAssets._();

  // 基础路径
  static const String _basePath = 'assets/icons';
  static const String _svgPath = '$_basePath/svg';
  static const String _pngPath = '$_basePath/png';

  // ==================== SVG 图标 ====================
  
  // 导航相关
  static const String homeSvg = '$_svgPath/nav/home.svg';
  static const String marketSvg = '$_svgPath/nav/market.svg';
  static const String tradeSvg = '$_svgPath/nav/trade.svg';
  static const String portfolioSvg = '$_svgPath/nav/portfolio.svg';
  static const String profileSvg = '$_svgPath/nav/profile.svg';
  
  // 功能相关
  static const String searchSvg = '$_svgPath/function/search.svg';
  static const String settingsSvg = '$_svgPath/function/settings.svg';
  static const String notificationSvg = '$_svgPath/function/notification.svg';
  static const String favoriteSvg = '$_svgPath/function/favorite.svg';
  static const String shareSvg = '$_svgPath/function/share.svg';
  static const String moreSvg = '$_svgPath/function/more.svg';
  
  // 交易相关
  static const String buySvg = '$_svgPath/trade/buy.svg';
  static const String sellSvg = '$_svgPath/trade/sell.svg';
  static const String increaseSvg = '$_svgPath/trade/increase.svg';
  static const String decreaseSvg = '$_svgPath/trade/decrease.svg';
  static const String flatSvg = '$_svgPath/trade/flat.svg';
  static const String chartSvg = '$_svgPath/trade/chart.svg';
  static const String candlestickSvg = '$_svgPath/trade/candlestick.svg';
  static const String depthSvg = '$_svgPath/trade/depth.svg';
  
  // 状态相关
  static const String successSvg = '$_svgPath/status/success.svg';
  static const String errorSvg = '$_svgPath/status/error.svg';
  static const String warningSvg = '$_svgPath/status/warning.svg';
  static const String infoSvg = '$_svgPath/status/info.svg';
  static const String loadingSvg = '$_svgPath/status/loading.svg';
  
  // 操作相关
  static const String addSvg = '$_svgPath/action/add.svg';
  static const String deleteSvg = '$_svgPath/action/delete.svg';
  static const String editSvg = '$_svgPath/action/edit.svg';
  static const String copySvg = '$_svgPath/action/copy.svg';
  static const String refreshSvg = '$_svgPath/action/refresh.svg';
  static const String closeSvg = '$_svgPath/action/close.svg';
  static const String backSvg = '$_svgPath/action/back.svg';
  static const String forwardSvg = '$_svgPath/action/forward.svg';
  
  // 账户相关
  static const String loginSvg = '$_svgPath/account/login.svg';
  static const String logoutSvg = '$_svgPath/account/logout.svg';
  static const String registerSvg = '$_svgPath/account/register.svg';
  static const String passwordSvg = '$_svgPath/account/password.svg';
  static const String emailSvg = '$_svgPath/account/email.svg';
  static const String phoneSvg = '$_svgPath/account/phone.svg';
  
  // 金融相关
  static const String currencySvg = '$_svgPath/finance/currency.svg';
  static const String walletSvg = '$_svgPath/finance/wallet.svg';
  static const String bankSvg = '$_svgPath/finance/bank.svg';
  static const String creditCardSvg = '$_svgPath/finance/credit_card.svg';
  static const String transferSvg = '$_svgPath/finance/transfer.svg';
  static const String incomeSvg = '$_svgPath/finance/income.svg';
  static const String expenseSvg = '$_svgPath/finance/expense.svg';
  
  // 时间相关
  static const String calendarSvg = '$_svgPath/time/calendar.svg';
  static const String timeSvg = '$_svgPath/time/time.svg';
  static const String historySvg = '$_svgPath/time/history.svg';
  
  // 网络状态
  static const String onlineSvg = '$_svgPath/network/online.svg';
  static const String offlineSvg = '$_svgPath/network/offline.svg';
  static const String connectingSvg = '$_svgPath/network/connecting.svg';
  
  // 主题相关
  static const String lightThemeSvg = '$_svgPath/theme/light.svg';
  static const String darkThemeSvg = '$_svgPath/theme/dark.svg';
  static const String autoThemeSvg = '$_svgPath/theme/auto.svg';
  
  // 语言相关
  static const String languageSvg = '$_svgPath/language/language.svg';
  static const String translateSvg = '$_svgPath/language/translate.svg';
  
  // 帮助相关
  static const String helpSvg = '$_svgPath/help/help.svg';
  static const String questionSvg = '$_svgPath/help/question.svg';
  static const String feedbackSvg = '$_svgPath/help/feedback.svg';
  static const String aboutSvg = '$_svgPath/help/about.svg';
  
  // 安全相关
  static const String securitySvg = '$_svgPath/security/security.svg';
  static const String fingerprintSvg = '$_svgPath/security/fingerprint.svg';
  static const String faceIdSvg = '$_svgPath/security/face_id.svg';
  static const String verificationSvg = '$_svgPath/security/verification.svg';

  // ==================== PNG 图标 ====================
  
  // 应用图标
  static const String appIconPng = '$_pngPath/app/app_icon.png';
  static const String appIcon2xPng = '$_pngPath/app/<EMAIL>';
  static const String appIcon3xPng = '$_pngPath/app/<EMAIL>';
  
  // 启动图标
  static const String launchIconPng = '$_pngPath/launch/launch_icon.png';
  static const String launchIcon2xPng = '$_pngPath/launch/<EMAIL>';
  static const String launchIcon3xPng = '$_pngPath/launch/<EMAIL>';
  
  // 占位符图标
  static const String placeholderPng = '$_pngPath/placeholder/placeholder.png';
  static const String avatarPlaceholderPng = '$_pngPath/placeholder/avatar_placeholder.png';
  static const String imagePlaceholderPng = '$_pngPath/placeholder/image_placeholder.png';
  
  // 错误图标
  static const String errorIconPng = '$_pngPath/error/error_icon.png';
  static const String networkErrorPng = '$_pngPath/error/network_error.png';
  static const String notFoundPng = '$_pngPath/error/not_found.png';

  // ==================== 工具方法 ====================
  
  /// 获取所有SVG图标路径
  static List<String> getAllSvgIcons() {
    return [
      // 导航相关
      homeSvg, marketSvg, tradeSvg, portfolioSvg, profileSvg,
      // 功能相关
      searchSvg, settingsSvg, notificationSvg, favoriteSvg, shareSvg, moreSvg,
      // 交易相关
      buySvg, sellSvg, increaseSvg, decreaseSvg, flatSvg, chartSvg, candlestickSvg, depthSvg,
      // 状态相关
      successSvg, errorSvg, warningSvg, infoSvg, loadingSvg,
      // 操作相关
      addSvg, deleteSvg, editSvg, copySvg, refreshSvg, closeSvg, backSvg, forwardSvg,
      // 账户相关
      loginSvg, logoutSvg, registerSvg, passwordSvg, emailSvg, phoneSvg,
      // 金融相关
      currencySvg, walletSvg, bankSvg, creditCardSvg, transferSvg, incomeSvg, expenseSvg,
      // 时间相关
      calendarSvg, timeSvg, historySvg,
      // 网络状态
      onlineSvg, offlineSvg, connectingSvg,
      // 主题相关
      lightThemeSvg, darkThemeSvg, autoThemeSvg,
      // 语言相关
      languageSvg, translateSvg,
      // 帮助相关
      helpSvg, questionSvg, feedbackSvg, aboutSvg,
      // 安全相关
      securitySvg, fingerprintSvg, faceIdSvg, verificationSvg,
    ];
  }
  
  /// 获取所有PNG图标路径
  static List<String> getAllPngIcons() {
    return [
      // 应用图标
      appIconPng, appIcon2xPng, appIcon3xPng,
      // 启动图标
      launchIconPng, launchIcon2xPng, launchIcon3xPng,
      // 占位符图标
      placeholderPng, avatarPlaceholderPng, imagePlaceholderPng,
      // 错误图标
      errorIconPng, networkErrorPng, notFoundPng,
    ];
  }
  
  /// 根据名称获取SVG图标路径
  static String? getSvgIconByName(String name) {
    switch (name.toLowerCase()) {
      case 'home':
        return homeSvg;
      case 'market':
        return marketSvg;
      case 'trade':
        return tradeSvg;
      case 'portfolio':
        return portfolioSvg;
      case 'profile':
        return profileSvg;
      case 'search':
        return searchSvg;
      case 'settings':
        return settingsSvg;
      case 'notification':
        return notificationSvg;
      case 'favorite':
        return favoriteSvg;
      case 'share':
        return shareSvg;
      case 'buy':
        return buySvg;
      case 'sell':
        return sellSvg;
      case 'chart':
        return chartSvg;
      default:
        return null;
    }
  }
  
  /// 检查图标是否存在
  static bool iconExists(String iconPath) {
    // 这里可以添加实际的文件存在性检查逻辑
    return getAllSvgIcons().contains(iconPath) || getAllPngIcons().contains(iconPath);
  }
}
