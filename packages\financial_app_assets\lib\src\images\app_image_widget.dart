import 'package:flutter/material.dart';
import 'package:financial_app_assets/src/images/app_images.dart'; // 导入图片路径常量

/// 一个自定义的通用图片 Widget，封装了公共图片的加载逻辑。
class AppImageWidget extends StatelessWidget {
  final String imagePath; // 图片路径常量，例如 AppImages.homeIcon
  final double? width;
  final double? height;
  final BoxFit? fit;
  final Color? color;
  final BlendMode? colorBlendMode;
  final AlignmentGeometry alignment;
  final ImageRepeat repeat;
  final String? semanticLabel;
  final bool excludeFromSemantics;
  final FilterQuality filterQuality;

  const AppImageWidget({
    super.key,
    required this.imagePath,
    this.width,
    this.height,
    this.fit,
    this.color,
    this.colorBlendMode,
    this.alignment = Alignment.center,
    this.repeat = ImageRepeat.noRepeat,
    this.semanticLabel,
    this.excludeFromSemantics = false,
    this.filterQuality = FilterQuality.low,
  });

  @override
  Widget build(BuildContext context) {
    return Image.asset(
      imagePath,
      package: AppImages.packageName, // 内部自动注入包名
      width: width,
      height: height,
      fit: fit,
      color: color,
      colorBlendMode: colorBlendMode,
      alignment: alignment,
      repeat: repeat,
      semanticLabel: semanticLabel,
      excludeFromSemantics: excludeFromSemantics,
      filterQuality: filterQuality,
    );
  }
}

// 示例：如果某个图片有特殊的固定样式，可以再封装一个更具体的 Widget
class AppLogo extends StatelessWidget {
  final double size;

  const AppLogo({super.key, this.size = 100.0});

  @override
  Widget build(BuildContext context) {
    return AppImageWidget(
      imagePath: AppImages.logo,
      width: size,
      height: size,
      fit: BoxFit.contain,
    );
  }
}
