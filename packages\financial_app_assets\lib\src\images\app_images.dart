import 'package:flutter/material.dart';

/// 企业级图片资源管理类
///
/// 统一管理所有图片资源路径，提供类型安全的访问方式
/// 支持多种图片格式和分类管理
class AppImages {
  // 定义图片的根路径，确保与 pubspec.yaml 中 assets 的配置一致
  static const String _basePath = 'assets/images/';
  // 定义图片所在的包名
  static const String packageName = 'financial_app_assets';

  // ==================== 应用 Logo ====================
  static const String appLogo = '${_basePath}logos/app_logo.png';
  static const String appLogoWhite = '${_basePath}logos/app_logo_white.png';
  static const String appLogoIcon = '${_basePath}logos/app_icon.png';
  static const String companyLogo = '${_basePath}logos/company_logo.png';

  // ==================== 启动页和背景 ====================
  static const String splashBackground =
      '${_basePath}backgrounds/splash_bg.png';
  static const String loginBackground = '${_basePath}backgrounds/login_bg.png';
  static const String homeBackground = '${_basePath}backgrounds/home_bg.png';
  static const String gradientBackground =
      '${_basePath}backgrounds/gradient_bg.png';

  // ==================== 硬币图片 ====================
  static const String coinBtc = '${_basePath}coins/btc.png';
  static const String coinEth = '${_basePath}coins/eth.png';
  static const String coinUsdt = '${_basePath}coins/usdt.png';
  static const String coinBnb = '${_basePath}coins/bnb.png';
  static const String coinAda = '${_basePath}coins/ada.png';
  static const String coinSol = '${_basePath}coins/sol.png';
  static const String coinDot = '${_basePath}coins/dot.png';
  static const String coinAvax = '${_basePath}coins/avax.png';
  static const String coinMatic = '${_basePath}coins/matic.png';
  static const String coinLink = '${_basePath}coins/link.png';

  // ==================== 图表和数据可视化 ====================
  static const String chartUp = '${_basePath}charts/chart_up.png';
  static const String chartDown = '${_basePath}charts/chart_down.png';
  static const String chartNeutral = '${_basePath}charts/chart_neutral.png';
  static const String candlestickChart = '${_basePath}charts/candlestick.png';
  static const String lineChart = '${_basePath}charts/line_chart.png';

  // ==================== 插图和装饰 ====================
  static const String emptyState = '${_basePath}illustrations/empty_state.png';
  static const String errorState = '${_basePath}illustrations/error_state.png';
  static const String successState =
      '${_basePath}illustrations/success_state.png';
  static const String loadingState =
      '${_basePath}illustrations/loading_state.png';
  static const String noConnection =
      '${_basePath}illustrations/no_connection.png';

  // ==================== 用户头像 ====================
  static const String defaultAvatar = '${_basePath}avatars/default_avatar.png';
  static const String userPlaceholder =
      '${_basePath}avatars/user_placeholder.png';

  // ==================== 功能图标 (PNG 格式) ====================
  static const String homeIcon = '${_basePath}icons/home.png';
  static const String marketIcon = '${_basePath}icons/market.png';
  static const String tradeIcon = '${_basePath}icons/trade.png';
  static const String portfolioIcon = '${_basePath}icons/portfolio.png';
  static const String profileIcon = '${_basePath}icons/profile.png';
  static const String settingsIcon = '${_basePath}icons/settings.png';
  static const String notificationIcon = '${_basePath}icons/notification.png';
  static const String searchIcon = '${_basePath}icons/search.png';

  // ==================== 状态图标 ====================
  static const String successIcon = '${_basePath}icons/success.png';
  static const String errorIcon = '${_basePath}icons/error.png';
  static const String warningIcon = '${_basePath}icons/warning.png';
  static const String infoIcon = '${_basePath}icons/info.png';

  // ==================== 兼容性别名 ====================
  /// 兼容旧版本的 logo 属性
  static const String logo = appLogo;

  // ==================== 资源集合 ====================

  /// 获取所有硬币图片
  static List<String> get allCoinImages => [
    coinBtc,
    coinEth,
    coinUsdt,
    coinBnb,
    coinAda,
    coinSol,
    coinDot,
    coinAvax,
    coinMatic,
    coinLink,
  ];

  /// 获取所有 Logo 图片
  static List<String> get allLogos => [
    appLogo,
    appLogoWhite,
    appLogoIcon,
    companyLogo,
  ];

  /// 获取所有背景图片
  static List<String> get allBackgrounds => [
    splashBackground,
    loginBackground,
    homeBackground,
    gradientBackground,
  ];

  /// 获取所有图表图片
  static List<String> get allCharts => [
    chartUp,
    chartDown,
    chartNeutral,
    candlestickChart,
    lineChart,
  ];

  /// 获取所有插图
  static List<String> get allIllustrations => [
    emptyState,
    errorState,
    successState,
    loadingState,
    noConnection,
  ];

  /// 获取所有功能图标
  static List<String> get allFunctionIcons => [
    homeIcon,
    marketIcon,
    tradeIcon,
    portfolioIcon,
    profileIcon,
    settingsIcon,
    notificationIcon,
    searchIcon,
  ];

  /// 获取所有状态图标
  static List<String> get allStatusIcons => [
    successIcon,
    errorIcon,
    warningIcon,
    infoIcon,
  ];

  /// 获取关键资源 (需要预加载)
  static List<String> get criticalAssets => [
    appLogo,
    splashBackground,
    loadingState,
    ...allFunctionIcons,
  ];

  /// 获取所有图片资源
  static List<String> get allImages => [
    ...allLogos,
    ...allBackgrounds,
    ...allCoinImages,
    ...allCharts,
    ...allIllustrations,
    ...allFunctionIcons,
    ...allStatusIcons,
    defaultAvatar,
    userPlaceholder,
  ];

  // ==================== 工具方法 ====================

  /// 获取 ImageProvider (AssetImage)
  static AssetImage assetImage(String path) {
    return AssetImage(path, package: packageName);
  }

  /// 获取 Image Widget
  static Image image(
    String path, {
    Key? key,
    double? width,
    double? height,
    BoxFit? fit,
    Color? color,
    BlendMode? colorBlendMode,
    AlignmentGeometry alignment = Alignment.center,
    ImageRepeat repeat = ImageRepeat.noRepeat,
    Rect? centerSlice,
    bool matchTextDirection = false,
    bool gaplessPlayback = false,
    bool isAntiAlias = false,
    String? semanticLabel,
    bool excludeFromSemantics = false,
    FilterQuality filterQuality = FilterQuality.low,
    int? cacheWidth,
    int? cacheHeight,
  }) {
    return Image.asset(
      path,
      key: key,
      package: packageName, // 自动注入 package 名称
      width: width,
      height: height,
      fit: fit,
      color: color,
      colorBlendMode: colorBlendMode,
      alignment: alignment,
      repeat: repeat,
      centerSlice: centerSlice,
      matchTextDirection: matchTextDirection,
      gaplessPlayback: gaplessPlayback,
      isAntiAlias: isAntiAlias,
      semanticLabel: semanticLabel,
      excludeFromSemantics: excludeFromSemantics,
      filterQuality: filterQuality,
      cacheWidth: cacheWidth,
      cacheHeight: cacheHeight,
    );
  }

  /// 根据硬币符号获取硬币图片
  static String? getCoinImage(String symbol) {
    switch (symbol.toUpperCase()) {
      case 'BTC':
        return coinBtc;
      case 'ETH':
        return coinEth;
      case 'USDT':
        return coinUsdt;
      case 'BNB':
        return coinBnb;
      case 'ADA':
        return coinAda;
      case 'SOL':
        return coinSol;
      case 'DOT':
        return coinDot;
      case 'AVAX':
        return coinAvax;
      case 'MATIC':
        return coinMatic;
      case 'LINK':
        return coinLink;
      default:
        return null;
    }
  }

  /// 检查资源是否存在
  static bool hasAsset(String path) {
    return allImages.contains(path);
  }
}
