import 'package:flutter/widgets.dart';
import '../core/asset_manager.dart';
import 'app_images.dart';

/// 图片资源管理器
/// 
/// 提供高级的图片资源管理功能，包括：
/// - 智能预加载
/// - 缓存管理
/// - 错误处理
/// - 性能优化
class ImageAssets {
  static final AssetManager _assetManager = AssetManager.instance;
  
  /// 预加载关键图片资源
  static Future<void> preloadCriticalImages() async {
    await _assetManager.preloadAssets(
      AppImages.criticalAssets,
      type: AssetType.image,
    );
  }
  
  /// 预加载所有硬币图片
  static Future<void> preloadCoinImages() async {
    await _assetManager.preloadAssets(
      AppImages.allCoinImages,
      type: AssetType.image,
    );
  }
  
  /// 预加载功能图标
  static Future<void> preloadFunctionIcons() async {
    await _assetManager.preloadAssets(
      AppImages.allFunctionIcons,
      type: AssetType.image,
    );
  }
  
  /// 获取优化的图片提供者
  static Future<ImageProvider> getOptimizedImage(
    String imagePath, {
    bool useCache = true,
    ImageConfiguration? configuration,
  }) async {
    return await _assetManager.getImage(
      imagePath,
      useCache: useCache,
      configuration: configuration,
    );
  }
  
  /// 批量预加载图片
  static Future<void> batchPreloadImages(
    List<String> imagePaths, {
    int concurrency = 3,
  }) async {
    await _assetManager.preloadAssets(
      imagePaths,
      type: AssetType.image,
      concurrency: concurrency,
    );
  }
  
  /// 获取图片信息
  static Future<AssetInfo> getImageInfo(String imagePath) async {
    return await _assetManager.getAssetInfo(imagePath);
  }
  
  /// 检查图片是否已缓存
  static Future<bool> isImageCached(String imagePath) async {
    final cached = await _assetManager.getImage(imagePath, useCache: true);
    return cached != null;
  }
  
  /// 清理图片缓存
  static Future<void> clearImageCache() async {
    await _assetManager.clearCache();
  }
  
  /// 获取缓存统计
  static Map<String, int> getCacheStats() {
    return _assetManager.getUsageStats();
  }
}

/// 图片资源常量扩展
extension ImageAssetsExtension on AppImages {
  /// 获取硬币图片的映射
  static Map<String, String> get coinImageMap => {
    'BTC': AppImages.coinBtc,
    'ETH': AppImages.coinEth,
    'USDT': AppImages.coinUsdt,
    'BNB': AppImages.coinBnb,
    'ADA': AppImages.coinAda,
    'SOL': AppImages.coinSol,
    'DOT': AppImages.coinDot,
    'AVAX': AppImages.coinAvax,
    'MATIC': AppImages.coinMatic,
    'LINK': AppImages.coinLink,
  };
  
  /// 获取功能图标的映射
  static Map<String, String> get functionIconMap => {
    'home': AppImages.homeIcon,
    'market': AppImages.marketIcon,
    'trade': AppImages.tradeIcon,
    'portfolio': AppImages.portfolioIcon,
    'profile': AppImages.profileIcon,
    'settings': AppImages.settingsIcon,
    'notification': AppImages.notificationIcon,
    'search': AppImages.searchIcon,
  };
  
  /// 获取状态图标的映射
  static Map<String, String> get statusIconMap => {
    'success': AppImages.successIcon,
    'error': AppImages.errorIcon,
    'warning': AppImages.warningIcon,
    'info': AppImages.infoIcon,
  };
}
