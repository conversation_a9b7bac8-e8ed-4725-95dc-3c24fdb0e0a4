import 'package:flutter/material.dart';

/// 优化的图片组件
///
/// 提供图片缓存、压缩、懒加载等优化功能
class OptimizedImage extends StatefulWidget {
  /// 图片路径
  final String imagePath;

  /// 图片宽度
  final double? width;

  /// 图片高度
  final double? height;

  /// 适配方式
  final BoxFit? fit;

  /// 对齐方式
  final AlignmentGeometry alignment;

  /// 是否启用缓存
  final bool enableCache;

  /// 缓存宽度
  final int? cacheWidth;

  /// 缓存高度
  final int? cacheHeight;

  /// 占位符
  final Widget? placeholder;

  /// 错误占位符
  final Widget? errorWidget;

  /// 是否懒加载
  final bool lazyLoad;

  const OptimizedImage({
    super.key,
    required this.imagePath,
    this.width,
    this.height,
    this.fit,
    this.alignment = Alignment.center,
    this.enableCache = true,
    this.cacheWidth,
    this.cacheHeight,
    this.placeholder,
    this.errorWidget,
    this.lazyLoad = false,
  });

  @override
  State<OptimizedImage> createState() => _OptimizedImageState();
}

class _OptimizedImageState extends State<OptimizedImage> {
  bool _isLoaded = false;
  bool _hasError = false;

  @override
  void initState() {
    super.initState();
    if (!widget.lazyLoad) {
      _loadImage();
    }
  }

  void _loadImage() {
    if (_isLoaded || _hasError) return;

    // 预加载图片
    precacheImage(AssetImage(widget.imagePath), context)
        .then((_) {
          if (mounted) {
            setState(() {
              _isLoaded = true;
            });
          }
        })
        .catchError((error) {
          if (mounted) {
            setState(() {
              _hasError = true;
            });
          }
        });
  }

  @override
  Widget build(BuildContext context) {
    if (widget.lazyLoad && !_isLoaded && !_hasError) {
      return _buildPlaceholder();
    }

    if (_hasError) {
      return _buildErrorWidget();
    }

    return Image.asset(
      widget.imagePath,
      width: widget.width,
      height: widget.height,
      fit: widget.fit,
      alignment: widget.alignment,
      cacheWidth: widget.cacheWidth,
      cacheHeight: widget.cacheHeight,
      errorBuilder: (context, error, stackTrace) {
        return _buildErrorWidget();
      },
      frameBuilder: (context, child, frame, wasSynchronouslyLoaded) {
        if (wasSynchronouslyLoaded || frame != null) {
          return child;
        }
        return _buildPlaceholder();
      },
    );
  }

  Widget _buildPlaceholder() {
    return widget.placeholder ??
        Container(
          width: widget.width,
          height: widget.height,
          color: Colors.grey[200],
          child: const Center(child: CircularProgressIndicator()),
        );
  }

  Widget _buildErrorWidget() {
    return widget.errorWidget ??
        Container(
          width: widget.width,
          height: widget.height,
          color: Colors.grey[300],
          child: const Icon(Icons.error_outline, color: Colors.grey),
        );
  }
}

/// 优化的网络图片组件
class OptimizedNetworkImage extends StatelessWidget {
  /// 图片URL
  final String imageUrl;

  /// 图片宽度
  final double? width;

  /// 图片高度
  final double? height;

  /// 适配方式
  final BoxFit? fit;

  /// 占位符
  final Widget? placeholder;

  /// 错误占位符
  final Widget? errorWidget;

  const OptimizedNetworkImage({
    super.key,
    required this.imageUrl,
    this.width,
    this.height,
    this.fit,
    this.placeholder,
    this.errorWidget,
  });

  @override
  Widget build(BuildContext context) {
    return Image.network(
      imageUrl,
      width: width,
      height: height,
      fit: fit,
      loadingBuilder: (context, child, loadingProgress) {
        if (loadingProgress == null) return child;

        return placeholder ??
            Container(
              width: width,
              height: height,
              color: Colors.grey[200],
              child: Center(
                child: CircularProgressIndicator(
                  value: loadingProgress.expectedTotalBytes != null
                      ? loadingProgress.cumulativeBytesLoaded /
                            loadingProgress.expectedTotalBytes!
                      : null,
                ),
              ),
            );
      },
      errorBuilder: (context, error, stackTrace) {
        return errorWidget ??
            Container(
              width: width,
              height: height,
              color: Colors.grey[300],
              child: const Icon(Icons.error_outline, color: Colors.grey),
            );
      },
    );
  }
}

/// 图片优化工具类
class ImageOptimizer {
  /// 计算缓存尺寸
  static Size calculateCacheSize(Size originalSize, double devicePixelRatio) {
    return Size(
      originalSize.width * devicePixelRatio,
      originalSize.height * devicePixelRatio,
    );
  }

  /// 预加载图片列表
  static Future<void> preloadImages(
    BuildContext context,
    List<String> imagePaths,
  ) async {
    final futures = imagePaths.map(
      (path) => precacheImage(AssetImage(path), context),
    );
    await Future.wait(futures);
  }

  /// 清理图片缓存
  static void clearImageCache() {
    imageCache.clear();
    imageCache.clearLiveImages();
  }

  /// 设置图片缓存大小
  static void setImageCacheSize(int maxCacheSize) {
    imageCache.maximumSize = maxCacheSize;
  }
}
