import 'package:flutter/services.dart';
import 'package:crypto/crypto.dart';

/// 资源工具类
///
/// 提供资源管理的实用工具方法
class AssetUtils {
  /// 计算文件哈希值
  static String calculateHash(Uint8List bytes) {
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  /// 获取文件扩展名
  static String getFileExtension(String filePath) {
    return filePath.split('.').last.toLowerCase();
  }

  /// 检查是否为图片文件
  static bool isImageFile(String filePath) {
    final extension = getFileExtension(filePath);
    return [
      'png',
      'jpg',
      'jpeg',
      'gif',
      'bmp',
      'webp',
      'svg',
    ].contains(extension);
  }

  /// 检查是否为字体文件
  static bool isFontFile(String filePath) {
    final extension = getFileExtension(filePath);
    return ['ttf', 'otf', 'woff', 'woff2'].contains(extension);
  }

  /// 检查是否为动画文件
  static bool isAnimationFile(String filePath) {
    final extension = getFileExtension(filePath);
    return ['json', 'lottie', 'gif'].contains(extension);
  }

  /// 格式化文件大小
  static String formatFileSize(int bytes) {
    if (bytes < 1024) {
      return '$bytes B';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)} KB';
    } else if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    } else {
      return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
    }
  }

  /// 生成资源路径
  static String generateAssetPath(String category, String filename) {
    return 'assets/$category/$filename';
  }

  /// 验证资源路径格式
  static bool isValidAssetPath(String path) {
    return path.startsWith('assets/') && path.contains('.');
  }

  /// 获取资源类别
  static String getAssetCategory(String path) {
    if (!isValidAssetPath(path)) return 'unknown';

    final parts = path.split('/');
    if (parts.length >= 3) {
      return parts[1]; // assets/[category]/...
    }
    return 'unknown';
  }

  /// 生成缩略图路径
  static String generateThumbnailPath(String originalPath, int size) {
    final parts = originalPath.split('.');
    if (parts.length < 2) return originalPath;

    final nameWithoutExt = parts.sublist(0, parts.length - 1).join('.');
    final extension = parts.last;

    return '${nameWithoutExt}_thumb_${size}x$size.$extension';
  }

  /// 检查资源是否需要更新
  static bool needsUpdate(DateTime lastModified, Duration maxAge) {
    return DateTime.now().difference(lastModified) > maxAge;
  }

  /// 生成资源清单
  static Map<String, dynamic> generateAssetManifest(List<String> assetPaths) {
    final manifest = <String, dynamic>{
      'version': '1.0.0',
      'generated_at': DateTime.now().toIso8601String(),
      'total_assets': assetPaths.length,
      'categories': <String, List<String>>{},
    };

    for (final path in assetPaths) {
      final category = getAssetCategory(path);
      if (!manifest['categories'].containsKey(category)) {
        manifest['categories'][category] = <String>[];
      }
      manifest['categories'][category].add(path);
    }

    return manifest;
  }

  /// 验证资源完整性
  static Future<bool> validateAssetIntegrity(
    String assetPath,
    String expectedHash,
  ) async {
    try {
      final bytes = await rootBundle.load(assetPath);
      final actualHash = calculateHash(bytes.buffer.asUint8List());
      return actualHash == expectedHash;
    } catch (e) {
      return false;
    }
  }

  /// 获取资源元数据
  static Map<String, dynamic> getAssetMetadata(String assetPath) {
    return {
      'path': assetPath,
      'category': getAssetCategory(assetPath),
      'extension': getFileExtension(assetPath),
      'is_image': isImageFile(assetPath),
      'is_font': isFontFile(assetPath),
      'is_animation': isAnimationFile(assetPath),
      'generated_at': DateTime.now().toIso8601String(),
    };
  }
}

/// 资源验证器
class AssetValidator {
  /// 验证图片资源
  static Future<ValidationResult> validateImage(String imagePath) async {
    try {
      final bytes = await rootBundle.load(imagePath);
      final size = bytes.lengthInBytes;

      // 检查文件大小 (最大 5MB)
      if (size > 5 * 1024 * 1024) {
        return ValidationResult(
          isValid: false,
          message: '图片文件过大: ${AssetUtils.formatFileSize(size)}',
        );
      }

      // 检查文件格式
      if (!AssetUtils.isImageFile(imagePath)) {
        return ValidationResult(isValid: false, message: '不支持的图片格式');
      }

      return ValidationResult(
        isValid: true,
        message: '图片验证通过',
        metadata: {
          'size': size,
          'format': AssetUtils.getFileExtension(imagePath),
        },
      );
    } catch (e) {
      return ValidationResult(isValid: false, message: '图片验证失败: $e');
    }
  }

  /// 验证字体资源
  static Future<ValidationResult> validateFont(String fontPath) async {
    try {
      final bytes = await rootBundle.load(fontPath);
      final size = bytes.lengthInBytes;

      // 检查文件大小 (最大 2MB)
      if (size > 2 * 1024 * 1024) {
        return ValidationResult(
          isValid: false,
          message: '字体文件过大: ${AssetUtils.formatFileSize(size)}',
        );
      }

      // 检查文件格式
      if (!AssetUtils.isFontFile(fontPath)) {
        return ValidationResult(isValid: false, message: '不支持的字体格式');
      }

      return ValidationResult(
        isValid: true,
        message: '字体验证通过',
        metadata: {
          'size': size,
          'format': AssetUtils.getFileExtension(fontPath),
        },
      );
    } catch (e) {
      return ValidationResult(isValid: false, message: '字体验证失败: $e');
    }
  }

  /// 批量验证资源
  static Future<List<ValidationResult>> validateAssets(
    List<String> assetPaths,
  ) async {
    final results = <ValidationResult>[];

    for (final path in assetPaths) {
      ValidationResult result;

      if (AssetUtils.isImageFile(path)) {
        result = await validateImage(path);
      } else if (AssetUtils.isFontFile(path)) {
        result = await validateFont(path);
      } else {
        result = ValidationResult(isValid: true, message: '资源类型无需验证');
      }

      result = result.copyWith(assetPath: path);
      results.add(result);
    }

    return results;
  }
}

/// 验证结果
class ValidationResult {
  final bool isValid;
  final String message;
  final String? assetPath;
  final Map<String, dynamic>? metadata;

  const ValidationResult({
    required this.isValid,
    required this.message,
    this.assetPath,
    this.metadata,
  });

  ValidationResult copyWith({
    bool? isValid,
    String? message,
    String? assetPath,
    Map<String, dynamic>? metadata,
  }) {
    return ValidationResult(
      isValid: isValid ?? this.isValid,
      message: message ?? this.message,
      assetPath: assetPath ?? this.assetPath,
      metadata: metadata ?? this.metadata,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'isValid': isValid,
      'message': message,
      'assetPath': assetPath,
      'metadata': metadata,
    };
  }
}
