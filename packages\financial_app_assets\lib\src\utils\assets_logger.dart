import 'package:financial_app_core/financial_app_core.dart';

/// 资源模块专用日志管理器
/// 
/// 提供资源模块特定的日志记录功能
class AssetsLogger extends ModuleLogger {
  static final AssetsLogger _instance = AssetsLogger._internal();
  factory AssetsLogger() => _instance;
  AssetsLogger._internal();

  @override
  String get moduleName => 'Assets';

  // ==================== 资源特定的日志方法 ====================

  /// 资源加载日志
  void logAssetLoading({
    required String assetPath,
    required String assetType, // 'image', 'font', 'icon', 'animation', 'audio'
    required bool success,
    int? fileSize,
    Duration? loadTime,
    String? cacheStatus, // 'hit', 'miss', 'bypass'
    String? error,
  }) {
    final metadata = {
      'asset_path': assetPath,
      'asset_type': assetType,
      'success': success,
      'file_size_bytes': fileSize,
      'cache_status': cacheStatus,
      if (loadTime != null) 'load_time_ms': loadTime.inMilliseconds,
      if (error != null) 'error': error,
      'timestamp': DateTime.now().toIso8601String(),
    };

    if (success) {
      debug('资源加载成功: $assetPath', metadata: metadata);
      if (loadTime != null) {
        logPerformance('asset_loading', loadTime, metadata: metadata);
      }
    } else {
      warning('资源加载失败: $assetPath', metadata: metadata);
    }

    logBusinessEvent('asset_loading', metadata);
  }

  /// 图片处理日志
  void logImageProcessing({
    required String imagePath,
    required String operation, // 'resize', 'compress', 'format_convert', 'cache'
    required bool success,
    Map<String, dynamic>? processingParams,
    int? originalSize,
    int? processedSize,
    Duration? processingTime,
    String? error,
  }) {
    final metadata = {
      'image_path': imagePath,
      'operation': operation,
      'success': success,
      'processing_params': processingParams,
      'original_size_bytes': originalSize,
      'processed_size_bytes': processedSize,
      'compression_ratio': originalSize != null && processedSize != null 
        ? processedSize / originalSize : null,
      if (processingTime != null) 'processing_time_ms': processingTime.inMilliseconds,
      if (error != null) 'error': error,
      'timestamp': DateTime.now().toIso8601String(),
    };

    if (success) {
      info('图片处理成功: $operation ($imagePath)', metadata: metadata);
      if (processingTime != null) {
        logPerformance('image_processing', processingTime, metadata: metadata);
      }
    } else {
      warning('图片处理失败: $operation ($imagePath)', metadata: metadata);
    }

    logBusinessEvent('image_processing', metadata);
  }

  /// 字体加载日志
  void logFontLoading({
    required String fontFamily,
    required String fontPath,
    required bool success,
    List<String>? fontWeights,
    int? fileSize,
    Duration? loadTime,
    String? error,
  }) {
    final metadata = {
      'font_family': fontFamily,
      'font_path': fontPath,
      'success': success,
      'font_weights': fontWeights,
      'weights_count': fontWeights?.length ?? 0,
      'file_size_bytes': fileSize,
      if (loadTime != null) 'load_time_ms': loadTime.inMilliseconds,
      if (error != null) 'error': error,
      'timestamp': DateTime.now().toIso8601String(),
    };

    if (success) {
      info('字体加载成功: $fontFamily', metadata: metadata);
      if (loadTime != null) {
        logPerformance('font_loading', loadTime, metadata: metadata);
      }
    } else {
      warning('字体加载失败: $fontFamily', metadata: metadata);
    }

    logBusinessEvent('font_loading', metadata);
  }

  /// 图标加载日志
  void logIconLoading({
    required String iconName,
    required String iconSet, // 'material', 'cupertino', 'custom'
    required bool success,
    String? iconPath,
    int? iconSize,
    Duration? loadTime,
    String? error,
  }) {
    final metadata = {
      'icon_name': iconName,
      'icon_set': iconSet,
      'icon_path': iconPath,
      'success': success,
      'icon_size': iconSize,
      if (loadTime != null) 'load_time_ms': loadTime.inMilliseconds,
      if (error != null) 'error': error,
      'timestamp': DateTime.now().toIso8601String(),
    };

    if (success) {
      debug('图标加载成功: $iconName ($iconSet)', metadata: metadata);
      if (loadTime != null) {
        logPerformance('icon_loading', loadTime, metadata: metadata);
      }
    } else {
      warning('图标加载失败: $iconName ($iconSet)', metadata: metadata);
    }

    logBusinessEvent('icon_loading', metadata);
  }

  /// 动画资源日志
  void logAnimationAsset({
    required String animationPath,
    required String action, // 'load', 'play', 'pause', 'stop', 'dispose'
    required bool success,
    String? animationType, // 'lottie', 'gif', 'rive'
    Duration? animationDuration,
    int? frameCount,
    Duration? actionTime,
    String? error,
  }) {
    final metadata = {
      'animation_path': animationPath,
      'action': action,
      'animation_type': animationType,
      'success': success,
      'animation_duration_ms': animationDuration?.inMilliseconds,
      'frame_count': frameCount,
      if (actionTime != null) 'action_time_ms': actionTime.inMilliseconds,
      if (error != null) 'error': error,
      'timestamp': DateTime.now().toIso8601String(),
    };

    if (success) {
      info('动画资源操作成功: $action ($animationPath)', metadata: metadata);
      if (actionTime != null) {
        logPerformance('animation_$action', actionTime, metadata: metadata);
      }
    } else {
      warning('动画资源操作失败: $action ($animationPath)', metadata: metadata);
    }

    logBusinessEvent('animation_asset', metadata);
  }

  /// 资源缓存日志
  void logAssetCache({
    required String operation, // 'hit', 'miss', 'set', 'evict', 'clear'
    required String assetKey,
    String? assetType,
    int? cacheSize,
    int? totalCacheSize,
    Duration? operationTime,
  }) {
    final metadata = {
      'operation': operation,
      'asset_key': assetKey,
      'asset_type': assetType,
      'cache_size_bytes': cacheSize,
      'total_cache_size_bytes': totalCacheSize,
      if (operationTime != null) 'operation_time_ms': operationTime.inMilliseconds,
      'timestamp': DateTime.now().toIso8601String(),
    };

    switch (operation) {
      case 'hit':
        debug('资源缓存命中: $assetKey', metadata: metadata);
        break;
      case 'miss':
        debug('资源缓存未命中: $assetKey', metadata: metadata);
        break;
      case 'set':
        debug('资源缓存设置: $assetKey', metadata: metadata);
        break;
      case 'evict':
        info('资源缓存驱逐: $assetKey', metadata: metadata);
        break;
      case 'clear':
        info('资源缓存清空', metadata: metadata);
        break;
      default:
        debug('资源缓存操作: $operation', metadata: metadata);
    }

    logBusinessEvent('asset_cache', metadata);
  }

  /// 资源预加载日志
  void logAssetPreloading({
    required List<String> assetPaths,
    required bool success,
    int? successCount,
    int? failureCount,
    Duration? preloadTime,
    List<String>? failedAssets,
    String? strategy, // 'eager', 'lazy', 'priority'
  }) {
    final metadata = {
      'asset_paths': assetPaths,
      'total_assets': assetPaths.length,
      'success': success,
      'success_count': successCount ?? 0,
      'failure_count': failureCount ?? 0,
      'success_rate': successCount != null ? successCount / assetPaths.length : null,
      'failed_assets': failedAssets,
      'preload_strategy': strategy,
      if (preloadTime != null) 'preload_time_ms': preloadTime.inMilliseconds,
      'timestamp': DateTime.now().toIso8601String(),
    };

    if (success) {
      info('资源预加载完成', metadata: metadata);
      if (preloadTime != null) {
        logPerformance('asset_preloading', preloadTime, metadata: metadata);
      }
    } else {
      warning('资源预加载部分失败', metadata: metadata);
    }

    logBusinessEvent('asset_preloading', metadata);
  }

  /// 主题资源切换日志
  void logThemeAssetSwitch({
    required String fromTheme,
    required String toTheme,
    required bool success,
    List<String>? switchedAssets,
    Duration? switchTime,
    String? error,
  }) {
    final metadata = {
      'from_theme': fromTheme,
      'to_theme': toTheme,
      'success': success,
      'switched_assets': switchedAssets,
      'switched_assets_count': switchedAssets?.length ?? 0,
      if (switchTime != null) 'switch_time_ms': switchTime.inMilliseconds,
      if (error != null) 'error': error,
      'timestamp': DateTime.now().toIso8601String(),
    };

    if (success) {
      info('主题资源切换成功: $fromTheme -> $toTheme', metadata: metadata);
      if (switchTime != null) {
        logPerformance('theme_asset_switch', switchTime, metadata: metadata);
      }
    } else {
      warning('主题资源切换失败: $fromTheme -> $toTheme', metadata: metadata);
    }

    logBusinessEvent('theme_asset_switch', metadata);
  }

  /// 资源优化日志
  void logAssetOptimization({
    required String optimizationType, // 'compression', 'format_conversion', 'size_reduction'
    required List<String> assetPaths,
    required bool success,
    int? originalTotalSize,
    int? optimizedTotalSize,
    Duration? optimizationTime,
    Map<String, dynamic>? optimizationStats,
    String? error,
  }) {
    final metadata = {
      'optimization_type': optimizationType,
      'asset_paths': assetPaths,
      'assets_count': assetPaths.length,
      'success': success,
      'original_total_size_bytes': originalTotalSize,
      'optimized_total_size_bytes': optimizedTotalSize,
      'size_reduction_ratio': originalTotalSize != null && optimizedTotalSize != null 
        ? (originalTotalSize - optimizedTotalSize) / originalTotalSize : null,
      'optimization_stats': optimizationStats,
      if (optimizationTime != null) 'optimization_time_ms': optimizationTime.inMilliseconds,
      if (error != null) 'error': error,
      'timestamp': DateTime.now().toIso8601String(),
    };

    if (success) {
      info('资源优化完成: $optimizationType', metadata: metadata);
      if (optimizationTime != null) {
        logPerformance('asset_optimization', optimizationTime, metadata: metadata);
      }
    } else {
      warning('资源优化失败: $optimizationType', metadata: metadata);
    }

    logBusinessEvent('asset_optimization', metadata);
  }

  /// 资源内存使用日志
  void logMemoryUsage({
    required Map<String, int> memoryUsageByType,
    required int totalMemoryUsage,
    int? memoryLimit,
    bool? memoryWarning,
    List<String>? largestAssets,
  }) {
    final metadata = {
      'memory_usage_by_type': memoryUsageByType,
      'total_memory_usage_bytes': totalMemoryUsage,
      'memory_limit_bytes': memoryLimit,
      'memory_utilization': memoryLimit != null ? totalMemoryUsage / memoryLimit : null,
      'memory_warning': memoryWarning,
      'largest_assets': largestAssets,
      'timestamp': DateTime.now().toIso8601String(),
    };

    if (memoryWarning == true) {
      warning('资源内存使用告警', metadata: metadata);
    } else {
      debug('资源内存使用监控', metadata: metadata);
    }

    logBusinessEvent('asset_memory_usage', metadata);
  }

  /// 资源清理日志
  void logAssetCleanup({
    required String cleanupType, // 'cache', 'unused', 'expired', 'memory_pressure'
    required bool success,
    int? cleanedAssetsCount,
    int? freedMemoryBytes,
    Duration? cleanupTime,
    String? error,
  }) {
    final metadata = {
      'cleanup_type': cleanupType,
      'success': success,
      'cleaned_assets_count': cleanedAssetsCount,
      'freed_memory_bytes': freedMemoryBytes,
      if (cleanupTime != null) 'cleanup_time_ms': cleanupTime.inMilliseconds,
      if (error != null) 'error': error,
      'timestamp': DateTime.now().toIso8601String(),
    };

    if (success) {
      info('资源清理完成: $cleanupType', metadata: metadata);
      if (cleanupTime != null) {
        logPerformance('asset_cleanup', cleanupTime, metadata: metadata);
      }
    } else {
      warning('资源清理失败: $cleanupType', metadata: metadata);
    }

    logBusinessEvent('asset_cleanup', metadata);
  }

  /// 资源版本管理日志
  void logAssetVersioning({
    required String action, // 'check', 'update', 'rollback'
    required bool success,
    String? currentVersion,
    String? targetVersion,
    List<String>? updatedAssets,
    Duration? actionTime,
    String? error,
  }) {
    final metadata = {
      'action': action,
      'success': success,
      'current_version': currentVersion,
      'target_version': targetVersion,
      'updated_assets': updatedAssets,
      'updated_assets_count': updatedAssets?.length ?? 0,
      if (actionTime != null) 'action_time_ms': actionTime.inMilliseconds,
      if (error != null) 'error': error,
      'timestamp': DateTime.now().toIso8601String(),
    };

    if (success) {
      info('资源版本管理操作成功: $action', metadata: metadata);
      if (actionTime != null) {
        logPerformance('asset_versioning', actionTime, metadata: metadata);
      }
    } else {
      warning('资源版本管理操作失败: $action', metadata: metadata);
    }

    logBusinessEvent('asset_versioning', metadata);
  }
}

/// 资源模块日志工具类
class AssetsLoggerUtils {
  static final AssetsLogger _logger = AssetsLogger();

  // 基础日志方法
  static void debug(String message, {Map<String, dynamic>? metadata}) =>
      _logger.debug(message, metadata: metadata);

  static void info(String message, {Map<String, dynamic>? metadata}) =>
      _logger.info(message, metadata: metadata);

  static void warning(String message, {Map<String, dynamic>? metadata}) =>
      _logger.warning(message, metadata: metadata);

  static void error(String message, {
    dynamic error,
    StackTrace? stackTrace,
    Map<String, dynamic>? metadata,
  }) => _logger.error(message, error: error, stackTrace: stackTrace, metadata: metadata);

  // 资源特定方法
  static void logAssetLoading({
    required String assetPath,
    required String assetType,
    required bool success,
    int? fileSize,
    Duration? loadTime,
    String? cacheStatus,
    String? error,
  }) => _logger.logAssetLoading(
    assetPath: assetPath,
    assetType: assetType,
    success: success,
    fileSize: fileSize,
    loadTime: loadTime,
    cacheStatus: cacheStatus,
    error: error,
  );

  static void logImageProcessing({
    required String imagePath,
    required String operation,
    required bool success,
    Map<String, dynamic>? processingParams,
    int? originalSize,
    int? processedSize,
    Duration? processingTime,
    String? error,
  }) => _logger.logImageProcessing(
    imagePath: imagePath,
    operation: operation,
    success: success,
    processingParams: processingParams,
    originalSize: originalSize,
    processedSize: processedSize,
    processingTime: processingTime,
    error: error,
  );

  static void logAssetCache({
    required String operation,
    required String assetKey,
    String? assetType,
    int? cacheSize,
    int? totalCacheSize,
    Duration? operationTime,
  }) => _logger.logAssetCache(
    operation: operation,
    assetKey: assetKey,
    assetType: assetType,
    cacheSize: cacheSize,
    totalCacheSize: totalCacheSize,
    operationTime: operationTime,
  );
}
