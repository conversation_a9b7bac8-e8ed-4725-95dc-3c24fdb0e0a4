import 'dart:ui' as ui;
import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// 图片工具类
///
/// 提供图片处理、压缩、格式转换等功能
class ImageUtils {
  ImageUtils._();

  // ==================== 图片加载 ====================

  /// 从资源加载图片
  static Future<ui.Image> loadImageFromAsset(String assetPath) async {
    final data = await rootBundle.load(assetPath);
    final codec = await ui.instantiateImageCodec(data.buffer.asUint8List());
    final frame = await codec.getNextFrame();
    return frame.image;
  }

  /// 从网络加载图片
  static Future<ui.Image> loadImageFromNetwork(String url) async {
    final response = await NetworkAssetBundle(Uri.parse(url)).load(url);
    final codec = await ui.instantiateImageCodec(response.buffer.asUint8List());
    final frame = await codec.getNextFrame();
    return frame.image;
  }

  /// 从字节数据加载图片
  static Future<ui.Image> loadImageFromBytes(Uint8List bytes) async {
    final codec = await ui.instantiateImageCodec(bytes);
    final frame = await codec.getNextFrame();
    return frame.image;
  }

  // ==================== 图片信息 ====================

  /// 获取图片尺寸
  static Future<Size> getImageSize(String assetPath) async {
    final image = await loadImageFromAsset(assetPath);
    return Size(image.width.toDouble(), image.height.toDouble());
  }

  /// 获取图片宽高比
  static Future<double> getImageAspectRatio(String assetPath) async {
    final size = await getImageSize(assetPath);
    return size.width / size.height;
  }

  /// 检查图片是否为横向
  static Future<bool> isLandscape(String assetPath) async {
    final aspectRatio = await getImageAspectRatio(assetPath);
    return aspectRatio > 1.0;
  }

  /// 检查图片是否为纵向
  static Future<bool> isPortrait(String assetPath) async {
    final aspectRatio = await getImageAspectRatio(assetPath);
    return aspectRatio < 1.0;
  }

  /// 检查图片是否为正方形
  static Future<bool> isSquare(String assetPath) async {
    final aspectRatio = await getImageAspectRatio(assetPath);
    return (aspectRatio - 1.0).abs() < 0.01;
  }

  // ==================== 尺寸计算 ====================

  /// 计算适配尺寸（保持宽高比）
  static Size calculateFitSize(Size originalSize, Size targetSize) {
    final aspectRatio = originalSize.width / originalSize.height;
    final targetAspectRatio = targetSize.width / targetSize.height;

    if (aspectRatio > targetAspectRatio) {
      // 原图更宽，以宽度为准
      return Size(targetSize.width, targetSize.width / aspectRatio);
    } else {
      // 原图更高，以高度为准
      return Size(targetSize.height * aspectRatio, targetSize.height);
    }
  }

  /// 计算填充尺寸（可能裁剪）
  static Size calculateFillSize(Size originalSize, Size targetSize) {
    final aspectRatio = originalSize.width / originalSize.height;
    final targetAspectRatio = targetSize.width / targetSize.height;

    if (aspectRatio > targetAspectRatio) {
      // 原图更宽，以高度为准
      return Size(targetSize.height * aspectRatio, targetSize.height);
    } else {
      // 原图更高，以宽度为准
      return Size(targetSize.width, targetSize.width / aspectRatio);
    }
  }

  /// 计算缩放比例
  static double calculateScaleFactor(
    Size originalSize,
    Size targetSize,
    BoxFit fit,
  ) {
    switch (fit) {
      case BoxFit.contain:
        return math.min(
          targetSize.width / originalSize.width,
          targetSize.height / originalSize.height,
        );
      case BoxFit.cover:
        return math.max(
          targetSize.width / originalSize.width,
          targetSize.height / originalSize.height,
        );
      case BoxFit.fill:
        return 1.0; // 拉伸填充
      case BoxFit.fitWidth:
        return targetSize.width / originalSize.width;
      case BoxFit.fitHeight:
        return targetSize.height / originalSize.height;
      case BoxFit.none:
        return 1.0;
      case BoxFit.scaleDown:
        final scale = math.min(
          targetSize.width / originalSize.width,
          targetSize.height / originalSize.height,
        );
        return math.min(scale, 1.0);
    }
  }

  // ==================== 缓存优化 ====================

  /// 计算最佳缓存尺寸
  static Size calculateOptimalCacheSize(
    Size originalSize,
    Size displaySize,
    double devicePixelRatio,
  ) {
    final scaledDisplaySize = Size(
      displaySize.width * devicePixelRatio,
      displaySize.height * devicePixelRatio,
    );

    // 不超过原图尺寸
    final cacheWidth = math.min(scaledDisplaySize.width, originalSize.width);
    final cacheHeight = math.min(scaledDisplaySize.height, originalSize.height);

    return Size(cacheWidth, cacheHeight);
  }

  /// 是否需要缓存
  static bool shouldCache(Size originalSize, Size displaySize) {
    const threshold = 2.0; // 原图尺寸超过显示尺寸2倍时才缓存
    return originalSize.width > displaySize.width * threshold ||
        originalSize.height > displaySize.height * threshold;
  }

  // ==================== 格式检测 ====================

  /// 检测图片格式
  static ImageFormat detectImageFormat(String path) {
    final extension = path.toLowerCase().split('.').last;
    switch (extension) {
      case 'jpg':
      case 'jpeg':
        return ImageFormat.jpeg;
      case 'png':
        return ImageFormat.png;
      case 'gif':
        return ImageFormat.gif;
      case 'webp':
        return ImageFormat.webp;
      case 'bmp':
        return ImageFormat.bmp;
      case 'svg':
        return ImageFormat.svg;
      default:
        return ImageFormat.unknown;
    }
  }

  /// 是否为矢量图
  static bool isVectorImage(String path) {
    return detectImageFormat(path) == ImageFormat.svg;
  }

  /// 是否为动图
  static bool isAnimatedImage(String path) {
    return detectImageFormat(path) == ImageFormat.gif;
  }

  /// 是否支持透明度
  static bool supportsTransparency(String path) {
    final format = detectImageFormat(path);
    return format == ImageFormat.png ||
        format == ImageFormat.gif ||
        format == ImageFormat.webp ||
        format == ImageFormat.svg;
  }

  // ==================== 颜色分析 ====================

  /// 获取图片主色调
  static Future<Color> getDominantColor(String assetPath) async {
    final image = await loadImageFromAsset(assetPath);
    final bytes = await image.toByteData(format: ui.ImageByteFormat.rawRgba);

    if (bytes == null) return Colors.grey;

    final pixels = bytes.buffer.asUint8List();
    final colorCounts = <int, int>{};

    // 采样像素（每隔10个像素采样一次以提高性能）
    for (int i = 0; i < pixels.length; i += 40) {
      // RGBA = 4 bytes
      if (i + 3 < pixels.length) {
        final r = pixels[i];
        final g = pixels[i + 1];
        final b = pixels[i + 2];
        final a = pixels[i + 3];

        // 忽略透明像素
        if (a < 128) continue;

        final color = (r << 16) | (g << 8) | b;
        colorCounts[color] = (colorCounts[color] ?? 0) + 1;
      }
    }

    if (colorCounts.isEmpty) return Colors.grey;

    // 找到出现次数最多的颜色
    final dominantColor = colorCounts.entries
        .reduce((a, b) => a.value > b.value ? a : b)
        .key;

    return Color(0xFF000000 | dominantColor);
  }

  /// 检查图片是否为深色
  static Future<bool> isDarkImage(String assetPath) async {
    final dominantColor = await getDominantColor(assetPath);
    final luminance = dominantColor.computeLuminance();
    return luminance < 0.5;
  }

  // ==================== 工具方法 ====================

  /// 生成占位符颜色
  static Color generatePlaceholderColor(String text) {
    final hash = text.hashCode;
    final hue = (hash % 360).toDouble();
    return HSVColor.fromAHSV(1.0, hue, 0.3, 0.8).toColor();
  }

  /// 创建渐变占位符
  static LinearGradient createGradientPlaceholder(String text) {
    final baseColor = generatePlaceholderColor(text);
    return LinearGradient(
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
      colors: [
        baseColor.withValues(alpha: 0.8),
        baseColor.withValues(alpha: 0.4),
      ],
    );
  }

  /// 验证图片路径
  static bool isValidImagePath(String path) {
    final validExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp', 'svg'];
    final extension = path.toLowerCase().split('.').last;
    return validExtensions.contains(extension);
  }

  /// 获取图片文件大小估算
  static Future<int> estimateImageSize(String assetPath) async {
    try {
      final data = await rootBundle.load(assetPath);
      return data.lengthInBytes;
    } catch (e) {
      return 0;
    }
  }
}

/// 图片格式枚举
enum ImageFormat { jpeg, png, gif, webp, bmp, svg, unknown }

/// 图片质量枚举
enum ImageQuality { low, medium, high, original }

/// 图片压缩配置
class ImageCompressionConfig {
  final ImageQuality quality;
  final int? maxWidth;
  final int? maxHeight;
  final ImageFormat? outputFormat;

  const ImageCompressionConfig({
    this.quality = ImageQuality.medium,
    this.maxWidth,
    this.maxHeight,
    this.outputFormat,
  });
}
