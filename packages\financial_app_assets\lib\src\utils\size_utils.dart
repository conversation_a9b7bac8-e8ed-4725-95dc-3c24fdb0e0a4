import 'dart:math' as math;
import 'package:flutter/material.dart';

/// 尺寸工具类
///
/// 提供响应式设计、屏幕适配、尺寸计算等功能
class SizeUtils {
  SizeUtils._();

  // ==================== 设计稿基准 ====================

  /// 设计稿宽度（iPhone 6/7/8）
  static const double designWidth = 375.0;

  /// 设计稿高度（iPhone 6/7/8）
  static const double designHeight = 667.0;

  /// 设计稿像素密度
  static const double designDensity = 2.0;

  // ==================== 屏幕信息 ====================

  /// 获取屏幕宽度
  static double screenWidth(BuildContext context) {
    return MediaQuery.of(context).size.width;
  }

  /// 获取屏幕高度
  static double screenHeight(BuildContext context) {
    return MediaQuery.of(context).size.height;
  }

  /// 获取屏幕像素密度
  static double screenDensity(BuildContext context) {
    return MediaQuery.of(context).devicePixelRatio;
  }

  /// 获取状态栏高度
  static double statusBarHeight(BuildContext context) {
    return MediaQuery.of(context).padding.top;
  }

  /// 获取底部安全区域高度
  static double bottomSafeHeight(BuildContext context) {
    return MediaQuery.of(context).padding.bottom;
  }

  /// 获取可用屏幕高度（去除状态栏和底部安全区域）
  static double availableHeight(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    return mediaQuery.size.height -
        mediaQuery.padding.top -
        mediaQuery.padding.bottom;
  }

  /// 获取键盘高度
  static double keyboardHeight(BuildContext context) {
    return MediaQuery.of(context).viewInsets.bottom;
  }

  /// 检查是否有键盘显示
  static bool isKeyboardVisible(BuildContext context) {
    return keyboardHeight(context) > 0;
  }

  // ==================== 响应式适配 ====================

  /// 宽度适配
  static double w(BuildContext context, double width) {
    return width * screenWidth(context) / designWidth;
  }

  /// 高度适配
  static double h(BuildContext context, double height) {
    return height * screenHeight(context) / designHeight;
  }

  /// 字体大小适配
  static double sp(BuildContext context, double fontSize) {
    final scale = math.min(
      screenWidth(context) / designWidth,
      screenHeight(context) / designHeight,
    );
    return fontSize * scale;
  }

  /// 最小尺寸适配（取宽高适配的最小值）
  static double min(BuildContext context, double size) {
    return math.min(w(context, size), h(context, size));
  }

  /// 最大尺寸适配（取宽高适配的最大值）
  static double max(BuildContext context, double size) {
    return math.max(w(context, size), h(context, size));
  }

  /// 对角线适配
  static double diagonal(BuildContext context, double size) {
    final screenDiagonal = math.sqrt(
      math.pow(screenWidth(context), 2) + math.pow(screenHeight(context), 2),
    );
    final designDiagonal = math.sqrt(
      math.pow(designWidth, 2) + math.pow(designHeight, 2),
    );
    return size * screenDiagonal / designDiagonal;
  }

  // ==================== 设备类型判断 ====================

  /// 是否为手机
  static bool isPhone(BuildContext context) {
    return screenWidth(context) < 600;
  }

  /// 是否为平板
  static bool isTablet(BuildContext context) {
    return screenWidth(context) >= 600 && screenWidth(context) < 1200;
  }

  /// 是否为桌面
  static bool isDesktop(BuildContext context) {
    return screenWidth(context) >= 1200;
  }

  /// 是否为小屏手机
  static bool isSmallPhone(BuildContext context) {
    return screenWidth(context) < 360;
  }

  /// 是否为大屏手机
  static bool isLargePhone(BuildContext context) {
    return screenWidth(context) > 414;
  }

  /// 是否为横屏
  static bool isLandscape(BuildContext context) {
    return MediaQuery.of(context).orientation == Orientation.landscape;
  }

  /// 是否为竖屏
  static bool isPortrait(BuildContext context) {
    return MediaQuery.of(context).orientation == Orientation.portrait;
  }

  // ==================== 断点判断 ====================

  /// 获取断点类型
  static BreakPoint getBreakPoint(BuildContext context) {
    final width = screenWidth(context);
    if (width < 600) {
      return BreakPoint.mobile;
    } else if (width < 1200) {
      return BreakPoint.tablet;
    } else {
      return BreakPoint.desktop;
    }
  }

  /// 根据断点返回不同的值
  static T responsive<T>(
    BuildContext context, {
    required T mobile,
    T? tablet,
    T? desktop,
  }) {
    final breakPoint = getBreakPoint(context);
    switch (breakPoint) {
      case BreakPoint.mobile:
        return mobile;
      case BreakPoint.tablet:
        return tablet ?? mobile;
      case BreakPoint.desktop:
        return desktop ?? tablet ?? mobile;
    }
  }

  // ==================== 边距和间距 ====================

  /// 标准边距
  static EdgeInsets padding(
    BuildContext context, {
    double? all,
    double? horizontal,
    double? vertical,
    double? left,
    double? top,
    double? right,
    double? bottom,
  }) {
    return EdgeInsets.only(
      left: w(context, left ?? horizontal ?? all ?? 0),
      top: h(context, top ?? vertical ?? all ?? 0),
      right: w(context, right ?? horizontal ?? all ?? 0),
      bottom: h(context, bottom ?? vertical ?? all ?? 0),
    );
  }

  /// 对称边距
  static EdgeInsets paddingSymmetric(
    BuildContext context, {
    double? horizontal,
    double? vertical,
  }) {
    return EdgeInsets.symmetric(
      horizontal: w(context, horizontal ?? 0),
      vertical: h(context, vertical ?? 0),
    );
  }

  /// 统一边距
  static EdgeInsets paddingAll(BuildContext context, double value) {
    return EdgeInsets.all(min(context, value));
  }

  // ==================== 圆角半径 ====================

  /// 圆角半径适配
  static double radius(BuildContext context, double radius) {
    return min(context, radius);
  }

  /// 圆角边框
  static BorderRadius borderRadius(BuildContext context, double radius) {
    return BorderRadius.circular(SizeUtils.radius(context, radius));
  }

  /// 部分圆角
  static BorderRadius borderRadiusOnly(
    BuildContext context, {
    double? topLeft,
    double? topRight,
    double? bottomLeft,
    double? bottomRight,
  }) {
    return BorderRadius.only(
      topLeft: Radius.circular(radius(context, topLeft ?? 0)),
      topRight: Radius.circular(radius(context, topRight ?? 0)),
      bottomLeft: Radius.circular(radius(context, bottomLeft ?? 0)),
      bottomRight: Radius.circular(radius(context, bottomRight ?? 0)),
    );
  }

  // ==================== 阴影和高度 ====================

  /// 阴影模糊半径适配
  static double shadowBlur(BuildContext context, double blur) {
    return min(context, blur);
  }

  /// 阴影偏移适配
  static Offset shadowOffset(BuildContext context, double dx, double dy) {
    return Offset(w(context, dx), h(context, dy));
  }

  /// 标准阴影
  static List<BoxShadow> shadow(
    BuildContext context, {
    Color color = Colors.black26,
    double blur = 4.0,
    double dx = 0.0,
    double dy = 2.0,
  }) {
    return [
      BoxShadow(
        color: color,
        blurRadius: shadowBlur(context, blur),
        offset: shadowOffset(context, dx, dy),
      ),
    ];
  }

  // ==================== 工具方法 ====================

  /// 限制尺寸范围
  static double clamp(double value, double min, double max) {
    return math.max(min, math.min(max, value));
  }

  /// 获取安全的尺寸（确保不为负数）
  static double safe(double value) {
    return math.max(0, value);
  }

  /// 四舍五入到指定小数位
  static double round(double value, [int decimals = 2]) {
    final factor = math.pow(10, decimals);
    return (value * factor).round() / factor;
  }

  /// 获取屏幕信息摘要
  static ScreenInfo getScreenInfo(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    return ScreenInfo(
      width: mediaQuery.size.width,
      height: mediaQuery.size.height,
      density: mediaQuery.devicePixelRatio,
      statusBarHeight: mediaQuery.padding.top,
      bottomSafeHeight: mediaQuery.padding.bottom,
      orientation: mediaQuery.orientation,
      breakPoint: getBreakPoint(context),
    );
  }

  /// 计算文本尺寸
  static Size calculateTextSize(
    String text,
    TextStyle style,
    BuildContext context, {
    int? maxLines,
    double? maxWidth,
  }) {
    final textPainter = TextPainter(
      text: TextSpan(text: text, style: style),
      maxLines: maxLines,
      textDirection: TextDirection.ltr,
    );

    textPainter.layout(maxWidth: maxWidth ?? screenWidth(context));
    return textPainter.size;
  }
}

/// 断点枚举
enum BreakPoint { mobile, tablet, desktop }

/// 屏幕信息类
class ScreenInfo {
  final double width;
  final double height;
  final double density;
  final double statusBarHeight;
  final double bottomSafeHeight;
  final Orientation orientation;
  final BreakPoint breakPoint;

  const ScreenInfo({
    required this.width,
    required this.height,
    required this.density,
    required this.statusBarHeight,
    required this.bottomSafeHeight,
    required this.orientation,
    required this.breakPoint,
  });

  /// 是否为横屏
  bool get isLandscape => orientation == Orientation.landscape;

  /// 是否为竖屏
  bool get isPortrait => orientation == Orientation.portrait;

  /// 屏幕宽高比
  double get aspectRatio => width / height;

  /// 可用高度
  double get availableHeight => height - statusBarHeight - bottomSafeHeight;

  /// 屏幕对角线长度
  double get diagonal => math.sqrt(width * width + height * height);

  @override
  String toString() {
    return 'ScreenInfo(width: $width, height: $height, density: $density, '
        'orientation: $orientation, breakPoint: $breakPoint)';
  }
}
