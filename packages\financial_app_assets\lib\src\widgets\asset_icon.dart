import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import '../icons/app_icons.dart';
import '../icons/financial_icons.dart';
import '../utils/size_utils.dart';

/// 资源图标组件
///
/// 统一的图标组件，支持多种图标类型：
/// - Material Icons
/// - 自定义图标字体
/// - SVG 图标
/// - PNG 图标
class AssetIcon extends StatelessWidget {
  /// 图标数据或路径
  final dynamic icon;

  /// 图标大小
  final double? size;

  /// 图标颜色
  final Color? color;

  /// 图标类型
  final AssetIconType? type;

  /// 语义标签
  final String? semanticLabel;

  /// 文本方向
  final TextDirection? textDirection;

  /// 是否启用响应式大小
  final bool responsive;

  /// 占位符图标
  final IconData? placeholder;

  /// 错误图标
  final IconData? errorIcon;

  const AssetIcon(
    this.icon, {
    super.key,
    this.size,
    this.color,
    this.type,
    this.semanticLabel,
    this.textDirection,
    this.responsive = false,
    this.placeholder,
    this.errorIcon,
  });

  /// 创建 Material 图标
  const AssetIcon.material(
    IconData iconData, {
    super.key,
    this.size,
    this.color,
    this.semanticLabel,
    this.textDirection,
    this.responsive = false,
  }) : icon = iconData,
       type = AssetIconType.material,
       placeholder = null,
       errorIcon = null;

  /// 创建自定义字体图标
  const AssetIcon.font(
    IconData iconData, {
    super.key,
    this.size,
    this.color,
    this.semanticLabel,
    this.textDirection,
    this.responsive = false,
  }) : icon = iconData,
       type = AssetIconType.font,
       placeholder = null,
       errorIcon = null;

  /// 创建 SVG 图标
  const AssetIcon.svg(
    String assetPath, {
    super.key,
    this.size,
    this.color,
    this.semanticLabel,
    this.responsive = false,
    this.placeholder,
    this.errorIcon,
  }) : icon = assetPath,
       type = AssetIconType.svg,
       textDirection = null;

  /// 创建 PNG 图标
  const AssetIcon.png(
    String assetPath, {
    super.key,
    this.size,
    this.color,
    this.semanticLabel,
    this.responsive = false,
    this.placeholder,
    this.errorIcon,
  }) : icon = assetPath,
       type = AssetIconType.png,
       textDirection = null;

  /// 根据名称创建图标
  factory AssetIcon.named(
    String iconName, {
    Key? key,
    double? size,
    Color? color,
    String? semanticLabel,
    bool responsive = false,
  }) {
    // 首先尝试从 Material Icons 获取常用图标
    IconData? materialIcon;
    switch (iconName.toLowerCase()) {
      case 'home':
        materialIcon = AppIcons.home;
        break;
      case 'market':
        materialIcon = AppIcons.market;
        break;
      case 'trade':
        materialIcon = AppIcons.trade;
        break;
      case 'portfolio':
        materialIcon = AppIcons.portfolio;
        break;
      case 'profile':
        materialIcon = AppIcons.profile;
        break;
      case 'search':
        materialIcon = AppIcons.search;
        break;
      case 'settings':
        materialIcon = AppIcons.settings;
        break;
    }

    if (materialIcon != null) {
      return AssetIcon.material(
        materialIcon,
        key: key,
        size: size,
        color: color,
        semanticLabel: semanticLabel,
        responsive: responsive,
      );
    }

    // 然后尝试从 FinancialIcons 获取
    final financialIcon = FinancialIcons.getIconByName(iconName);
    if (financialIcon != null) {
      return AssetIcon.font(
        financialIcon,
        key: key,
        size: size,
        color: color,
        semanticLabel: semanticLabel,
        responsive: responsive,
      );
    }

    // 默认返回问号图标
    return AssetIcon.material(
      Icons.help_outline,
      key: key,
      size: size,
      color: color,
      semanticLabel: semanticLabel ?? 'Unknown icon: $iconName',
      responsive: responsive,
    );
  }

  @override
  Widget build(BuildContext context) {
    final iconSize = _getIconSize(context);
    final iconColor = color ?? Theme.of(context).iconTheme.color;

    switch (_getIconType()) {
      case AssetIconType.material:
      case AssetIconType.font:
        return Icon(
          icon as IconData,
          size: iconSize,
          color: iconColor,
          semanticLabel: semanticLabel,
          textDirection: textDirection,
        );

      case AssetIconType.svg:
        return _buildSvgIcon(context, iconSize, iconColor);

      case AssetIconType.png:
        return _buildPngIcon(context, iconSize, iconColor);
    }
  }

  /// 构建 SVG 图标
  Widget _buildSvgIcon(
    BuildContext context,
    double iconSize,
    Color? iconColor,
  ) {
    return SvgPicture.asset(
      icon as String,
      width: iconSize,
      height: iconSize,
      colorFilter: iconColor != null
          ? ColorFilter.mode(iconColor, BlendMode.srcIn)
          : null,
      semanticsLabel: semanticLabel,
      placeholderBuilder: (context) => _buildPlaceholder(iconSize, iconColor),
    );
  }

  /// 构建 PNG 图标
  Widget _buildPngIcon(
    BuildContext context,
    double iconSize,
    Color? iconColor,
  ) {
    return Image.asset(
      icon as String,
      width: iconSize,
      height: iconSize,
      color: iconColor,
      semanticLabel: semanticLabel,
      errorBuilder: (context, error, stackTrace) {
        return _buildErrorIcon(iconSize, iconColor);
      },
      frameBuilder: (context, child, frame, wasSynchronouslyLoaded) {
        if (wasSynchronouslyLoaded || frame != null) {
          return child;
        }
        return _buildPlaceholder(iconSize, iconColor);
      },
    );
  }

  /// 构建占位符
  Widget _buildPlaceholder(double iconSize, Color? iconColor) {
    return Icon(
      placeholder ?? Icons.image_outlined,
      size: iconSize,
      color: iconColor?.withValues(alpha: 0.5) ?? Colors.grey[400],
    );
  }

  /// 构建错误图标
  Widget _buildErrorIcon(double iconSize, Color? iconColor) {
    return Icon(
      errorIcon ?? Icons.broken_image_outlined,
      size: iconSize,
      color: iconColor?.withValues(alpha: 0.5) ?? Colors.grey[400],
    );
  }

  /// 获取图标大小
  double _getIconSize(BuildContext context) {
    final baseSize = size ?? 24.0;
    return responsive ? SizeUtils.min(context, baseSize) : baseSize;
  }

  /// 获取图标类型
  AssetIconType _getIconType() {
    if (type != null) return type!;

    if (icon is IconData) {
      // 检查是否为自定义字体图标
      final iconData = icon as IconData;
      if (iconData.fontFamily == 'FinancialIcons') {
        return AssetIconType.font;
      }
      return AssetIconType.material;
    }

    if (icon is String) {
      final path = icon as String;
      if (path.endsWith('.svg')) {
        return AssetIconType.svg;
      }
      return AssetIconType.png;
    }

    return AssetIconType.material;
  }
}

/// 图标按钮组件
class AssetIconButton extends StatelessWidget {
  /// 图标
  final AssetIcon icon;

  /// 点击回调
  final VoidCallback? onPressed;

  /// 长按回调
  final VoidCallback? onLongPress;

  /// 按钮大小
  final double? iconSize;

  /// 按钮颜色
  final Color? color;

  /// 禁用颜色
  final Color? disabledColor;

  /// 高亮颜色
  final Color? highlightColor;

  /// 水波纹颜色
  final Color? splashColor;

  /// 内边距
  final EdgeInsetsGeometry? padding;

  /// 对齐方式
  final AlignmentGeometry alignment;

  /// 工具提示
  final String? tooltip;

  /// 是否启用反馈
  final bool enableFeedback;

  /// 约束条件
  final BoxConstraints? constraints;

  const AssetIconButton({
    super.key,
    required this.icon,
    required this.onPressed,
    this.onLongPress,
    this.iconSize,
    this.color,
    this.disabledColor,
    this.highlightColor,
    this.splashColor,
    this.padding,
    this.alignment = Alignment.center,
    this.tooltip,
    this.enableFeedback = true,
    this.constraints,
  });

  @override
  Widget build(BuildContext context) {
    Widget button = IconButton(
      onPressed: onPressed,
      onLongPress: onLongPress,
      iconSize: iconSize,
      color: color,
      disabledColor: disabledColor,
      highlightColor: highlightColor,
      splashColor: splashColor,
      padding: padding ?? const EdgeInsets.all(8.0),
      alignment: alignment,
      enableFeedback: enableFeedback,
      constraints: constraints,
      icon: icon,
    );

    if (tooltip != null) {
      button = Tooltip(message: tooltip!, child: button);
    }

    return button;
  }
}

/// 图标类型枚举
enum AssetIconType {
  /// Material Icons
  material,

  /// 自定义字体图标
  font,

  /// SVG 图标
  svg,

  /// PNG 图标
  png,
}

/// 图标主题数据
class AssetIconTheme {
  /// 默认大小
  final double size;

  /// 默认颜色
  final Color? color;

  /// 是否启用响应式
  final bool responsive;

  const AssetIconTheme({this.size = 24.0, this.color, this.responsive = false});

  /// 复制并修改主题
  AssetIconTheme copyWith({double? size, Color? color, bool? responsive}) {
    return AssetIconTheme(
      size: size ?? this.size,
      color: color ?? this.color,
      responsive: responsive ?? this.responsive,
    );
  }
}

/// 图标主题组件
class AssetIconThemeData extends InheritedWidget {
  /// 主题数据
  final AssetIconTheme theme;

  const AssetIconThemeData({
    super.key,
    required this.theme,
    required super.child,
  });

  /// 获取主题数据
  static AssetIconTheme? of(BuildContext context) {
    return context
        .dependOnInheritedWidgetOfExactType<AssetIconThemeData>()
        ?.theme;
  }

  @override
  bool updateShouldNotify(AssetIconThemeData oldWidget) {
    return theme != oldWidget.theme;
  }
}
