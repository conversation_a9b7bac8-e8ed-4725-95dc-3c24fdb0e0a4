import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';

import '../core/asset_manager.dart';
import '../images/app_images.dart';

/// 优化的资源图片组件
///
/// 提供智能的图片加载和缓存功能，包括：
/// - 自动缓存管理
/// - 错误处理和回退
/// - 加载状态显示
/// - 性能优化
class AppAssetImage extends StatefulWidget {
  /// 图片路径
  final String imagePath;

  /// 图片宽度
  final double? width;

  /// 图片高度
  final double? height;

  /// 图片适配方式
  final BoxFit? fit;

  /// 图片颜色
  final Color? color;

  /// 颜色混合模式
  final BlendMode? colorBlendMode;

  /// 对齐方式
  final AlignmentGeometry alignment;

  /// 重复方式
  final ImageRepeat repeat;

  /// 是否启用缓存
  final bool enableCache;

  /// 错误时的回退图片
  final String? fallbackImage;

  /// 加载时的占位符
  final Widget? placeholder;

  /// 错误时的占位符
  final Widget? errorWidget;

  /// 语义标签
  final String? semanticLabel;

  /// 是否排除语义
  final bool excludeFromSemantics;

  /// 过滤质量
  final FilterQuality filterQuality;

  /// 缓存宽度
  final int? cacheWidth;

  /// 缓存高度
  final int? cacheHeight;

  /// 是否为网络图片
  final bool isNetworkImage;

  /// 加载超时时间
  final Duration? timeout;

  const AppAssetImage({
    Key? key,
    required this.imagePath,
    this.width,
    this.height,
    this.fit,
    this.color,
    this.colorBlendMode,
    this.alignment = Alignment.center,
    this.repeat = ImageRepeat.noRepeat,
    this.enableCache = true,
    this.fallbackImage,
    this.placeholder,
    this.errorWidget,
    this.semanticLabel,
    this.excludeFromSemantics = false,
    this.filterQuality = FilterQuality.low,
    this.cacheWidth,
    this.cacheHeight,
    this.isNetworkImage = false,
    this.timeout,
  }) : super(key: key);

  @override
  State<AppAssetImage> createState() => _AssetImageState();
}

class _AssetImageState extends State<AppAssetImage> {
  final AssetManager _assetManager = AssetManager.instance;
  ImageProvider? _imageProvider;
  bool _isLoading = true;
  bool _hasError = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadImage();
  }

  @override
  void didUpdateWidget(AppAssetImage oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.imagePath != widget.imagePath) {
      _loadImage();
    }
  }

  Future<void> _loadImage() async {
    setState(() {
      _isLoading = true;
      _hasError = false;
      _errorMessage = null;
    });

    try {
      if (widget.isNetworkImage) {
        // 网络图片处理
        _imageProvider = CachedNetworkImageProvider(widget.imagePath);
      } else {
        // 本地资源图片处理
        _imageProvider = await _assetManager.getImage(
          widget.imagePath,
          useCache: widget.enableCache,
        );
      }

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _hasError = true;
        _errorMessage = e.toString();
      });

      // 尝试加载回退图片
      if (widget.fallbackImage != null) {
        try {
          _imageProvider = await _assetManager.getImage(
            widget.fallbackImage!,
            useCache: widget.enableCache,
          );
          setState(() {
            _hasError = false;
          });
        } catch (fallbackError) {
          // 回退图片也加载失败，使用默认错误图片
          _imageProvider = const AssetImage(
            AppImages.errorState,
            package: AppImages.packageName,
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return _buildPlaceholder();
    }

    if (_hasError && _imageProvider == null) {
      return _buildErrorWidget();
    }

    if (widget.isNetworkImage) {
      return _buildNetworkImage();
    }

    return _buildAssetImage();
  }

  Widget _buildPlaceholder() {
    if (widget.placeholder != null) {
      return widget.placeholder!;
    }

    return Container(
      width: widget.width,
      height: widget.height,
      decoration: BoxDecoration(
        color: Colors.grey[200],
        borderRadius: BorderRadius.circular(4),
      ),
      child: const Center(child: CircularProgressIndicator(strokeWidth: 2)),
    );
  }

  Widget _buildErrorWidget() {
    if (widget.errorWidget != null) {
      return widget.errorWidget!;
    }

    return Container(
      width: widget.width,
      height: widget.height,
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(4),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error_outline, color: Colors.grey[400], size: 24),
          const SizedBox(height: 4),
          Text('加载失败', style: TextStyle(color: Colors.grey[600], fontSize: 12)),
        ],
      ),
    );
  }

  Widget _buildNetworkImage() {
    return CachedNetworkImage(
      imageUrl: widget.imagePath,
      width: widget.width,
      height: widget.height,
      fit: widget.fit,
      color: widget.color,
      colorBlendMode: widget.colorBlendMode,
      alignment: widget.alignment as Alignment,
      repeat: widget.repeat,
      placeholder: (context, url) => _buildPlaceholder(),
      errorWidget: (context, url, error) => _buildErrorWidget(),
      filterQuality: widget.filterQuality,
      memCacheWidth: widget.cacheWidth,
      memCacheHeight: widget.cacheHeight,
      fadeInDuration: const Duration(milliseconds: 300),
      fadeOutDuration: const Duration(milliseconds: 100),
    );
  }

  Widget _buildAssetImage() {
    return Image(
      image: _imageProvider!,
      width: widget.width,
      height: widget.height,
      fit: widget.fit,
      color: widget.color,
      colorBlendMode: widget.colorBlendMode,
      alignment: widget.alignment,
      repeat: widget.repeat,
      semanticLabel: widget.semanticLabel,
      excludeFromSemantics: widget.excludeFromSemantics,
      filterQuality: widget.filterQuality,
      // cacheWidth: widget.cacheWidth,
      // cacheHeight: widget.cacheHeight,
      frameBuilder: (context, child, frame, wasSynchronouslyLoaded) {
        if (wasSynchronouslyLoaded) {
          return child;
        }
        return AnimatedOpacity(
          opacity: frame == null ? 0 : 1,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
          child: child,
        );
      },
      errorBuilder: (context, error, stackTrace) {
        return _buildErrorWidget();
      },
    );
  }
}
