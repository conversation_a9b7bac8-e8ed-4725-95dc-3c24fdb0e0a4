import 'package:flutter/material.dart';
import '../utils/size_utils.dart';

/// 缓存资源图片组件
///
/// 提供高级缓存功能的图片组件，支持：
/// - 内存缓存
/// - 磁盘缓存
/// - 预加载
/// - 渐进式加载
/// - 错误重试
class CachedAssetImage extends StatefulWidget {
  /// 图片路径
  final String imagePath;

  /// 图片宽度
  final double? width;

  /// 图片高度
  final double? height;

  /// 适配方式
  final BoxFit? fit;

  /// 对齐方式
  final AlignmentGeometry alignment;

  /// 占位符构建器
  final Widget Function(BuildContext context)? placeholderBuilder;

  /// 错误构建器
  final Widget Function(BuildContext context, Object error)? errorBuilder;

  /// 加载构建器
  final Widget Function(
    BuildContext context,
    Widget child,
    ImageChunkEvent? loadingProgress,
  )?
  loadingBuilder;

  /// 是否启用缓存
  final bool enableCache;

  /// 缓存键
  final String? cacheKey;

  /// 最大缓存时间（毫秒）
  final int maxCacheAge;

  /// 是否启用内存缓存
  final bool enableMemoryCache;

  /// 是否启用磁盘缓存
  final bool enableDiskCache;

  /// 缓存宽度
  final int? cacheWidth;

  /// 缓存高度
  final int? cacheHeight;

  /// 是否响应式
  final bool responsive;

  /// 重试次数
  final int retryCount;

  /// 重试延迟（毫秒）
  final int retryDelay;

  /// 是否预加载
  final bool preload;

  /// 渐进式加载
  final bool progressive;

  /// 语义标签
  final String? semanticLabel;

  const CachedAssetImage({
    super.key,
    required this.imagePath,
    this.width,
    this.height,
    this.fit,
    this.alignment = Alignment.center,
    this.placeholderBuilder,
    this.errorBuilder,
    this.loadingBuilder,
    this.enableCache = true,
    this.cacheKey,
    this.maxCacheAge = 7 * 24 * 60 * 60 * 1000, // 7天
    this.enableMemoryCache = true,
    this.enableDiskCache = true,
    this.cacheWidth,
    this.cacheHeight,
    this.responsive = false,
    this.retryCount = 3,
    this.retryDelay = 1000,
    this.preload = false,
    this.progressive = true,
    this.semanticLabel,
  });

  @override
  State<CachedAssetImage> createState() => _CachedAssetImageState();
}

class _CachedAssetImageState extends State<CachedAssetImage> {
  ImageProvider? _imageProvider;
  bool _isLoading = false;
  bool _hasError = false;
  Object? _error;
  int _currentRetry = 0;

  @override
  void initState() {
    super.initState();
    _loadImage();
  }

  @override
  void didUpdateWidget(CachedAssetImage oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.imagePath != widget.imagePath) {
      _loadImage();
    }
  }

  /// 加载图片
  Future<void> _loadImage() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
      _hasError = false;
      _error = null;
    });

    try {
      // 检查缓存
      if (widget.enableCache) {
        final cachedProvider = await _getCachedImageProvider();
        if (cachedProvider != null) {
          setState(() {
            _imageProvider = cachedProvider;
            _isLoading = false;
          });
          return;
        }
      }

      // 加载新图片
      final provider = await _createImageProvider();

      // 预加载图片
      if (widget.preload && mounted) {
        await precacheImage(provider, context);
      }

      // 缓存图片
      if (widget.enableCache) {
        await _cacheImageProvider(provider);
      }

      if (mounted) {
        setState(() {
          _imageProvider = provider;
          _isLoading = false;
          _currentRetry = 0;
        });
      }
    } catch (error) {
      if (mounted) {
        setState(() {
          _error = error;
          _hasError = true;
          _isLoading = false;
        });

        // 重试逻辑
        if (_currentRetry < widget.retryCount) {
          _currentRetry++;
          Future.delayed(Duration(milliseconds: widget.retryDelay), () {
            if (mounted) {
              _loadImage();
            }
          });
        }
      }
    }
  }

  /// 获取缓存的图片提供者
  Future<ImageProvider?> _getCachedImageProvider() async {
    try {
      // 这里可以实现实际的缓存逻辑
      // 例如从磁盘缓存或内存缓存中获取
      return null; // 暂时返回null，表示没有缓存
    } catch (e) {
      return null;
    }
  }

  /// 创建图片提供者
  Future<ImageProvider> _createImageProvider() async {
    final cacheWidth = _getCacheWidth();
    final cacheHeight = _getCacheHeight();

    if (cacheWidth != null || cacheHeight != null) {
      return ResizeImage(
        AssetImage(widget.imagePath),
        width: cacheWidth,
        height: cacheHeight,
      );
    }

    return AssetImage(widget.imagePath);
  }

  /// 缓存图片提供者
  Future<void> _cacheImageProvider(ImageProvider provider) async {
    try {
      // 这里可以实现实际的缓存逻辑
      // 例如保存到磁盘缓存或内存缓存
    } catch (e) {
      // 缓存失败不影响图片显示
    }
  }

  /// 获取缓存宽度
  int? _getCacheWidth() {
    if (widget.cacheWidth != null) return widget.cacheWidth;
    if (widget.width != null && widget.responsive) {
      return (SizeUtils.w(context, widget.width!) *
              MediaQuery.of(context).devicePixelRatio)
          .round();
    }
    if (widget.width != null) {
      return (widget.width! * MediaQuery.of(context).devicePixelRatio).round();
    }
    return null;
  }

  /// 获取缓存高度
  int? _getCacheHeight() {
    if (widget.cacheHeight != null) return widget.cacheHeight;
    if (widget.height != null && widget.responsive) {
      return (SizeUtils.h(context, widget.height!) *
              MediaQuery.of(context).devicePixelRatio)
          .round();
    }
    if (widget.height != null) {
      return (widget.height! * MediaQuery.of(context).devicePixelRatio).round();
    }
    return null;
  }

  /// 获取显示宽度
  double? _getDisplayWidth() {
    if (widget.width == null) return null;
    return widget.responsive
        ? SizeUtils.w(context, widget.width!)
        : widget.width;
  }

  /// 获取显示高度
  double? _getDisplayHeight() {
    if (widget.height == null) return null;
    return widget.responsive
        ? SizeUtils.h(context, widget.height!)
        : widget.height;
  }

  @override
  Widget build(BuildContext context) {
    // 错误状态
    if (_hasError) {
      return _buildErrorWidget();
    }

    // 加载状态
    if (_isLoading || _imageProvider == null) {
      return _buildLoadingWidget();
    }

    // 正常显示
    return _buildImageWidget();
  }

  /// 构建图片组件
  Widget _buildImageWidget() {
    return Image(
      image: _imageProvider!,
      width: _getDisplayWidth(),
      height: _getDisplayHeight(),
      fit: widget.fit,
      alignment: widget.alignment,
      semanticLabel: widget.semanticLabel,
      frameBuilder: widget.loadingBuilder != null
          ? (context, child, frame, wasSynchronouslyLoaded) {
              return widget.loadingBuilder!(context, child, null);
            }
          : null,
      errorBuilder: (context, error, stackTrace) {
        return _buildErrorWidget();
      },
    );
  }

  /// 构建加载组件
  Widget _buildLoadingWidget() {
    if (widget.placeholderBuilder != null) {
      return widget.placeholderBuilder!(context);
    }

    return Container(
      width: _getDisplayWidth(),
      height: _getDisplayHeight(),
      color: Colors.grey[200],
      child: const Center(child: CircularProgressIndicator()),
    );
  }

  /// 构建错误组件
  Widget _buildErrorWidget() {
    if (widget.errorBuilder != null) {
      return widget.errorBuilder!(context, _error ?? 'Unknown error');
    }

    return Container(
      width: _getDisplayWidth(),
      height: _getDisplayHeight(),
      color: Colors.grey[300],
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.broken_image, color: Colors.grey, size: 48),
          const SizedBox(height: 8),
          Text(
            'Failed to load image',
            style: TextStyle(color: Colors.grey[600], fontSize: 12),
          ),
          if (_currentRetry < widget.retryCount) ...[
            const SizedBox(height: 8),
            TextButton(onPressed: _loadImage, child: const Text('Retry')),
          ],
        ],
      ),
    );
  }
}

/// 缓存配置
class CacheConfig {
  /// 最大内存缓存大小（字节）
  final int maxMemoryCacheSize;

  /// 最大磁盘缓存大小（字节）
  final int maxDiskCacheSize;

  /// 默认缓存时间（毫秒）
  final int defaultCacheAge;

  /// 是否启用内存缓存
  final bool enableMemoryCache;

  /// 是否启用磁盘缓存
  final bool enableDiskCache;

  const CacheConfig({
    this.maxMemoryCacheSize = 100 * 1024 * 1024, // 100MB
    this.maxDiskCacheSize = 500 * 1024 * 1024, // 500MB
    this.defaultCacheAge = 7 * 24 * 60 * 60 * 1000, // 7天
    this.enableMemoryCache = true,
    this.enableDiskCache = true,
  });
}

/// 缓存管理器
class ImageCacheManager {
  static final ImageCacheManager _instance = ImageCacheManager._internal();
  factory ImageCacheManager() => _instance;
  ImageCacheManager._internal();

  CacheConfig _config = const CacheConfig();

  /// 设置缓存配置
  void setConfig(CacheConfig config) {
    _config = config;

    // 设置Flutter图片缓存大小
    if (config.enableMemoryCache) {
      imageCache.maximumSizeBytes = config.maxMemoryCacheSize;
    }
  }

  /// 清理缓存
  void clearCache() {
    imageCache.clear();
    imageCache.clearLiveImages();
    // 这里可以添加清理磁盘缓存的逻辑
  }

  /// 获取缓存大小
  int getCacheSize() {
    return imageCache.currentSizeBytes;
  }

  /// 获取缓存配置
  CacheConfig get config => _config;
}
