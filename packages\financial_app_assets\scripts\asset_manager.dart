#!/usr/bin/env dart

import 'dart:io';
import 'dart:convert';
import 'package:crypto/crypto.dart';
import 'package:path/path.dart' as path;

/// 资源管理脚本
///
/// 提供以下功能：
/// - 资源验证和检查
/// - 资源优化和压缩
/// - 资源清单生成
/// - 资源统计分析
/// - 资源同步和更新
void main(List<String> arguments) async {
  final manager = AssetManagerScript();

  if (arguments.isEmpty) {
    manager.printUsage();
    return;
  }

  final command = arguments[0];
  final options = arguments.skip(1).toList();

  switch (command) {
    case 'validate':
      await manager.validateAssets(options);
      break;
    case 'optimize':
      await manager.optimizeAssets(options);
      break;
    case 'generate-manifest':
      await manager.generateManifest(options);
      break;
    case 'stats':
      await manager.generateStats(options);
      break;
    case 'sync':
      await manager.syncAssets(options);
      break;
    case 'clean':
      await manager.cleanAssets(options);
      break;
    case 'help':
      manager.printUsage();
      break;
    default:
      print('未知命令: $command');
      manager.printUsage();
  }
}

class AssetManagerScript {
  static const String assetsDir = 'assets';
  static const String configFile = 'assets/configs/asset_config.json';
  static const String manifestFile = 'assets/asset_manifest.json';

  /// 打印使用说明
  void printUsage() {
    print('''
资源管理脚本 - Financial App Assets

用法: dart scripts/asset_manager.dart <命令> [选项]

命令:
  validate              验证所有资源文件
  optimize              优化资源文件 (压缩图片等)
  generate-manifest     生成资源清单文件
  stats                 生成资源统计报告
  sync                  同步资源文件
  clean                 清理临时文件和缓存
  help                  显示此帮助信息

选项:
  --verbose             显示详细输出
  --dry-run             仅显示将要执行的操作，不实际执行
  --output <文件>       指定输出文件路径
  --format <格式>       指定输出格式 (json, yaml, csv)

示例:
  dart scripts/asset_manager.dart validate --verbose
  dart scripts/asset_manager.dart optimize --dry-run
  dart scripts/asset_manager.dart generate-manifest --output manifest.json
  dart scripts/asset_manager.dart stats --format csv
''');
  }

  /// 验证资源文件
  Future<void> validateAssets(List<String> options) async {
    final verbose = options.contains('--verbose');
    final dryRun = options.contains('--dry-run');

    print('🔍 开始验证资源文件...');

    final assetFiles = await _findAssetFiles();
    final results = <ValidationResult>[];

    for (final file in assetFiles) {
      if (verbose) {
        print('验证: ${file.path}');
      }

      final result = await _validateAssetFile(file);
      results.add(result);

      if (!result.isValid) {
        print('❌ ${file.path}: ${result.message}');
      } else if (verbose) {
        print('✅ ${file.path}: ${result.message}');
      }
    }

    final validCount = results.where((r) => r.isValid).length;
    final invalidCount = results.length - validCount;

    print('\n📊 验证结果:');
    print('  总文件数: ${results.length}');
    print('  有效文件: $validCount');
    print('  无效文件: $invalidCount');

    if (invalidCount > 0) {
      exit(1);
    }
  }

  /// 优化资源文件
  Future<void> optimizeAssets(List<String> options) async {
    final verbose = options.contains('--verbose');
    final dryRun = options.contains('--dry-run');

    print('🚀 开始优化资源文件...');

    final imageFiles = await _findImageFiles();
    int optimizedCount = 0;
    int totalSavings = 0;

    for (final file in imageFiles) {
      if (verbose) {
        print('优化: ${file.path}');
      }

      final originalSize = await file.length();

      if (!dryRun) {
        final optimized = await _optimizeImage(file);
        if (optimized) {
          final newSize = await file.length();
          final savings = originalSize - newSize;
          totalSavings += savings;
          optimizedCount++;

          if (verbose) {
            print('  节省: ${_formatFileSize(savings)}');
          }
        }
      } else {
        print('  [DRY RUN] 将优化: ${file.path}');
      }
    }

    print('\n📊 优化结果:');
    print('  优化文件数: $optimizedCount');
    print('  总节省空间: ${_formatFileSize(totalSavings)}');
  }

  /// 生成资源清单
  Future<void> generateManifest(List<String> options) async {
    final verbose = options.contains('--verbose');
    final outputIndex = options.indexOf('--output');
    final outputFile = outputIndex != -1 && outputIndex + 1 < options.length
        ? options[outputIndex + 1]
        : manifestFile;

    print('📋 生成资源清单...');

    final assetFiles = await _findAssetFiles();
    final manifest = await _generateManifest(assetFiles);

    final manifestJson = JsonEncoder.withIndent('  ').convert(manifest);
    await File(outputFile).writeAsString(manifestJson);

    if (verbose) {
      print('清单内容:');
      print(manifestJson);
    }

    print('✅ 资源清单已生成: $outputFile');
    print('   总资源数: ${manifest['total_assets']}');
    print('   类别数: ${manifest['categories'].length}');
  }

  /// 生成统计报告
  Future<void> generateStats(List<String> options) async {
    final formatIndex = options.indexOf('--format');
    final format = formatIndex != -1 && formatIndex + 1 < options.length
        ? options[formatIndex + 1]
        : 'json';

    print('📊 生成资源统计报告...');

    final stats = await _generateStats();

    switch (format.toLowerCase()) {
      case 'json':
        final json = JsonEncoder.withIndent('  ').convert(stats);
        print(json);
        break;
      case 'csv':
        _printStatsAsCsv(stats);
        break;
      default:
        _printStatsAsTable(stats);
    }
  }

  /// 同步资源文件
  Future<void> syncAssets(List<String> options) async {
    // final verbose = options.contains('--verbose');

    print('🔄 同步资源文件...');

    // 这里可以实现从远程服务器同步资源的逻辑
    // 例如从 CDN 下载最新的资源文件

    print('✅ 资源同步完成');
  }

  /// 清理临时文件
  Future<void> cleanAssets(List<String> options) async {
    final verbose = options.contains('--verbose');

    print('🧹 清理临时文件和缓存...');

    final tempDirs = [
      '.dart_tool/build',
      'build',
      '.flutter-plugins-dependencies',
    ];

    int cleanedFiles = 0;

    for (final dirPath in tempDirs) {
      final dir = Directory(dirPath);
      if (await dir.exists()) {
        if (verbose) {
          print('清理目录: $dirPath');
        }
        await dir.delete(recursive: true);
        cleanedFiles++;
      }
    }

    print('✅ 清理完成，删除了 $cleanedFiles 个目录');
  }

  /// 查找所有资源文件
  Future<List<File>> _findAssetFiles() async {
    final assetsDirectory = Directory(assetsDir);
    if (!await assetsDirectory.exists()) {
      throw Exception('资源目录不存在: $assetsDir');
    }

    final files = <File>[];
    await for (final entity in assetsDirectory.list(recursive: true)) {
      if (entity is File) {
        files.add(entity);
      }
    }

    return files;
  }

  /// 查找图片文件
  Future<List<File>> _findImageFiles() async {
    final allFiles = await _findAssetFiles();
    return allFiles.where((file) => _isImageFile(file.path)).toList();
  }

  /// 验证单个资源文件
  Future<ValidationResult> _validateAssetFile(File file) async {
    try {
      final stat = await file.stat();
      final size = stat.size;

      // 检查文件大小
      if (size == 0) {
        return ValidationResult(
          isValid: false,
          message: '文件为空',
          filePath: file.path,
        );
      }

      // 检查文件类型特定的验证
      if (_isImageFile(file.path)) {
        return await _validateImageFile(file);
      } else if (_isFontFile(file.path)) {
        return await _validateFontFile(file);
      }

      return ValidationResult(
        isValid: true,
        message: '文件有效',
        filePath: file.path,
      );
    } catch (e) {
      return ValidationResult(
        isValid: false,
        message: '验证失败: $e',
        filePath: file.path,
      );
    }
  }

  /// 验证图片文件
  Future<ValidationResult> _validateImageFile(File file) async {
    final size = await file.length();

    // 检查图片大小限制 (5MB)
    if (size > 5 * 1024 * 1024) {
      return ValidationResult(
        isValid: false,
        message: '图片文件过大: ${_formatFileSize(size)}',
        filePath: file.path,
      );
    }

    return ValidationResult(
      isValid: true,
      message: '图片文件有效',
      filePath: file.path,
    );
  }

  /// 验证字体文件
  Future<ValidationResult> _validateFontFile(File file) async {
    final size = await file.length();

    // 检查字体大小限制 (2MB)
    if (size > 2 * 1024 * 1024) {
      return ValidationResult(
        isValid: false,
        message: '字体文件过大: ${_formatFileSize(size)}',
        filePath: file.path,
      );
    }

    return ValidationResult(
      isValid: true,
      message: '字体文件有效',
      filePath: file.path,
    );
  }

  /// 优化图片文件
  Future<bool> _optimizeImage(File file) async {
    // 这里可以集成图片压缩库
    // 例如使用 imagemagick 或其他图片处理工具
    return false; // 暂时返回 false
  }

  /// 生成资源清单
  Future<Map<String, dynamic>> _generateManifest(List<File> files) async {
    final manifest = <String, dynamic>{
      'version': '1.0.0',
      'generated_at': DateTime.now().toIso8601String(),
      'total_assets': files.length,
      'categories': <String, List<String>>{},
      'files': <Map<String, dynamic>>[],
    };

    for (final file in files) {
      final relativePath = path.relative(file.path);
      final category = _getAssetCategory(relativePath);
      final size = await file.length();
      final bytes = await file.readAsBytes();
      final hash = sha256.convert(bytes).toString();

      // 添加到类别
      if (!manifest['categories'].containsKey(category)) {
        manifest['categories'][category] = <String>[];
      }
      manifest['categories'][category].add(relativePath);

      // 添加文件信息
      manifest['files'].add({
        'path': relativePath,
        'category': category,
        'size': size,
        'hash': hash,
        'last_modified': (await file.lastModified()).toIso8601String(),
      });
    }

    return manifest;
  }

  /// 生成统计信息
  Future<Map<String, dynamic>> _generateStats() async {
    final files = await _findAssetFiles();
    final stats = <String, dynamic>{
      'total_files': files.length,
      'total_size': 0,
      'categories': <String, dynamic>{},
      'file_types': <String, dynamic>{},
      'size_distribution': <String, int>{},
    };

    for (final file in files) {
      final size = await file.length();
      final category = _getAssetCategory(file.path);
      final extension = path.extension(file.path).toLowerCase();

      stats['total_size'] += size;

      // 类别统计
      if (!stats['categories'].containsKey(category)) {
        stats['categories'][category] = {'count': 0, 'size': 0};
      }
      stats['categories'][category]['count']++;
      stats['categories'][category]['size'] += size;

      // 文件类型统计
      if (!stats['file_types'].containsKey(extension)) {
        stats['file_types'][extension] = {'count': 0, 'size': 0};
      }
      stats['file_types'][extension]['count']++;
      stats['file_types'][extension]['size'] += size;

      // 大小分布
      final sizeCategory = _getSizeCategory(size);
      stats['size_distribution'][sizeCategory] =
          (stats['size_distribution'][sizeCategory] ?? 0) + 1;
    }

    return stats;
  }

  /// 判断是否为图片文件
  bool _isImageFile(String filePath) {
    final extension = path.extension(filePath).toLowerCase();
    return [
      '.png',
      '.jpg',
      '.jpeg',
      '.gif',
      '.bmp',
      '.webp',
      '.svg',
    ].contains(extension);
  }

  /// 判断是否为字体文件
  bool _isFontFile(String filePath) {
    final extension = path.extension(filePath).toLowerCase();
    return ['.ttf', '.otf', '.woff', '.woff2'].contains(extension);
  }

  /// 获取资源类别
  String _getAssetCategory(String filePath) {
    final parts = filePath.split('/');
    if (parts.length >= 2 && parts[0] == 'assets') {
      return parts[1];
    }
    return 'unknown';
  }

  /// 获取文件大小类别
  String _getSizeCategory(int size) {
    if (size < 1024) return 'tiny';
    if (size < 10 * 1024) return 'small';
    if (size < 100 * 1024) return 'medium';
    if (size < 1024 * 1024) return 'large';
    return 'huge';
  }

  /// 格式化文件大小
  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  /// 以表格形式打印统计信息
  void _printStatsAsTable(Map<String, dynamic> stats) {
    print('📊 资源统计报告');
    print('=' * 50);
    print('总文件数: ${stats['total_files']}');
    print('总大小: ${_formatFileSize(stats['total_size'])}');
    print('');

    print('按类别统计:');
    final categories = stats['categories'] as Map<String, dynamic>;
    for (final entry in categories.entries) {
      final data = entry.value as Map<String, dynamic>;
      print(
        '  ${entry.key}: ${data['count']} 个文件, ${_formatFileSize(data['size'])}',
      );
    }
  }

  /// 以 CSV 格式打印统计信息
  void _printStatsAsCsv(Map<String, dynamic> stats) {
    print('category,count,size_bytes,size_formatted');
    final categories = stats['categories'] as Map<String, dynamic>;
    for (final entry in categories.entries) {
      final data = entry.value as Map<String, dynamic>;
      print(
        '${entry.key},${data['count']},${data['size']},${_formatFileSize(data['size'])}',
      );
    }
  }
}

/// 验证结果类
class ValidationResult {
  final bool isValid;
  final String message;
  final String? filePath;

  const ValidationResult({
    required this.isValid,
    required this.message,
    this.filePath,
  });
}
