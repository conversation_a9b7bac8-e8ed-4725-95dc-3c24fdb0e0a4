import 'package:financial_app_auth/src/domain/providers/auth_provider.dart';
import 'package:financial_app_auth/src/domain/repositories/auth_repository.dart';
import 'package:provider/provider.dart';
import 'package:provider/single_child_widget.dart'; // 导入 SingleChildWidget
import 'package:get_it/get_it.dart'; // 导入 GetIt

/// 认证模块的所有 Provider 列表。
/// 这个列表将在主应用中被 MultiProvider 集成。
// List<SingleChildWidget> authModuleProviders = [
//   ChangeNotifierProvider(create: (_) => GetIt.instance<AuthProvider>()),
//   // 如果认证模块还有其他 Provider，可以在这里添加
//   // ChangeNotifierProvider<SomeOtherAuthNotifier>(create: (_) => SomeOtherAuthNotifier()),
// ];
List<SingleChildWidget> authModuleProviders = [
  ChangeNotifierProvider<AuthProvider>(
    create: (context) => AuthProvider(
      authRepository:
          GetIt.instance<AuthRepository>(), // 通过 GetIt 获取 AuthRepository
    ),
  ),
  // 如果认证模块还有其他 Provider，可以在这里添加
  // ChangeNotifierProvider<SomeOtherAuthNotifier>(create: (_) => SomeOtherAuthNotifier()),
];
