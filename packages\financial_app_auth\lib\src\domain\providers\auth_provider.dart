import 'package:financial_app_auth/src/data/models/login_response_model.dart';
import 'package:financial_app_core/financial_app_core.dart';
import 'package:flutter/foundation.dart';
import 'package:financial_app_auth/src/domain/repositories/auth_repository.dart';
import 'package:get_it/get_it.dart'; // 导入 GetIt 以获取依赖
import 'package:event_bus/event_bus.dart'; // 导入 EventBus 类型

/// 用户认证状态提供者 (ChangeNotifier)
/// 管理用户的登录、登出状态以及相关信息。
class AuthProvider with ChangeNotifier {
  final AuthRepository _authRepository;
  final Logger _logger =
      GetIt.instance<Logger>(); // 直接通过 GetIt 获取 Logger，因为它是独立且优先注册的
  final EventBus _eventBus =
      GetIt.instance<EventBus>(); // 直接通过 GetIt 获取 EventBus，因为它是一个全局事件总线

  bool _isLoggedIn = false;
  String? _currentUserId; // 当前登录用户的ID
  bool _isLoading = false;

  bool get isLoggedIn => _isLoggedIn;
  String? get currentUserId => _currentUserId;
  bool get isLoading => _isLoading;
  void setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners(); // 通知监听者状态已改变
  }

  AuthProvider({required AuthRepository authRepository})
    : _authRepository = authRepository {
    // 初始化时检查登录状态
    _checkLoginStatus();
    // 订阅登出事件，以便在其他地方触发登出时更新状态
    _eventBus.on<UserLoggedOutEvent>().listen((event) {
      _logger.info('收到 UserLoggedOutEvent，更新 AuthProvider 状态。');
      _isLoggedIn = false;
      _currentUserId = null;
      notifyListeners(); // 通知所有监听者状态已更新
    });
    // 订阅登录事件
    _eventBus.on<UserLoggedInEvent>().listen((event) {
      _logger.info('收到 UserLoggedInEvent，更新 AuthProvider 状态。');
      _isLoggedIn = true;
      _currentUserId = event.userId;
      notifyListeners();
    });
  }

  /// 检查用户登录状态并更新 _isLoggedIn
  Future<void> _checkLoginStatus() async {
    _logger.debug('检查用户登录状态...');
    try {
      _isLoggedIn = await _authRepository.isAuthenticated();
      if (_isLoggedIn) {
        _currentUserId = await GetIt.instance<LocalStorageService>().getString(
          AppConstants.userIdKey,
        );
        _logger.info('用户已登录。UserID: $_currentUserId');
      } else {
        _logger.info('用户未登录。');
      }
    } catch (e, st) {
      _logger.error('检查登录状态失败: $e', e, st);
      _isLoggedIn = false; // 确保在出错时也设为未登录
    } finally {
      notifyListeners(); // 通知 UI 更新
    }
  }

  /// 模拟用户登录
  Future<void> login(String username, String password) async {
    _logger.info('AuthProvider 尝试登录...');
    try {
      // final loginResponse = await _authRepository.login(username, password);
      // 模拟一个登录响应
      final userModel = UserModel(id: '123', username: '测试用户');
      final loginResponse = LoginResponseModel(
        accessToken: '123',
        user: userModel,
      ); // 假设登录响应中包含用户信息
      _isLoggedIn = true;
      _currentUserId = loginResponse.user.id; // 假设登录响应中包含用户信息
      _logger.info('AuthProvider 登录成功，用户ID: $_currentUserId');
      notifyListeners(); // 通知 UI 状态更新
      _eventBus.fire(UserLoggedInEvent(loginResponse.user.id)); // 发布登录成功事件
    } catch (e, st) {
      _logger.error('AuthProvider 登录失败: $e', e, st);
      _isLoggedIn = false;
      _currentUserId = null;
      notifyListeners(); // 通知 UI 状态更新
      rethrow; // 重新抛出异常供 UI 层处理
    }
  }

  /// 模拟用户登出
  Future<void> logout() async {
    _logger.info('AuthProvider 尝试登出...');
    try {
      await _authRepository.logout();
      _isLoggedIn = false;
      _currentUserId = null;
      _logger.info('AuthProvider 登出成功。');
      notifyListeners(); // 通知 UI 状态更新
      _eventBus.fire(UserLoggedOutEvent()); // 发布登出事件
    } catch (e, st) {
      _logger.error('AuthProvider 登出失败: $e', e, st);
      rethrow; // 重新抛出异常
    }
  }
}
