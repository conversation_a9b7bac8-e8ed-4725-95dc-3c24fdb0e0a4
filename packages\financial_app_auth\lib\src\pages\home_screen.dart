import 'package:flutter/material.dart';
import 'package:financial_app_core/financial_app_core.dart';
import '../utils/auth_logger.dart';

/// 主页面
class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final _authLogger = AuthLogger();
  int _selectedIndex = 0;

  @override
  void initState() {
    super.initState();
    _authLogger.info('用户进入主页');
  }

  void _onItemTapped(int index) {
    setState(() {
      _selectedIndex = index;
    });
    _authLogger.info('用户切换到标签页: $index');
  }

  void _handleLogout() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认退出'),
        content: const Text('您确定要退出登录吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              _authLogger.info('用户退出登录');
              Navigator.of(context).pop();
              Navigator.of(context).pushReplacementNamed('/login');
            },
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('金融应用'),
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.notifications),
            onPressed: () {
              _authLogger.info('用户点击通知按钮');
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('通知功能开发中...')),
              );
            },
          ),
          IconButton(
            icon: const Icon(Icons.logout),
            onPressed: _handleLogout,
          ),
        ],
      ),
      body: IndexedStack(
        index: _selectedIndex,
        children: const [
          _DashboardTab(),
          _MarketTab(),
          _TradeTab(),
          _PortfolioTab(),
          _ProfileTab(),
        ],
      ),
      bottomNavigationBar: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        currentIndex: _selectedIndex,
        onTap: _onItemTapped,
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.dashboard),
            label: '首页',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.trending_up),
            label: '行情',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.swap_horiz),
            label: '交易',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.pie_chart),
            label: '投资组合',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.person),
            label: '我的',
          ),
        ],
      ),
    );
  }
}

/// 首页标签页
class _DashboardTab extends StatelessWidget {
  const _DashboardTab();

  @override
  Widget build(BuildContext context) {
    return const Padding(
      padding: EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '欢迎回来！',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          SizedBox(height: 16),
          Card(
            child: Padding(
              padding: EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('账户总资产', style: TextStyle(fontSize: 16)),
                  SizedBox(height: 8),
                  Text(
                    '¥ 100,000.00',
                    style: TextStyle(fontSize: 32, fontWeight: FontWeight.bold),
                  ),
                  SizedBox(height: 8),
                  Text(
                    '今日收益: +1,234.56 (+1.23%)',
                    style: TextStyle(color: Colors.green),
                  ),
                ],
              ),
            ),
          ),
          SizedBox(height: 16),
          Text(
            '快捷操作',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _QuickActionCard(
                icon: Icons.add,
                title: '买入',
                color: Colors.green,
              ),
              _QuickActionCard(
                icon: Icons.remove,
                title: '卖出',
                color: Colors.red,
              ),
              _QuickActionCard(
                icon: Icons.account_balance_wallet,
                title: '充值',
                color: Colors.blue,
              ),
              _QuickActionCard(
                icon: Icons.money_off,
                title: '提现',
                color: Colors.orange,
              ),
            ],
          ),
        ],
      ),
    );
  }
}

/// 行情标签页
class _MarketTab extends StatelessWidget {
  const _MarketTab();

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: Text(
        '行情模块开发中...',
        style: TextStyle(fontSize: 18),
      ),
    );
  }
}

/// 交易标签页
class _TradeTab extends StatelessWidget {
  const _TradeTab();

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: Text(
        '交易模块开发中...',
        style: TextStyle(fontSize: 18),
      ),
    );
  }
}

/// 投资组合标签页
class _PortfolioTab extends StatelessWidget {
  const _PortfolioTab();

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: Text(
        '投资组合模块开发中...',
        style: TextStyle(fontSize: 18),
      ),
    );
  }
}

/// 个人中心标签页
class _ProfileTab extends StatelessWidget {
  const _ProfileTab();

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: Text(
        '个人中心模块开发中...',
        style: TextStyle(fontSize: 18),
      ),
    );
  }
}

/// 快捷操作卡片
class _QuickActionCard extends StatelessWidget {
  final IconData icon;
  final String title;
  final Color color;

  const _QuickActionCard({
    required this.icon,
    required this.title,
    required this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      child: InkWell(
        onTap: () {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('$title 功能开发中...')),
          );
        },
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Icon(icon, size: 32, color: color),
              const SizedBox(height: 8),
              Text(title),
            ],
          ),
        ),
      ),
    );
  }
}
