import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:financial_app_auth/src/domain/providers/auth_provider.dart';
import 'package:financial_app_core/financial_app_core.dart'; // For Logger

/// 主页
class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final Logger _logger = GetIt.instance<Logger>();

  @override
  void initState() {
    super.initState();
    _logger.info('HomeScreen: 初始化。');
    // 在主页加载时，检查认证状态，如果未登录则强制跳转到登录页
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      if (!authProvider.isLoggedIn) {
        _logger.warning('HomeScreen: 用户未登录，重定向到登录页。');
        context.go('/login');
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('金融交易应用'),
        actions: [
          Consumer<AuthProvider>(
            builder: (context, authProvider, child) {
              if (authProvider.isLoggedIn) {
                return TextButton.icon(
                  onPressed: () async {
                    _logger.debug('HomeScreen: 用户点击登出。');
                    await authProvider.logout();
                    ScaffoldMessenger.of(
                      context,
                    ).showSnackBar(const SnackBar(content: Text('已登出。')));
                    context.go('/login'); // 登出后跳转到登录页
                  },
                  icon: const Icon(Icons.logout, color: Colors.white),
                  label: const Text(
                    '退出登录',
                    style: TextStyle(color: Colors.white),
                  ),
                );
              } else {
                return TextButton(
                  onPressed: () {
                    _logger.debug('HomeScreen: 用户点击登录。');
                    context.go('/login'); // 未登录时显示登录按钮
                  },
                  child: const Text(
                    '登录',
                    style: TextStyle(color: Colors.white),
                  ),
                );
              }
            },
          ),
        ],
      ),
      body: Center(
        child: Consumer<AuthProvider>(
          builder: (context, authProvider, child) {
            if (authProvider.isLoggedIn) {
              return Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    '欢迎回来, 用户ID: ${authProvider.currentUserId ?? '未知'}!',
                    style: const TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 20),
                  const Text(
                    '您已成功进入应用主页。',
                    style: TextStyle(fontSize: 18, color: Colors.white70),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 30),
                  ElevatedButton(
                    onPressed: () {
                      _logger.debug('HomeScreen: 点击了查看资料按钮。');
                      // 示例：导航到用户资料页面
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(content: Text('查看用户资料功能待实现')),
                      );
                    },
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 30,
                        vertical: 15,
                      ),
                    ),
                    child: const Text('查看我的资料', style: TextStyle(fontSize: 16)),
                  ),
                ],
              );
            } else {
              return const Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    '请登录以访问应用功能。',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  SizedBox(height: 20),
                  ElevatedButton(
                    onPressed: null, // 登录按钮已在 AppBar Actions 中，这里可以禁用或移除
                    child: Text('前往登录', style: TextStyle(fontSize: 16)),
                  ),
                ],
              );
            }
          },
        ),
      ),
    );
  }
}
