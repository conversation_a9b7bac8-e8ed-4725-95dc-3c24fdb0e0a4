import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:provider/provider.dart';
import 'package:financial_app_auth/src/domain/providers/auth_provider.dart';
import 'package:financial_app_core/financial_app_core.dart'; // 用于 ErrorDialog
import 'package:go_router/go_router.dart'; // 假设主应用使用了 GoRouter

/// 登录页面
class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final TextEditingController _usernameController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  final Logger _logger = GetIt.instance<Logger>();

  @override
  void dispose() {
    _usernameController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  void _performLogin() async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    try {
      _logger.debug('LoginScreen: 执行登录操作...');
      await authProvider.login(
        _usernameController.text,
        _passwordController.text,
      );

      // 登录成功后，导航到主页
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('登录成功！')));
      context.go('/'); // 使用 GoRouter 导航到主页
    } on AppException catch (e) {
      _logger.error('LoginScreen: 登录失败: ${e.message}');
      ErrorDialog.show(context, e.message, title: '登录失败');
    } catch (e) {
      _logger.error('LoginScreen: 登录时发生未知错误: $e');
      ErrorDialog.show(context, '发生未知错误：$e', title: '登录失败');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('用户登录')),
      body: Consumer<AuthProvider>(
        // 使用 Consumer 监听 AuthProvider 的变化
        builder: (context, authProvider, child) {
          // 如果已登录，显示已登录状态并提供登出按钮（主要用于演示，实际可能直接跳转）
          if (authProvider.isLoggedIn) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    '您已登录，用户ID: ${authProvider.currentUserId}！',
                    style: const TextStyle(fontSize: 20, color: Colors.white),
                  ),
                  const SizedBox(height: 20),
                  ElevatedButton(
                    onPressed: () async {
                      _logger.debug('LoginScreen: 执行登出操作...');
                      await authProvider.logout();
                      ScaffoldMessenger.of(
                        context,
                      ).showSnackBar(const SnackBar(content: Text('您已登出！')));
                      context.go('/login'); // 登出后返回登录页
                    },
                    child: const Text('登出'),
                  ),
                ],
              ),
            );
          }
          // 未登录时，显示登录表单
          return Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                TextField(
                  controller: _usernameController,
                  decoration: const InputDecoration(labelText: '用户名'),
                  style: const TextStyle(color: Colors.white),
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: _passwordController,
                  decoration: const InputDecoration(labelText: '密码'),
                  obscureText: true,
                  style: const TextStyle(color: Colors.white),
                ),
                const SizedBox(height: 24),
                ElevatedButton(
                  onPressed: _performLogin,
                  child:
                      authProvider
                          .isLoading // 假设 AuthProvider 有一个 isLoading 状态
                      ? const CircularProgressIndicator(color: Colors.white)
                      : const Text('登录'),
                ),
                const SizedBox(height: 16),
                TextButton(
                  onPressed: () {
                    // 导航到注册页面（如果存在）
                    _logger.debug('LoginScreen: 导航到注册页面...');
                    // context.go('/register'); // 假设有注册路由
                    ScaffoldMessenger.of(
                      context,
                    ).showSnackBar(const SnackBar(content: Text('注册功能待实现')));
                  },
                  child: const Text(
                    '没有账号？去注册',
                    style: TextStyle(color: Color(0xFF18BC9C)),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
