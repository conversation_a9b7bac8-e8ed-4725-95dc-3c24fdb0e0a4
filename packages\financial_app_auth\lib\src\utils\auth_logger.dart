import 'package:financial_app_core/financial_app_core.dart';

/// 认证模块专用日志管理器
/// 
/// 提供认证模块特定的日志记录功能，简化日志调用
class AuthLogger extends ModuleLogger {
  static final AuthLogger _instance = AuthLogger._internal();
  factory AuthLogger() => _instance;
  AuthLogger._internal();

  @override
  String get moduleName => 'Auth';

  // ==================== 认证特定的日志方法 ====================

  /// 用户登录日志
  void logLogin({
    required String username,
    required bool success,
    String? reason,
    String? sessionId,
    Duration? duration,
  }) {
    final metadata = {
      'username': username,
      'success': success,
      'session_id': sessionId,
      if (reason != null) 'reason': reason,
      if (duration != null) 'login_duration_ms': duration.inMilliseconds,
      'timestamp': DateTime.now().toIso8601String(),
    };

    if (success) {
      info('用户登录成功', metadata: metadata);
      logBusinessEvent('user_login_success', metadata);
    } else {
      warning('用户登录失败', metadata: metadata);
      logBusinessEvent('user_login_failed', metadata);
    }
  }

  /// 用户登出日志
  void logLogout({
    required String username,
    String? sessionId,
    Duration? sessionDuration,
  }) {
    final metadata = {
      'username': username,
      'session_id': sessionId,
      if (sessionDuration != null) 'session_duration_ms': sessionDuration.inMilliseconds,
      'timestamp': DateTime.now().toIso8601String(),
    };

    info('用户登出', metadata: metadata);
    logBusinessEvent('user_logout', metadata);
  }

  /// 用户注册日志
  void logRegistration({
    required String username,
    required bool success,
    String? reason,
    String? registrationMethod,
  }) {
    final metadata = {
      'username': username,
      'success': success,
      'registration_method': registrationMethod ?? 'email',
      if (reason != null) 'reason': reason,
      'timestamp': DateTime.now().toIso8601String(),
    };

    if (success) {
      info('用户注册成功', metadata: metadata);
      logBusinessEvent('user_registration_success', metadata);
    } else {
      warning('用户注册失败', metadata: metadata);
      logBusinessEvent('user_registration_failed', metadata);
    }
  }

  /// Token 刷新日志
  void logTokenRefresh({
    required bool success,
    String? reason,
    Duration? tokenLifetime,
  }) {
    final metadata = {
      'success': success,
      if (reason != null) 'reason': reason,
      if (tokenLifetime != null) 'token_lifetime_ms': tokenLifetime.inMilliseconds,
      'timestamp': DateTime.now().toIso8601String(),
    };

    if (success) {
      debug('Token 刷新成功', metadata: metadata);
    } else {
      warning('Token 刷新失败', metadata: metadata);
    }
  }

  /// 密码重置日志
  void logPasswordReset({
    required String username,
    required String step, // 'request', 'verify', 'complete'
    required bool success,
    String? reason,
  }) {
    final metadata = {
      'username': username,
      'step': step,
      'success': success,
      if (reason != null) 'reason': reason,
      'timestamp': DateTime.now().toIso8601String(),
    };

    if (success) {
      info('密码重置$step成功', metadata: metadata);
    } else {
      warning('密码重置$step失败', metadata: metadata);
    }

    logBusinessEvent('password_reset_$step', metadata);
  }

  /// 生物识别认证日志
  void logBiometricAuth({
    required String type, // 'fingerprint', 'face', 'voice'
    required bool success,
    String? reason,
  }) {
    final metadata = {
      'biometric_type': type,
      'success': success,
      if (reason != null) 'reason': reason,
      'timestamp': DateTime.now().toIso8601String(),
    };

    if (success) {
      info('生物识别认证成功', metadata: metadata);
    } else {
      warning('生物识别认证失败', metadata: metadata);
    }

    logBusinessEvent('biometric_auth', metadata);
  }

  /// 会话管理日志
  void logSessionEvent({
    required String event, // 'created', 'expired', 'invalidated'
    String? sessionId,
    Duration? sessionDuration,
    String? reason,
  }) {
    final metadata = {
      'event': event,
      'session_id': sessionId,
      if (sessionDuration != null) 'session_duration_ms': sessionDuration.inMilliseconds,
      if (reason != null) 'reason': reason,
      'timestamp': DateTime.now().toIso8601String(),
    };

    switch (event) {
      case 'created':
        debug('会话创建', metadata: metadata);
        break;
      case 'expired':
        info('会话过期', metadata: metadata);
        break;
      case 'invalidated':
        warning('会话失效', metadata: metadata);
        break;
      default:
        info('会话事件: $event', metadata: metadata);
    }

    logBusinessEvent('session_$event', metadata);
  }

  /// 权限检查日志
  void logPermissionCheck({
    required String permission,
    required bool granted,
    String? userId,
    String? resource,
  }) {
    final metadata = {
      'permission': permission,
      'granted': granted,
      'user_id': userId,
      'resource': resource,
      'timestamp': DateTime.now().toIso8601String(),
    };

    if (granted) {
      debug('权限检查通过: $permission', metadata: metadata);
    } else {
      warning('权限检查失败: $permission', metadata: metadata);
    }
  }

  /// 安全事件日志
  void logSecurityEvent({
    required String eventType, // 'suspicious_login', 'multiple_failures', 'account_locked'
    String? userId,
    String? details,
    Map<String, dynamic>? additionalData,
  }) {
    final metadata = {
      'event_type': eventType,
      'user_id': userId,
      'details': details,
      'timestamp': DateTime.now().toIso8601String(),
      ...?additionalData,
    };

    warning('安全事件: $eventType', metadata: metadata);
    logBusinessEvent('security_event', metadata);
  }

  /// API 认证日志
  void logApiAuth({
    required String endpoint,
    required String method,
    required bool authenticated,
    String? reason,
    int? statusCode,
  }) {
    final metadata = {
      'endpoint': endpoint,
      'method': method,
      'authenticated': authenticated,
      'status_code': statusCode,
      if (reason != null) 'reason': reason,
      'timestamp': DateTime.now().toIso8601String(),
    };

    if (authenticated) {
      debug('API 认证成功: $method $endpoint', metadata: metadata);
    } else {
      warning('API 认证失败: $method $endpoint', metadata: metadata);
    }
  }

  /// 用户状态变更日志
  void logUserStatusChange({
    required String userId,
    required String fromStatus,
    required String toStatus,
    String? reason,
  }) {
    final metadata = {
      'user_id': userId,
      'from_status': fromStatus,
      'to_status': toStatus,
      'reason': reason,
      'timestamp': DateTime.now().toIso8601String(),
    };

    info('用户状态变更: $fromStatus -> $toStatus', metadata: metadata);
    logBusinessEvent('user_status_change', metadata);
  }

  /// 认证配置变更日志
  void logConfigChange({
    required String configKey,
    required String operation, // 'create', 'update', 'delete'
    String? oldValue,
    String? newValue,
  }) {
    final metadata = {
      'config_key': configKey,
      'operation': operation,
      'old_value': oldValue,
      'new_value': newValue,
      'timestamp': DateTime.now().toIso8601String(),
    };

    info('认证配置变更: $configKey', metadata: metadata);
    logBusinessEvent('auth_config_change', metadata);
  }
}

/// 认证模块日志工具类
/// 提供静态方法，方便在整个认证模块中使用
class AuthLoggerUtils {
  static final AuthLogger _logger = AuthLogger();

  // 提供静态访问方法
  static void debug(String message, {Map<String, dynamic>? metadata}) =>
      _logger.debug(message, metadata: metadata);

  static void info(String message, {Map<String, dynamic>? metadata}) =>
      _logger.info(message, metadata: metadata);

  static void warning(String message, {Map<String, dynamic>? metadata}) =>
      _logger.warning(message, metadata: metadata);

  static void error(String message, {
    dynamic error,
    StackTrace? stackTrace,
    Map<String, dynamic>? metadata,
  }) => _logger.error(message, error: error, stackTrace: stackTrace, metadata: metadata);

  // 认证特定方法的静态访问
  static void logLogin({
    required String username,
    required bool success,
    String? reason,
    String? sessionId,
    Duration? duration,
  }) => _logger.logLogin(
    username: username,
    success: success,
    reason: reason,
    sessionId: sessionId,
    duration: duration,
  );

  static void logLogout({
    required String username,
    String? sessionId,
    Duration? sessionDuration,
  }) => _logger.logLogout(
    username: username,
    sessionId: sessionId,
    sessionDuration: sessionDuration,
  );

  static void logTokenRefresh({
    required bool success,
    String? reason,
    Duration? tokenLifetime,
  }) => _logger.logTokenRefresh(
    success: success,
    reason: reason,
    tokenLifetime: tokenLifetime,
  );

  static void logSecurityEvent({
    required String eventType,
    String? userId,
    String? details,
    Map<String, dynamic>? additionalData,
  }) => _logger.logSecurityEvent(
    eventType: eventType,
    userId: userId,
    details: details,
    additionalData: additionalData,
  );
}
