# API 请求日志增强功能总结

## 概述

本次更新为项目的 API 请求功能添加了全面的日志记录能力，包括详细的请求信息、响应数据、错误处理和性能监控。所有敏感信息都会被自动过滤，确保日志安全。

## 主要改进

### 1. 增强的请求日志记录 🚀

**文件**: `api_service_impl.dart` 和 `optimized_api_service.dart`

**新增功能**:
- 详细记录 HTTP 方法、完整 URL、请求路径
- 记录查询参数和请求头信息
- 显示请求体数据（已过滤敏感信息）
- 记录超时配置（连接、接收、发送超时）
- 添加请求开始时间戳用于性能分析

**日志示例**:
```
[Market] 🚀 API请求开始
{
  "method": "GET",
  "url": "https://api.example.com/market/ticker/AAPL",
  "query_parameters": {"symbol": "AAPL"},
  "headers": {"Authorization": "***HIDDEN***"},
  "timeout": {"connect": 10000, "receive": 30000}
}
```

### 2. 详细的响应日志记录 ✅

**新增功能**:
- HTTP 状态码和状态消息
- 请求耗时统计（毫秒级精度）
- 响应头信息
- 响应数据内容（已过滤敏感信息）
- 数据大小统计
- 重定向信息检测

**日志示例**:
```
[Market] ✅ API请求成功
{
  "status_code": 200,
  "duration_ms": 245,
  "response_data": {"symbol": "AAPL", "price": 150.25},
  "data_size": 156
}
```

### 3. 全面的错误日志记录 ❌

**新增功能**:
- 详细的错误类型和错误消息
- 请求耗时（即使失败也记录）
- 错误响应数据
- 完整的堆栈跟踪信息
- 请求上下文信息

**日志示例**:
```
[Auth] ❌ API请求失败
{
  "error_type": "DioExceptionType.badResponse",
  "status_code": 401,
  "duration_ms": 123,
  "error_message": "Http status error [401]"
}
```

### 4. 智能重试日志记录 🔄 (仅优化版)

**新增功能**:
- 重试次数和最大重试次数
- 指数退避延迟时间
- 重试成功/失败状态
- 重试原因和错误信息

**日志示例**:
```
[Market] 🔄 API请求重试
{
  "retry_count": 1,
  "max_retries": 3,
  "delay_ms": 1000,
  "error_type": "DioExceptionType.connectionTimeout"
}
```

### 5. 敏感信息自动过滤 🔒

**过滤的敏感字段**:
- **请求头**: authorization, cookie, x-api-key, bearer, token, password, secret
- **请求/响应数据**: password, token, secret, key, pin, otp, cvv, ssn, credit_card, bank_account, refresh_token, access_token

**过滤效果**:
```json
{
  "username": "user123",
  "password": "***HIDDEN***",
  "api_token": "***HIDDEN***"
}
```

### 6. 数据截断和优化 📏

**截断规则**:
- 字符串数据超过 500 字符自动截断
- 列表数据超过 10 个元素显示前 10 个
- Map 数据超过 1000 字符添加截断标记
- 请求数据超过 100 字符的字符串会被截断

### 7. 性能监控集成 📊

**性能指标**:
- 请求耗时统计（毫秒）
- 数据大小计算
- 重试次数统计
- 请求去重检测

## 文件修改清单

### 核心文件
1. **`api_service_impl.dart`** - 基础 API 服务日志增强
2. **`optimized_api_service.dart`** - 优化版 API 服务日志增强

### 新增文件
3. **`API_LOGGING_GUIDE.md`** - 详细使用指南
4. **`api_logging_example.dart`** - 使用示例代码
5. **`API_LOGGING_ENHANCEMENT_SUMMARY.md`** - 本总结文档

## 使用方法

### 基本使用
```dart
// 创建 API 服务实例
final apiService = ApiServiceImpl(
  logger: Logger(),
  moduleName: 'YourModule',
);

// 发起请求，日志会自动记录
final response = await apiService.get('/api/data');
```

### 日志级别配置
```dart
// 切换到简洁模式（隐藏调试日志）
await LogConfigManager.switchToConciseMode();

// 切换到静默模式（只显示警告和错误）
await LogConfigManager.switchToQuietMode();
```

## 配置建议

### 开发环境
```dart
await AppLogger.initialize(config: LogConfig.development());
```
- 显示所有详细日志
- 包含调试信息和性能数据
- 便于问题排查和性能优化

### 生产环境
```dart
await AppLogger.initialize(config: LogConfig.production());
```
- 只记录警告和错误
- 启用远程日志上报
- 优化性能和存储空间

## 安全特性

1. **敏感信息过滤**: 自动识别并隐藏敏感数据
2. **数据截断**: 防止日志文件过大
3. **级别控制**: 可配置的日志输出级别
4. **安全存储**: 本地日志文件加密存储

## 性能影响

- **最小化开销**: 日志记录异步执行，不阻塞主线程
- **智能截断**: 自动限制日志数据大小
- **级别过滤**: 生产环境可关闭详细日志
- **内存优化**: 及时清理日志缓存

## 监控集成

日志数据可以集成到监控系统：
- **性能监控**: 请求耗时分析
- **错误监控**: 错误率统计
- **业务监控**: API 调用模式分析
- **安全监控**: 异常请求检测

## 后续扩展

1. **自定义过滤规则**: 支持业务特定的敏感信息过滤
2. **日志聚合**: 集成 ELK 或其他日志分析系统
3. **实时监控**: 支持实时日志流处理
4. **智能告警**: 基于日志模式的自动告警

## 总结

本次 API 日志增强为项目提供了：
- ✅ 全面的请求/响应日志记录
- ✅ 自动的敏感信息保护
- ✅ 详细的性能监控数据
- ✅ 灵活的配置选项
- ✅ 完整的错误追踪能力

这些改进将大大提升开发效率、问题排查能力和系统监控水平，为项目的稳定运行提供强有力的支持。
