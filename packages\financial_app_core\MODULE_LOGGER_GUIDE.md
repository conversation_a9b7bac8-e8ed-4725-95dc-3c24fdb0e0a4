# 模块化日志管理器使用指南

## 概述

模块化日志管理器为每个业务模块提供专用的日志记录功能，简化了日志调用方式，提高了代码的可维护性和可读性。

## 设计理念

### 🎯 核心优势
- **模块专用**：每个模块有自己的日志管理器
- **常量管理**：模块名称作为常量统一管理
- **简化调用**：无需每次指定模块名称
- **类型安全**：编译时检查，减少错误
- **功能丰富**：提供模块特定的日志方法

### 📊 架构设计
```
ModuleLogger (基类)
├── CoreLogger (核心模块)
├── AuthLogger (认证模块)
├── MarketLogger (市场数据模块)
├── MainLogger (主应用模块)
└── 自定义模块日志管理器...
```

## 快速开始

### 1. 基础使用

```dart
// 旧方式 - 需要每次指定模块名
AppLogger.logModule('Auth', LogLevel.info, '用户登录成功');

// 新方式 - 使用专用日志管理器
final authLogger = AuthLogger();
authLogger.info('用户登录成功');
```

### 2. 模块特定功能

```dart
// 认证模块专用方法
authLogger.logLogin(
  username: 'john_doe',
  success: true,
  sessionId: 'sess_12345',
);

// 市场数据模块专用方法
marketLogger.logStockDataFetch(
  symbols: ['AAPL', 'GOOGL'],
  success: true,
  duration: Duration(milliseconds: 150),
);
```

## 各模块日志管理器

### 🔧 CoreLogger - 核心模块

**用途**：核心服务、配置管理、基础设施

```dart
final coreLogger = CoreLogger();

// 基础日志
coreLogger.info('核心服务初始化完成');
coreLogger.debug('配置加载', metadata: {'count': 15});

// 性能监控
coreLogger.logPerformance('config_load', duration);

// 健康检查
coreLogger.logHealthCheck('database', true, 
  details: 'Connection healthy');
```

### 🔐 AuthLogger - 认证模块

**用途**：用户认证、权限管理、安全事件

```dart
final authLogger = AuthLogger();

// 用户登录
authLogger.logLogin(
  username: 'john_doe',
  success: true,
  sessionId: 'sess_12345',
  duration: Duration(milliseconds: 250),
);

// Token 管理
authLogger.logTokenRefresh(success: true);

// 安全事件
authLogger.logSecurityEvent(
  eventType: 'suspicious_login',
  userId: 'user_12345',
  details: 'Unusual location',
);

// 生物识别
authLogger.logBiometricAuth(
  type: 'fingerprint',
  success: true,
);
```

**专用方法列表**：
- `logLogin()` - 用户登录日志
- `logLogout()` - 用户登出日志
- `logRegistration()` - 用户注册日志
- `logTokenRefresh()` - Token 刷新日志
- `logPasswordReset()` - 密码重置日志
- `logBiometricAuth()` - 生物识别认证日志
- `logSecurityEvent()` - 安全事件日志
- `logPermissionCheck()` - 权限检查日志

### 📈 MarketLogger - 市场数据模块

**用途**：股票数据、实时行情、K线图表

```dart
final marketLogger = MarketLogger();

// 股票数据获取
marketLogger.logStockDataFetch(
  symbols: ['AAPL', 'GOOGL'],
  success: true,
  dataSource: 'primary',
  duration: Duration(milliseconds: 150),
);

// K线数据
marketLogger.logKlineDataFetch(
  symbol: 'AAPL',
  timeframe: '15min',
  success: true,
  dataPoints: 100,
);

// WebSocket 连接
marketLogger.logWebSocketEvent(
  event: 'connected',
  url: 'wss://api.example.com/ws',
);

// 实时订阅
marketLogger.logRealtimeSubscription(
  symbol: 'AAPL',
  action: 'subscribe',
  success: true,
);
```

**专用方法列表**：
- `logStockDataFetch()` - 股票数据获取日志
- `logKlineDataFetch()` - K线数据获取日志
- `logRealtimeSubscription()` - 实时行情订阅日志
- `logMarketDataUpdate()` - 市场数据更新日志
- `logDataSourceSwitch()` - 数据源切换日志
- `logWebSocketEvent()` - WebSocket 事件日志
- `logDataQualityCheck()` - 数据质量检查日志
- `logStockSearch()` - 股票搜索日志

### 🏠 MainLogger - 主应用模块

**用途**：应用生命周期、页面导航、UI 事件

```dart
final mainLogger = MainLogger();

// 应用启动
mainLogger.logAppStartup(
  success: true,
  startupDuration: Duration(milliseconds: 1200),
  initializedModules: ['Core', 'Auth', 'Market'],
);

// 页面导航
mainLogger.logNavigation(
  fromRoute: '/login',
  toRoute: '/dashboard',
  userId: 'user_12345',
);

// UI 事件
mainLogger.logUIEvent(
  eventType: 'button_click',
  elementId: 'refresh_portfolio',
  screenName: 'dashboard',
  userId: 'user_12345',
);
```

**专用方法列表**：
- `logAppStartup()` - 应用启动日志
- `logModuleInitialization()` - 模块初始化日志
- `logNavigation()` - 页面导航日志
- `logAppLifecycle()` - 应用生命周期日志
- `logUIEvent()` - UI 事件日志
- `logPerformanceMetrics()` - 性能指标日志
- `logFeatureFlag()` - 功能开关日志
- `logThemeChange()` - 主题切换日志

## 工具类简化调用

每个模块都提供了工具类，支持静态方法调用：

```dart
// 认证模块工具类
AuthLoggerUtils.logLogin(
  username: 'jane_doe',
  success: true,
);

AuthLoggerUtils.info('用户信息更新成功');

// 市场数据模块工具类
MarketLoggerUtils.logStockDataFetch(
  symbols: ['TSLA', 'NVDA'],
  success: true,
);

MarketLoggerUtils.debug('缓存命中');

// 主应用模块工具类
MainLoggerUtils.logNavigation(
  fromRoute: '/dashboard',
  toRoute: '/portfolio',
  userId: 'user_12345',
);
```

## 自定义模块日志管理器

### 创建自定义模块

```dart
// 1. 继承 ModuleLogger
class TradingLogger extends ModuleLogger {
  static final TradingLogger _instance = TradingLogger._internal();
  factory TradingLogger() => _instance;
  TradingLogger._internal();

  @override
  String get moduleName => 'Trading';

  // 2. 添加模块特定方法
  void logOrderPlaced({
    required String orderId,
    required String symbol,
    required int quantity,
    required double price,
  }) {
    final metadata = {
      'order_id': orderId,
      'symbol': symbol,
      'quantity': quantity,
      'price': price,
      'timestamp': DateTime.now().toIso8601String(),
    };

    info('订单创建成功', metadata: metadata);
    logBusinessEvent('order_placed', metadata);
  }
}

// 3. 使用自定义日志管理器
final tradingLogger = TradingLogger();
tradingLogger.logOrderPlaced(
  orderId: 'ORD-2024-001',
  symbol: 'AAPL',
  quantity: 100,
  price: 150.25,
);
```

### 使用工厂模式

```dart
// 动态创建模块日志管理器
final tradingLogger = LoggerFactory.getLogger('Trading');
final notificationLogger = LoggerFactory.getLogger('Notification');

// 注册自定义日志管理器
LoggerFactory.registerLogger('Trading', TradingLogger());
```

## 最佳实践

### 1. 模块命名规范

```dart
// 推荐的模块名称
'Core'          // 核心模块
'Auth'          // 认证模块
'Market'        // 市场数据模块
'Trading'       // 交易模块
'Portfolio'     // 投资组合模块
'Notification'  // 通知模块
'Settings'      // 设置模块
'Main'          // 主应用模块
```

### 2. 日志级别使用

```dart
// debug - 详细调试信息
authLogger.debug('Token 验证开始');

// info - 一般业务信息
authLogger.info('用户登录成功');

// warning - 警告信息
authLogger.warning('Token 即将过期');

// error - 错误信息
authLogger.error('登录失败', error: exception);

// fatal - 严重错误
authLogger.fatal('认证服务不可用', error: exception);
```

### 3. 元数据使用

```dart
// 结构化元数据
authLogger.info('用户操作', metadata: {
  'user_id': 'user_12345',
  'action': 'profile_update',
  'timestamp': DateTime.now().toIso8601String(),
  'ip_address': '*************',
  'user_agent': 'Mobile App v1.0',
});
```

### 4. 性能监控

```dart
// 使用 Stopwatch 测量性能
final stopwatch = Stopwatch()..start();

// 执行操作
await performOperation();

stopwatch.stop();

// 记录性能日志
marketLogger.logPerformance('data_fetch', stopwatch.elapsed, metadata: {
  'symbols_count': 50,
  'cache_hit_rate': 0.75,
});
```

### 5. 错误处理

```dart
try {
  await riskyOperation();
} catch (e, stackTrace) {
  marketLogger.error('操作失败', 
    error: e, 
    stackTrace: stackTrace,
    metadata: {
      'operation': 'data_fetch',
      'retry_count': 3,
      'will_retry': true,
    });
}
```

## 迁移指南

### 从旧方式迁移

```dart
// 旧方式
AppLogger.logModule('Auth', LogLevel.info, '用户登录成功');
AppLogger.logModule('Market', LogLevel.error, '数据获取失败', error: e);

// 新方式
AuthLoggerUtils.info('用户登录成功');
MarketLoggerUtils.error('数据获取失败', error: e);

// 或者使用实例
final authLogger = AuthLogger();
authLogger.info('用户登录成功');
```

### 批量替换建议

1. **按模块逐步迁移**：先迁移一个模块，测试无误后再迁移其他模块
2. **使用工具类**：优先使用 `*LoggerUtils` 静态方法，减少代码改动
3. **保留旧代码**：在迁移期间保留旧的日志调用作为备用
4. **团队培训**：确保团队成员了解新的日志调用方式

## 常见问题

### Q: 如何选择使用实例还是工具类？
A: 
- **实例方式**：适合需要频繁调用的场景，可以保存实例避免重复创建
- **工具类方式**：适合偶尔调用的场景，代码更简洁

### Q: 自定义模块日志管理器如何命名？
A: 建议使用 `{ModuleName}Logger` 格式，如 `TradingLogger`、`PortfolioLogger`

### Q: 如何处理模块间的日志关联？
A: 使用相同的 `correlation_id` 或 `session_id` 在元数据中关联不同模块的日志

### Q: 性能影响如何？
A: 模块化日志管理器是轻量级的，性能影响微乎其微。单例模式确保内存使用效率。

## 总结

模块化日志管理器提供了：

✅ **简化的调用方式** - 无需每次指定模块名称  
✅ **类型安全** - 编译时检查，减少错误  
✅ **模块特定功能** - 每个模块有专门的日志方法  
✅ **统一的接口** - 所有模块使用相同的基础接口  
✅ **易于扩展** - 可以轻松添加新的模块日志管理器  
✅ **向后兼容** - 与现有的 AppLogger 完全兼容  

通过使用模块化日志管理器，您的代码将更加清晰、可维护，并且具有更好的可读性。
