# financial_app_core 工具类总结

## 概述

已成功为 financial_app_core 模块添加了一系列常用的工具类，方便日常开发使用。这些工具类涵盖了日期处理、字符串操作、数字格式化、数据验证和通用格式化等常见需求。

## 新增工具类列表

### 1. DateUtils - 日期工具类
**文件位置**: `lib/src/utils/date_utils.dart`

**主要功能**:
- 日期格式化（支持多种格式，包括中文格式）
- 相对时间显示（如：刚刚、5分钟前、昨天等）
- 日期解析和转换
- 时间戳转换（支持毫秒和秒级）
- 时间戳直接转为指定格式字符串
- 日期判断（今天、昨天、本周、本月等）
- 工作日计算
- 日期范围获取（今天开始/结束、本周开始等）

**常用方法**:
```dart
DateUtils.formatDate(DateTime.now())                    // 2024-01-01
DateUtils.formatDateTime(DateTime.now())                // 2024-01-01 12:30:45
DateUtils.formatChineseDate(DateTime.now())             // 2024年01月01日
DateUtils.formatRelativeTime(twoHoursAgo)               // 2小时前
DateUtils.isToday(DateTime.now())                       // true
DateUtils.addBusinessDays(DateTime.now(), 5)            // 添加5个工作日

// 时间戳转换新功能
DateUtils.timestampToDateString(1640995200000)          // 2022-01-01
DateUtils.timestampToDateTimeString(1640995200000)      // 2022-01-01 08:00:00
DateUtils.timestampToChineseDateString(1640995200000)   // 2022年01月01日
DateUtils.timestampToRelativeTimeString(timestamp)      // 刚刚
DateUtils.timestampSecondsToDateString(1640995200)      // 2022-01-01 (秒级时间戳)
```

### 2. StringUtils - 字符串工具类
**文件位置**: `lib/src/utils/string_utils.dart`

**主要功能**:
- 字符串空值判断
- 敏感信息遮罩（手机号、身份证、银行卡、邮箱等）
- 字符串格式转换（驼峰、下划线等）
- 字符串处理（截取、反转、压缩空白等）
- 随机字符串生成
- Base64编码解码
- 字符串相似度计算
- HTML标签处理

**常用方法**:
```dart
StringUtils.maskPhone("***********")                    // 138****5678
StringUtils.maskEmail("<EMAIL>")               // u**<EMAIL>
StringUtils.maskIdCard("110101199001011234")            // 110101********1234
StringUtils.camelToSnake("userName")                    // user_name
StringUtils.snakeToCamel("user_name")                   // userName
StringUtils.truncate("很长的字符串", 10)                  // 很长的字符...
```

### 3. NumberUtils - 数字工具类
**文件位置**: `lib/src/utils/number_utils.dart`

**主要功能**:
- 数字格式化（千分位、小数位控制）
- 价格格式化
- 百分比格式化
- 大数字格式化（K、M、B等单位）
- 文件大小格式化
- 安全数学运算（避免除零错误）
- 数字范围判断和限制
- 统计计算（平均值、中位数、标准差）
- 金融计算（利息、复利、涨跌幅等）
- 数字转中文

**常用方法**:
```dart
NumberUtils.formatPrice(1234.56)                       // 1,234.5600 USDT
NumberUtils.formatLargeNumber(1500000)                 // 1.5M
NumberUtils.formatPercentage(0.15, showSign: true)     // +15.00%
NumberUtils.safeDivide(10, 0, defaultValue: -1)        // -1.0
NumberUtils.isInRange(50, 0, 100)                      // true
NumberUtils.average([1, 2, 3, 4, 5])                   // 3.0
```

### 4. ValidationUtils - 验证工具类
**文件位置**: `lib/src/utils/validation_utils.dart`

**主要功能**:
- 常用格式验证（手机号、邮箱、身份证、银行卡等）
- 密码强度验证
- 数字和范围验证
- 字符串长度验证
- 中文姓名验证
- 用户名验证
- URL和IP地址验证
- QQ号、微信号验证
- 车牌号验证

**常用方法**:
```dart
ValidationUtils.isValidPhone("***********")            // true
ValidationUtils.isValidEmail("<EMAIL>")       // true
ValidationUtils.isValidPassword("Abc123456")           // true
ValidationUtils.getPasswordStrength("Abc123456")       // 1 (中等)
ValidationUtils.isValidIdCard("110101199001011234")    // true
ValidationUtils.isValidRange("50", 0, 100)             // true
```

### 5. FormatUtils - 通用格式化工具类
**文件位置**: `lib/src/utils/format_utils.dart`

**主要功能**:
- 手机号格式化显示（带分隔符、遮罩）
- 银行卡号格式化显示
- 身份证号格式化显示
- 金额格式化显示
- 涨跌幅格式化显示
- 交易量格式化显示
- 时间范围格式化显示
- 进度格式化显示
- 地址格式化显示
- 评分、距离、温度等格式化显示
- 年龄和倒计时格式化显示

**常用方法**:
```dart
FormatUtils.formatPhoneDisplay("***********")          // 138 1234 5678
FormatUtils.formatAmountDisplay(1234.56)               // ¥1,234.56
FormatUtils.formatPriceChangeDisplay(5.2, 5.0)         // ***** (*****%)
FormatUtils.formatDistanceDisplay(1500)                // 1.5km
FormatUtils.formatProgressDisplay(75, 100)             // 75/100 (75.00%)
```

## 配置更新

### 1. 依赖添加
在 `pubspec.yaml` 中添加了 `intl: ^0.20.2` 依赖，用于日期格式化功能。

### 2. 常量扩展
在 `AppConstants` 中添加了日期格式、正则表达式、数字格式化等常量：
```dart
// 日期格式常量
static const String dateFormatYMD = 'yyyy-MM-dd';
static const String dateFormatYMDHMS = 'yyyy-MM-dd HH:mm:ss';

// 正则表达式常量
static const String phoneRegex = r'^1[3-9]\d{9}$';
static const String emailRegex = r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$';

// 数字格式化常量
static const int defaultDecimalPlaces = 2;
static const int priceDecimalPlaces = 4;
```

### 3. 导出配置
在 `financial_app_core.dart` 中添加了新工具类的导出：
```dart
export 'src/utils/date_utils.dart';
export 'src/utils/string_utils.dart';
export 'src/utils/number_utils.dart';
export 'src/utils/validation_utils.dart';
export 'src/utils/format_utils.dart';
```

## 测试验证

### 1. 单元测试
创建了完整的单元测试文件 `test/utils_test.dart`，覆盖所有工具类的主要功能。

### 2. 示例代码
提供了详细的使用示例：
- `example/utils_example.dart` - 完整的使用示例
- `example/simple_test.dart` - 简单的功能验证
- `lib/src/utils/README.md` - 详细的使用文档

### 3. 测试结果
所有测试均通过，工具类功能正常：
```
📝 StringUtils 测试: ✅
🔢 NumberUtils 测试: ✅  
✅ ValidationUtils 测试: ✅
```

## 使用方式

### 1. 导入方式
```dart
import 'package:financial_app_core/financial_app_core.dart';
```

### 2. 调用方式
所有工具类都是静态方法，无需实例化：
```dart
// 日期处理
String dateStr = DateUtils.formatDate(DateTime.now());

// 字符串处理
String maskedPhone = StringUtils.maskPhone("***********");

// 数字处理
String price = NumberUtils.formatPrice(1234.56);

// 数据验证
bool isValid = ValidationUtils.isValidPhone("***********");

// 格式化显示
String display = FormatUtils.formatAmountDisplay(1234.56);
```

## 特性优势

1. **功能全面**: 涵盖了日常开发中最常用的工具方法
2. **类型安全**: 所有方法都有完整的类型定义和空值保护
3. **性能优化**: 使用静态方法，避免不必要的实例化
4. **易于使用**: 统一的导入方式，清晰的方法命名
5. **文档完整**: 提供了详细的使用文档和示例代码
6. **测试覆盖**: 完整的单元测试确保代码质量

## 后续扩展

如需添加新的工具方法，建议：
1. 在对应的工具类中添加静态方法
2. 更新相应的测试用例
3. 在文档中添加使用示例
4. 保持代码风格的一致性

这些工具类将大大提高 financial_app_core 模块的实用性，为整个项目的开发提供强有力的基础支持。
