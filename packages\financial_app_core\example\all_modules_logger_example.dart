import 'package:financial_app_core/financial_app_core.dart';

/// 所有业务模块日志管理器使用示例
/// 
/// 展示如何在各个业务模块中使用专用的日志管理器
/// 注意：在实际项目中，您需要导入相应的模块包
class AllModulesLoggerExample {
  
  /// 初始化示例
  static Future<void> initializeExample() async {
    print('🚀 初始化企业级日志系统...');
    
    // 初始化日志系统
    await AppLogger.initialize(config: LogConfig.development());
    
    final coreLogger = CoreLogger();
    coreLogger.info('所有模块日志系统初始化完成');
  }
  
  /// 认证模块日志示例
  static void authModuleExample() {
    print('\n🔐 认证模块日志示例:');
    print('在实际项目中的用法：');
    print('');
    print('import \'package:financial_app_auth/financial_app_auth.dart\';');
    print('');
    print('// 基础日志');
    print('AuthLoggerUtils.info(\'用户登录成功\');');
    print('AuthLoggerUtils.warning(\'密码即将过期\');');
    print('');
    print('// 专用方法');
    print('AuthLoggerUtils.logLogin(');
    print('  username: \'john_doe\',');
    print('  success: true,');
    print('  sessionId: \'sess_12345\',');
    print(');');
    print('');
    print('AuthLoggerUtils.logSecurityEvent(');
    print('  eventType: \'suspicious_login\',');
    print('  userId: \'user_12345\',');
    print('  details: \'Login from unusual location\',');
    print(');');
    
    // 使用核心模块演示
    final coreLogger = CoreLogger();
    coreLogger.info('认证模块示例完成');
  }
  
  /// 市场数据模块日志示例
  static void marketModuleExample() {
    print('\n📈 市场数据模块日志示例:');
    print('在实际项目中的用法：');
    print('');
    print('import \'package:financial_app_market/financial_app_market.dart\';');
    print('');
    print('// 基础日志');
    print('MarketLoggerUtils.info(\'市场数据更新完成\');');
    print('MarketLoggerUtils.debug(\'缓存命中\');');
    print('');
    print('// 专用方法');
    print('MarketLoggerUtils.logStockDataFetch(');
    print('  symbols: [\'AAPL\', \'GOOGL\'],');
    print('  success: true,');
    print('  duration: Duration(milliseconds: 150),');
    print(');');
    print('');
    print('MarketLoggerUtils.logWebSocketEvent(');
    print('  event: \'connected\',');
    print('  url: \'wss://api.example.com/ws\',');
    print(');');
    
    final coreLogger = CoreLogger();
    coreLogger.info('市场数据模块示例完成');
  }
  
  /// 交易模块日志示例
  static void tradeModuleExample() {
    print('\n💰 交易模块日志示例:');
    print('在实际项目中的用法：');
    print('');
    print('import \'package:financial_app_trade/financial_app_trade.dart\';');
    print('');
    print('// 基础日志');
    print('TradeLoggerUtils.info(\'订单处理完成\');');
    print('TradeLoggerUtils.error(\'订单执行失败\', error: exception);');
    print('');
    print('// 专用方法');
    print('TradeLoggerUtils.logOrderCreated(');
    print('  userId: \'user_12345\',');
    print('  orderId: \'ORD-2024-001\',');
    print('  symbol: \'AAPL\',');
    print('  orderType: \'market\',');
    print('  side: \'buy\',');
    print('  quantity: 100,');
    print('  success: true,');
    print(');');
    
    final coreLogger = CoreLogger();
    coreLogger.info('交易模块示例完成');
  }
  
  /// 投资组合模块日志示例
  static void portfolioModuleExample() {
    print('\n📊 投资组合模块日志示例:');
    print('在实际项目中的用法：');
    print('');
    print('import \'package:financial_app_portfolio/financial_app_portfolio.dart\';');
    print('');
    print('// 基础日志');
    print('PortfolioLoggerUtils.info(\'投资组合计算完成\');');
    print('PortfolioLoggerUtils.warning(\'风险水平较高\');');
    print('');
    print('// 专用方法');
    print('PortfolioLoggerUtils.logPortfolioCreated(');
    print('  userId: \'user_12345\',');
    print('  portfolioId: \'portfolio_001\',');
    print('  portfolioName: \'我的投资组合\',');
    print('  success: true,');
    print('  initialValue: 10000.0,');
    print(');');
    
    final coreLogger = CoreLogger();
    coreLogger.info('投资组合模块示例完成');
  }
  
  /// 通知模块日志示例
  static void notificationModuleExample() {
    print('\n🔔 通知模块日志示例:');
    print('在实际项目中的用法：');
    print('');
    print('import \'package:financial_app_notification/financial_app_notification.dart\';');
    print('');
    print('// 基础日志');
    print('NotificationLoggerUtils.info(\'通知发送成功\');');
    print('NotificationLoggerUtils.warning(\'通知权限被拒绝\');');
    print('');
    print('// 专用方法');
    print('NotificationLoggerUtils.logPushNotificationSent(');
    print('  userId: \'user_12345\',');
    print('  notificationType: \'price_alert\',');
    print('  success: true,');
    print('  title: \'AAPL价格提醒\',');
    print('  body: \'AAPL价格已达到150美元\',');
    print(');');
    
    final coreLogger = CoreLogger();
    coreLogger.info('通知模块示例完成');
  }
  
  /// WebSocket模块日志示例
  static void wsModuleExample() {
    print('\n🌐 WebSocket模块日志示例:');
    print('在实际项目中的用法：');
    print('');
    print('import \'package:financial_ws_client/financial_ws_client.dart\';');
    print('');
    print('// 基础日志');
    print('WsLoggerUtils.info(\'WebSocket连接建立\');');
    print('WsLoggerUtils.warning(\'连接不稳定\');');
    print('');
    print('// 专用方法');
    print('WsLoggerUtils.logConnection(');
    print('  url: \'wss://api.example.com/ws\',');
    print('  action: \'connected\',');
    print('  success: true,');
    print('  connectionTime: Duration(milliseconds: 200),');
    print(');');
    
    final coreLogger = CoreLogger();
    coreLogger.info('WebSocket模块示例完成');
  }
  
  /// 图表模块日志示例
  static void chartModuleExample() {
    print('\n📈 图表模块日志示例:');
    print('在实际项目中的用法：');
    print('');
    print('import \'package:financial_trading_chart/financial_trading_chart.dart\';');
    print('');
    print('// 基础日志');
    print('ChartLoggerUtils.info(\'图表渲染完成\');');
    print('ChartLoggerUtils.debug(\'数据更新\');');
    print('');
    print('// 专用方法');
    print('ChartLoggerUtils.logChartInitialization(');
    print('  chartId: \'chart_001\',');
    print('  chartType: \'candlestick\',');
    print('  success: true,');
    print('  symbol: \'AAPL\',');
    print('  timeframe: \'15min\',');
    print(');');
    
    final coreLogger = CoreLogger();
    coreLogger.info('图表模块示例完成');
  }
  
  /// 资源模块日志示例
  static void assetsModuleExample() {
    print('\n🎨 资源模块日志示例:');
    print('在实际项目中的用法：');
    print('');
    print('import \'package:financial_app_assets/financial_app_assets.dart\';');
    print('');
    print('// 基础日志');
    print('AssetsLoggerUtils.info(\'资源加载完成\');');
    print('AssetsLoggerUtils.warning(\'资源缓存已满\');');
    print('');
    print('// 专用方法');
    print('AssetsLoggerUtils.logAssetLoading(');
    print('  assetPath: \'assets/images/logo.png\',');
    print('  assetType: \'image\',');
    print('  success: true,');
    print('  fileSize: 2048,');
    print('  cacheStatus: \'hit\',');
    print(');');
    
    final coreLogger = CoreLogger();
    coreLogger.info('资源模块示例完成');
  }
  
  /// 主应用模块日志示例
  static void mainModuleExample() {
    print('\n🏠 主应用模块日志示例:');
    print('在实际项目中的用法：');
    print('');
    print('import \'package:financial_app_main/financial_app_main.dart\';');
    print('');
    print('// 基础日志');
    print('MainLoggerUtils.info(\'应用启动完成\');');
    print('MainLoggerUtils.warning(\'内存使用率较高\');');
    print('');
    print('// 专用方法');
    print('MainLoggerUtils.logAppStartup(');
    print('  success: true,');
    print('  startupDuration: Duration(milliseconds: 1200),');
    print('  initializedModules: [\'Core\', \'Auth\', \'Market\'],');
    print(');');
    print('');
    print('MainLoggerUtils.logNavigation(');
    print('  fromRoute: \'/login\',');
    print('  toRoute: \'/dashboard\',');
    print('  userId: \'user_12345\',');
    print(');');
    
    final coreLogger = CoreLogger();
    coreLogger.info('主应用模块示例完成');
  }
  
  /// 统一的模块常量管理示例
  static void moduleConstantsExample() {
    print('\n🎯 模块常量管理示例:');
    print('每个模块都有自己的日志管理器，模块名称作为常量管理：');
    print('');
    print('// 认证模块');
    print('class AuthLogger extends ModuleLogger {');
    print('  @override');
    print('  String get moduleName => \'Auth\';  // 模块名称常量');
    print('}');
    print('');
    print('// 市场数据模块');
    print('class MarketLogger extends ModuleLogger {');
    print('  @override');
    print('  String get moduleName => \'Market\';  // 模块名称常量');
    print('}');
    print('');
    print('这样的设计优势：');
    print('✅ 模块名称统一管理，避免拼写错误');
    print('✅ 简化日志调用，无需每次指定模块名');
    print('✅ 类型安全，编译时检查');
    print('✅ 易于维护和扩展');
    
    final coreLogger = CoreLogger();
    coreLogger.info('模块常量管理示例完成');
  }
}

/// 运行所有模块日志示例
Future<void> main() async {
  print('🎯 所有业务模块日志管理器示例\n');
  
  // 初始化
  await AllModulesLoggerExample.initializeExample();
  
  // 运行各模块示例
  AllModulesLoggerExample.authModuleExample();
  AllModulesLoggerExample.marketModuleExample();
  AllModulesLoggerExample.tradeModuleExample();
  AllModulesLoggerExample.portfolioModuleExample();
  AllModulesLoggerExample.notificationModuleExample();
  AllModulesLoggerExample.wsModuleExample();
  AllModulesLoggerExample.chartModuleExample();
  AllModulesLoggerExample.assetsModuleExample();
  AllModulesLoggerExample.mainModuleExample();
  AllModulesLoggerExample.moduleConstantsExample();
  
  print('\n✅ 所有模块日志示例运行完成！');
  print('📊 每个模块都有自己的专用日志管理器');
  print('🔧 简化了日志调用，提高了代码可维护性');
  print('🎯 模块名称作为常量统一管理');
}
