import 'package:financial_app_core/financial_app_core.dart';

/// API 日志功能使用示例
///
/// 这个示例展示了如何使用增强的 API 日志功能
class ApiLoggingExample {
  /// 初始化示例
  static Future<void> initializeExample() async {
    print('🚀 初始化 API 日志示例...');

    // 初始化日志系统
    await AppLogger.initialize(config: LogConfig.development());

    AppLogger.logModule('ApiExample', LogLevel.info, 'API 日志示例初始化完成');
  }

  /// 基础 API 请求示例
  static Future<void> basicApiRequestExample() async {
    print('\n📝 基础 API 请求日志示例:');

    // 创建 API 服务实例
    final apiService = ApiServiceImpl(moduleName: 'ExampleModule');

    try {
      // 模拟 GET 请求
      print('\n--- GET 请求示例 ---');
      await _simulateGetRequest(apiService);

      // 模拟 POST 请求
      print('\n--- POST 请求示例 ---');
      await _simulatePostRequest(apiService);

      // 模拟带敏感数据的请求
      print('\n--- 敏感数据过滤示例 ---');
      await _simulateSensitiveDataRequest(apiService);
    } catch (e) {
      AppLogger.logModule('ApiExample', LogLevel.error, '示例执行失败', error: e);
    }
  }

  /// 优化版 API 服务示例
  static Future<void> optimizedApiServiceExample() async {
    print('\n🔧 优化版 API 服务日志示例:');

    // 创建优化版 API 服务实例
    final optimizedApiService = OptimizedApiService(
      moduleName: 'OptimizedModule',
    );

    try {
      // 模拟请求去重
      print('\n--- 请求去重示例 ---');
      await _simulateDuplicateRequests(optimizedApiService);

      // 模拟重试机制
      print('\n--- 重试机制示例 ---');
      await _simulateRetryRequest(optimizedApiService);
    } catch (e) {
      AppLogger.logModule('ApiExample', LogLevel.error, '优化版示例执行失败', error: e);
    } finally {
      // 清理资源
      optimizedApiService.dispose();
    }
  }

  /// 模拟 GET 请求
  static Future<void> _simulateGetRequest(ApiServiceImpl apiService) async {
    try {
      // 这里会触发详细的请求日志
      await apiService.get(
        '/market/ticker/AAPL',
        queryParameters: {'symbol': 'AAPL', 'interval': '1d', 'limit': 100},
        headers: {
          'X-API-Key': 'your-api-key-here',
          'User-Agent': 'FinancialApp/1.0.0',
        },
      );
    } catch (e) {
      // 错误会被自动记录
      print('GET 请求失败（这是预期的，因为这是模拟请求）');
    }
  }

  /// 模拟 POST 请求
  static Future<void> _simulatePostRequest(ApiServiceImpl apiService) async {
    try {
      // 这里会触发详细的请求日志
      await apiService.post(
        '/auth/login',
        data: {
          'username': 'testuser',
          'password': 'secretpassword123', // 这会被自动隐藏
          'remember_me': true,
        },
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      // 错误会被自动记录
      print('POST 请求失败（这是预期的，因为这是模拟请求）');
    }
  }

  /// 模拟包含敏感数据的请求
  static Future<void> _simulateSensitiveDataRequest(
    ApiServiceImpl apiService,
  ) async {
    try {
      await apiService.post(
        '/user/profile',
        data: {
          'user_id': '12345',
          'email': '<EMAIL>',
          'credit_card': '4111-1111-1111-1111', // 会被隐藏
          'ssn': '***********', // 会被隐藏
          'api_token': 'abc123def456', // 会被隐藏
          'public_info': '这是公开信息',
        },
        headers: {
          'Authorization': 'Bearer your-jwt-token-here', // 会被隐藏
          'X-API-Secret': 'secret-key', // 会被隐藏
        },
      );
    } catch (e) {
      print('敏感数据请求失败（这是预期的，因为这是模拟请求）');
    }
  }

  /// 模拟重复请求（优化版特有功能）
  static Future<void> _simulateDuplicateRequests(
    OptimizedApiService apiService,
  ) async {
    try {
      // 同时发起多个相同的请求，会触发去重日志
      final futures = List.generate(
        3,
        (index) =>
            apiService.get('/market/data', queryParameters: {'symbol': 'AAPL'}),
      );

      await Future.wait(futures);
    } catch (e) {
      print('重复请求处理完成（这是预期的，因为这是模拟请求）');
    }
  }

  /// 模拟需要重试的请求
  static Future<void> _simulateRetryRequest(
    OptimizedApiService apiService,
  ) async {
    try {
      // 这个请求会因为网络问题失败并触发重试机制
      await apiService.get('/unreliable-endpoint');
    } catch (e) {
      print('重试请求最终失败（这是预期的，因为这是模拟请求）');
    }
  }

  /// 日志配置示例
  static Future<void> logConfigurationExample() async {
    print('\n⚙️ 日志配置示例:');

    // 显示当前配置
    LogConfigManager.printConfigStatus();

    // 切换到简洁模式
    print('\n切换到简洁模式...');
    await LogConfigManager.switchToConciseMode();

    // 模拟一些日志输出
    AppLogger.logModule('ConfigExample', LogLevel.debug, '这条调试日志在简洁模式下不会显示');
    AppLogger.logModule('ConfigExample', LogLevel.info, '这条信息日志会显示');
    AppLogger.logModule('ConfigExample', LogLevel.warning, '这条警告日志会显示');

    // 切换到静默模式
    print('\n切换到静默模式...');
    await LogConfigManager.switchToQuietMode();

    // 模拟一些日志输出
    AppLogger.logModule('ConfigExample', LogLevel.info, '这条信息日志在静默模式下不会显示');
    AppLogger.logModule('ConfigExample', LogLevel.warning, '这条警告日志会显示');
    AppLogger.logModule('ConfigExample', LogLevel.error, '这条错误日志会显示');

    // 恢复到开发模式
    print('\n恢复到开发模式...');
    await LogConfigManager.switchToDevelopmentMode();

    AppLogger.logModule('ConfigExample', LogLevel.info, '已恢复到开发模式，所有日志都会显示');
  }

  /// 运行完整示例
  static Future<void> runFullExample() async {
    await initializeExample();
    await basicApiRequestExample();
    await optimizedApiServiceExample();
    await logConfigurationExample();

    print('\n✅ API 日志示例运行完成！');
    print('请查看控制台输出，观察不同类型的日志记录。');
  }
}

/// 示例运行入口
void main() async {
  await ApiLoggingExample.runFullExample();
}
