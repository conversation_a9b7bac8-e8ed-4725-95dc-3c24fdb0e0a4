import 'package:financial_app_core/financial_app_core.dart';

/// API 日志功能测试
/// 
/// 测试 metadata 是否能正确显示
class ApiLoggingTest {
  
  /// 测试 metadata 显示功能
  static Future<void> testMetadataDisplay() async {
    // 初始化日志系统
    await AppLogger.initialize(config: LogConfig.development());
    
    AppLogger.logModule('Test', LogLevel.info, '开始测试 metadata 显示功能');
    
    // 测试基础 metadata
    AppLogger.logModule(
      'Test',
      LogLevel.info,
      '基础 metadata 测试',
      metadata: {
        'user_id': '12345',
        'action': 'test_metadata',
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
    
    // 测试复杂 metadata
    AppLogger.logModule(
      'Test',
      LogLevel.info,
      '复杂 metadata 测试',
      metadata: {
        'request': {
          'method': 'POST',
          'url': 'https://api.example.com/test',
          'headers': {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer token123',
          },
          'body': {
            'username': 'testuser',
            'password': 'secret123',
          },
        },
        'response': {
          'status': 200,
          'duration_ms': 245,
          'data_size': 1024,
        },
        'performance': {
          'cpu_usage': 15.5,
          'memory_usage': 128.7,
        },
      },
    );
    
    // 测试结构化日志模式
    await AppLogger.initialize(
      config: LogConfig(
        structuredLogging: true,
        showModuleNames: true,
        showTimestamps: true,
      ),
    );
    
    AppLogger.logModule(
      'Test',
      LogLevel.info,
      '结构化日志 metadata 测试',
      metadata: {
        'api_call': {
          'endpoint': '/api/v1/users',
          'method': 'GET',
          'query_params': {
            'page': 1,
            'limit': 20,
            'sort': 'created_at',
          },
        },
        'performance_metrics': {
          'response_time_ms': 156,
          'cache_hit': true,
          'db_queries': 3,
        },
        'user_context': {
          'user_id': 'user_789',
          'session_id': 'sess_abc123',
          'ip_address': '*************',
        },
      },
    );
    
    AppLogger.logModule('Test', LogLevel.info, 'metadata 显示功能测试完成');
  }
  
  /// 测试 API 服务的 metadata 日志
  static Future<void> testApiServiceMetadata() async {
    await AppLogger.initialize(config: LogConfig.development());
    
    AppLogger.logModule('Test', LogLevel.info, '开始测试 API 服务 metadata 日志');
    
    // 创建 API 服务实例
    final apiService = ApiServiceImpl(moduleName: 'TestModule');
    
    try {
      // 这会触发详细的 metadata 日志
      await apiService.get(
        '/test/endpoint',
        queryParameters: {
          'param1': 'value1',
          'param2': 'value2',
          'timestamp': DateTime.now().millisecondsSinceEpoch,
        },
        headers: {
          'X-Test-Header': 'test-value',
          'Authorization': 'Bearer test-token',
        },
      );
    } catch (e) {
      // 预期会失败，因为这是模拟请求
      AppLogger.logModule('Test', LogLevel.info, 'API 请求失败（预期行为）', error: e);
    }
    
    // 测试 POST 请求的 metadata
    try {
      await apiService.post(
        '/test/create',
        data: {
          'name': 'Test User',
          'email': '<EMAIL>',
          'password': 'secret123',
          'metadata': {
            'source': 'mobile_app',
            'version': '1.0.0',
          },
        },
        queryParameters: {
          'validate': true,
          'send_email': false,
        },
      );
    } catch (e) {
      AppLogger.logModule('Test', LogLevel.info, 'POST 请求失败（预期行为）', error: e);
    }
    
    AppLogger.logModule('Test', LogLevel.info, 'API 服务 metadata 日志测试完成');
  }
  
  /// 测试优化版 API 服务的 metadata 日志
  static Future<void> testOptimizedApiServiceMetadata() async {
    await AppLogger.initialize(config: LogConfig.development());
    
    AppLogger.logModule('Test', LogLevel.info, '开始测试优化版 API 服务 metadata 日志');
    
    // 创建优化版 API 服务实例
    final optimizedApiService = OptimizedApiService(moduleName: 'OptimizedTestModule');
    
    try {
      // 这会触发详细的 metadata 日志，包括请求去重信息
      final futures = List.generate(2, (index) => 
        optimizedApiService.get(
          '/test/duplicate',
          queryParameters: {
            'test_param': 'duplicate_test',
            'request_id': index,
          },
        )
      );
      
      await Future.wait(futures);
    } catch (e) {
      AppLogger.logModule('Test', LogLevel.info, '重复请求测试完成（预期行为）', error: e);
    }
    
    // 清理资源
    optimizedApiService.dispose();
    
    AppLogger.logModule('Test', LogLevel.info, '优化版 API 服务 metadata 日志测试完成');
  }
  
  /// 运行所有测试
  static Future<void> runAllTests() async {
    AppLogger.logModule('Test', LogLevel.info, '🧪 开始运行 API 日志测试');
    
    await testMetadataDisplay();
    await testApiServiceMetadata();
    await testOptimizedApiServiceMetadata();
    
    AppLogger.logModule('Test', LogLevel.info, '✅ 所有 API 日志测试完成');
    AppLogger.logModule('Test', LogLevel.info, '请检查控制台输出，确认 metadata 信息是否正确显示');
  }
}

/// 测试运行入口
void main() async {
  await ApiLoggingTest.runAllTests();
}
