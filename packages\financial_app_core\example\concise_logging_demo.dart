import 'package:financial_app_core/financial_app_core.dart';

/// 简洁日志演示
/// 
/// 展示如何隐藏堆栈跟踪和详细信息，让日志输出更简洁
class ConciseLoggingDemo {
  
  /// 演示标准模式 vs 简洁模式的区别
  static Future<void> demonstrateComparison() async {
    print('🎯 日志输出模式对比演示\n');
    
    final coreLogger = CoreLogger();
    
    // 1. 标准开发模式 - 显示所有详细信息
    print('1️⃣ 标准开发模式 (显示堆栈跟踪和详细信息):');
    print('─' * 60);
    
    await AppLogger.initialize(config: LogConfig.development());
    
    coreLogger.debug('这是调试日志 - 会显示文件路径和行号');
    coreLogger.info('这是信息日志 - 包含完整的堆栈信息');
    coreLogger.warning('这是警告日志 - 显示方法调用栈');
    coreLogger.error('这是错误日志 - 包含详细的错误堆栈');
    
    await Future.delayed(Duration(seconds: 2));
    
    // 2. 简洁模式 - 隐藏详细信息
    print('\n2️⃣ 简洁模式 (隐藏堆栈跟踪和详细信息):');
    print('─' * 60);
    
    await AppLogger.initialize(config: LogConfig.concise());
    
    coreLogger.debug('这是调试日志 - 在简洁模式下不显示');
    coreLogger.info('这是信息日志 - 简洁格式，无堆栈信息');
    coreLogger.warning('这是警告日志 - 只显示核心内容');
    coreLogger.error('这是错误日志 - 简化的错误信息');
    
    await Future.delayed(Duration(seconds: 2));
    
    // 3. 超简洁模式 - 只显示消息
    print('\n3️⃣ 超简洁模式 (只显示消息内容):');
    print('─' * 60);
    
    // 创建自定义超简洁配置
    await AppLogger.initialize(config: LogConfig(
      minLevel: LogLevel.info,
      enableFileLogging: false,
      enableConsoleLogging: true,
      showDebugLogs: false,
      showVerboseLogs: false,
      showModuleNames: false,  // 隐藏模块名称
      showTimestamps: false,   // 隐藏时间戳
    ));
    
    coreLogger.info('用户登录成功');
    coreLogger.warning('内存使用率较高');
    coreLogger.error('网络连接失败');
  }
  
  /// 演示不同场景下的最佳配置
  static Future<void> demonstrateScenarios() async {
    print('\n🎬 不同场景下的日志配置演示\n');
    
    final coreLogger = CoreLogger();
    
    // 场景1: 日常开发 - 简洁模式
    print('📝 场景1: 日常开发 - 简洁模式');
    print('特点: 隐藏调试日志和堆栈信息，保留重要信息');
    print('─' * 50);
    
    await LogConfigManager.switchToConciseMode();
    
    coreLogger.debug('数据库查询: SELECT * FROM users - 不显示');
    coreLogger.info('用户 张三 登录成功');
    coreLogger.warning('API 响应时间超过 2 秒');
    coreLogger.error('支付接口调用失败');
    
    await Future.delayed(Duration(seconds: 1));
    
    // 场景2: 问题排查 - 静默模式
    print('\n🔍 场景2: 问题排查 - 静默模式');
    print('特点: 只显示警告和错误，专注问题定位');
    print('─' * 50);
    
    await LogConfigManager.switchToQuietMode();
    
    coreLogger.debug('详细调试信息 - 不显示');
    coreLogger.info('正常业务流程 - 不显示');
    coreLogger.warning('⚠️ 数据库连接池接近满载');
    coreLogger.error('❌ 第三方服务不可用');
    
    await Future.delayed(Duration(seconds: 1));
    
    // 场景3: 性能测试 - 最小输出
    print('\n⚡ 场景3: 性能测试 - 最小输出');
    print('特点: 极简输出，减少日志对性能的影响');
    print('─' * 50);
    
    await AppLogger.initialize(config: LogConfig(
      minLevel: LogLevel.error,
      enableFileLogging: false,
      enableConsoleLogging: true,
      showDebugLogs: false,
      showVerboseLogs: false,
      showModuleNames: false,
      showTimestamps: false,
    ));
    
    coreLogger.debug('调试信息 - 不显示');
    coreLogger.info('业务信息 - 不显示');
    coreLogger.warning('警告信息 - 不显示');
    coreLogger.error('严重错误 - 显示');
  }
  
  /// 演示动态切换效果
  static Future<void> demonstrateDynamicSwitching() async {
    print('\n🔄 动态切换演示\n');
    
    final coreLogger = CoreLogger();
    
    // 初始状态 - 开发模式
    await LogConfigManager.switchToDevelopmentMode();
    print('🔧 当前模式: 开发模式');
    coreLogger.debug('调试信息: 数据库连接初始化');
    coreLogger.info('业务信息: 用户认证成功');
    
    await Future.delayed(Duration(seconds: 1));
    
    // 切换到简洁模式
    print('\n🎯 切换到简洁模式...');
    await LogConfigManager.switchToConciseMode();
    coreLogger.debug('调试信息: 这条不会显示');
    coreLogger.info('业务信息: 订单创建成功');
    
    await Future.delayed(Duration(seconds: 1));
    
    // 切换到静默模式
    print('\n🔇 切换到静默模式...');
    await LogConfigManager.switchToQuietMode();
    coreLogger.info('业务信息: 这条不会显示');
    coreLogger.warning('警告: 库存不足');
    coreLogger.error('错误: 支付失败');
    
    await Future.delayed(Duration(seconds: 1));
    
    // 恢复简洁模式
    print('\n🎯 恢复简洁模式...');
    await LogConfigManager.switchToConciseMode();
    coreLogger.info('已恢复到简洁模式');
  }
  
  /// 展示配置前后的对比
  static Future<void> showBeforeAfterComparison() async {
    print('\n📊 配置前后对比\n');
    
    final coreLogger = CoreLogger();
    
    print('❌ 配置前 - 冗长的日志输出:');
    print('─' * 40);
    
    // 模拟冗长的日志输出
    await AppLogger.initialize(config: LogConfig.development());
    
    coreLogger.debug('开始处理用户请求');
    coreLogger.info('验证用户权限');
    coreLogger.debug('查询用户信息');
    coreLogger.debug('检查账户状态');
    coreLogger.info('处理业务逻辑');
    coreLogger.debug('更新数据库');
    coreLogger.info('返回处理结果');
    
    await Future.delayed(Duration(seconds: 2));
    
    print('\n✅ 配置后 - 简洁的日志输出:');
    print('─' * 40);
    
    // 简洁的日志输出
    await AppLogger.initialize(config: LogConfig.concise());
    
    coreLogger.debug('开始处理用户请求 - 不显示');
    coreLogger.info('验证用户权限');
    coreLogger.debug('查询用户信息 - 不显示');
    coreLogger.debug('检查账户状态 - 不显示');
    coreLogger.info('处理业务逻辑');
    coreLogger.debug('更新数据库 - 不显示');
    coreLogger.info('返回处理结果');
  }
  
  /// 提供使用建议
  static void showUsageRecommendations() {
    print('\n💡 使用建议\n');
    
    print('🎯 推荐配置:');
    print('  • 日常开发: LogConfig.concise() - 简洁模式');
    print('  • 问题调试: LogConfig.development() - 开发模式');
    print('  • 问题排查: LogConfig.quiet() - 静默模式');
    print('  • 生产环境: LogConfig.production() - 生产模式');
    print('');
    
    print('🔧 快速切换:');
    print('  • DebugLogController.hideDebugLogs() - 隐藏调试日志');
    print('  • DebugLogController.showDebugLogs() - 显示调试日志');
    print('  • LogConfigManager.toggleDebugLogs() - 切换调试日志');
    print('');
    
    print('📱 移动端建议:');
    print('  • 在设置中添加"开发者选项"');
    print('  • 提供日志级别选择器');
    print('  • 支持手势快速切换');
    print('');
    
    print('🎨 视觉优化:');
    print('  • 隐藏文件路径和行号');
    print('  • 减少堆栈跟踪信息');
    print('  • 使用颜色区分日志级别');
    print('  • 保持输出格式一致');
  }
}

/// 运行简洁日志演示
Future<void> main() async {
  print('🎯 简洁日志输出演示\n');
  print('本演示将展示如何隐藏图片中显示的详细堆栈信息\n');
  
  // 对比演示
  await ConciseLoggingDemo.demonstrateComparison();
  
  // 场景演示
  await ConciseLoggingDemo.demonstrateScenarios();
  
  // 动态切换演示
  await ConciseLoggingDemo.demonstrateDynamicSwitching();
  
  // 前后对比
  await ConciseLoggingDemo.showBeforeAfterComparison();
  
  // 使用建议
  ConciseLoggingDemo.showUsageRecommendations();
  
  print('\n✅ 演示完成！');
  print('🎯 现在您的日志输出将更加简洁，不再显示冗长的堆栈信息');
  print('🔧 可以根据需要随时切换不同的日志模式');
}
