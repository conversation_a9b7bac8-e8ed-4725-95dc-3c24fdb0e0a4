import 'package:financial_app_core/financial_app_core.dart';

/// 日志配置管理示例
/// 
/// 展示如何动态控制日志显示，特别是隐藏调试日志
class LogConfigExample {
  
  /// 初始化示例
  static Future<void> initializeExample() async {
    print('🚀 初始化日志配置管理示例...');
    
    // 使用开发模式初始化（显示所有日志）
    await AppLogger.initialize(config: LogConfig.development());
    
    final coreLogger = CoreLogger();
    coreLogger.info('日志配置管理示例初始化完成');
  }
  
  /// 演示不同的日志配置模式
  static Future<void> demonstrateLogModes() async {
    print('\n📊 演示不同的日志配置模式:');
    
    final coreLogger = CoreLogger();
    
    // 1. 开发模式 - 显示所有日志
    print('\n1️⃣ 开发模式 (显示所有日志):');
    await LogConfigManager.switchToDevelopmentMode();
    
    coreLogger.debug('这是调试日志 - 在开发模式下可见');
    coreLogger.info('这是信息日志');
    coreLogger.warning('这是警告日志');
    coreLogger.error('这是错误日志');
    
    await Future.delayed(Duration(seconds: 1));
    
    // 2. 简洁模式 - 隐藏调试日志
    print('\n2️⃣ 简洁模式 (隐藏调试日志):');
    await LogConfigManager.switchToConciseMode();
    
    coreLogger.debug('这是调试日志 - 在简洁模式下不可见');
    coreLogger.info('这是信息日志 - 仍然可见');
    coreLogger.warning('这是警告日志 - 仍然可见');
    coreLogger.error('这是错误日志 - 仍然可见');
    
    await Future.delayed(Duration(seconds: 1));
    
    // 3. 静默模式 - 只显示警告和错误
    print('\n3️⃣ 静默模式 (只显示警告和错误):');
    await LogConfigManager.switchToQuietMode();
    
    coreLogger.debug('这是调试日志 - 在静默模式下不可见');
    coreLogger.info('这是信息日志 - 在静默模式下不可见');
    coreLogger.warning('这是警告日志 - 仍然可见');
    coreLogger.error('这是错误日志 - 仍然可见');
    
    await Future.delayed(Duration(seconds: 1));
    
    // 恢复到开发模式
    await LogConfigManager.switchToDevelopmentMode();
  }
  
  /// 演示动态切换功能
  static Future<void> demonstrateDynamicToggle() async {
    print('\n🔄 演示动态切换功能:');
    
    final coreLogger = CoreLogger();
    
    // 显示当前配置状态
    LogConfigManager.printConfigStatus();
    
    // 切换调试日志显示
    print('\n🔧 切换调试日志显示状态:');
    coreLogger.debug('切换前的调试日志');
    
    await LogConfigManager.toggleDebugLogs();
    coreLogger.debug('切换后的调试日志 - 应该不可见');
    
    await LogConfigManager.toggleDebugLogs();
    coreLogger.debug('再次切换后的调试日志 - 应该可见');
    
    // 切换模块名称显示
    print('\n🏷️ 切换模块名称显示状态:');
    coreLogger.info('切换前 - 带模块名称');
    
    await LogConfigManager.toggleModuleNames();
    coreLogger.info('切换后 - 不带模块名称');
    
    await LogConfigManager.toggleModuleNames();
    coreLogger.info('再次切换后 - 带模块名称');
  }
  
  /// 演示预设配置
  static Future<void> demonstratePresets() async {
    print('\n📋 演示预设配置:');
    
    // 列出所有预设
    LogConfigManager.listPresets();
    
    final coreLogger = CoreLogger();
    
    // 测试每个预设
    for (final preset in LogConfigManager.presetConfigs.keys) {
      print('\n🎯 测试预设: $preset');
      await LogConfigManager.switchToPreset(preset);
      
      coreLogger.debug('调试日志测试');
      coreLogger.info('信息日志测试');
      coreLogger.warning('警告日志测试');
      
      await Future.delayed(Duration(milliseconds: 500));
    }
    
    // 恢复到开发模式
    await LogConfigManager.switchToDevelopmentMode();
  }
  
  /// 演示实际使用场景
  static Future<void> demonstrateRealWorldUsage() async {
    print('\n🌍 实际使用场景演示:');
    
    final coreLogger = CoreLogger();
    
    // 场景1: 开发调试时
    print('\n📝 场景1: 开发调试时 - 需要看到所有日志');
    await LogConfigManager.switchToDevelopmentMode();
    
    coreLogger.debug('正在初始化数据库连接...');
    coreLogger.debug('数据库连接参数: host=localhost, port=5432');
    coreLogger.info('数据库连接成功');
    coreLogger.debug('开始执行数据迁移...');
    coreLogger.info('数据迁移完成');
    
    await Future.delayed(Duration(seconds: 1));
    
    // 场景2: 生产环境监控时
    print('\n🔍 场景2: 生产环境监控时 - 只关注重要信息');
    await LogConfigManager.switchToConciseMode();
    
    coreLogger.debug('这些调试信息在生产环境中被隐藏');
    coreLogger.debug('减少日志噪音，提高可读性');
    coreLogger.info('用户登录成功');
    coreLogger.warning('API调用频率较高');
    coreLogger.error('数据库连接超时');
    
    await Future.delayed(Duration(seconds: 1));
    
    // 场景3: 问题排查时
    print('\n🚨 场景3: 问题排查时 - 只看错误和警告');
    await LogConfigManager.switchToQuietMode();
    
    coreLogger.debug('这些调试信息被隐藏');
    coreLogger.info('这些普通信息也被隐藏');
    coreLogger.warning('内存使用率达到80%');
    coreLogger.error('支付接口调用失败');
    coreLogger.error('用户认证失败');
    
    // 恢复到开发模式
    await LogConfigManager.switchToDevelopmentMode();
  }
  
  /// 演示配置状态查看
  static void demonstrateConfigStatus() {
    print('\n📊 配置状态查看:');
    
    // 打印当前配置状态
    LogConfigManager.printConfigStatus();
    
    // 获取配置状态字符串
    final status = LogConfigManager.getConfigStatus();
    print('\n📋 配置状态详情:');
    print(status);
  }
  
  /// 使用建议和最佳实践
  static void showBestPractices() {
    print('\n💡 使用建议和最佳实践:');
    print('');
    print('1. 🔧 开发阶段:');
    print('   • 使用 LogConfig.development() 或 LogConfigManager.switchToDevelopmentMode()');
    print('   • 显示所有日志，便于调试和问题定位');
    print('');
    print('2. 🎯 测试阶段:');
    print('   • 使用 LogConfig.concise() 或 LogConfigManager.switchToConciseMode()');
    print('   • 隐藏调试日志，关注业务逻辑和错误');
    print('');
    print('3. 🚀 生产环境:');
    print('   • 使用 LogConfig.production() 或 LogConfigManager.switchToProductionMode()');
    print('   • 最小化日志输出，提高性能');
    print('');
    print('4. 🔍 问题排查:');
    print('   • 使用 LogConfig.quiet() 或 LogConfigManager.switchToQuietMode()');
    print('   • 只显示警告和错误，快速定位问题');
    print('');
    print('5. 🔄 动态切换:');
    print('   • 使用 LogConfigManager.toggleDebugLogs() 快速切换调试日志');
    print('   • 使用 LogConfigManager.toggleModuleNames() 控制模块名称显示');
    print('   • 使用 LogConfigManager.printConfigStatus() 查看当前状态');
    print('');
    print('6. 📱 移动应用:');
    print('   • 在设置页面添加日志级别选择');
    print('   • 提供"开发者选项"来切换详细日志');
    print('   • 在崩溃报告中包含相关日志');
  }
}

/// 运行日志配置示例
Future<void> main() async {
  print('🎯 日志配置管理示例\n');
  
  // 初始化
  await LogConfigExample.initializeExample();
  
  // 演示不同模式
  await LogConfigExample.demonstrateLogModes();
  
  // 演示动态切换
  await LogConfigExample.demonstrateDynamicToggle();
  
  // 演示预设配置
  await LogConfigExample.demonstratePresets();
  
  // 演示实际使用场景
  await LogConfigExample.demonstrateRealWorldUsage();
  
  // 演示配置状态查看
  LogConfigExample.demonstrateConfigStatus();
  
  // 显示最佳实践
  LogConfigExample.showBestPractices();
  
  print('\n✅ 日志配置管理示例运行完成！');
  print('🎯 现在您可以根据需要动态控制日志显示');
  print('🔧 特别是可以隐藏绿色的调试日志，让输出更简洁');
}
