import 'package:financial_app_core/financial_app_core.dart';

/// 企业级日志系统使用示例
/// 
/// 这个示例展示了如何在实际项目中使用 AppLogger
class LoggerExample {
  
  /// 初始化示例
  static Future<void> initializeExample() async {
    print('🚀 初始化企业级日志系统...');
    
    // 根据环境选择配置
    final config = _isProduction() 
        ? LogConfig.production()
        : LogConfig.development();
    
    await AppLogger.initialize(config: config);
    
    AppLogger.logModule('System', LogLevel.info, '日志系统初始化完成');
  }
  
  /// 基础日志记录示例
  static void basicLoggingExample() {
    print('\n📝 基础日志记录示例:');
    
    // 不同级别的日志
    AppLogger.logModule('Example', LogLevel.debug, '这是调试信息');
    AppLogger.logModule('Example', LogLevel.info, '这是一般信息');
    AppLogger.logModule('Example', LogLevel.warning, '这是警告信息');
    AppLogger.logModule('Example', LogLevel.error, '这是错误信息');
    
    // 带错误对象的日志
    try {
      throw Exception('模拟异常');
    } catch (e, stackTrace) {
      AppLogger.logModule(
        'Example', 
        LogLevel.error, 
        '捕获到异常', 
        error: e, 
        stackTrace: stackTrace,
      );
    }
    
    // 带元数据的日志
    AppLogger.logModule(
      'Example',
      LogLevel.info,
      '用户操作记录',
      metadata: {
        'user_id': '12345',
        'action': 'view_portfolio',
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
  }
  
  /// 网络请求日志示例
  static void networkLoggingExample() {
    print('\n🌐 网络请求日志示例:');
    
    // 模拟网络请求
    final headers = {
      'Authorization': 'Bearer secret_token_12345',
      'Content-Type': 'application/json',
    };
    
    final requestBody = {
      'username': 'john_doe',
      'password': 'secret_password_123',
      'amount': 1000.50,
    };
    
    // 记录请求（敏感信息会被自动过滤）
    AppLogger.logNetworkRequest(
      'API',
      'POST',
      'https://api.example.com/auth/login',
      headers: headers,
      body: requestBody,
    );
    
    // 模拟响应
    final responseData = {
      'token': 'jwt_token_abcdef123456',
      'user_id': '12345',
      'expires_in': 3600,
    };
    
    AppLogger.logNetworkResponse(
      'API',
      'POST',
      'https://api.example.com/auth/login',
      200,
      responseData: responseData,
      duration: Duration(milliseconds: 250),
    );
  }
  
  /// 性能监控示例
  static Future<void> performanceLoggingExample() async {
    print('\n⚡ 性能监控示例:');
    
    final stopwatch = Stopwatch()..start();
    
    // 模拟耗时操作
    await Future.delayed(Duration(milliseconds: 100));
    
    stopwatch.stop();
    
    // 记录性能日志
    AppLogger.logPerformance(
      'Database',
      'fetchUserPortfolio',
      stopwatch.elapsed,
      metadata: {
        'user_id': '12345',
        'records_count': 150,
        'cache_hit': false,
      },
    );
    
    // 另一个性能示例
    await _simulateApiCall();
  }
  
  /// 业务事件日志示例
  static void businessEventExample() {
    print('\n💼 业务事件日志示例:');
    
    // 交易事件
    AppLogger.logBusinessEvent('Trading', 'order_placed', {
      'order_id': 'ORD-2024-001',
      'symbol': 'AAPL',
      'side': 'buy',
      'quantity': 100,
      'price': 150.25,
      'order_type': 'market',
    });
    
    // 用户注册事件
    AppLogger.logBusinessEvent('Auth', 'user_registered', {
      'user_id': '12345',
      'registration_method': 'email',
      'referral_code': 'REF123',
    });
    
    // 支付事件
    AppLogger.logBusinessEvent('Payment', 'payment_processed', {
      'payment_id': 'PAY-2024-001',
      'amount': 1000.00,
      'currency': 'USD',
      'payment_method': 'credit_card',
      'status': 'success',
    });
  }
  
  /// 用户行为追踪示例
  static void userActionExample() {
    print('\n👤 用户行为追踪示例:');
    
    const userId = 'user_12345';
    
    // 页面访问
    AppLogger.logUserAction('UI', 'page_view', userId, context: {
      'page': 'dashboard',
      'previous_page': 'login',
      'session_id': 'sess_abcdef123',
    });
    
    // 按钮点击
    AppLogger.logUserAction('UI', 'button_click', userId, context: {
      'button_id': 'refresh_portfolio',
      'screen': 'portfolio',
      'position': {'x': 100, 'y': 200},
    });
    
    // 功能使用
    AppLogger.logUserAction('Feature', 'chart_interaction', userId, context: {
      'chart_type': 'candlestick',
      'timeframe': '15min',
      'symbol': 'AAPL',
      'action': 'zoom_in',
    });
  }
  
  /// 系统健康检查示例
  static void healthCheckExample() {
    print('\n🏥 系统健康检查示例:');
    
    // 数据库连接检查
    AppLogger.logHealthCheck(
      'Database',
      'connection_pool',
      true,
      details: 'All connections healthy',
      metrics: {
        'active_connections': 8,
        'max_connections': 20,
        'avg_response_time_ms': 15,
      },
    );
    
    // API 服务检查
    AppLogger.logHealthCheck(
      'ExternalAPI',
      'market_data_service',
      false,
      details: 'Service responding slowly',
      metrics: {
        'response_time_ms': 5000,
        'error_rate': 0.05,
        'last_success': DateTime.now().subtract(Duration(minutes: 2)).toIso8601String(),
      },
    );
    
    // 内存使用检查
    AppLogger.logHealthCheck(
      'System',
      'memory_usage',
      true,
      metrics: {
        'used_mb': 256,
        'available_mb': 1024,
        'usage_percentage': 25,
      },
    );
  }
  
  /// 日志分析示例
  static void logAnalysisExample() {
    print('\n📊 日志分析示例:');
    
    // 获取整体统计
    final stats = AppLogger.getLogStatistics();
    print('日志统计信息:');
    stats.forEach((key, count) {
      print('  $key: $count 条');
    });
    
    // 获取特定模块统计
    final apiStats = AppLogger.getModuleStatistics('API');
    print('\nAPI模块统计:');
    apiStats.forEach((level, count) {
      print('  $level: $count 条');
    });
    
    // 获取最近日志
    final recentLogs = AppLogger.getRecentLogs(limit: 5);
    print('\n最近5条日志:');
    for (final log in recentLogs) {
      print('  ${log.timestamp}: [${log.module}] ${log.level.name.toUpperCase()} - ${log.message}');
    }
  }
  
  /// 安全日志示例
  static void securityLoggingExample() {
    print('\n🔒 安全日志示例:');
    
    // 敏感数据会被自动过滤
    final sensitiveData = {
      'username': 'john_doe',
      'password': 'secret123',  // 会被过滤
      'token': 'jwt_abc123',    // 会被过滤
      'credit_card': '1234-5678-9012-3456',  // 会被过滤
      'amount': 1000.50,        // 不会被过滤
    };
    
    AppLogger.secureLog('Auth', LogLevel.info, '用户登录数据', data: sensitiveData);
  }
  
  /// 模拟API调用
  static Future<void> _simulateApiCall() async {
    final stopwatch = Stopwatch()..start();
    
    AppLogger.logModule('API', LogLevel.info, '开始获取市场数据');
    
    // 模拟网络延迟
    await Future.delayed(Duration(milliseconds: 200));
    
    stopwatch.stop();
    
    AppLogger.logPerformance(
      'API',
      'getMarketData',
      stopwatch.elapsed,
      metadata: {
        'symbols_count': 50,
        'data_source': 'primary',
      },
    );
  }
  
  /// 检查是否为生产环境
  static bool _isProduction() {
    // 在实际项目中，这里应该检查环境变量或构建配置
    return false; // 示例中返回 false
  }
}

/// 运行所有示例
Future<void> main() async {
  print('🎯 企业级日志系统示例\n');
  
  // 初始化
  await LoggerExample.initializeExample();
  
  // 运行各种示例
  LoggerExample.basicLoggingExample();
  LoggerExample.networkLoggingExample();
  await LoggerExample.performanceLoggingExample();
  LoggerExample.businessEventExample();
  LoggerExample.userActionExample();
  LoggerExample.healthCheckExample();
  LoggerExample.securityLoggingExample();
  LoggerExample.logAnalysisExample();
  
  print('\n✅ 所有示例运行完成！');
  print('📁 日志文件已保存到应用文档目录');
  print('📊 可以通过 AppLogger.exportLogs() 导出日志文件');
}
