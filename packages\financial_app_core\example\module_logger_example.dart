import 'package:financial_app_core/financial_app_core.dart';

// 注意：在实际项目中，您需要导入相应的模块包
// 这里为了示例，我们直接导入日志管理器类
// import 'package:financial_app_auth/financial_app_auth.dart';
// import 'package:financial_app_market/financial_app_market.dart';
// 等等...

/// 模块化日志管理器使用示例
///
/// 展示如何在各个业务模块中使用专用的日志管理器
class ModuleLoggerExample {
  /// 核心模块日志示例
  static void coreModuleExample() {
    print('\n🔧 核心模块日志示例:');

    final coreLogger = CoreLogger();

    // 基础日志
    coreLogger.info('核心服务初始化完成');
    coreLogger.debug(
      '配置加载成功',
      metadata: {'config_count': 15, 'load_time_ms': 120},
    );

    // 性能日志
    coreLogger.logPerformance('config_load', Duration(milliseconds: 120));

    // 健康检查
    coreLogger.logHealthCheck(
      'database',
      true,
      details: 'Connection pool healthy',
      metrics: {'active_connections': 5, 'max_connections': 20},
    );
  }

  /// 认证模块日志示例
  static void authModuleExample() {
    print('\n🔐 认证模块日志示例:');

    // 使用专用的认证日志管理器
    final authLogger = AuthLogger();

    // 用户登录
    authLogger.logLogin(
      username: 'john_doe',
      success: true,
      sessionId: 'sess_12345',
      duration: Duration(milliseconds: 250),
    );

    // Token 刷新
    authLogger.logTokenRefresh(
      success: true,
      tokenLifetime: Duration(hours: 1),
    );

    // 安全事件
    authLogger.logSecurityEvent(
      eventType: 'suspicious_login',
      userId: 'user_12345',
      details: 'Login from unusual location',
      additionalData: {
        'ip_address': '*************',
        'location': 'Unknown',
        'device': 'iPhone 13',
      },
    );

    // 生物识别认证
    authLogger.logBiometricAuth(type: 'fingerprint', success: true);
  }

  /// 市场数据模块日志示例
  static void marketModuleExample() {
    print('\n📈 市场数据模块日志示例:');

    final marketLogger = MarketLogger();

    // 股票数据获取
    marketLogger.logStockDataFetch(
      symbols: ['AAPL', 'GOOGL', 'MSFT'],
      success: true,
      dataSource: 'primary',
      duration: Duration(milliseconds: 150),
    );

    // K线数据获取
    marketLogger.logKlineDataFetch(
      symbol: 'AAPL',
      timeframe: '15min',
      success: true,
      dataPoints: 100,
      duration: Duration(milliseconds: 80),
    );

    // WebSocket 事件
    marketLogger.logWebSocketEvent(
      event: 'connected',
      url: 'wss://api.example.com/ws',
    );

    // 实时行情订阅
    marketLogger.logRealtimeSubscription(
      symbol: 'AAPL',
      action: 'subscribe',
      success: true,
    );

    // 数据质量检查
    marketLogger.logDataQualityCheck(
      symbol: 'AAPL',
      passed: true,
      metrics: {
        'price_variance': 0.02,
        'volume_consistency': 0.98,
        'timestamp_accuracy': 0.99,
      },
    );
  }

  /// 主应用模块日志示例
  static void mainModuleExample() {
    print('\n🏠 主应用模块日志示例:');

    final mainLogger = MainLogger();

    // 应用启动
    mainLogger.logAppStartup(
      success: true,
      startupDuration: Duration(milliseconds: 1200),
      initializedModules: ['Core', 'Auth', 'Market', 'Trading'],
    );

    // 页面导航
    mainLogger.logNavigation(
      fromRoute: '/login',
      toRoute: '/dashboard',
      userId: 'user_12345',
      navigationDuration: Duration(milliseconds: 300),
    );

    // UI 事件
    mainLogger.logUIEvent(
      eventType: 'button_click',
      elementId: 'refresh_portfolio',
      screenName: 'dashboard',
      userId: 'user_12345',
      eventData: {
        'position': {'x': 100, 'y': 200},
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      },
    );

    // 应用生命周期
    mainLogger.logAppLifecycle(
      state: 'resumed',
      previousState: 'paused',
      sessionDuration: Duration(minutes: 15),
    );

    // 性能指标
    mainLogger.logPerformanceMetrics(
      metricType: 'memory',
      metrics: {'used_mb': 256, 'available_mb': 1024, 'usage_percentage': 25},
      alertTriggered: false,
    );
  }

  /// 使用工具类的简化调用示例
  static void utilsExample() {
    print('\n🛠️ 工具类简化调用示例:');

    // 认证模块工具类
    AuthLoggerUtils.logLogin(
      username: 'jane_doe',
      success: true,
      sessionId: 'sess_67890',
    );

    AuthLoggerUtils.logSecurityEvent(
      eventType: 'account_locked',
      userId: 'user_67890',
      details: 'Too many failed login attempts',
    );

    // 市场数据模块工具类
    MarketLoggerUtils.logStockDataFetch(
      symbols: ['TSLA', 'NVDA'],
      success: true,
      duration: Duration(milliseconds: 200),
    );

    MarketLoggerUtils.logWebSocketEvent(
      event: 'disconnected',
      reason: 'Network timeout',
    );

    // 主应用模块工具类
    MainLoggerUtils.logNavigation(
      fromRoute: '/dashboard',
      toRoute: '/portfolio',
      userId: 'user_12345',
    );

    MainLoggerUtils.logUIEvent(
      eventType: 'swipe',
      elementId: 'portfolio_list',
      screenName: 'portfolio',
      userId: 'user_12345',
    );
  }

  /// 所有业务模块日志示例
  static void allBusinessModulesExample() {
    print('\n🎯 所有业务模块日志示例:');

    // 通知模块
    NotificationLoggerUtils.logPushNotificationSent(
      userId: 'user_12345',
      notificationType: 'price_alert',
      success: true,
      title: 'AAPL价格提醒',
      body: 'AAPL价格已达到150美元',
    );

    // 投资组合模块
    PortfolioLoggerUtils.logPortfolioCreated(
      userId: 'user_12345',
      portfolioId: 'portfolio_001',
      portfolioName: '我的投资组合',
      success: true,
      portfolioType: 'growth',
      initialValue: 10000.0,
    );

    // 交易模块
    TradeLoggerUtils.logOrderCreated(
      userId: 'user_12345',
      orderId: 'ORD-2024-001',
      symbol: 'AAPL',
      orderType: 'market',
      side: 'buy',
      quantity: 100,
      price: 150.25,
      success: true,
    );

    // WebSocket模块
    WsLoggerUtils.logConnection(
      url: 'wss://api.example.com/ws',
      action: 'connected',
      success: true,
      connectionId: 'conn_12345',
      connectionTime: Duration(milliseconds: 200),
    );

    // 图表模块
    ChartLoggerUtils.logChartInitialization(
      chartId: 'chart_001',
      chartType: 'candlestick',
      success: true,
      symbol: 'AAPL',
      timeframe: '15min',
      initTime: Duration(milliseconds: 150),
    );

    // 资源模块
    AssetsLoggerUtils.logAssetLoading(
      assetPath: 'assets/images/logo.png',
      assetType: 'image',
      success: true,
      fileSize: 2048,
      loadTime: Duration(milliseconds: 50),
      cacheStatus: 'hit',
    );
  }

  /// 错误处理示例
  static void errorHandlingExample() {
    print('\n❌ 错误处理示例:');

    final marketLogger = MarketLogger();

    try {
      // 模拟一个可能失败的操作
      throw Exception('网络连接失败');
    } catch (e, stackTrace) {
      // 记录详细的错误信息
      marketLogger.error(
        '获取市场数据失败',
        error: e,
        stackTrace: stackTrace,
        metadata: {
          'symbols': ['AAPL', 'GOOGL'],
          'retry_count': 3,
          'last_success': DateTime.now()
              .subtract(Duration(minutes: 5))
              .toIso8601String(),
        },
      );

      // 记录业务事件
      marketLogger.logBusinessEvent('data_fetch_failed', {
        'error_type': 'network_error',
        'affected_symbols': ['AAPL', 'GOOGL'],
        'will_retry': true,
      });
    }
  }

  /// 性能监控示例
  static Future<void> performanceMonitoringExample() async {
    print('\n⚡ 性能监控示例:');

    final authLogger = AuthLogger();
    final marketLogger = MarketLogger();

    // 认证性能监控
    final loginStopwatch = Stopwatch()..start();

    // 模拟登录操作
    await Future.delayed(Duration(milliseconds: 200));

    loginStopwatch.stop();
    authLogger.logPerformance(
      'user_login',
      loginStopwatch.elapsed,
      metadata: {'username': 'john_doe', 'auth_method': 'password'},
    );

    // 市场数据性能监控
    final dataFetchStopwatch = Stopwatch()..start();

    // 模拟数据获取
    await Future.delayed(Duration(milliseconds: 150));

    dataFetchStopwatch.stop();
    marketLogger.logPerformance(
      'market_data_fetch',
      dataFetchStopwatch.elapsed,
      metadata: {
        'symbols_count': 50,
        'data_source': 'primary',
        'cache_hit_rate': 0.75,
      },
    );
  }

  /// 业务流程追踪示例
  static void businessFlowExample() {
    print('\n💼 业务流程追踪示例:');

    final authLogger = AuthLogger();
    final tradingLogger = LoggerFactory.getLogger('Trading');
    final userId = 'user_12345';

    // 1. 用户登录
    authLogger.logLogin(
      username: 'john_doe',
      success: true,
      sessionId: 'sess_12345',
    );

    // 2. 查看投资组合
    MainLoggerUtils.logNavigation(
      fromRoute: '/login',
      toRoute: '/portfolio',
      userId: userId,
    );

    // 3. 下单交易
    tradingLogger.logBusinessEvent('order_placed', {
      'user_id': userId,
      'order_type': 'market',
      'symbol': 'AAPL',
      'quantity': 100,
      'estimated_value': 15025.00,
    });

    // 4. 订单执行
    tradingLogger.logBusinessEvent('order_executed', {
      'user_id': userId,
      'order_id': 'ORD-2024-001',
      'execution_price': 150.30,
      'total_value': 15030.00,
    });

    // 5. 发送通知
    final notificationLogger = LoggerFactory.getLogger('Notification');
    notificationLogger.logBusinessEvent('notification_sent', {
      'user_id': userId,
      'type': 'order_confirmation',
      'order_id': 'ORD-2024-001',
      'delivery_method': 'push',
    });
  }
}

/// 运行所有模块日志示例
Future<void> main() async {
  print('🎯 模块化日志管理器示例\n');

  // 初始化日志系统
  await AppLogger.initialize(config: LogConfig.development());

  // 运行各种示例
  ModuleLoggerExample.coreModuleExample();
  ModuleLoggerExample.authModuleExample();
  ModuleLoggerExample.marketModuleExample();
  ModuleLoggerExample.mainModuleExample();
  ModuleLoggerExample.utilsExample();
  ModuleLoggerExample.customModuleExample();
  ModuleLoggerExample.errorHandlingExample();
  await ModuleLoggerExample.performanceMonitoringExample();
  ModuleLoggerExample.businessFlowExample();

  print('\n✅ 所有模块日志示例运行完成！');
  print('📊 每个模块都有自己的专用日志管理器');
  print('🔧 简化了日志调用，提高了代码可维护性');
}
