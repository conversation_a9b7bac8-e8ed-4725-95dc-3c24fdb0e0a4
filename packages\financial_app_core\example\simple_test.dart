// 简单的工具类测试，不依赖Flutter框架
import '../lib/src/utils/string_utils.dart';
import '../lib/src/utils/number_utils.dart';
import '../lib/src/utils/validation_utils.dart';

void main() {
  print('=== financial_app_core 工具类简单测试 ===\n');
  
  // 测试字符串工具类
  testStringUtils();
  
  // 测试数字工具类
  testNumberUtils();
  
  // 测试验证工具类
  testValidationUtils();
  
  print('✅ 所有测试完成！');
}

void testStringUtils() {
  print('📝 StringUtils 测试:');
  
  // 测试手机号加星号
  final phone = '13812345678';
  final maskedPhone = StringUtils.maskPhone(phone);
  print('手机号加星: $phone -> $maskedPhone');
  
  // 测试邮箱加星号
  final email = '<EMAIL>';
  final maskedEmail = StringUtils.maskEmail(email);
  print('邮箱加星: $email -> $maskedEmail');
  
  // 测试字符串转换
  final camelCase = StringUtils.snakeToCamel('user_name');
  final snakeCase = StringUtils.camelToSnake('userName');
  print('下划线转驼峰: user_name -> $camelCase');
  print('驼峰转下划线: userName -> $snakeCase');
  
  // 测试字符串截取
  final truncated = StringUtils.truncate('这是一个很长的字符串示例', 10);
  print('字符串截取: $truncated');
  
  print('');
}

void testNumberUtils() {
  print('🔢 NumberUtils 测试:');
  
  // 测试数字格式化
  final price = 1234.5678;
  final formattedPrice = NumberUtils.formatPrice(price);
  print('价格格式化: $price -> $formattedPrice');
  
  // 测试大数字格式化
  final volume = 1500000;
  final formattedVolume = NumberUtils.formatLargeNumber(volume);
  print('大数字格式化: $volume -> $formattedVolume');
  
  // 测试百分比格式化
  final change = 0.0523;
  final formattedPercent = NumberUtils.formatPercentage(change, showSign: true);
  print('百分比格式化: $change -> $formattedPercent');
  
  // 测试安全除法
  final safeDivision = NumberUtils.safeDivide(10, 0, defaultValue: -1);
  print('安全除法 (10/0): $safeDivision');
  
  // 测试数字范围判断
  final inRange = NumberUtils.isInRange(50, 0, 100);
  print('范围判断 (50 in 0-100): $inRange');
  
  print('');
}

void testValidationUtils() {
  print('✅ ValidationUtils 测试:');
  
  // 测试手机号验证
  final testPhone = '13812345678';
  final isValidPhone = ValidationUtils.isValidPhone(testPhone);
  print('手机号验证 ($testPhone): $isValidPhone');
  
  // 测试邮箱验证
  final testEmail = '<EMAIL>';
  final isValidEmail = ValidationUtils.isValidEmail(testEmail);
  print('邮箱验证 ($testEmail): $isValidEmail');
  
  // 测试密码验证
  final testPassword = 'Abc123456';
  final isValidPassword = ValidationUtils.isValidPassword(testPassword);
  final passwordStrength = ValidationUtils.getPasswordStrength(testPassword);
  print('密码验证 ($testPassword): $isValidPassword');
  print('密码强度 ($testPassword): $passwordStrength');
  
  // 测试数字验证
  final isValidNumber = ValidationUtils.isValidNumber('123.45');
  print('数字验证 ("123.45"): $isValidNumber');
  
  // 测试范围验证
  final isValidRange = ValidationUtils.isValidRange('50', 0, 100);
  print('范围验证 ("50" in 0-100): $isValidRange');
  
  print('');
}
