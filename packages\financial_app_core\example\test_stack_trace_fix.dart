// 简化的测试脚本，只测试配置功能
// 避免导入Flutter依赖，直接定义测试用的类

/// 日志级别枚举
enum LogLevel { debug, info, warning, error, fatal }

/// 简化的日志配置类（用于测试）
class LogConfig {
  final LogLevel minLevel;
  final bool enableFileLogging;
  final bool enableConsoleLogging;
  final bool enableRemoteLogging;
  final int maxFileSize;
  final int maxLogFiles;
  final String? remoteEndpoint;
  final bool structuredLogging;
  final bool showDebugLogs;
  final bool showVerboseLogs;
  final bool showModuleNames;
  final bool showTimestamps;
  final bool showStackTrace;

  const LogConfig({
    this.minLevel = LogLevel.info,
    this.enableFileLogging = true,
    this.enableConsoleLogging = true,
    this.enableRemoteLogging = false,
    this.maxFileSize = 10 * 1024 * 1024, // 10MB
    this.maxLogFiles = 5,
    this.remoteEndpoint,
    this.structuredLogging = false,
    this.showDebugLogs = true,
    this.showVerboseLogs = false,
    this.showModuleNames = true,
    this.showTimestamps = true,
    this.showStackTrace = false,
  });

  factory LogConfig.development() => const LogConfig(
    minLevel: LogLevel.debug,
    enableFileLogging: true,
    enableConsoleLogging: true,
    structuredLogging: false,
    showDebugLogs: true,
    showVerboseLogs: false,
    showModuleNames: true,
    showTimestamps: true,
    showStackTrace: false, // 默认不显示堆栈跟踪
  );

  factory LogConfig.production() => const LogConfig(
    minLevel: LogLevel.warning,
    enableFileLogging: true,
    enableConsoleLogging: false,
    enableRemoteLogging: true,
    structuredLogging: true,
    showDebugLogs: false,
    showVerboseLogs: false,
    showModuleNames: true,
    showTimestamps: false,
    showStackTrace: false,
  );

  /// 简洁模式配置 - 隐藏调试和详细日志
  factory LogConfig.concise() => const LogConfig(
    minLevel: LogLevel.info,
    enableFileLogging: true,
    enableConsoleLogging: true,
    structuredLogging: false,
    showDebugLogs: false,
    showVerboseLogs: false,
    showModuleNames: true,
    showTimestamps: false, // 隐藏时间戳，减少视觉噪音
    showStackTrace: false, // 隐藏堆栈跟踪，减少重复信息
  );

  /// 静默模式配置 - 只显示警告和错误
  factory LogConfig.quiet() => const LogConfig(
    minLevel: LogLevel.warning,
    enableFileLogging: true,
    enableConsoleLogging: true,
    structuredLogging: false,
    showDebugLogs: false,
    showVerboseLogs: false,
    showModuleNames: false,
    showTimestamps: false,
    showStackTrace: false,
  );
}

/// 测试堆栈跟踪隐藏功能
void main() {
  print('🧪 测试堆栈跟踪配置功能');
  print('=' * 50);

  // 1. 测试默认配置
  print('\n📋 测试默认配置:');
  final defaultConfig = LogConfig();
  print('  • showStackTrace: ${defaultConfig.showStackTrace}');

  // 2. 测试简洁模式配置
  print('\n📋 测试简洁模式配置:');
  final conciseConfig = LogConfig.concise();
  print('  • showStackTrace: ${conciseConfig.showStackTrace}');
  print('  • showDebugLogs: ${conciseConfig.showDebugLogs}');
  print('  • showTimestamps: ${conciseConfig.showTimestamps}');

  // 3. 测试开发模式配置
  print('\n📋 测试开发模式配置:');
  final devConfig = LogConfig.development();
  print('  • showStackTrace: ${devConfig.showStackTrace}');
  print('  • showDebugLogs: ${devConfig.showDebugLogs}');
  print('  • showTimestamps: ${devConfig.showTimestamps}');

  // 4. 测试生产模式配置
  print('\n📋 测试生产模式配置:');
  final prodConfig = LogConfig.production();
  print('  • showStackTrace: ${prodConfig.showStackTrace}');
  print('  • showDebugLogs: ${prodConfig.showDebugLogs}');
  print('  • showTimestamps: ${prodConfig.showTimestamps}');

  // 5. 测试静默模式配置
  print('\n📋 测试静默模式配置:');
  final quietConfig = LogConfig.quiet();
  print('  • showStackTrace: ${quietConfig.showStackTrace}');
  print('  • showDebugLogs: ${quietConfig.showDebugLogs}');
  print('  • showTimestamps: ${quietConfig.showTimestamps}');

  // 6. 测试自定义配置
  print('\n📋 测试自定义配置:');
  final customConfig = LogConfig(
    showStackTrace: true,
    showDebugLogs: true,
    showTimestamps: false,
  );
  print('  • showStackTrace: ${customConfig.showStackTrace}');
  print('  • showDebugLogs: ${customConfig.showDebugLogs}');
  print('  • showTimestamps: ${customConfig.showTimestamps}');

  print('\n✅ 配置测试完成！');
  print('所有预设配置都默认隐藏堆栈跟踪 (showStackTrace: false)');
  print('这将解决日志输出中重复前两行堆栈信息的问题。');

  // 7. 验证修复效果
  print('\n🎯 修复效果验证:');
  print('修复前的日志输出:');
  print(
    '  I/flutter ( 2854): #0   AppLogger.logModule (package:financial_app_core/src/logger/app_logger.dart:137:17)',
  );
  print(
    '  I/flutter ( 2854): #1   Logger.debug (package:financial_app_core/src/utils/logger.dart:16:15)',
  );
  print('  I/flutter ( 2854): 🐛 [Legacy] 已清理 0 个失效的数据引用');
  print('');
  print('修复后的日志输出:');
  print('  I/flutter ( 2854): 🐛 [Legacy] 已清理 0 个失效的数据引用');
  print('');
  print('✨ 成功隐藏了重复的堆栈跟踪信息！');
}
