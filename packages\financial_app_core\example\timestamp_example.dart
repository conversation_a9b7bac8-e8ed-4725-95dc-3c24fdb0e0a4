// 时间戳转换功能示例
import '../lib/src/utils/date_utils.dart';

void main() {
  print('=== DateUtils 时间戳转换功能示例 ===\n');
  
  // 当前时间
  final now = DateTime.now();
  print('当前时间: $now');
  
  // 获取时间戳
  final timestampMs = DateUtils.toTimestamp(now);
  final timestampSec = DateUtils.toTimestampSeconds(now);
  
  print('毫秒级时间戳: $timestampMs');
  print('秒级时间戳: $timestampSec');
  print('');
  
  // 毫秒级时间戳转换示例
  print('=== 毫秒级时间戳转换 ===');
  
  // 转为标准日期格式
  String dateStr = DateUtils.timestampToDateString(timestampMs);
  print('转为日期: $dateStr');
  
  // 转为标准日期时间格式
  String dateTimeStr = DateUtils.timestampToDateTimeString(timestampMs);
  print('转为日期时间: $dateTimeStr');
  
  // 转为自定义格式
  String customFormat = DateUtils.timestampToDateTimeString(timestampMs, format: 'yyyy年MM月dd日 HH:mm');
  print('自定义格式: $customFormat');
  
  // 转为中文日期
  String chineseDate = DateUtils.timestampToChineseDateString(timestampMs);
  print('中文日期: $chineseDate');
  
  // 转为中文日期时间
  String chineseDateWithTime = DateUtils.timestampToChineseDateString(timestampMs, includeTime: true);
  print('中文日期时间: $chineseDateWithTime');
  
  // 转为相对时间
  String relativeTime = DateUtils.timestampToRelativeTimeString(timestampMs);
  print('相对时间: $relativeTime');
  print('');
  
  // 秒级时间戳转换示例
  print('=== 秒级时间戳转换 ===');
  
  String secDateStr = DateUtils.timestampSecondsToDateString(timestampSec);
  print('秒级转日期: $secDateStr');
  
  String secDateTimeStr = DateUtils.timestampSecondsToDateTimeString(timestampSec);
  print('秒级转日期时间: $secDateTimeStr');
  
  String secCustomFormat = DateUtils.timestampSecondsToDateTimeString(timestampSec, format: 'MM/dd HH:mm:ss');
  print('秒级自定义格式: $secCustomFormat');
  print('');
  
  // 历史时间戳示例
  print('=== 历史时间戳转换示例 ===');
  
  // 一小时前的时间戳
  final oneHourAgo = now.subtract(const Duration(hours: 1));
  final oneHourAgoTimestamp = DateUtils.toTimestamp(oneHourAgo);
  
  print('一小时前时间戳: $oneHourAgoTimestamp');
  print('转为相对时间: ${DateUtils.timestampToRelativeTimeString(oneHourAgoTimestamp)}');
  print('转为标准格式: ${DateUtils.timestampToDateTimeString(oneHourAgoTimestamp)}');
  print('');
  
  // 昨天的时间戳
  final yesterday = now.subtract(const Duration(days: 1));
  final yesterdayTimestamp = DateUtils.toTimestamp(yesterday);
  
  print('昨天时间戳: $yesterdayTimestamp');
  print('转为相对时间: ${DateUtils.timestampToRelativeTimeString(yesterdayTimestamp)}');
  print('转为中文日期: ${DateUtils.timestampToChineseDateString(yesterdayTimestamp)}');
  print('');
  
  // 一周前的时间戳
  final oneWeekAgo = now.subtract(const Duration(days: 7));
  final oneWeekAgoTimestamp = DateUtils.toTimestamp(oneWeekAgo);
  
  print('一周前时间戳: $oneWeekAgoTimestamp');
  print('转为相对时间: ${DateUtils.timestampToRelativeTimeString(oneWeekAgoTimestamp)}');
  print('转为日期: ${DateUtils.timestampToDateString(oneWeekAgoTimestamp)}');
  print('');
  
  // 常见的API时间戳格式示例
  print('=== 常见API时间戳格式处理 ===');
  
  // 模拟从API获取的时间戳数据
  final apiTimestamps = [
    1640995200000, // 2022-01-01 00:00:00 (毫秒)
    1640995200,    // 2022-01-01 00:00:00 (秒)
    1672531200000, // 2023-01-01 00:00:00 (毫秒)
    1672531200,    // 2023-01-01 00:00:00 (秒)
  ];
  
  print('处理API时间戳数据:');
  for (int i = 0; i < apiTimestamps.length; i++) {
    final timestamp = apiTimestamps[i];
    
    if (timestamp > 1000000000000) {
      // 毫秒级时间戳
      print('毫秒时间戳 $timestamp -> ${DateUtils.timestampToDateTimeString(timestamp)}');
    } else {
      // 秒级时间戳
      print('秒级时间戳 $timestamp -> ${DateUtils.timestampSecondsToDateTimeString(timestamp)}');
    }
  }
  print('');
  
  // 实际应用场景示例
  print('=== 实际应用场景示例 ===');
  
  // 交易记录时间显示
  final tradeTimestamp = DateUtils.toTimestamp(now.subtract(const Duration(minutes: 30)));
  print('交易时间: ${DateUtils.timestampToRelativeTimeString(tradeTimestamp)}');
  print('详细时间: ${DateUtils.timestampToDateTimeString(tradeTimestamp, format: 'MM-dd HH:mm:ss')}');
  
  // 消息时间显示
  final messageTimestamp = DateUtils.toTimestamp(now.subtract(const Duration(hours: 2)));
  print('消息时间: ${DateUtils.timestampToRelativeTimeString(messageTimestamp)}');
  print('完整时间: ${DateUtils.timestampToChineseDateString(messageTimestamp, includeTime: true)}');
  
  // 日志时间显示
  final logTimestamp = DateUtils.toTimestamp(now.subtract(const Duration(days: 3)));
  print('日志时间: ${DateUtils.timestampToDateString(logTimestamp)}');
  print('相对时间: ${DateUtils.timestampToRelativeTimeString(logTimestamp)}');
  
  print('\n✨ 时间戳转换功能演示完成！');
}
