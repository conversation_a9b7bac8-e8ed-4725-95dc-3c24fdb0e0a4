import 'package:financial_app_core/financial_app_core.dart';

/// 工具类使用示例
/// 展示如何在实际项目中使用financial_app_core提供的各种工具类
void main() {
  print('=== financial_app_core 工具类使用示例 ===\n');
  
  // 日期工具类示例
  dateUtilsExample();
  
  // 字符串工具类示例
  stringUtilsExample();
  
  // 数字工具类示例
  numberUtilsExample();
  
  // 验证工具类示例
  validationUtilsExample();
  
  // 格式化工具类示例
  formatUtilsExample();
  
  // 综合应用示例
  comprehensiveExample();
}

/// 日期工具类使用示例
void dateUtilsExample() {
  print('📅 DateUtils 示例:');
  
  final now = DateTime.now();
  final yesterday = now.subtract(const Duration(days: 1));
  final twoHoursAgo = now.subtract(const Duration(hours: 2));
  
  print('当前时间: ${DateUtils.formatDateTime(now)}');
  print('今天日期: ${DateUtils.formatDate(now)}');
  print('中文日期: ${DateUtils.formatChineseDate(now, includeTime: true)}');
  print('相对时间: ${DateUtils.formatRelativeTime(twoHoursAgo)}');
  print('是否今天: ${DateUtils.isToday(now)}');
  print('是否昨天: ${DateUtils.isYesterday(yesterday)}');
  print('星期几: ${DateUtils.getWeekdayName(now)}');
  print('月份: ${DateUtils.getMonthName(now)}');
  print('是否工作日: ${DateUtils.isBusinessDay(now)}');
  print('');
}

/// 字符串工具类使用示例
void stringUtilsExample() {
  print('📝 StringUtils 示例:');
  
  const phone = '***********';
  const email = '<EMAIL>';
  const idCard = '110101199001011234';
  const bankCard = '****************';
  
  print('原始手机号: $phone');
  print('加星手机号: ${StringUtils.maskPhone(phone)}');
  print('原始邮箱: $email');
  print('加星邮箱: ${StringUtils.maskEmail(email)}');
  print('原始身份证: $idCard');
  print('加星身份证: ${StringUtils.maskIdCard(idCard)}');
  print('原始银行卡: $bankCard');
  print('加星银行卡: ${StringUtils.maskBankCard(bankCard)}');
  
  print('首字母大写: ${StringUtils.capitalize("hello world")}');
  print('驼峰转下划线: ${StringUtils.camelToSnake("userName")}');
  print('下划线转驼峰: ${StringUtils.snakeToCamel("user_name")}');
  print('生成随机字符串: ${StringUtils.generateRandom(8)}');
  print('字符串截取: ${StringUtils.truncate("这是一个很长的字符串示例", 10)}');
  print('');
}

/// 数字工具类使用示例
void numberUtilsExample() {
  print('🔢 NumberUtils 示例:');
  
  const price = 1234.5678;
  const volume = 1500000;
  const change = 0.0523;
  
  print('原始价格: $price');
  print('格式化价格: ${NumberUtils.formatPrice(price)}');
  print('格式化数字: ${NumberUtils.formatNumber(price, decimalPlaces: 2)}');
  print('原始成交量: $volume');
  print('格式化大数字: ${NumberUtils.formatLargeNumber(volume)}');
  print('原始涨跌幅: $change');
  print('格式化百分比: ${NumberUtils.formatPercentage(change, showSign: true)}');
  
  print('文件大小: ${NumberUtils.formatFileSize(1048576)}');
  print('安全除法: ${NumberUtils.safeDivide(10, 0, defaultValue: -1)}');
  print('数字范围判断: ${NumberUtils.isInRange(50, 0, 100)}');
  
  final numbers = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10];
  print('数组: $numbers');
  print('平均值: ${NumberUtils.average(numbers)}');
  print('中位数: ${NumberUtils.median(numbers)}');
  print('标准差: ${NumberUtils.standardDeviation(numbers).toStringAsFixed(2)}');
  
  print('数字转中文: ${NumberUtils.numberToChinese(12345)}');
  print('');
}

/// 验证工具类使用示例
void validationUtilsExample() {
  print('✅ ValidationUtils 示例:');
  
  const testPhone = '***********';
  const testEmail = '<EMAIL>';
  const testPassword = 'Abc123456';
  const testIdCard = '110101199001011234';
  
  print('手机号验证 ($testPhone): ${ValidationUtils.isValidPhone(testPhone)}');
  print('邮箱验证 ($testEmail): ${ValidationUtils.isValidEmail(testEmail)}');
  print('密码验证 ($testPassword): ${ValidationUtils.isValidPassword(testPassword)}');
  print('密码强度 ($testPassword): ${ValidationUtils.getPasswordStrength(testPassword)}');
  print('身份证验证 ($testIdCard): ${ValidationUtils.isValidIdCard(testIdCard)}');
  
  print('数字验证 ("123.45"): ${ValidationUtils.isValidNumber("123.45")}');
  print('整数验证 ("123"): ${ValidationUtils.isValidInteger("123")}');
  print('范围验证 ("50", 0-100): ${ValidationUtils.isValidRange("50", 0, 100)}');
  print('长度验证 ("hello", 3-10): ${ValidationUtils.isValidLength("hello", minLength: 3, maxLength: 10)}');
  
  print('中文姓名验证 ("张三"): ${ValidationUtils.isValidChineseName("张三")}');
  print('用户名验证 ("user123"): ${ValidationUtils.isValidUsername("user123")}');
  print('URL验证 ("https://example.com"): ${ValidationUtils.isValidUrl("https://example.com")}');
  print('');
}

/// 格式化工具类使用示例
void formatUtilsExample() {
  print('🎨 FormatUtils 示例:');
  
  const phone = '***********';
  const bankCard = '****************';
  const amount = 1234.56;
  
  print('手机号格式化显示: ${FormatUtils.formatPhoneDisplay(phone, maskMiddle: false)}');
  print('手机号遮罩显示: ${FormatUtils.formatPhoneDisplay(phone, maskMiddle: true)}');
  print('银行卡格式化显示: ${FormatUtils.formatBankCardDisplay(bankCard, maskMiddle: false)}');
  print('银行卡遮罩显示: ${FormatUtils.formatBankCardDisplay(bankCard, maskMiddle: true)}');
  
  print('金额显示: ${FormatUtils.formatAmountDisplay(amount)}');
  print('金额显示(带符号): ${FormatUtils.formatAmountDisplay(amount, showSign: true)}');
  
  print('涨跌幅显示: ${FormatUtils.formatPriceChangeDisplay(5.2, 5.0)}');
  print('交易量显示: ${FormatUtils.formatVolumeDisplay(1500000, unit: "手")}');
  
  print('距离显示: ${FormatUtils.formatDistanceDisplay(1500)}');
  print('进度显示: ${FormatUtils.formatProgressDisplay(75, 100)}');
  print('评分显示: ${FormatUtils.formatRatingDisplay(4.5, showStars: true)}');
  print('温度显示: ${FormatUtils.formatTemperatureDisplay(25.5)}');
  
  final birthDate = DateTime(1990, 5, 15);
  print('年龄显示: ${FormatUtils.formatAgeDisplay(birthDate)}');
  
  final targetTime = DateTime.now().add(const Duration(days: 1, hours: 2, minutes: 30));
  print('倒计时显示: ${FormatUtils.formatCountdownDisplay(targetTime)}');
  print('');
}

/// 综合应用示例
void comprehensiveExample() {
  print('🚀 综合应用示例:');
  
  // 模拟用户信息
  final userInfo = {
    'name': '张三',
    'phone': '***********',
    'email': '<EMAIL>',
    'idCard': '110101199001011234',
    'bankCard': '****************',
    'birthDate': DateTime(1990, 1, 1),
  };
  
  // 模拟交易信息
  final tradeInfo = {
    'symbol': 'BTCUSDT',
    'price': 45678.9123,
    'change': 1234.56,
    'changePercent': 2.78,
    'volume': 1500000,
    'tradeTime': DateTime.now().subtract(const Duration(minutes: 5)),
  };
  
  print('=== 用户信息展示 ===');
  print('姓名: ${userInfo['name']}');
  print('手机: ${FormatUtils.formatPhoneDisplay(userInfo['phone'] as String, maskMiddle: true)}');
  print('邮箱: ${StringUtils.maskEmail(userInfo['email'] as String)}');
  print('身份证: ${StringUtils.maskIdCard(userInfo['idCard'] as String)}');
  print('银行卡: ${FormatUtils.formatBankCardDisplay(userInfo['bankCard'] as String, maskMiddle: true)}');
  print('年龄: ${FormatUtils.formatAgeDisplay(userInfo['birthDate'] as DateTime)}');
  
  print('\n=== 交易信息展示 ===');
  print('交易对: ${tradeInfo['symbol']}');
  print('当前价格: ${NumberUtils.formatPrice(tradeInfo['price'] as double)}');
  print('涨跌额: ${FormatUtils.formatPriceChangeDisplay(
    tradeInfo['change'] as double, 
    tradeInfo['changePercent'] as double
  )}');
  print('成交量: ${FormatUtils.formatVolumeDisplay(tradeInfo['volume'] as int, unit: "BTC")}');
  print('交易时间: ${DateUtils.formatRelativeTime(tradeInfo['tradeTime'] as DateTime)}');
  
  print('\n=== 数据验证结果 ===');
  print('手机号有效性: ${ValidationUtils.isValidPhone(userInfo['phone'] as String)}');
  print('邮箱有效性: ${ValidationUtils.isValidEmail(userInfo['email'] as String)}');
  print('身份证有效性: ${ValidationUtils.isValidIdCard(userInfo['idCard'] as String)}');
  
  print('\n=== 统计信息 ===');
  final recentPrices = [45000.0, 45200.0, 44800.0, 45678.9123, 46000.0];
  print('最近价格: ${recentPrices.map((p) => NumberUtils.formatPrice(p)).join(', ')}');
  print('平均价格: ${NumberUtils.formatPrice(NumberUtils.average(recentPrices))}');
  print('价格波动: ${NumberUtils.standardDeviation(recentPrices).toStringAsFixed(2)}');
  
  print('\n✨ 工具类示例演示完成！');
}
