# API 请求日志增强指南

## 概述

本指南介绍了如何使用增强的 API 请求日志功能，该功能已集成到 `ApiServiceImpl` 和 `OptimizedApiService` 中，提供详细的请求和响应日志记录。

## 主要特性

### 🚀 请求日志记录
- **详细的请求信息**：记录 HTTP 方法、完整 URL、请求头、请求参数
- **敏感信息过滤**：自动隐藏密码、token 等敏感数据
- **请求时间追踪**：记录请求开始时间，用于计算耗时
- **超时配置显示**：显示连接、接收、发送超时设置

### ✅ 响应日志记录
- **响应状态信息**：HTTP 状态码、状态消息
- **性能指标**：请求耗时（毫秒）
- **数据大小统计**：响应数据大小
- **重定向信息**：是否重定向及重定向地址

### ❌ 错误日志记录
- **详细错误信息**：错误类型、错误消息、堆栈跟踪
- **请求上下文**：失败请求的完整信息
- **响应数据**：错误响应的详细内容

### 🔄 重试日志记录（仅优化版）
- **重试策略**：重试次数、延迟时间
- **重试结果**：成功或失败的详细信息
- **指数退避**：显示退避延迟时间

## 日志格式示例

### 成功请求日志
```
[Market] 🚀 API请求开始
{
  "method": "GET",
  "url": "https://api.example.com/market/ticker/AAPL",
  "base_url": "https://api.example.com",
  "path": "/market/ticker/AAPL",
  "query_parameters": {"symbol": "AAPL", "interval": "1d"},
  "headers": {"Content-Type": "application/json", "Authorization": "***HIDDEN***"},
  "request_data": null,
  "content_type": "application/json",
  "timeout": {"connect": 10000, "receive": 30000, "send": 30000}
}

[Market] ✅ API请求成功
{
  "method": "GET",
  "url": "https://api.example.com/market/ticker/AAPL",
  "status_code": 200,
  "status_message": "OK",
  "duration_ms": 245,
  "response_headers": {"content-type": "application/json"},
  "response_data": {"symbol": "AAPL", "price": 150.25, "change": 2.15},
  "data_size": 156,
  "is_redirect": false,
  "redirect_location": null
}
```

### 失败请求日志
```
[Auth] ❌ API请求失败
{
  "method": "POST",
  "url": "https://api.example.com/auth/login",
  "error_type": "DioExceptionType.badResponse",
  "error_message": "Http status error [401]",
  "status_code": 401,
  "duration_ms": 123,
  "response_data": {"error": "Invalid credentials"},
  "request_data": {"username": "user123", "password": "***HIDDEN***"},
  "stack_trace": "..."
}
```

### 重试日志（优化版）
```
[Market] 🔄 API请求重试
{
  "method": "GET",
  "url": "https://api.example.com/market/data",
  "retry_count": 1,
  "max_retries": 3,
  "delay_ms": 1000,
  "error_type": "DioExceptionType.connectionTimeout",
  "error_message": "Connection timeout",
  "status_code": null
}

[Market] ✅ API请求重试成功
{
  "method": "GET",
  "url": "https://api.example.com/market/data",
  "retry_count": 1,
  "status_code": 200
}
```

## 敏感信息过滤

系统会自动过滤以下敏感信息：

### 请求头过滤
- `authorization`
- `cookie`
- `x-api-key`
- `x-auth-token`
- `bearer`
- `token`
- `password`
- `secret`

### 请求/响应数据过滤
- `password`
- `token`
- `secret`
- `key`
- `pin`
- `otp`
- `cvv`
- `ssn`
- `credit_card`
- `bank_account`
- `authorization`
- `refresh_token`
- `access_token`

## 数据截断规则

为了避免日志过大，系统会自动截断长数据：

- **字符串数据**：超过 500 字符的响应数据会被截断
- **列表数据**：超过 10 个元素的列表会被截断
- **Map 数据**：超过 1000 字符的 Map 会添加截断标记
- **请求数据**：超过 100 字符的字符串请求数据会被截断

## 配置日志级别

可以通过 `LogConfigManager` 调整日志显示级别：

```dart
// 切换到简洁模式（隐藏调试日志）
await LogConfigManager.switchToConciseMode();

// 切换到静默模式（只显示警告和错误）
await LogConfigManager.switchToQuietMode();

// 切换到开发模式（显示所有日志）
await LogConfigManager.switchToDevelopmentMode();
```

## 使用建议

### 开发环境
- 使用开发模式配置，显示所有详细日志
- 关注请求耗时，优化性能瓶颈
- 检查敏感信息是否正确过滤

### 生产环境
- 使用生产模式配置，减少日志输出
- 重点关注错误和警告日志
- 定期清理日志文件

### 调试技巧
1. **性能分析**：通过 `duration_ms` 字段分析请求耗时
2. **错误排查**：结合错误类型和响应数据快速定位问题
3. **重试监控**：观察重试日志，调整重试策略
4. **数据验证**：检查请求和响应数据格式是否正确

## 注意事项

1. **日志安全**：敏感信息已自动过滤，但仍需定期审查日志内容
2. **性能影响**：详细日志会增加一定的性能开销，生产环境建议适当调整级别
3. **存储空间**：日志文件会占用存储空间，需要定期清理
4. **网络流量**：如果启用远程日志上报，会产生额外的网络流量

## 扩展功能

### 自定义日志处理
可以通过继承或修改现有的日志处理逻辑来实现自定义需求：

```dart
// 自定义敏感信息过滤规则
Map<String, dynamic> customSanitizeHeaders(Map<String, dynamic> headers) {
  // 添加自定义过滤逻辑
  return headers;
}
```

### 集成监控系统
日志数据可以集成到监控系统中：
- **性能监控**：收集请求耗时数据
- **错误监控**：统计错误率和错误类型
- **业务监控**：分析 API 调用模式

这个增强的日志系统为开发和运维提供了强大的工具，帮助快速定位问题、优化性能和监控系统健康状态。
