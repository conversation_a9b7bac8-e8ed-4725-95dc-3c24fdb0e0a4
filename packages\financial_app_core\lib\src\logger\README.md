# 企业级日志系统 - AppLogger

## 概述

AppLogger 是一个为企业级 Flutter 应用设计的高级日志系统，提供了丰富的功能来满足生产环境的需求。

## 主要特性

### 🎯 核心功能
- **多级别日志**：支持 debug、info、warning、error、fatal 五个级别
- **模块化日志**：每个模块可以独立记录日志
- **敏感信息过滤**：自动过滤密码、token 等敏感数据
- **结构化日志**：支持 JSON 格式的结构化日志输出

### 📊 企业级功能
- **日志统计**：实时统计各模块的日志数量
- **性能监控**：专门的性能日志记录
- **业务事件追踪**：记录关键业务事件
- **用户行为分析**：追踪用户操作行为
- **系统健康检查**：组件健康状态监控

### 🔧 高级特性
- **文件轮转**：自动轮转大日志文件
- **远程上报**：支持远程日志收集（可集成 Sentry 等）
- **环境配置**：开发/生产环境不同配置
- **日志导出**：支持日志文件导出
- **内存优化**：智能管理内存中的日志缓存

## 快速开始

### 1. 初始化

```dart
import 'package:financial_app_core/financial_app_core.dart';

// 开发环境初始化
await AppLogger.initialize(config: LogConfig.development());

// 生产环境初始化
await AppLogger.initialize(config: LogConfig.production());

// 自定义配置
await AppLogger.initialize(
  config: LogConfig(
    minLevel: LogLevel.info,
    enableFileLogging: true,
    enableConsoleLogging: false,
    enableRemoteLogging: true,
    remoteEndpoint: 'https://logs.yourcompany.com',
    structuredLogging: true,
  ),
);
```

### 2. 基础日志记录

```dart
// 基础日志
AppLogger.logModule('Auth', LogLevel.info, '用户登录成功');
AppLogger.logModule('Market', LogLevel.error, '获取市场数据失败', error: exception);

// 带元数据的日志
AppLogger.logModule(
  'Trading', 
  LogLevel.info, 
  '交易订单创建',
  metadata: {
    'order_id': '12345',
    'symbol': 'AAPL',
    'quantity': 100,
  },
);
```

### 3. 专业日志类型

```dart
// 性能日志
final stopwatch = Stopwatch()..start();
// ... 执行操作
stopwatch.stop();
AppLogger.logPerformance('API', 'fetchUserData', stopwatch.elapsed);

// 业务事件日志
AppLogger.logBusinessEvent('Trading', 'order_placed', {
  'order_id': '12345',
  'symbol': 'AAPL',
  'side': 'buy',
  'quantity': 100,
});

// 用户行为日志
AppLogger.logUserAction('UI', 'button_click', userId, context: {
  'screen': 'dashboard',
  'button': 'refresh_portfolio',
});

// 健康检查日志
AppLogger.logHealthCheck('Database', 'connection', isConnected, 
  details: 'Connection pool: 5/10 active');
```

## 配置选项

### LogConfig 参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `minLevel` | LogLevel | info | 最小日志级别 |
| `enableFileLogging` | bool | true | 启用文件日志 |
| `enableConsoleLogging` | bool | true | 启用控制台日志 |
| `enableRemoteLogging` | bool | false | 启用远程日志 |
| `maxFileSize` | int | 10MB | 单个日志文件最大大小 |
| `maxLogFiles` | int | 5 | 保留的日志文件数量 |
| `remoteEndpoint` | String? | null | 远程日志服务端点 |
| `structuredLogging` | bool | false | 启用结构化日志 |

### 预设配置

```dart
// 开发环境配置
LogConfig.development()
- minLevel: debug
- enableFileLogging: true
- enableConsoleLogging: true
- structuredLogging: false

// 生产环境配置
LogConfig.production()
- minLevel: warning
- enableFileLogging: true
- enableConsoleLogging: false
- enableRemoteLogging: true
- structuredLogging: true
```

## 日志分析

### 获取统计信息

```dart
// 获取所有日志统计
final stats = AppLogger.getLogStatistics();
print('总错误日志: ${stats['Auth:error'] ?? 0}');

// 获取特定模块统计
final authStats = AppLogger.getModuleStatistics('Auth');
print('Auth模块信息日志: ${authStats['info'] ?? 0}');

// 获取最近日志
final recentLogs = AppLogger.getRecentLogs(limit: 50);
for (final log in recentLogs) {
  print('${log.timestamp}: [${log.module}] ${log.message}');
}
```

### 日志导出

```dart
// 导出日志文件
final logFile = await AppLogger.exportLogs();
// 可以通过分享功能发送给技术支持

// 清理旧日志
await AppLogger.cleanOldLogs();
```

## 最佳实践

### 1. 模块命名规范
```dart
// 推荐的模块命名
AppLogger.logModule('Auth', ...);        // 认证模块
AppLogger.logModule('Market', ...);      // 市场数据模块
AppLogger.logModule('Trading', ...);     // 交易模块
AppLogger.logModule('UI', ...);          // 用户界面
AppLogger.logModule('Network', ...);     // 网络请求
```

### 2. 日志级别使用指南
- **debug**: 详细的调试信息，仅在开发环境使用
- **info**: 一般信息，如用户操作、业务流程
- **warning**: 警告信息，如性能问题、非致命错误
- **error**: 错误信息，如异常、失败的操作
- **fatal**: 严重错误，如系统崩溃、数据损坏

### 3. 敏感信息处理
系统会自动过滤以下敏感信息：
- password, token, authorization
- secret, key, pin, otp, cvv
- ssn, credit_card, bank_account

### 4. 性能考虑
- 在生产环境中设置合适的日志级别
- 定期清理旧日志文件
- 使用异步日志记录避免阻塞主线程
- 合理设置日志文件大小限制

## 迁移指南

### 从旧的 Logger 迁移

```dart
// 旧方式
Logger().info('用户登录');

// 新方式
AppLogger.logModule('Auth', LogLevel.info, '用户登录');
```

旧的 Logger 类已标记为 @deprecated，但仍然可用以保持向后兼容性。建议逐步迁移到新的 AppLogger。

## 故障排除

### 常见问题

1. **日志文件过大**
   - 检查 `maxFileSize` 配置
   - 确保定期调用 `cleanOldLogs()`

2. **性能影响**
   - 在生产环境中提高 `minLevel`
   - 禁用不必要的日志输出

3. **远程日志不工作**
   - 检查网络连接
   - 验证 `remoteEndpoint` 配置
   - 实现 `RemoteLogOutput` 的具体逻辑

## 扩展功能

### 集成第三方服务

```dart
// 集成 Sentry
class SentryLogOutput extends LogOutput {
  @override
  void output(OutputEvent event) {
    // 发送到 Sentry
  }
}

// 集成 Firebase Crashlytics
class FirebaseLogOutput extends LogOutput {
  @override
  void output(OutputEvent event) {
    // 发送到 Firebase
  }
}
```

## 版本历史

- **v2.0.0**: 企业级功能重构
- **v1.0.0**: 基础日志功能
