import 'package:logger/logger.dart';

/// 简洁日志打印器
///
/// 专为简洁模式设计，隐藏堆栈跟踪、文件路径等详细信息
/// 只显示最核心的日志内容
class ConcisePrinter extends LogPrinter {
  final bool showTime;
  final bool showLevel;
  final bool colors;
  final Map<Level, String> levelEmojis;
  final Map<Level, AnsiColor?> levelColors;

  ConcisePrinter({
    this.showTime = false,
    this.showLevel = true,
    this.colors = true,
  }) : levelEmojis = {
         Level.trace: '🔍',
         Level.debug: '🐛',
         Level.info: 'ℹ️',
         Level.warning: '⚠️',
         Level.error: '❌',
         Level.fatal: '💀',
       },
       levelColors = {
         Level.trace: AnsiColor.fg(8),
         Level.debug: AnsiColor.fg(8),
         Level.info: AnsiColor.fg(12),
         Level.warning: AnsiColor.fg(208),
         Level.error: AnsiColor.fg(196),
         Level.fatal: AnsiColor.fg(199),
       };

  @override
  List<String> log(LogEvent event) {
    final messageColor = _getLevelColor(event.level);
    final message = _formatMessage(event);

    if (colors && messageColor != null) {
      return [messageColor(message)];
    } else {
      return [message];
    }
  }

  String _formatMessage(LogEvent event) {
    final buffer = StringBuffer();

    // 添加时间戳（如果启用）
    if (showTime) {
      final time = DateTime.now();
      buffer.write(
        '${time.hour.toString().padLeft(2, '0')}:'
        '${time.minute.toString().padLeft(2, '0')}:'
        '${time.second.toString().padLeft(2, '0')} ',
      );
    }

    // 添加级别标识（如果启用）
    if (showLevel) {
      final emoji = levelEmojis[event.level] ?? '';
      final levelName = event.level.name.toUpperCase().padRight(7);
      buffer.write('$emoji $levelName ');
    }

    // 添加消息内容
    buffer.write(event.message);

    // 如果有错误信息，添加到同一行（简化显示）
    if (event.error != null) {
      buffer.write(' | Error: ${event.error}');
    }

    return buffer.toString();
  }

  AnsiColor? _getLevelColor(Level level) {
    if (!colors) return null;
    return levelColors[level];
  }
}

/// 超简洁日志打印器
///
/// 最简化的日志输出，只显示消息内容
class MinimalPrinter extends LogPrinter {
  final bool colors;
  final Map<Level, AnsiColor?> levelColors;

  MinimalPrinter({this.colors = true})
    : levelColors = {
        Level.trace: AnsiColor.fg(8),
        Level.debug: AnsiColor.fg(8),
        Level.info: AnsiColor.fg(12),
        Level.warning: AnsiColor.fg(208),
        Level.error: AnsiColor.fg(196),
        Level.fatal: AnsiColor.fg(199),
      };

  @override
  List<String> log(LogEvent event) {
    final messageColor = _getLevelColor(event.level);
    final message = event.message.toString();

    if (colors && messageColor != null) {
      return [messageColor(message)];
    } else {
      return [message];
    }
  }

  AnsiColor? _getLevelColor(Level level) {
    if (!colors) return null;
    return levelColors[level];
  }
}

/// 单行日志打印器
///
/// 将所有信息压缩到一行，适合快速浏览
class SingleLinePrinter extends LogPrinter {
  final bool showTime;
  final bool showLevel;
  final bool showModule;
  final bool colors;
  final Map<Level, String> levelEmojis;
  final Map<Level, AnsiColor?> levelColors;

  SingleLinePrinter({
    this.showTime = false,
    this.showLevel = true,
    this.showModule = true,
    this.colors = true,
  }) : levelEmojis = {
         Level.trace: '🔍',
         Level.debug: '🐛',
         Level.info: 'ℹ️',
         Level.warning: '⚠️',
         Level.error: '❌',
         Level.fatal: '💀',
       },
       levelColors = {
         Level.trace: AnsiColor.fg(8),
         Level.debug: AnsiColor.fg(8),
         Level.info: AnsiColor.fg(12),
         Level.warning: AnsiColor.fg(208),
         Level.error: AnsiColor.fg(196),
         Level.fatal: AnsiColor.fg(199),
       };

  @override
  List<String> log(LogEvent event) {
    final messageColor = _getLevelColor(event.level);
    final message = _formatSingleLine(event);

    if (colors && messageColor != null) {
      return [messageColor(message)];
    } else {
      return [message];
    }
  }

  String _formatSingleLine(LogEvent event) {
    final parts = <String>[];

    // 时间戳
    if (showTime) {
      final time = DateTime.now();
      parts.add(
        '${time.hour.toString().padLeft(2, '0')}:'
        '${time.minute.toString().padLeft(2, '0')}:'
        '${time.second.toString().padLeft(2, '0')}',
      );
    }

    // 级别
    if (showLevel) {
      final emoji = levelEmojis[event.level] ?? '';
      parts.add('$emoji${event.level.name.toUpperCase()}');
    }

    // 模块名称（从消息中提取）
    if (showModule) {
      final message = event.message.toString();
      final moduleMatch = RegExp(r'^\[([^\]]+)\]').firstMatch(message);
      if (moduleMatch != null) {
        parts.add('[${moduleMatch.group(1)}]');
      }
    }

    // 消息内容（移除模块名称前缀）
    String cleanMessage = event.message.toString();
    cleanMessage = cleanMessage.replaceFirst(RegExp(r'^\[[^\]]+\]\s*'), '');
    parts.add(cleanMessage);

    // 错误信息
    if (event.error != null) {
      parts.add('Error: ${event.error}');
    }

    return parts.join(' | ');
  }

  AnsiColor? _getLevelColor(Level level) {
    if (!colors) return null;
    return levelColors[level];
  }
}

/// 日志打印器工厂
class LogPrinterFactory {
  /// 根据配置创建合适的打印器
  static LogPrinter createPrinter({
    required bool showDebugLogs,
    required bool showTimestamps,
    required bool showModuleNames,
    required bool structuredLogging,
    bool showStackTrace = false, // 新增堆栈跟踪控制参数
  }) {
    if (structuredLogging) {
      return PrettyPrinter(
        methodCount: showStackTrace ? 2 : 0, // 根据配置控制堆栈跟踪
        errorMethodCount: showStackTrace ? 8 : 0,
        lineLength: 120,
        colors: false,
        printEmojis: false,
        dateTimeFormat: DateTimeFormat.none,
        stackTraceBeginIndex: showStackTrace ? 0 : 999, // 隐藏堆栈跟踪
      );
    }

    if (!showDebugLogs && !showTimestamps) {
      // 超简洁模式 - 只显示消息
      return MinimalPrinter();
    } else if (!showDebugLogs) {
      // 简洁模式 - 单行显示
      return SingleLinePrinter(
        showTime: showTimestamps,
        showLevel: true,
        showModule: showModuleNames,
      );
    } else {
      // 标准模式 - 使用 PrettyPrinter，但控制堆栈跟踪
      return PrettyPrinter(
        methodCount: showStackTrace ? 2 : 0, // 根据配置控制堆栈跟踪
        errorMethodCount: showStackTrace ? 8 : 0,
        lineLength: 120,
        colors: true,
        printEmojis: true,
        dateTimeFormat: showTimestamps
            ? DateTimeFormat.onlyTimeAndSinceStart
            : DateTimeFormat.none,
        stackTraceBeginIndex: showStackTrace ? 0 : 999, // 隐藏堆栈跟踪
      );
    }
  }
}
