import 'dart:convert';

import 'app_logger.dart';
import 'log_config_manager.dart';

/// 日志聚合器
///
/// 提供日志收集、分析、导出和性能监控功能
class LogAggregator {
  static final LogAggregator _instance = LogAggregator._internal();
  factory LogAggregator() => _instance;
  LogAggregator._internal();

  /// 获取单例实例
  static LogAggregator get instance => _instance;

  /// 日志缓存
  final List<AppLogRecord> _logBuffer = <AppLogRecord>[];

  /// 最大缓存大小
  static const int _maxBufferSize = 10000;

  /// 性能指标
  final Map<String, PerformanceMetrics> _performanceMetrics = {};

  /// 错误模式分析
  final Map<String, ErrorPattern> _errorPatterns = {};

  /// 添加日志记录
  void addLogRecord(AppLogRecord record) {
    _logBuffer.add(record);

    // 记录统计信息
    LogConfigManager.recordLogEvent(record.module, record.level);

    // 更新性能指标
    _updatePerformanceMetrics(record);

    // 分析错误模式
    if (record.level == LogLevel.error) {
      _analyzeErrorPattern(record);
    }

    // 保持缓存大小
    if (_logBuffer.length > _maxBufferSize) {
      _logBuffer.removeAt(0);
    }
  }

  /// 更新性能指标
  void _updatePerformanceMetrics(AppLogRecord record) {
    final key = record.module;

    if (!_performanceMetrics.containsKey(key)) {
      _performanceMetrics[key] = PerformanceMetrics(module: key);
    }

    final metrics = _performanceMetrics[key]!;
    metrics.totalLogs++;

    if (record.level == LogLevel.error) {
      metrics.errorCount++;
    } else if (record.level == LogLevel.warning) {
      metrics.warningCount++;
    }

    // 计算平均响应时间（如果有duration信息）
    if (record.metadata != null &&
        record.metadata!.containsKey('duration_ms')) {
      final duration = record.metadata!['duration_ms'] as int?;
      if (duration != null) {
        metrics.addDuration(duration);
      }
    }

    metrics.lastActivity = record.timestamp;
  }

  /// 分析错误模式
  void _analyzeErrorPattern(AppLogRecord record) {
    final errorMessage = record.message;
    final module = record.module;

    // 简单的错误分类
    String category = 'unknown';
    if (errorMessage.contains('网络') ||
        errorMessage.contains('network') ||
        errorMessage.contains('connection')) {
      category = 'network';
    } else if (errorMessage.contains('API') ||
        errorMessage.contains('请求') ||
        errorMessage.contains('response')) {
      category = 'api';
    } else if (errorMessage.contains('数据库') ||
        errorMessage.contains('database') ||
        errorMessage.contains('sql')) {
      category = 'database';
    } else if (errorMessage.contains('权限') ||
        errorMessage.contains('auth') ||
        errorMessage.contains('token')) {
      category = 'auth';
    } else if (errorMessage.contains('解析') ||
        errorMessage.contains('parse') ||
        errorMessage.contains('json')) {
      category = 'parsing';
    }

    final key = '$module:$category';

    if (!_errorPatterns.containsKey(key)) {
      _errorPatterns[key] = ErrorPattern(module: module, category: category);
    }

    final pattern = _errorPatterns[key]!;
    pattern.count++;
    pattern.lastOccurrence = record.timestamp;
    pattern.recentMessages.add(errorMessage);

    // 保持最近10条错误消息
    if (pattern.recentMessages.length > 10) {
      pattern.recentMessages.removeAt(0);
    }
  }

  /// 获取性能报告
  Map<String, dynamic> getPerformanceReport() {
    final report = <String, dynamic>{};

    // 总体性能指标
    int totalLogs = 0;
    int totalErrors = 0;
    int totalWarnings = 0;
    double totalAvgDuration = 0;
    int modulesWithDuration = 0;

    for (final metrics in _performanceMetrics.values) {
      totalLogs += metrics.totalLogs;
      totalErrors += metrics.errorCount;
      totalWarnings += metrics.warningCount;

      if (metrics.averageDuration > 0) {
        totalAvgDuration += metrics.averageDuration;
        modulesWithDuration++;
      }
    }

    report['summary'] = {
      'total_logs': totalLogs,
      'total_errors': totalErrors,
      'total_warnings': totalWarnings,
      'error_rate': totalLogs > 0
          ? '${(totalErrors / totalLogs * 100).toStringAsFixed(2)}%'
          : '0%',
      'warning_rate': totalLogs > 0
          ? '${(totalWarnings / totalLogs * 100).toStringAsFixed(2)}%'
          : '0%',
      'average_duration': modulesWithDuration > 0
          ? '${(totalAvgDuration / modulesWithDuration).toStringAsFixed(2)}ms'
          : 'N/A',
    };

    // 按模块的性能指标
    final moduleReports = <String, Map<String, dynamic>>{};

    for (final entry in _performanceMetrics.entries) {
      final module = entry.key;
      final metrics = entry.value;

      moduleReports[module] = {
        'total_logs': metrics.totalLogs,
        'error_count': metrics.errorCount,
        'warning_count': metrics.warningCount,
        'error_rate': metrics.totalLogs > 0
            ? '${(metrics.errorCount / metrics.totalLogs * 100).toStringAsFixed(2)}%'
            : '0%',
        'average_duration': metrics.averageDuration > 0
            ? '${metrics.averageDuration.toStringAsFixed(2)}ms'
            : 'N/A',
        'last_activity': metrics.lastActivity.toIso8601String(),
      };
    }

    report['modules'] = moduleReports;

    return report;
  }

  /// 获取错误分析报告
  Map<String, dynamic> getErrorAnalysisReport() {
    final report = <String, dynamic>{};

    // 错误统计
    final errorStats = <String, dynamic>{};
    int totalErrorPatterns = 0;

    for (final pattern in _errorPatterns.values) {
      totalErrorPatterns += pattern.count;
    }

    errorStats['total_error_patterns'] = _errorPatterns.length;
    errorStats['total_errors'] = totalErrorPatterns;

    // 按类别统计
    final categoryStats = <String, int>{};
    for (final pattern in _errorPatterns.values) {
      categoryStats[pattern.category] =
          (categoryStats[pattern.category] ?? 0) + pattern.count;
    }

    errorStats['by_category'] = categoryStats;

    // 最频繁的错误模式
    final sortedPatterns = _errorPatterns.entries.toList()
      ..sort((a, b) => b.value.count.compareTo(a.value.count));

    final topErrors = <Map<String, dynamic>>[];
    for (final entry in sortedPatterns.take(10)) {
      final pattern = entry.value;
      topErrors.add({
        'module': pattern.module,
        'category': pattern.category,
        'count': pattern.count,
        'last_occurrence': pattern.lastOccurrence.toIso8601String(),
        'recent_messages': pattern.recentMessages.take(3).toList(),
      });
    }

    errorStats['top_errors'] = topErrors;

    report['error_analysis'] = errorStats;

    return report;
  }

  /// 导出日志数据
  Future<String> exportLogs({
    String format = 'json',
    DateTime? startTime,
    DateTime? endTime,
    List<String>? modules,
    List<LogLevel>? levels,
  }) async {
    // 过滤日志
    var filteredLogs = _logBuffer.where((log) {
      if (startTime != null && log.timestamp.isBefore(startTime)) return false;
      if (endTime != null && log.timestamp.isAfter(endTime)) return false;
      if (modules != null && !modules.contains(log.module)) return false;
      if (levels != null && !levels.contains(log.level)) return false;
      return true;
    }).toList();

    // 根据格式导出
    switch (format.toLowerCase()) {
      case 'json':
        return _exportAsJson(filteredLogs);
      case 'csv':
        return _exportAsCsv(filteredLogs);
      case 'txt':
        return _exportAsText(filteredLogs);
      default:
        throw ArgumentError('不支持的导出格式: $format');
    }
  }

  /// 导出为JSON格式
  String _exportAsJson(List<AppLogRecord> logs) {
    final data = {
      'export_info': {
        'timestamp': DateTime.now().toIso8601String(),
        'total_logs': logs.length,
        'format': 'json',
      },
      'performance_report': getPerformanceReport(),
      'error_analysis': getErrorAnalysisReport(),
      'logs': logs.map((log) => log.toJson()).toList(),
    };

    return const JsonEncoder.withIndent('  ').convert(data);
  }

  /// 导出为CSV格式
  String _exportAsCsv(List<AppLogRecord> logs) {
    final buffer = StringBuffer();

    // CSV头部
    buffer.writeln('timestamp,module,level,message,error,metadata');

    // CSV数据
    for (final log in logs) {
      final timestamp = log.timestamp.toIso8601String();
      final module = log.module;
      final level = log.level.name;
      final message = _escapeCsvField(log.message);
      final error = log.error != null
          ? _escapeCsvField(log.error.toString())
          : '';
      final metadata = log.metadata != null
          ? _escapeCsvField(jsonEncode(log.metadata))
          : '';

      buffer.writeln('$timestamp,$module,$level,$message,$error,$metadata');
    }

    return buffer.toString();
  }

  /// 导出为文本格式
  String _exportAsText(List<AppLogRecord> logs) {
    final buffer = StringBuffer();

    buffer.writeln('日志导出报告');
    buffer.writeln('=' * 50);
    buffer.writeln('导出时间: ${DateTime.now()}');
    buffer.writeln('日志数量: ${logs.length}');
    buffer.writeln('');

    // 性能报告
    final perfReport = getPerformanceReport();
    buffer.writeln('性能概览:');
    final summary = perfReport['summary'] as Map<String, dynamic>;
    summary.forEach((key, value) {
      buffer.writeln('  $key: $value');
    });
    buffer.writeln('');

    // 日志详情
    buffer.writeln('日志详情:');
    buffer.writeln('-' * 50);

    for (final log in logs) {
      buffer.writeln(
        '[${log.timestamp}] [${log.level.name.toUpperCase()}] [${log.module}] ${log.message}',
      );
      if (log.error != null) {
        buffer.writeln('  错误: ${log.error}');
      }
      if (log.metadata != null && log.metadata!.isNotEmpty) {
        buffer.writeln('  元数据: ${jsonEncode(log.metadata)}');
      }
      buffer.writeln('');
    }

    return buffer.toString();
  }

  /// 转义CSV字段
  String _escapeCsvField(String field) {
    if (field.contains(',') || field.contains('"') || field.contains('\n')) {
      return '"${field.replaceAll('"', '""')}"';
    }
    return field;
  }

  /// 清除日志缓存
  void clearLogs() {
    _logBuffer.clear();
    _performanceMetrics.clear();
    _errorPatterns.clear();
    AppLogger.logModule('LogAggregator', LogLevel.info, '📊 日志聚合器缓存已清除');
  }

  /// 获取缓存状态
  Map<String, dynamic> getBufferStatus() {
    return {
      'buffer_size': _logBuffer.length,
      'max_buffer_size': _maxBufferSize,
      'buffer_usage':
          '${(_logBuffer.length / _maxBufferSize * 100).toStringAsFixed(1)}%',
      'performance_metrics_count': _performanceMetrics.length,
      'error_patterns_count': _errorPatterns.length,
    };
  }
}

/// 应用日志记录
class AppLogRecord {
  final DateTime timestamp;
  final String module;
  final LogLevel level;
  final String message;
  final Object? error;
  final StackTrace? stackTrace;
  final Map<String, dynamic>? metadata;

  AppLogRecord({
    required this.timestamp,
    required this.module,
    required this.level,
    required this.message,
    this.error,
    this.stackTrace,
    this.metadata,
  });

  Map<String, dynamic> toJson() {
    return {
      'timestamp': timestamp.toIso8601String(),
      'module': module,
      'level': level.name,
      'message': message,
      'error': error?.toString(),
      'stack_trace': stackTrace?.toString(),
      'metadata': metadata,
    };
  }
}

/// 性能指标
class PerformanceMetrics {
  final String module;
  int totalLogs = 0;
  int errorCount = 0;
  int warningCount = 0;
  DateTime lastActivity = DateTime.now();

  final List<int> _durations = <int>[];

  PerformanceMetrics({required this.module});

  void addDuration(int duration) {
    _durations.add(duration);

    // 保持最近100个持续时间记录
    if (_durations.length > 100) {
      _durations.removeAt(0);
    }
  }

  double get averageDuration {
    if (_durations.isEmpty) return 0;
    return _durations.reduce((a, b) => a + b) / _durations.length;
  }
}

/// 错误模式
class ErrorPattern {
  final String module;
  final String category;
  int count = 0;
  DateTime lastOccurrence = DateTime.now();
  final List<String> recentMessages = <String>[];

  ErrorPattern({required this.module, required this.category});
}

/// 日志监控面板
class LogMonitoringDashboard {
  static final LogMonitoringDashboard _instance =
      LogMonitoringDashboard._internal();
  factory LogMonitoringDashboard() => _instance;
  LogMonitoringDashboard._internal();

  /// 获取单例实例
  static LogMonitoringDashboard get instance => _instance;

  /// 实时监控状态
  bool _isMonitoring = false;

  /// 监控阈值配置
  final Map<String, dynamic> _thresholds = {
    'error_rate_threshold': 5.0, // 错误率阈值 (%)
    'response_time_threshold': 1000, // 响应时间阈值 (ms)
    'log_frequency_threshold': 100, // 日志频率阈值 (条/分钟)
  };

  /// 告警回调
  final List<Function(AlertInfo)> _alertCallbacks = [];

  /// 开始监控
  void startMonitoring() {
    if (_isMonitoring) return;

    _isMonitoring = true;
    AppLogger.logModule('LogMonitor', LogLevel.info, '🔍 开始日志监控');

    // 定期检查告警条件
    _scheduleAlertCheck();
  }

  /// 停止监控
  void stopMonitoring() {
    _isMonitoring = false;
    AppLogger.logModule('LogMonitor', LogLevel.info, '⏹️ 停止日志监控');
  }

  /// 添加告警回调
  void addAlertCallback(Function(AlertInfo) callback) {
    _alertCallbacks.add(callback);
  }

  /// 设置监控阈值
  void setThreshold(String key, dynamic value) {
    _thresholds[key] = value;
    AppLogger.logModule(
      'LogMonitor',
      LogLevel.info,
      '⚙️ 更新监控阈值: $key = $value',
    );
  }

  /// 定期检查告警条件
  void _scheduleAlertCheck() {
    if (!_isMonitoring) return;

    Future.delayed(const Duration(minutes: 1), () {
      _checkAlertConditions();
      _scheduleAlertCheck();
    });
  }

  /// 检查告警条件
  void _checkAlertConditions() {
    final aggregator = LogAggregator.instance;
    final perfReport = aggregator.getPerformanceReport();
    final errorReport = aggregator.getErrorAnalysisReport();

    // 检查错误率
    _checkErrorRate(perfReport);

    // 检查响应时间
    _checkResponseTime(perfReport);

    // 检查错误模式
    _checkErrorPatterns(errorReport);
  }

  /// 检查错误率告警
  void _checkErrorRate(Map<String, dynamic> perfReport) {
    final summary = perfReport['summary'] as Map<String, dynamic>;
    final errorRateStr = summary['error_rate'] as String;
    final errorRate = double.tryParse(errorRateStr.replaceAll('%', '')) ?? 0;

    final threshold = _thresholds['error_rate_threshold'] as double;

    if (errorRate > threshold) {
      _triggerAlert(
        AlertInfo(
          type: AlertType.errorRate,
          severity: AlertSeverity.high,
          message: '错误率过高: $errorRateStr (阈值: $threshold%)',
          data: {'current_rate': errorRate, 'threshold': threshold},
        ),
      );
    }
  }

  /// 检查响应时间告警
  void _checkResponseTime(Map<String, dynamic> perfReport) {
    final modules = perfReport['modules'] as Map<String, Map<String, dynamic>>;
    final threshold = _thresholds['response_time_threshold'] as int;

    for (final entry in modules.entries) {
      final module = entry.key;
      final moduleData = entry.value;
      final avgDurationStr = moduleData['average_duration'] as String;

      if (avgDurationStr != 'N/A') {
        final avgDuration =
            double.tryParse(avgDurationStr.replaceAll('ms', '')) ?? 0;

        if (avgDuration > threshold) {
          _triggerAlert(
            AlertInfo(
              type: AlertType.responseTime,
              severity: AlertSeverity.medium,
              message:
                  '模块 $module 响应时间过长: ${avgDuration}ms (阈值: ${threshold}ms)',
              data: {
                'module': module,
                'current_time': avgDuration,
                'threshold': threshold,
              },
            ),
          );
        }
      }
    }
  }

  /// 检查错误模式告警
  void _checkErrorPatterns(Map<String, dynamic> errorReport) {
    final errorAnalysis = errorReport['error_analysis'] as Map<String, dynamic>;
    final topErrors = errorAnalysis['top_errors'] as List<dynamic>;

    for (final error in topErrors.take(3)) {
      final errorData = error as Map<String, dynamic>;
      final count = errorData['count'] as int;

      // 如果某个错误模式出现次数过多，触发告警
      if (count > 10) {
        _triggerAlert(
          AlertInfo(
            type: AlertType.errorPattern,
            severity: AlertSeverity.medium,
            message:
                '错误模式频繁出现: ${errorData['module']}:${errorData['category']} ($count 次)',
            data: errorData,
          ),
        );
      }
    }
  }

  /// 触发告警
  void _triggerAlert(AlertInfo alert) {
    AppLogger.logModule(
      'LogMonitor',
      LogLevel.warning,
      '🚨 触发告警: ${alert.message}',
    );

    for (final callback in _alertCallbacks) {
      try {
        callback(alert);
      } catch (e) {
        AppLogger.logModule('LogMonitor', LogLevel.error, '告警回调执行失败', error: e);
      }
    }
  }

  /// 获取监控状态
  Map<String, dynamic> getMonitoringStatus() {
    return {
      'is_monitoring': _isMonitoring,
      'thresholds': Map.from(_thresholds),
      'alert_callbacks_count': _alertCallbacks.length,
      'buffer_status': LogAggregator.instance.getBufferStatus(),
    };
  }
}

/// 告警信息
class AlertInfo {
  final AlertType type;
  final AlertSeverity severity;
  final String message;
  final Map<String, dynamic> data;
  final DateTime timestamp;

  AlertInfo({
    required this.type,
    required this.severity,
    required this.message,
    required this.data,
  }) : timestamp = DateTime.now();
}

/// 告警类型
enum AlertType { errorRate, responseTime, errorPattern, logFrequency }

/// 告警严重程度
enum AlertSeverity { low, medium, high, critical }
