import 'app_logger.dart';

/// 日志配置管理器
///
/// 提供动态切换日志显示模式的功能，包括敏感信息过滤、性能监控等高级功能
class LogConfigManager {
  static LogConfig _currentConfig = LogConfig.development();

  /// 敏感信息关键词列表
  static const List<String> _sensitiveKeys = [
    'password',
    'pwd',
    'passwd',
    'token',
    'access_token',
    'refresh_token',
    'jwt',
    'secret',
    'api_secret',
    'client_secret',
    'key',
    'private_key',
    'public_key',
    'api_key',
    'auth',
    'authorization',
    'bearer',
    'pin',
    'otp',
    'code',
    'verification_code',
    'card',
    'credit_card',
    'bank_account',
    'ssn',
    'social_security',
    'id_number',
  ];

  /// 性能监控数据
  static final Map<String, int> _logCounts = <String, int>{};
  static final Map<String, int> _errorCounts = <String, int>{};
  static final Map<String, List<DateTime>> _logTimestamps =
      <String, List<DateTime>>{};

  /// 日志过滤规则
  static final List<RegExp> _filterPatterns = <RegExp>[];

  /// 获取当前配置
  static LogConfig get currentConfig => _currentConfig;

  /// 获取敏感信息关键词列表
  static List<String> get sensitiveKeys => List.unmodifiable(_sensitiveKeys);

  /// 添加自定义敏感信息关键词
  static void addSensitiveKey(String key) {
    if (!_sensitiveKeys.contains(key.toLowerCase())) {
      // 由于_sensitiveKeys是const，我们需要创建一个新的列表来管理自定义关键词
      _customSensitiveKeys.add(key.toLowerCase());
    }
  }

  /// 自定义敏感信息关键词
  static final List<String> _customSensitiveKeys = <String>[];

  /// 获取所有敏感信息关键词（包括自定义的）
  static List<String> get allSensitiveKeys => [
    ..._sensitiveKeys,
    ..._customSensitiveKeys,
  ];

  /// 过滤敏感信息
  static Map<String, dynamic> filterSensitiveData(Map<String, dynamic>? data) {
    if (data == null) return {};

    final filtered = <String, dynamic>{};

    for (final entry in data.entries) {
      final key = entry.key.toLowerCase();
      final isSensitive = allSensitiveKeys.any(
        (sensitiveKey) => key.contains(sensitiveKey),
      );

      if (isSensitive) {
        filtered[entry.key] = '***FILTERED***';
      } else if (entry.value is Map<String, dynamic>) {
        filtered[entry.key] = filterSensitiveData(
          entry.value as Map<String, dynamic>,
        );
      } else {
        filtered[entry.key] = entry.value;
      }
    }

    return filtered;
  }

  /// 记录日志统计信息
  static void recordLogEvent(String module, LogLevel level) {
    final key = '$module:${level.name}';
    _logCounts[key] = (_logCounts[key] ?? 0) + 1;

    if (level == LogLevel.error) {
      _errorCounts[module] = (_errorCounts[module] ?? 0) + 1;
    }

    // 记录时间戳用于性能分析
    _logTimestamps.putIfAbsent(module, () => <DateTime>[]);
    _logTimestamps[module]!.add(DateTime.now());

    // 保持最近1000条记录
    if (_logTimestamps[module]!.length > 1000) {
      _logTimestamps[module]!.removeAt(0);
    }
  }

  /// 更新日志配置
  static Future<void> updateConfig(LogConfig newConfig) async {
    _currentConfig = newConfig;
    // 重新初始化日志系统
    await AppLogger.initialize(config: newConfig);
  }

  /// 切换到开发模式（显示所有日志）
  static Future<void> switchToDevelopmentMode() async {
    await updateConfig(LogConfig.development());
    AppLogger.logModule('LogConfig', LogLevel.info, '已切换到开发模式 - 显示所有日志');
  }

  /// 切换到简洁模式（隐藏调试日志）
  static Future<void> switchToConciseMode() async {
    await updateConfig(LogConfig.concise());
    AppLogger.logModule('LogConfig', LogLevel.info, '已切换到简洁模式 - 隐藏调试日志');
  }

  /// 切换到静默模式（只显示警告和错误）
  static Future<void> switchToQuietMode() async {
    await updateConfig(LogConfig.quiet());
    AppLogger.logModule('LogConfig', LogLevel.info, '已切换到静默模式 - 只显示警告和错误');
  }

  /// 切换到生产模式
  static Future<void> switchToProductionMode() async {
    await updateConfig(LogConfig.production());
    AppLogger.logModule('LogConfig', LogLevel.warning, '已切换到生产模式');
  }

  /// 切换调试日志显示状态
  static Future<void> toggleDebugLogs() async {
    final newConfig = LogConfig(
      minLevel: _currentConfig.minLevel,
      enableFileLogging: _currentConfig.enableFileLogging,
      enableConsoleLogging: _currentConfig.enableConsoleLogging,
      enableRemoteLogging: _currentConfig.enableRemoteLogging,
      maxFileSize: _currentConfig.maxFileSize,
      maxLogFiles: _currentConfig.maxLogFiles,
      remoteEndpoint: _currentConfig.remoteEndpoint,
      structuredLogging: _currentConfig.structuredLogging,
      showDebugLogs: !_currentConfig.showDebugLogs,
      showVerboseLogs: _currentConfig.showVerboseLogs,
      showModuleNames: _currentConfig.showModuleNames,
      showTimestamps: _currentConfig.showTimestamps,
      showStackTrace: _currentConfig.showStackTrace,
    );

    await updateConfig(newConfig);

    final status = newConfig.showDebugLogs ? '显示' : '隐藏';
    AppLogger.logModule('LogConfig', LogLevel.info, '调试日志已切换为: $status');
  }

  /// 切换模块名称显示状态
  static Future<void> toggleModuleNames() async {
    final newConfig = LogConfig(
      minLevel: _currentConfig.minLevel,
      enableFileLogging: _currentConfig.enableFileLogging,
      enableConsoleLogging: _currentConfig.enableConsoleLogging,
      enableRemoteLogging: _currentConfig.enableRemoteLogging,
      maxFileSize: _currentConfig.maxFileSize,
      maxLogFiles: _currentConfig.maxLogFiles,
      remoteEndpoint: _currentConfig.remoteEndpoint,
      structuredLogging: _currentConfig.structuredLogging,
      showDebugLogs: _currentConfig.showDebugLogs,
      showVerboseLogs: _currentConfig.showVerboseLogs,
      showModuleNames: !_currentConfig.showModuleNames,
      showTimestamps: _currentConfig.showTimestamps,
      showStackTrace: _currentConfig.showStackTrace,
    );

    await updateConfig(newConfig);

    final status = newConfig.showModuleNames ? '显示' : '隐藏';
    AppLogger.logModule('LogConfig', LogLevel.info, '模块名称已切换为: $status');
  }

  /// 切换时间戳显示状态
  static Future<void> toggleTimestamps() async {
    final newConfig = LogConfig(
      minLevel: _currentConfig.minLevel,
      enableFileLogging: _currentConfig.enableFileLogging,
      enableConsoleLogging: _currentConfig.enableConsoleLogging,
      enableRemoteLogging: _currentConfig.enableRemoteLogging,
      maxFileSize: _currentConfig.maxFileSize,
      maxLogFiles: _currentConfig.maxLogFiles,
      remoteEndpoint: _currentConfig.remoteEndpoint,
      structuredLogging: _currentConfig.structuredLogging,
      showDebugLogs: _currentConfig.showDebugLogs,
      showVerboseLogs: _currentConfig.showVerboseLogs,
      showModuleNames: _currentConfig.showModuleNames,
      showTimestamps: !_currentConfig.showTimestamps,
      showStackTrace: _currentConfig.showStackTrace,
    );

    await updateConfig(newConfig);

    final status = newConfig.showTimestamps ? '显示' : '隐藏';
    AppLogger.logModule('LogConfig', LogLevel.info, '时间戳已切换为: $status');
  }

  /// 切换堆栈跟踪显示状态
  static Future<void> toggleStackTrace() async {
    final newConfig = LogConfig(
      minLevel: _currentConfig.minLevel,
      enableFileLogging: _currentConfig.enableFileLogging,
      enableConsoleLogging: _currentConfig.enableConsoleLogging,
      enableRemoteLogging: _currentConfig.enableRemoteLogging,
      maxFileSize: _currentConfig.maxFileSize,
      maxLogFiles: _currentConfig.maxLogFiles,
      remoteEndpoint: _currentConfig.remoteEndpoint,
      structuredLogging: _currentConfig.structuredLogging,
      showDebugLogs: _currentConfig.showDebugLogs,
      showVerboseLogs: _currentConfig.showVerboseLogs,
      showModuleNames: _currentConfig.showModuleNames,
      showTimestamps: _currentConfig.showTimestamps,
      showStackTrace: !_currentConfig.showStackTrace,
    );

    await updateConfig(newConfig);

    final status = newConfig.showStackTrace ? '显示' : '隐藏';
    AppLogger.logModule('LogConfig', LogLevel.info, '堆栈跟踪已切换为: $status');
  }

  /// 隐藏堆栈跟踪信息（快捷方法）
  static Future<void> hideStackTrace() async {
    if (_currentConfig.showStackTrace) {
      await toggleStackTrace();
    }
  }

  /// 显示堆栈跟踪信息（快捷方法）
  static Future<void> showStackTrace() async {
    if (!_currentConfig.showStackTrace) {
      await toggleStackTrace();
    }
  }

  /// 获取当前配置状态描述
  static String getConfigStatus() {
    final config = _currentConfig;
    final buffer = StringBuffer();

    buffer.writeln('📊 当前日志配置状态:');
    buffer.writeln('  • 最小级别: ${config.minLevel.name}');
    buffer.writeln('  • 调试日志: ${config.showDebugLogs ? "显示" : "隐藏"}');
    buffer.writeln('  • 详细日志: ${config.showVerboseLogs ? "显示" : "隐藏"}');
    buffer.writeln('  • 模块名称: ${config.showModuleNames ? "显示" : "隐藏"}');
    buffer.writeln('  • 时间戳: ${config.showTimestamps ? "显示" : "隐藏"}');
    buffer.writeln('  • 堆栈跟踪: ${config.showStackTrace ? "显示" : "隐藏"}');
    buffer.writeln('  • 控制台输出: ${config.enableConsoleLogging ? "启用" : "禁用"}');
    buffer.writeln('  • 文件输出: ${config.enableFileLogging ? "启用" : "禁用"}');
    buffer.writeln('  • 远程输出: ${config.enableRemoteLogging ? "启用" : "禁用"}');

    return buffer.toString();
  }

  /// 打印当前配置状态
  static void printConfigStatus() {
    AppLogger.logModule('LogConfig', LogLevel.info, getConfigStatus());
  }

  /// 预设配置选项
  static const Map<String, String> presetConfigs = {
    'development': '开发模式 - 显示所有日志',
    'concise': '简洁模式 - 隐藏调试日志',
    'quiet': '静默模式 - 只显示警告和错误',
    'production': '生产模式 - 最小日志输出',
  };

  /// 根据预设名称切换配置
  static Future<void> switchToPreset(String presetName) async {
    switch (presetName.toLowerCase()) {
      case 'development':
        await switchToDevelopmentMode();
        break;
      case 'concise':
        await switchToConciseMode();
        break;
      case 'quiet':
        await switchToQuietMode();
        break;
      case 'production':
        await switchToProductionMode();
        break;
      default:
        AppLogger.logModule(
          'LogConfig',
          LogLevel.warning,
          '未知的预设配置: $presetName',
        );
        break;
    }
  }

  /// 列出所有可用的预设配置
  static void listPresets() {
    final buffer = StringBuffer();
    buffer.writeln('📋 可用的日志配置预设:');

    presetConfigs.forEach((key, description) {
      buffer.writeln('  • $key: $description');
    });

    AppLogger.logModule('LogConfig', LogLevel.info, buffer.toString());
  }

  /// 获取日志统计信息
  static Map<String, dynamic> getLogStatistics() {
    final stats = <String, dynamic>{};

    // 总体统计
    int totalLogs = 0;
    int totalErrors = 0;

    for (final count in _logCounts.values) {
      totalLogs += count;
    }

    for (final count in _errorCounts.values) {
      totalErrors += count;
    }

    stats['total_logs'] = totalLogs;
    stats['total_errors'] = totalErrors;
    stats['error_rate'] = totalLogs > 0
        ? '${(totalErrors / totalLogs * 100).toStringAsFixed(2)}%'
        : '0%';

    // 按模块统计
    final moduleStats = <String, Map<String, dynamic>>{};

    for (final entry in _logCounts.entries) {
      final parts = entry.key.split(':');
      if (parts.length == 2) {
        final module = parts[0];
        final level = parts[1];

        moduleStats.putIfAbsent(module, () => <String, dynamic>{});
        moduleStats[module]![level] = entry.value;

        // 计算模块总数
        final currentTotal = moduleStats[module]!['total'] as int? ?? 0;
        moduleStats[module]!['total'] = currentTotal + entry.value;
      }
    }

    stats['modules'] = moduleStats;

    // 错误频率分析
    final errorFrequency = <String, dynamic>{};
    for (final entry in _errorCounts.entries) {
      final timestamps = _logTimestamps[entry.key] ?? [];
      if (timestamps.isNotEmpty) {
        final now = DateTime.now();
        final recentErrors = timestamps
            .where((t) => now.difference(t).inMinutes <= 60)
            .length;

        errorFrequency[entry.key] = {
          'total_errors': entry.value,
          'errors_last_hour': recentErrors,
        };
      }
    }

    stats['error_frequency'] = errorFrequency;

    return stats;
  }

  /// 打印日志统计信息
  static void printLogStatistics() {
    final stats = getLogStatistics();
    final buffer = StringBuffer();

    buffer.writeln('📊 日志统计信息:');
    buffer.writeln('  • 总日志数: ${stats['total_logs']}');
    buffer.writeln('  • 总错误数: ${stats['total_errors']}');
    buffer.writeln('  • 错误率: ${stats['error_rate']}');

    final modules = stats['modules'] as Map<String, Map<String, dynamic>>;
    if (modules.isNotEmpty) {
      buffer.writeln('  • 按模块统计:');
      modules.forEach((module, moduleStats) {
        buffer.writeln('    - $module: ${moduleStats['total']} 条日志');
        if (moduleStats.containsKey('error')) {
          buffer.writeln('      错误: ${moduleStats['error']} 条');
        }
      });
    }

    AppLogger.logModule('LogConfig', LogLevel.info, buffer.toString());
  }

  /// 重置统计信息
  static void resetStatistics() {
    _logCounts.clear();
    _errorCounts.clear();
    _logTimestamps.clear();
    AppLogger.logModule('LogConfig', LogLevel.info, '📊 日志统计信息已重置');
  }

  /// 添加日志过滤规则
  static void addFilterPattern(String pattern) {
    try {
      final regex = RegExp(pattern);
      _filterPatterns.add(regex);
      AppLogger.logModule('LogConfig', LogLevel.info, '✅ 已添加日志过滤规则: $pattern');
    } catch (e) {
      AppLogger.logModule(
        'LogConfig',
        LogLevel.error,
        '❌ 无效的正则表达式: $pattern',
        error: e,
      );
    }
  }

  /// 清除所有过滤规则
  static void clearFilterPatterns() {
    _filterPatterns.clear();
    AppLogger.logModule('LogConfig', LogLevel.info, '🧹 已清除所有日志过滤规则');
  }

  /// 检查消息是否应该被过滤
  static bool shouldFilterMessage(String message) {
    for (final pattern in _filterPatterns) {
      if (pattern.hasMatch(message)) {
        return true;
      }
    }
    return false;
  }

  /// 导出日志配置
  static Map<String, dynamic> exportConfig() {
    return {
      'config': {
        'minLevel': _currentConfig.minLevel.name,
        'enableFileLogging': _currentConfig.enableFileLogging,
        'enableConsoleLogging': _currentConfig.enableConsoleLogging,
        'enableRemoteLogging': _currentConfig.enableRemoteLogging,
        'showDebugLogs': _currentConfig.showDebugLogs,
        'showVerboseLogs': _currentConfig.showVerboseLogs,
        'showModuleNames': _currentConfig.showModuleNames,
        'showTimestamps': _currentConfig.showTimestamps,
        'showStackTrace': _currentConfig.showStackTrace,
      },
      'sensitive_keys': allSensitiveKeys,
      'filter_patterns': _filterPatterns.map((p) => p.pattern).toList(),
      'statistics': getLogStatistics(),
      'export_time': DateTime.now().toIso8601String(),
    };
  }
}
