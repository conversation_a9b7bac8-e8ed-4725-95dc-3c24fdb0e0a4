import 'app_logger.dart';

/// 模块日志管理器基类
/// 
/// 为每个业务模块提供统一的日志接口，简化日志调用
abstract class ModuleLogger {
  /// 模块名称
  String get moduleName;

  /// 调试日志
  void debug(String message, {Map<String, dynamic>? metadata}) {
    AppLogger.logModule(moduleName, LogLevel.debug, message, metadata: metadata);
  }

  /// 信息日志
  void info(String message, {Map<String, dynamic>? metadata}) {
    AppLogger.logModule(moduleName, LogLevel.info, message, metadata: metadata);
  }

  /// 警告日志
  void warning(String message, {Map<String, dynamic>? metadata}) {
    AppLogger.logModule(moduleName, LogLevel.warning, message, metadata: metadata);
  }

  /// 错误日志
  void error(String message, {
    dynamic error,
    StackTrace? stackTrace,
    Map<String, dynamic>? metadata,
  }) {
    AppLogger.logModule(
      moduleName,
      LogLevel.error,
      message,
      error: error,
      stackTrace: stackTrace,
      metadata: metadata,
    );
  }

  /// 严重错误日志
  void fatal(String message, {
    dynamic error,
    StackTrace? stackTrace,
    Map<String, dynamic>? metadata,
  }) {
    AppLogger.logModule(
      moduleName,
      LogLevel.fatal,
      message,
      error: error,
      stackTrace: stackTrace,
      metadata: metadata,
    );
  }

  /// 网络请求日志
  void logNetworkRequest(
    String method,
    String url, {
    Map<String, dynamic>? headers,
    dynamic body,
  }) {
    AppLogger.logNetworkRequest(moduleName, method, url,
        headers: headers, body: body);
  }

  /// 网络响应日志
  void logNetworkResponse(
    String method,
    String url,
    int statusCode, {
    dynamic responseData,
    Duration? duration,
  }) {
    AppLogger.logNetworkResponse(
      moduleName,
      method,
      url,
      statusCode,
      responseData: responseData,
      duration: duration,
    );
  }

  /// 性能日志
  void logPerformance(
    String operation,
    Duration duration, {
    Map<String, dynamic>? metadata,
  }) {
    AppLogger.logPerformance(moduleName, operation, duration,
        metadata: metadata);
  }

  /// 业务事件日志
  void logBusinessEvent(String eventName, Map<String, dynamic> eventData) {
    AppLogger.logBusinessEvent(moduleName, eventName, eventData);
  }

  /// 用户行为日志
  void logUserAction(
    String action,
    String? userId, {
    Map<String, dynamic>? context,
  }) {
    AppLogger.logUserAction(moduleName, action, userId, context: context);
  }

  /// 健康检查日志
  void logHealthCheck(
    String component,
    bool isHealthy, {
    String? details,
    Map<String, dynamic>? metrics,
  }) {
    AppLogger.logHealthCheck(
      moduleName,
      component,
      isHealthy,
      details: details,
      metrics: metrics,
    );
  }

  /// 安全日志
  void logSecure(
    LogLevel level,
    String message, {
    Map<String, dynamic>? data,
  }) {
    AppLogger.secureLog(moduleName, level, message, data: data);
  }
}

/// 核心模块日志管理器
class CoreLogger extends ModuleLogger {
  static final CoreLogger _instance = CoreLogger._internal();
  factory CoreLogger() => _instance;
  CoreLogger._internal();

  @override
  String get moduleName => 'Core';
}

/// 主应用模块日志管理器
class MainLogger extends ModuleLogger {
  static final MainLogger _instance = MainLogger._internal();
  factory MainLogger() => _instance;
  MainLogger._internal();

  @override
  String get moduleName => 'Main';
}

/// 通用日志工厂
class LoggerFactory {
  static final Map<String, ModuleLogger> _loggers = {};

  /// 获取或创建模块日志管理器
  static ModuleLogger getLogger(String moduleName) {
    return _loggers.putIfAbsent(
      moduleName,
      () => _GenericModuleLogger(moduleName),
    );
  }

  /// 注册自定义日志管理器
  static void registerLogger(String moduleName, ModuleLogger logger) {
    _loggers[moduleName] = logger;
  }

  /// 获取所有已注册的日志管理器
  static Map<String, ModuleLogger> getAllLoggers() {
    return Map.unmodifiable(_loggers);
  }
}

/// 通用模块日志管理器实现
class _GenericModuleLogger extends ModuleLogger {
  final String _moduleName;

  _GenericModuleLogger(this._moduleName);

  @override
  String get moduleName => _moduleName;
}
