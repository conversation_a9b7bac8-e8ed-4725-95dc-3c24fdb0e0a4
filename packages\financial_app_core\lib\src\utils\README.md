# 工具类使用说明

financial_app_core模块提供了一系列常用的工具类，方便日常开发使用。

## 工具类列表

### 1. DateUtils - 日期工具类

提供日期转换、格式化、计算等功能。

```dart
import 'package:financial_app_core/financial_app_core.dart';

// 格式化日期
String dateStr = DateUtils.formatDate(DateTime.now()); // 2024-01-01
String dateTimeStr = DateUtils.formatDateTime(DateTime.now()); // 2024-01-01 12:30:45

// 中文日期格式
String chineseDate = DateUtils.formatChineseDate(DateTime.now()); // 2024年01月01日

// 相对时间
String relativeTime = DateUtils.formatRelativeTime(DateTime.now().subtract(Duration(hours: 2))); // 2小时前

// 日期判断
bool isToday = DateUtils.isToday(DateTime.now()); // true
bool isThisWeek = DateUtils.isThisWeek(DateTime.now()); // true

// 时间戳转换
DateTime dateTime = DateUtils.fromTimestamp(1640995200000);
int timestamp = DateUtils.toTimestamp(DateTime.now());

// 时间戳直接转为格式化字符串
String dateStr = DateUtils.timestampToDateString(1640995200000); // 2022-01-01
String dateTimeStr = DateUtils.timestampToDateTimeString(1640995200000); // 2022-01-01 08:00:00
String chineseStr = DateUtils.timestampToChineseDateString(1640995200000); // 2022年01月01日
String relativeStr = DateUtils.timestampToRelativeTimeString(timestamp); // 刚刚

// 秒级时间戳转换
DateTime secDateTime = DateUtils.fromTimestampSeconds(1640995200);
String secDateStr = DateUtils.timestampSecondsToDateString(1640995200); // 2022-01-01
String secDateTimeStr = DateUtils.timestampSecondsToDateTimeString(1640995200); // 2022-01-01 08:00:00

// 工作日计算
DateTime nextBusinessDay = DateUtils.addBusinessDays(DateTime.now(), 5);
bool isWorkDay = DateUtils.isBusinessDay(DateTime.now());
```

### 2. StringUtils - 字符串工具类

提供字符串处理、格式化、验证等功能。

```dart
import 'package:financial_app_core/financial_app_core.dart';

// 空值判断
bool isEmpty = StringUtils.isEmpty(null); // true
bool isNotBlank = StringUtils.isNotBlank("  "); // false

// 手机号加星号
String maskedPhone = StringUtils.maskPhone("***********"); // 138****5678

// 身份证加星号
String maskedId = StringUtils.maskIdCard("110101********1234"); // 110101********1234

// 银行卡号加星号
String maskedCard = StringUtils.maskBankCard("****************"); // 6222************7890

// 邮箱加星号
String maskedEmail = StringUtils.maskEmail("<EMAIL>"); // u***<EMAIL>

// 字符串转换
String capitalized = StringUtils.capitalize("hello world"); // Hello world
String camelCase = StringUtils.snakeToCamel("user_name"); // userName
String snakeCase = StringUtils.camelToSnake("userName"); // user_name

// 生成随机字符串
String randomStr = StringUtils.generateRandom(8); // 随机8位字符串

// 字符串截取
String truncated = StringUtils.truncate("很长的字符串", 5); // 很长的字...

// Base64编码
String encoded = StringUtils.toBase64("Hello World");
String decoded = StringUtils.fromBase64(encoded);

// 字符串相似度
double similarity = StringUtils.similarity("hello", "hallo"); // 0.8
```

### 3. NumberUtils - 数字工具类

提供数字格式化、计算、转换等功能。

```dart
import 'package:financial_app_core/financial_app_core.dart';

// 数字格式化
String formatted = NumberUtils.formatNumber(1234.567, decimalPlaces: 2); // 1,234.57

// 价格格式化
String price = NumberUtils.formatPrice(123.456); // 123.4560 USDT

// 百分比格式化
String percentage = NumberUtils.formatPercentage(0.15, showSign: true); // +15.00%

// 大数字格式化
String largeNum = NumberUtils.formatLargeNumber(1500000); // 1.5M

// 文件大小格式化
String fileSize = NumberUtils.formatFileSize(1048576); // 1.00 MB

// 涨跌幅计算
String priceChange = NumberUtils.calculatePriceChange(100, 105); // *****%

// 安全除法
double result = NumberUtils.safeDivide(10, 0, defaultValue: 0); // 0

// 数字范围判断
bool inRange = NumberUtils.isInRange(50, 0, 100); // true

// 数字限制
num clamped = NumberUtils.clamp(150, 0, 100); // 100

// 统计计算
List<num> numbers = [1, 2, 3, 4, 5];
double avg = NumberUtils.average(numbers); // 3.0
double median = NumberUtils.median(numbers); // 3.0
double stdDev = NumberUtils.standardDeviation(numbers); // 1.58

// 数字转中文
String chinese = NumberUtils.numberToChinese(12345); // 一万二千三百四十五

// 浮点数比较
bool isEqual = NumberUtils.isEqual(0.1 + 0.2, 0.3); // true
```

### 4. ValidationUtils - 验证工具类

提供常用数据格式验证功能。

```dart
import 'package:financial_app_core/financial_app_core.dart';

// 手机号验证
bool isValidPhone = ValidationUtils.isValidPhone("***********"); // true

// 邮箱验证
bool isValidEmail = ValidationUtils.isValidEmail("<EMAIL>"); // true

// 身份证验证
bool isValidId = ValidationUtils.isValidIdCard("110101********1234"); // true

// 密码强度验证
bool isValidPwd = ValidationUtils.isValidPassword("Abc123456"); // true
int pwdStrength = ValidationUtils.getPasswordStrength("Abc123456"); // 1 (中等)

// 银行卡号验证
bool isValidCard = ValidationUtils.isValidBankCard("****************"); // true

// URL验证
bool isValidUrl = ValidationUtils.isValidUrl("https://example.com"); // true

// IP地址验证
bool isValidIP = ValidationUtils.isValidIP("***********"); // true

// 数字验证
bool isValidNum = ValidationUtils.isValidNumber("123.45"); // true
bool isValidInt = ValidationUtils.isValidInteger("123"); // true

// 范围验证
bool inRange = ValidationUtils.isValidRange("50", 0, 100); // true

// 长度验证
bool validLength = ValidationUtils.isValidLength("hello", minLength: 3, maxLength: 10); // true

// 中文姓名验证
bool isValidName = ValidationUtils.isValidChineseName("张三"); // true

// 用户名验证
bool isValidUsername = ValidationUtils.isValidUsername("user123"); // true

// QQ号验证
bool isValidQQ = ValidationUtils.isValidQQ("********"); // true

// 微信号验证
bool isValidWechat = ValidationUtils.isValidWechat("wechat_123"); // true

// 车牌号验证
bool isValidPlate = ValidationUtils.isValidPlateNumber("京A12345"); // true
```

### 5. FormatUtils - 通用格式化工具类

提供各种数据的格式化显示功能。

```dart
import 'package:financial_app_core/financial_app_core.dart';

// 手机号格式化显示
String phoneDisplay = FormatUtils.formatPhoneDisplay("***********"); // 138 1234 5678
String maskedPhone = FormatUtils.formatPhoneDisplay("***********", maskMiddle: true); // 138 **** 5678

// 银行卡号格式化显示
String cardDisplay = FormatUtils.formatBankCardDisplay("****************"); // 6222 0212 3456 7890

// 身份证号格式化显示
String idDisplay = FormatUtils.formatIdCardDisplay("110101********1234"); // 110101 ******** 1234

// 金额格式化显示
String amountDisplay = FormatUtils.formatAmountDisplay(1234.56); // ¥1,234.56

// 涨跌幅格式化显示
String changeDisplay = FormatUtils.formatPriceChangeDisplay(5.2, 5.0); // ***** (*****%)

// 交易量格式化显示
String volumeDisplay = FormatUtils.formatVolumeDisplay(1500000, unit: "手"); // 1.5M 手

// 时间范围格式化显示
String timeRange = FormatUtils.formatTimeRangeDisplay(
  DateTime.now(),
  DateTime.now().add(Duration(hours: 2))
); // 01-01 12:00 - 01-01 14:00

// 文件大小格式化显示
String sizeDisplay = FormatUtils.formatFileSizeDisplay(1048576); // 1.00 MB

// 进度格式化显示
String progressDisplay = FormatUtils.formatProgressDisplay(75, 100); // 75/100 (75.00%)

// 地址格式化显示
String addressDisplay = FormatUtils.formatAddressDisplay("北京市", "朝阳区", "三里屯", "某某大厦");

// 评分格式化显示
String ratingDisplay = FormatUtils.formatRatingDisplay(4.5, showStars: true); // 4.5 ★★★★☆

// 距离格式化显示
String distanceDisplay = FormatUtils.formatDistanceDisplay(1500); // 1.5km

// 温度格式化显示
String tempDisplay = FormatUtils.formatTemperatureDisplay(25.5); // 25.5°C

// 年龄格式化显示
String ageDisplay = FormatUtils.formatAgeDisplay(DateTime(1990, 1, 1)); // 34岁

// 倒计时格式化显示
String countdownDisplay = FormatUtils.formatCountdownDisplay(
  DateTime.now().add(Duration(days: 1, hours: 2, minutes: 30))
); // 1天02时30分00秒
```

## 使用建议

1. **导入方式**：统一从 `financial_app_core` 包导入，无需单独导入各个工具类
2. **性能考虑**：工具类方法都是静态方法，无需实例化，性能较好
3. **错误处理**：大部分方法都有空值保护，但建议在使用前进行必要的参数检查
4. **扩展性**：如需添加新的工具方法，建议在对应的工具类中添加，保持代码组织的一致性

## 常用组合使用示例

```dart
// 用户信息显示
String displayUserInfo(String phone, String name, String idCard) {
  final maskedPhone = StringUtils.maskPhone(phone);
  final maskedId = StringUtils.maskIdCard(idCard);
  final formattedPhone = FormatUtils.formatPhoneDisplay(maskedPhone, maskMiddle: false);
  
  return "姓名: $name\n手机: $formattedPhone\n身份证: $maskedId";
}

// 交易信息显示
String displayTradeInfo(double price, double change, int volume, DateTime time) {
  final priceStr = NumberUtils.formatPrice(price);
  final changeStr = NumberUtils.formatPercentage(change, showSign: true);
  final volumeStr = FormatUtils.formatVolumeDisplay(volume);
  final timeStr = DateUtils.formatDateTime(time, format: 'MM-dd HH:mm');
  
  return "价格: $priceStr\n涨跌: $changeStr\n成交量: $volumeStr\n时间: $timeStr";
}

// 表单验证
bool validateUserForm(String phone, String email, String password) {
  return ValidationUtils.isValidPhone(phone) &&
         ValidationUtils.isValidEmail(email) &&
         ValidationUtils.isValidPassword(password);
}
```
