# DateUtils 时间戳转换功能详解

## 功能概述

DateUtils 现在提供了完整的时间戳转换功能，支持毫秒级和秒级时间戳，可以直接转换为各种格式的日期字符串，无需先转为 DateTime 对象。

## 新增方法列表

### 毫秒级时间戳转换

| 方法名 | 功能描述 | 示例输入 | 示例输出 |
|--------|----------|----------|----------|
| `timestampToDateString()` | 转为标准日期格式 | `1640995200000` | `2022-01-01` |
| `timestampToDateTimeString()` | 转为标准日期时间格式 | `1640995200000` | `2022-01-01 08:00:00` |
| `timestampToChineseDateString()` | 转为中文日期格式 | `1640995200000` | `2022年01月01日` |
| `timestampToRelativeTimeString()` | 转为相对时间格式 | `当前时间戳` | `刚刚` |

### 秒级时间戳转换

| 方法名 | 功能描述 | 示例输入 | 示例输出 |
|--------|----------|----------|----------|
| `fromTimestampSeconds()` | 转为DateTime对象 | `1640995200` | `DateTime对象` |
| `timestampSecondsToDateString()` | 转为标准日期格式 | `1640995200` | `2022-01-01` |
| `timestampSecondsToDateTimeString()` | 转为标准日期时间格式 | `1640995200` | `2022-01-01 08:00:00` |

### 辅助方法

| 方法名 | 功能描述 | 示例 |
|--------|----------|------|
| `toTimestampSeconds()` | DateTime转秒级时间戳 | `DateTime.now()` → `1751599026` |

## 使用场景

### 1. API数据处理
```dart
// 处理从API获取的时间戳数据
final apiResponse = {
  'created_at': 1640995200000,  // 毫秒级
  'updated_at': 1640995200,     // 秒级
};

// 直接转换显示
String createdTime = DateUtils.timestampToDateTimeString(apiResponse['created_at']);
String updatedTime = DateUtils.timestampSecondsToDateTimeString(apiResponse['updated_at']);
```

### 2. 交易记录显示
```dart
// 交易时间显示
final tradeTimestamp = 1751595426160;
String tradeTime = DateUtils.timestampToRelativeTimeString(tradeTimestamp); // "1小时前"
String detailTime = DateUtils.timestampToDateTimeString(tradeTimestamp, format: 'MM-dd HH:mm:ss');
```

### 3. 消息时间显示
```dart
// 消息列表时间显示
final messageTimestamp = 1751512626160;
String messageTime = DateUtils.timestampToRelativeTimeString(messageTimestamp); // "昨天"
String fullTime = DateUtils.timestampToChineseDateString(messageTimestamp, includeTime: true);
```

### 4. 日志时间处理
```dart
// 日志时间格式化
final logTimestamp = 1750994226160;
String logDate = DateUtils.timestampToDateString(logTimestamp); // "2025-06-27"
String relativeTime = DateUtils.timestampToRelativeTimeString(logTimestamp); // "1周前"
```

## 自定义格式支持

所有转换方法都支持自定义格式：

```dart
// 自定义日期格式
String customDate = DateUtils.timestampToDateString(timestamp, format: 'MM/dd/yyyy');

// 自定义日期时间格式
String customDateTime = DateUtils.timestampToDateTimeString(timestamp, format: 'yyyy年MM月dd日 HH:mm');

// 中文格式选项
String chineseDate = DateUtils.timestampToChineseDateString(timestamp); // 仅日期
String chineseDateWithTime = DateUtils.timestampToChineseDateString(timestamp, includeTime: true); // 包含时间
```

## 性能优势

### 之前的方式（需要两步）：
```dart
// 旧方式：先转DateTime，再格式化
DateTime dateTime = DateUtils.fromTimestamp(timestamp);
String formatted = DateUtils.formatDateTime(dateTime);
```

### 现在的方式（一步到位）：
```dart
// 新方式：直接转换
String formatted = DateUtils.timestampToDateTimeString(timestamp);
```

## 错误处理

所有时间戳转换方法都有内置的错误处理：

```dart
// 无效时间戳会返回对应的默认值
String result = DateUtils.timestampToDateString(0); // 返回 "1970-01-01"
```

## 兼容性说明

- 支持毫秒级时间戳（13位数字，如：1640995200000）
- 支持秒级时间戳（10位数字，如：1640995200）
- 自动识别时间戳类型的建议：

```dart
String formatTimestamp(int timestamp) {
  if (timestamp > 1000000000000) {
    // 毫秒级时间戳
    return DateUtils.timestampToDateTimeString(timestamp);
  } else {
    // 秒级时间戳
    return DateUtils.timestampSecondsToDateTimeString(timestamp);
  }
}
```

## 实际应用示例

### 金融应用中的使用
```dart
// 交易记录
class TradeRecord {
  final int timestamp;
  final double amount;
  
  String get timeDisplay => DateUtils.timestampToRelativeTimeString(timestamp);
  String get fullTimeDisplay => DateUtils.timestampToDateTimeString(timestamp, format: 'MM-dd HH:mm:ss');
}

// 市场数据
class MarketData {
  final int updateTime;
  
  String get lastUpdateDisplay => DateUtils.timestampToRelativeTimeString(updateTime);
}

// 用户活动日志
class ActivityLog {
  final int createdAt;
  
  String get activityTime => DateUtils.timestampToChineseDateString(createdAt, includeTime: true);
}
```

这些新增的时间戳转换功能大大简化了日期时间的处理流程，提高了开发效率，特别适合处理API返回的时间戳数据。
