import 'package:intl/intl.dart';
import 'app_constants.dart';

/// 日期工具类。
/// 提供常用的日期转换、格式化、计算等功能。
class DateUtils {
  // 私有构造函数，防止实例化
  DateUtils._();

  /// 格式化日期为字符串
  /// [date] 要格式化的日期
  /// [format] 格式化模式，默认为 yyyy-MM-dd
  static String formatDate(DateTime date, {String? format}) {
    final formatter = DateFormat(format ?? AppConstants.dateFormatYMD);
    return formatter.format(date);
  }

  /// 格式化日期时间为字符串
  /// [dateTime] 要格式化的日期时间
  /// [format] 格式化模式，默认为 yyyy-MM-dd HH:mm:ss
  static String formatDateTime(DateTime dateTime, {String? format}) {
    final formatter = DateFormat(format ?? AppConstants.dateFormatYMDHMS);
    return formatter.format(dateTime);
  }

  /// 格式化为中文日期
  /// [date] 要格式化的日期
  /// [includeTime] 是否包含时间，默认为false
  static String formatChineseDate(DateTime date, {bool includeTime = false}) {
    final format = includeTime
        ? AppConstants.dateFormatChineseWithTime
        : AppConstants.dateFormatChinese;
    final formatter = DateFormat(format);
    return formatter.format(date);
  }

  /// 格式化为相对时间（如：刚刚、5分钟前、昨天等）
  /// [dateTime] 要格式化的日期时间
  static String formatRelativeTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inSeconds < 60) {
      return '刚刚';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}分钟前';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}小时前';
    } else if (difference.inDays == 1) {
      return '昨天';
    } else if (difference.inDays == 2) {
      return '前天';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}天前';
    } else if (difference.inDays < 30) {
      final weeks = (difference.inDays / 7).floor();
      return '${weeks}周前';
    } else if (difference.inDays < 365) {
      final months = (difference.inDays / 30).floor();
      return '${months}个月前';
    } else {
      final years = (difference.inDays / 365).floor();
      return '${years}年前';
    }
  }

  /// 解析字符串为日期
  /// [dateString] 日期字符串
  /// [format] 解析格式，默认为 yyyy-MM-dd
  static DateTime? parseDate(String dateString, {String? format}) {
    try {
      final formatter = DateFormat(format ?? AppConstants.dateFormatYMD);
      return formatter.parse(dateString);
    } catch (e) {
      return null;
    }
  }

  /// 解析字符串为日期时间
  /// [dateTimeString] 日期时间字符串
  /// [format] 解析格式，默认为 yyyy-MM-dd HH:mm:ss
  static DateTime? parseDateTime(String dateTimeString, {String? format}) {
    try {
      final formatter = DateFormat(format ?? AppConstants.dateFormatYMDHMS);
      return formatter.parse(dateTimeString);
    } catch (e) {
      return null;
    }
  }

  /// 获取今天的开始时间（00:00:00）
  static DateTime get todayStart {
    final now = DateTime.now();
    return DateTime(now.year, now.month, now.day);
  }

  /// 获取今天的结束时间（23:59:59）
  static DateTime get todayEnd {
    final now = DateTime.now();
    return DateTime(now.year, now.month, now.day, 23, 59, 59);
  }

  /// 获取本周的开始时间（周一00:00:00）
  static DateTime get weekStart {
    final now = DateTime.now();
    final weekday = now.weekday;
    final daysToSubtract = weekday - 1;
    final monday = now.subtract(Duration(days: daysToSubtract));
    return DateTime(monday.year, monday.month, monday.day);
  }

  /// 获取本月的开始时间
  static DateTime get monthStart {
    final now = DateTime.now();
    return DateTime(now.year, now.month, 1);
  }

  /// 获取本年的开始时间
  static DateTime get yearStart {
    final now = DateTime.now();
    return DateTime(now.year, 1, 1);
  }

  /// 判断是否为今天
  /// [date] 要判断的日期
  static bool isToday(DateTime date) {
    final now = DateTime.now();
    return date.year == now.year &&
        date.month == now.month &&
        date.day == now.day;
  }

  /// 判断是否为昨天
  /// [date] 要判断的日期
  static bool isYesterday(DateTime date) {
    final yesterday = DateTime.now().subtract(const Duration(days: 1));
    return date.year == yesterday.year &&
        date.month == yesterday.month &&
        date.day == yesterday.day;
  }

  /// 判断是否为本周
  /// [date] 要判断的日期
  static bool isThisWeek(DateTime date) {
    final now = DateTime.now();
    final startOfWeek = weekStart;
    final endOfWeek = startOfWeek.add(
      const Duration(days: 6, hours: 23, minutes: 59, seconds: 59),
    );
    return date.isAfter(startOfWeek.subtract(const Duration(seconds: 1))) &&
        date.isBefore(endOfWeek.add(const Duration(seconds: 1)));
  }

  /// 判断是否为本月
  /// [date] 要判断的日期
  static bool isThisMonth(DateTime date) {
    final now = DateTime.now();
    return date.year == now.year && date.month == now.month;
  }

  /// 判断是否为本年
  /// [date] 要判断的日期
  static bool isThisYear(DateTime date) {
    final now = DateTime.now();
    return date.year == now.year;
  }

  /// 计算两个日期之间的天数差
  /// [startDate] 开始日期
  /// [endDate] 结束日期
  static int daysBetween(DateTime startDate, DateTime endDate) {
    final start = DateTime(startDate.year, startDate.month, startDate.day);
    final end = DateTime(endDate.year, endDate.month, endDate.day);
    return end.difference(start).inDays;
  }

  /// 获取指定日期所在月份的天数
  /// [date] 指定日期
  static int getDaysInMonth(DateTime date) {
    final nextMonth = DateTime(date.year, date.month + 1, 1);
    final lastDayOfMonth = nextMonth.subtract(const Duration(days: 1));
    return lastDayOfMonth.day;
  }

  /// 获取指定日期是星期几（中文）
  /// [date] 指定日期
  static String getWeekdayName(DateTime date) {
    const weekdays = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
    return weekdays[date.weekday - 1];
  }

  /// 获取指定日期的月份名称（中文）
  /// [date] 指定日期
  static String getMonthName(DateTime date) {
    const months = [
      '一月',
      '二月',
      '三月',
      '四月',
      '五月',
      '六月',
      '七月',
      '八月',
      '九月',
      '十月',
      '十一月',
      '十二月',
    ];
    return months[date.month - 1];
  }

  /// 时间戳转DateTime
  /// [timestamp] 时间戳（毫秒）
  static DateTime fromTimestamp(int timestamp) {
    return DateTime.fromMillisecondsSinceEpoch(timestamp);
  }

  /// 时间戳转格式化日期字符串
  /// [timestamp] 时间戳（毫秒）
  /// [format] 格式化模式，默认为 yyyy-MM-dd
  static String timestampToDateString(int timestamp, {String? format}) {
    final dateTime = fromTimestamp(timestamp);
    return formatDate(dateTime, format: format);
  }

  /// 时间戳转格式化日期时间字符串
  /// [timestamp] 时间戳（毫秒）
  /// [format] 格式化模式，默认为 yyyy-MM-dd HH:mm:ss
  static String timestampToDateTimeString(int timestamp, {String? format}) {
    final dateTime = fromTimestamp(timestamp);
    return formatDateTime(dateTime, format: format);
  }

  /// 时间戳转中文日期字符串
  /// [timestamp] 时间戳（毫秒）
  /// [includeTime] 是否包含时间，默认为false
  static String timestampToChineseDateString(
    int timestamp, {
    bool includeTime = false,
  }) {
    final dateTime = fromTimestamp(timestamp);
    return formatChineseDate(dateTime, includeTime: includeTime);
  }

  /// 时间戳转相对时间字符串
  /// [timestamp] 时间戳（毫秒）
  static String timestampToRelativeTimeString(int timestamp) {
    final dateTime = fromTimestamp(timestamp);
    return formatRelativeTime(dateTime);
  }

  /// 秒级时间戳转DateTime
  /// [timestamp] 时间戳（秒）
  static DateTime fromTimestampSeconds(int timestamp) {
    return DateTime.fromMillisecondsSinceEpoch(timestamp * 1000);
  }

  /// 秒级时间戳转格式化日期字符串
  /// [timestamp] 时间戳（秒）
  /// [format] 格式化模式，默认为 yyyy-MM-dd
  static String timestampSecondsToDateString(int timestamp, {String? format}) {
    final dateTime = fromTimestampSeconds(timestamp);
    return formatDate(dateTime, format: format);
  }

  /// 秒级时间戳转格式化日期时间字符串
  /// [timestamp] 时间戳（秒）
  /// [format] 格式化模式，默认为 yyyy-MM-dd HH:mm:ss
  static String timestampSecondsToDateTimeString(
    int timestamp, {
    String? format,
  }) {
    final dateTime = fromTimestampSeconds(timestamp);
    return formatDateTime(dateTime, format: format);
  }

  /// DateTime转时间戳
  /// [dateTime] 日期时间
  static int toTimestamp(DateTime dateTime) {
    return dateTime.millisecondsSinceEpoch;
  }

  /// DateTime转秒级时间戳
  /// [dateTime] 日期时间
  static int toTimestampSeconds(DateTime dateTime) {
    return (dateTime.millisecondsSinceEpoch / 1000).round();
  }

  /// 获取UTC时间
  /// [dateTime] 本地时间
  static DateTime toUtc(DateTime dateTime) {
    return dateTime.toUtc();
  }

  /// 获取本地时间
  /// [utcDateTime] UTC时间
  static DateTime toLocal(DateTime utcDateTime) {
    return utcDateTime.toLocal();
  }

  /// 添加工作日（跳过周末）
  /// [date] 起始日期
  /// [days] 要添加的工作日天数
  static DateTime addBusinessDays(DateTime date, int days) {
    var result = date;
    var remainingDays = days;

    while (remainingDays > 0) {
      result = result.add(const Duration(days: 1));
      // 如果不是周末（周六=6，周日=7）
      if (result.weekday < 6) {
        remainingDays--;
      }
    }

    return result;
  }

  /// 判断是否为工作日
  /// [date] 要判断的日期
  static bool isBusinessDay(DateTime date) {
    return date.weekday < 6; // 周一到周五
  }

  /// 判断是否为周末
  /// [date] 要判断的日期
  static bool isWeekend(DateTime date) {
    return date.weekday >= 6; // 周六和周日
  }
}
