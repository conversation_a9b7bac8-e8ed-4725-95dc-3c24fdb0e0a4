import 'dart:math';
import 'date_utils.dart' as date_utils;
import 'number_utils.dart';
import 'string_utils.dart';

/// 通用格式化工具类。
/// 提供各种数据的格式化功能，整合其他工具类的功能。
class FormatUtils {
  // 私有构造函数，防止实例化
  FormatUtils._();

  /// 格式化手机号显示
  /// [phone] 手机号
  /// [maskMiddle] 是否遮罩中间部分，默认true
  /// [separator] 分隔符，默认空格
  static String formatPhoneDisplay(String phone, {bool maskMiddle = true, String separator = ' '}) {
    if (StringUtils.isEmpty(phone)) return phone;
    
    String formattedPhone = phone;
    
    // 先遮罩处理
    if (maskMiddle) {
      formattedPhone = StringUtils.maskPhone(phone);
    }
    
    // 添加分隔符：138 1234 5678
    if (formattedPhone.length == 11) {
      return '${formattedPhone.substring(0, 3)}$separator'
             '${formattedPhone.substring(3, 7)}$separator'
             '${formattedPhone.substring(7)}';
    }
    
    return formattedPhone;
  }

  /// 格式化银行卡号显示
  /// [cardNumber] 银行卡号
  /// [maskMiddle] 是否遮罩中间部分，默认true
  /// [separator] 分隔符，默认空格
  /// [groupSize] 分组大小，默认4位
  static String formatBankCardDisplay(
    String cardNumber, {
    bool maskMiddle = true,
    String separator = ' ',
    int groupSize = 4,
  }) {
    if (StringUtils.isEmpty(cardNumber)) return cardNumber;
    
    String formattedCard = cardNumber.replaceAll(RegExp(r'\s+'), '');
    
    // 先遮罩处理
    if (maskMiddle) {
      formattedCard = StringUtils.maskBankCard(formattedCard);
    }
    
    // 添加分隔符
    final buffer = StringBuffer();
    for (int i = 0; i < formattedCard.length; i++) {
      if (i > 0 && i % groupSize == 0) {
        buffer.write(separator);
      }
      buffer.write(formattedCard[i]);
    }
    
    return buffer.toString();
  }

  /// 格式化身份证号显示
  /// [idCard] 身份证号
  /// [maskMiddle] 是否遮罩中间部分，默认true
  /// [separator] 分隔符，默认空格
  static String formatIdCardDisplay(String idCard, {bool maskMiddle = true, String separator = ' '}) {
    if (StringUtils.isEmpty(idCard)) return idCard;
    
    String formattedId = idCard;
    
    // 先遮罩处理
    if (maskMiddle) {
      formattedId = StringUtils.maskIdCard(idCard);
    }
    
    // 18位身份证格式：123456 ******** 123X
    if (formattedId.length == 18) {
      return '${formattedId.substring(0, 6)}$separator'
             '${formattedId.substring(6, 14)}$separator'
             '${formattedId.substring(14)}';
    }
    
    return formattedId;
  }

  /// 格式化金额显示
  /// [amount] 金额
  /// [showSymbol] 是否显示货币符号，默认true
  /// [showSign] 是否显示正负号，默认false
  /// [decimalPlaces] 小数位数，默认2位
  /// [currencySymbol] 货币符号，默认¥
  static String formatAmountDisplay(
    num amount, {
    bool showSymbol = true,
    bool showSign = false,
    int decimalPlaces = 2,
    String currencySymbol = '¥',
  }) {
    final sign = showSign && amount > 0 ? '+' : '';
    final formattedAmount = NumberUtils.formatNumber(amount, decimalPlaces: decimalPlaces);
    
    if (showSymbol) {
      return '$currencySymbol$sign$formattedAmount';
    } else {
      return '$sign$formattedAmount';
    }
  }

  /// 格式化涨跌幅显示
  /// [changeValue] 涨跌值
  /// [changePercent] 涨跌幅百分比
  /// [showBoth] 是否同时显示值和百分比，默认true
  /// [decimalPlaces] 小数位数，默认2位
  static String formatPriceChangeDisplay(
    num changeValue,
    num changePercent, {
    bool showBoth = true,
    int decimalPlaces = 2,
  }) {
    final sign = changeValue >= 0 ? '+' : '';
    final valueStr = '$sign${changeValue.toStringAsFixed(decimalPlaces)}';
    final percentStr = NumberUtils.formatPercentage(changePercent / 100, showSign: true);
    
    if (showBoth) {
      return '$valueStr ($percentStr)';
    } else {
      return percentStr;
    }
  }

  /// 格式化交易量显示
  /// [volume] 交易量
  /// [unit] 单位，默认为空
  static String formatVolumeDisplay(num volume, {String unit = ''}) {
    final formattedVolume = NumberUtils.formatLargeNumber(volume);
    return unit.isEmpty ? formattedVolume : '$formattedVolume $unit';
  }

  /// 格式化时间范围显示
  /// [startTime] 开始时间
  /// [endTime] 结束时间
  /// [separator] 分隔符，默认' - '
  /// [format] 时间格式，默认'MM-dd HH:mm'
  static String formatTimeRangeDisplay(
    DateTime startTime,
    DateTime endTime, {
    String separator = ' - ',
    String format = 'MM-dd HH:mm',
  }) {
    final startStr = date_utils.DateUtils.formatDateTime(startTime, format: format);
    final endStr = date_utils.DateUtils.formatDateTime(endTime, format: format);
    return '$startStr$separator$endStr';
  }

  /// 格式化文件大小显示
  /// [bytes] 字节数
  /// [showUnit] 是否显示单位，默认true
  /// [decimalPlaces] 小数位数，默认2位
  static String formatFileSizeDisplay(int bytes, {bool showUnit = true, int decimalPlaces = 2}) {
    if (!showUnit) {
      return bytes.toString();
    }
    return NumberUtils.formatFileSize(bytes, decimalPlaces: decimalPlaces);
  }

  /// 格式化进度显示
  /// [current] 当前值
  /// [total] 总值
  /// [showPercentage] 是否显示百分比，默认true
  /// [showFraction] 是否显示分数，默认true
  /// [separator] 分隔符，默认' '
  static String formatProgressDisplay(
    num current,
    num total, {
    bool showPercentage = true,
    bool showFraction = true,
    String separator = ' ',
  }) {
    final buffer = StringBuffer();
    
    if (showFraction) {
      buffer.write('$current/$total');
    }
    
    if (showPercentage) {
      if (showFraction) buffer.write(separator);
      final percentage = total > 0 ? (current / total) : 0;
      buffer.write('(${NumberUtils.formatPercentage(percentage)})');
    }
    
    return buffer.toString();
  }

  /// 格式化地址显示
  /// [province] 省份
  /// [city] 城市
  /// [district] 区县
  /// [detail] 详细地址
  /// [separator] 分隔符，默认''
  static String formatAddressDisplay(
    String? province,
    String? city,
    String? district,
    String? detail, {
    String separator = '',
  }) {
    final parts = <String>[];
    
    if (StringUtils.isNotEmpty(province)) parts.add(province!);
    if (StringUtils.isNotEmpty(city)) parts.add(city!);
    if (StringUtils.isNotEmpty(district)) parts.add(district!);
    if (StringUtils.isNotEmpty(detail)) parts.add(detail!);
    
    return parts.join(separator);
  }

  /// 格式化姓名显示
  /// [firstName] 名
  /// [lastName] 姓
  /// [maskMiddle] 是否遮罩中间字符，默认false
  static String formatNameDisplay(String? firstName, String? lastName, {bool maskMiddle = false}) {
    final fullName = StringBuffer();
    
    if (StringUtils.isNotEmpty(lastName)) {
      fullName.write(lastName);
    }
    
    if (StringUtils.isNotEmpty(firstName)) {
      fullName.write(firstName);
    }
    
    final name = fullName.toString();
    
    if (maskMiddle && name.length > 2) {
      return StringUtils.mask(name, 1, 1);
    }
    
    return name;
  }

  /// 格式化评分显示
  /// [score] 评分
  /// [maxScore] 最高分，默认5
  /// [showStars] 是否显示星号，默认false
  /// [decimalPlaces] 小数位数，默认1位
  static String formatRatingDisplay(
    num score,
    {
    num maxScore = 5,
    bool showStars = false,
    int decimalPlaces = 1,
  }) {
    final formattedScore = score.toStringAsFixed(decimalPlaces);
    
    if (showStars) {
      final fullStars = score.floor();
      final hasHalfStar = (score - fullStars) >= 0.5;
      final emptyStars = maxScore.toInt() - fullStars - (hasHalfStar ? 1 : 0);
      
      final stars = StringBuffer();
      stars.write('★' * fullStars);
      if (hasHalfStar) stars.write('☆');
      stars.write('☆' * emptyStars);
      
      return '$formattedScore ${stars.toString()}';
    }
    
    return '$formattedScore/$maxScore';
  }

  /// 格式化距离显示
  /// [distance] 距离（米）
  /// [showUnit] 是否显示单位，默认true
  static String formatDistanceDisplay(num distance, {bool showUnit = true}) {
    String result;
    
    if (distance < 1000) {
      result = '${distance.toInt()}';
      if (showUnit) result += 'm';
    } else {
      final km = distance / 1000;
      result = km.toStringAsFixed(km >= 10 ? 0 : 1);
      if (showUnit) result += 'km';
    }
    
    return result;
  }

  /// 格式化温度显示
  /// [temperature] 温度
  /// [unit] 温度单位，默认'°C'
  /// [decimalPlaces] 小数位数，默认1位
  static String formatTemperatureDisplay(
    num temperature, {
    String unit = '°C',
    int decimalPlaces = 1,
  }) {
    return '${temperature.toStringAsFixed(decimalPlaces)}$unit';
  }

  /// 格式化速度显示
  /// [speed] 速度
  /// [unit] 速度单位，默认'km/h'
  /// [decimalPlaces] 小数位数，默认1位
  static String formatSpeedDisplay(
    num speed, {
    String unit = 'km/h',
    int decimalPlaces = 1,
  }) {
    return '${speed.toStringAsFixed(decimalPlaces)} $unit';
  }

  /// 格式化年龄显示
  /// [birthDate] 出生日期
  /// [referenceDate] 参考日期，默认为当前日期
  static String formatAgeDisplay(DateTime birthDate, {DateTime? referenceDate}) {
    final reference = referenceDate ?? DateTime.now();
    final age = reference.year - birthDate.year;
    
    // 检查是否还没到生日
    final hasHadBirthdayThisYear = reference.month > birthDate.month ||
        (reference.month == birthDate.month && reference.day >= birthDate.day);
    
    final actualAge = hasHadBirthdayThisYear ? age : age - 1;
    
    return '${actualAge}岁';
  }

  /// 格式化倒计时显示
  /// [targetTime] 目标时间
  /// [currentTime] 当前时间，默认为当前时间
  /// [showDays] 是否显示天数，默认true
  /// [showHours] 是否显示小时，默认true
  /// [showMinutes] 是否显示分钟，默认true
  /// [showSeconds] 是否显示秒数，默认true
  static String formatCountdownDisplay(
    DateTime targetTime, {
    DateTime? currentTime,
    bool showDays = true,
    bool showHours = true,
    bool showMinutes = true,
    bool showSeconds = true,
  }) {
    final current = currentTime ?? DateTime.now();
    final difference = targetTime.difference(current);
    
    if (difference.isNegative) {
      return '已结束';
    }
    
    final days = difference.inDays;
    final hours = difference.inHours % 24;
    final minutes = difference.inMinutes % 60;
    final seconds = difference.inSeconds % 60;
    
    final parts = <String>[];
    
    if (showDays && days > 0) {
      parts.add('${days}天');
    }
    
    if (showHours && (hours > 0 || parts.isNotEmpty)) {
      parts.add('${hours.toString().padLeft(2, '0')}时');
    }
    
    if (showMinutes && (minutes > 0 || parts.isNotEmpty)) {
      parts.add('${minutes.toString().padLeft(2, '0')}分');
    }
    
    if (showSeconds) {
      parts.add('${seconds.toString().padLeft(2, '0')}秒');
    }
    
    return parts.isEmpty ? '0秒' : parts.join('');
  }
}
