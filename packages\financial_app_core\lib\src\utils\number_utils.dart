import 'dart:math';
import 'package:intl/intl.dart';
import 'app_constants.dart';

/// 数字工具类。
/// 提供常用的数字格式化、计算、转换等功能。
class NumberUtils {
  // 私有构造函数，防止实例化
  NumberUtils._();

  /// 格式化数字为字符串
  /// [number] 要格式化的数字
  /// [decimalPlaces] 小数位数，默认使用AppConstants.defaultDecimalPlaces
  /// [useThousandSeparator] 是否使用千分位分隔符，默认true
  static String formatNumber(
    num number, {
    int? decimalPlaces,
    bool useThousandSeparator = true,
  }) {
    final places = decimalPlaces ?? AppConstants.defaultDecimalPlaces;
    final formatter = NumberFormat();

    if (useThousandSeparator) {
      formatter.minimumFractionDigits = places;
      formatter.maximumFractionDigits = places;
    } else {
      return number.toStringAsFixed(places);
    }

    return formatter.format(number);
  }

  /// 格式化价格
  /// [price] 价格
  /// [decimalPlaces] 小数位数，默认使用AppConstants.priceDecimalPlaces
  /// [currencySymbol] 货币符号，默认使用AppConstants.defaultCurrencySymbol
  /// [showSymbol] 是否显示货币符号，默认true
  static String formatPrice(
    num price, {
    int? decimalPlaces,
    String? currencySymbol,
    bool showSymbol = true,
  }) {
    final places = decimalPlaces ?? AppConstants.priceDecimalPlaces;
    final symbol = currencySymbol ?? AppConstants.defaultCurrencySymbol;
    final formattedPrice = formatNumber(price, decimalPlaces: places);

    return showSymbol ? '$formattedPrice $symbol' : formattedPrice;
  }

  /// 格式化百分比
  /// [value] 数值（如0.15表示15%）
  /// [decimalPlaces] 小数位数，默认2位
  /// [showSign] 是否显示正负号，默认false
  static String formatPercentage(
    num value, {
    int decimalPlaces = 2,
    bool showSign = false,
  }) {
    final percentage = value * 100;
    final sign = showSign && percentage > 0 ? '+' : '';
    return '$sign${percentage.toStringAsFixed(decimalPlaces)}%';
  }

  /// 格式化大数字（如1000 -> 1K, 1000000 -> 1M）
  /// [number] 要格式化的数字
  /// [decimalPlaces] 小数位数，默认1位
  static String formatLargeNumber(num number, {int decimalPlaces = 1}) {
    final absNumber = number.abs();

    if (absNumber >= 1e12) {
      return '${(number / 1e12).toStringAsFixed(decimalPlaces)}T';
    } else if (absNumber >= 1e9) {
      return '${(number / 1e9).toStringAsFixed(decimalPlaces)}B';
    } else if (absNumber >= 1e6) {
      return '${(number / 1e6).toStringAsFixed(decimalPlaces)}M';
    } else if (absNumber >= 1e3) {
      return '${(number / 1e3).toStringAsFixed(decimalPlaces)}K';
    } else {
      return number.toString();
    }
  }

  /// 格式化文件大小
  /// [bytes] 字节数
  /// [decimalPlaces] 小数位数，默认2位
  static String formatFileSize(int bytes, {int decimalPlaces = 2}) {
    if (bytes <= 0) return '0 B';

    const units = ['B', 'KB', 'MB', 'GB', 'TB', 'PB'];
    final digitGroups = (log(bytes) / log(1024)).floor();
    final size = bytes / pow(1024, digitGroups);

    return '${size.toStringAsFixed(decimalPlaces)} ${units[digitGroups]}';
  }

  /// 安全除法（避免除零错误）
  /// [dividend] 被除数
  /// [divisor] 除数
  /// [defaultValue] 除数为0时的默认值，默认0
  static double safeDivide(
    num dividend,
    num divisor, {
    double defaultValue = 0,
  }) {
    if (divisor == 0) return defaultValue;
    return dividend / divisor;
  }

  /// 计算百分比变化
  /// [oldValue] 旧值
  /// [newValue] 新值
  /// 返回变化百分比（如0.15表示增长15%）
  static double calculatePercentageChange(num oldValue, num newValue) {
    if (oldValue == 0) return newValue == 0 ? 0 : double.infinity;
    return (newValue - oldValue) / oldValue;
  }

  /// 计算涨跌幅
  /// [oldPrice] 旧价格
  /// [newPrice] 新价格
  /// [decimalPlaces] 小数位数，默认2位
  /// [showSign] 是否显示正负号，默认true
  static String calculatePriceChange(
    num oldPrice,
    num newPrice, {
    int decimalPlaces = 2,
    bool showSign = true,
  }) {
    final change = calculatePercentageChange(oldPrice, newPrice);
    return formatPercentage(
      change,
      decimalPlaces: decimalPlaces,
      showSign: showSign,
    );
  }

  /// 四舍五入到指定小数位
  /// [number] 要处理的数字
  /// [decimalPlaces] 小数位数
  static double roundToDecimalPlaces(num number, int decimalPlaces) {
    final factor = pow(10, decimalPlaces);
    return (number * factor).round() / factor;
  }

  /// 向上取整到指定小数位
  /// [number] 要处理的数字
  /// [decimalPlaces] 小数位数
  static double ceilToDecimalPlaces(num number, int decimalPlaces) {
    final factor = pow(10, decimalPlaces);
    return (number * factor).ceil() / factor;
  }

  /// 向下取整到指定小数位
  /// [number] 要处理的数字
  /// [decimalPlaces] 小数位数
  static double floorToDecimalPlaces(num number, int decimalPlaces) {
    final factor = pow(10, decimalPlaces);
    return (number * factor).floor() / factor;
  }

  /// 判断数字是否在指定范围内
  /// [number] 要判断的数字
  /// [min] 最小值
  /// [max] 最大值
  /// [inclusive] 是否包含边界值，默认true
  static bool isInRange(num number, num min, num max, {bool inclusive = true}) {
    if (inclusive) {
      return number >= min && number <= max;
    } else {
      return number > min && number < max;
    }
  }

  /// 将数字限制在指定范围内
  /// [number] 要限制的数字
  /// [min] 最小值
  /// [max] 最大值
  static num clamp(num number, num min, num max) {
    if (number < min) return min;
    if (number > max) return max;
    return number;
  }

  /// 判断是否为正数
  /// [number] 要判断的数字
  /// [includeZero] 是否包含0，默认false
  static bool isPositive(num number, {bool includeZero = false}) {
    return includeZero ? number >= 0 : number > 0;
  }

  /// 判断是否为负数
  /// [number] 要判断的数字
  /// [includeZero] 是否包含0，默认false
  static bool isNegative(num number, {bool includeZero = false}) {
    return includeZero ? number <= 0 : number < 0;
  }

  /// 判断是否为偶数
  /// [number] 要判断的整数
  static bool isEven(int number) {
    return number % 2 == 0;
  }

  /// 判断是否为奇数
  /// [number] 要判断的整数
  static bool isOdd(int number) {
    return number % 2 != 0;
  }

  /// 计算平均值
  /// [numbers] 数字列表
  static double average(List<num> numbers) {
    if (numbers.isEmpty) return 0;
    return numbers.reduce((a, b) => a + b) / numbers.length;
  }

  /// 计算中位数
  /// [numbers] 数字列表
  static double median(List<num> numbers) {
    if (numbers.isEmpty) return 0;

    final sorted = List<num>.from(numbers)..sort();
    final middle = sorted.length ~/ 2;

    if (sorted.length % 2 == 0) {
      return (sorted[middle - 1] + sorted[middle]) / 2;
    } else {
      return sorted[middle].toDouble();
    }
  }

  /// 计算标准差
  /// [numbers] 数字列表
  static double standardDeviation(List<num> numbers) {
    if (numbers.isEmpty) return 0;

    final mean = average(numbers);
    final variance =
        numbers.map((x) => pow(x - mean, 2)).reduce((a, b) => a + b) /
        numbers.length;

    return sqrt(variance);
  }

  /// 生成随机数
  /// [min] 最小值
  /// [max] 最大值
  /// [isInteger] 是否为整数，默认false
  static num randomNumber(num min, num max, {bool isInteger = false}) {
    final random = Random();

    if (isInteger) {
      return min.toInt() + random.nextInt((max - min).toInt() + 1);
    } else {
      return min + random.nextDouble() * (max - min);
    }
  }

  /// 计算复合增长率（CAGR）
  /// [beginValue] 初始值
  /// [endValue] 结束值
  /// [periods] 期间数
  static double calculateCAGR(num beginValue, num endValue, num periods) {
    if (beginValue <= 0 || periods <= 0) return 0;
    return pow(endValue / beginValue, 1 / periods) - 1;
  }

  /// 计算简单利息
  /// [principal] 本金
  /// [rate] 利率（如0.05表示5%）
  /// [time] 时间（年）
  static double calculateSimpleInterest(num principal, num rate, num time) {
    return (principal * rate * time).toDouble();
  }

  /// 计算复利
  /// [principal] 本金
  /// [rate] 利率（如0.05表示5%）
  /// [time] 时间（年）
  /// [compoundFrequency] 复利频率（每年复利次数），默认1
  static double calculateCompoundInterest(
    num principal,
    num rate,
    num time, {
    int compoundFrequency = 1,
  }) {
    return (principal *
                pow(1 + rate / compoundFrequency, compoundFrequency * time) -
            principal)
        .toDouble();
  }

  /// 数字转中文
  /// [number] 要转换的数字（支持0-99999999）
  static String numberToChinese(int number) {
    if (number < 0 || number > 99999999) {
      throw ArgumentError('数字必须在0-99999999范围内');
    }

    if (number == 0) return '零';

    const digits = ['', '一', '二', '三', '四', '五', '六', '七', '八', '九'];
    const units = ['', '十', '百', '千', '万', '十', '百', '千'];

    final result = StringBuffer();
    final numStr = number.toString();
    final length = numStr.length;

    for (int i = 0; i < length; i++) {
      final digit = int.parse(numStr[i]);
      final unitIndex = length - i - 1;

      if (digit != 0) {
        result.write(digits[digit]);
        if (unitIndex > 0) {
          result.write(units[unitIndex]);
        }
      } else if (unitIndex == 4 && number >= 10000) {
        // 万位特殊处理
        result.write('万');
      }
    }

    return result.toString();
  }

  /// 判断两个浮点数是否相等（考虑精度问题）
  /// [a] 第一个数字
  /// [b] 第二个数字
  /// [epsilon] 精度阈值，默认1e-10
  static bool isEqual(double a, double b, {double epsilon = 1e-10}) {
    return (a - b).abs() < epsilon;
  }

  /// 判断浮点数是否为零（考虑精度问题）
  /// [number] 要判断的数字
  /// [epsilon] 精度阈值，默认1e-10
  static bool isZero(double number, {double epsilon = 1e-10}) {
    return number.abs() < epsilon;
  }
}
