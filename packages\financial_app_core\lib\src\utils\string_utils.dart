import 'dart:convert';
import 'dart:math';

/// 字符串工具类。
/// 提供常用的字符串处理、格式化、验证等功能。
class StringUtils {
  // 私有构造函数，防止实例化
  StringUtils._();

  /// 判断字符串是否为空或null
  /// [str] 要判断的字符串
  static bool isEmpty(String? str) {
    return str == null || str.isEmpty;
  }

  /// 判断字符串是否不为空且不为null
  /// [str] 要判断的字符串
  static bool isNotEmpty(String? str) {
    return !isEmpty(str);
  }

  /// 判断字符串是否为空白（null、空字符串或只包含空白字符）
  /// [str] 要判断的字符串
  static bool isBlank(String? str) {
    return str == null || str.trim().isEmpty;
  }

  /// 判断字符串是否不为空白
  /// [str] 要判断的字符串
  static bool isNotBlank(String? str) {
    return !isBlank(str);
  }

  /// 手机号加星号处理
  /// [phone] 手机号码
  /// [startLength] 开头保留位数，默认3位
  /// [endLength] 结尾保留位数，默认4位
  static String maskPhone(String phone, {int startLength = 3, int endLength = 4}) {
    if (isEmpty(phone) || phone.length < startLength + endLength) {
      return phone;
    }
    
    final start = phone.substring(0, startLength);
    final end = phone.substring(phone.length - endLength);
    final middle = '*' * (phone.length - startLength - endLength);
    
    return '$start$middle$end';
  }

  /// 身份证号加星号处理
  /// [idCard] 身份证号
  /// [startLength] 开头保留位数，默认6位
  /// [endLength] 结尾保留位数，默认4位
  static String maskIdCard(String idCard, {int startLength = 6, int endLength = 4}) {
    if (isEmpty(idCard) || idCard.length < startLength + endLength) {
      return idCard;
    }
    
    final start = idCard.substring(0, startLength);
    final end = idCard.substring(idCard.length - endLength);
    final middle = '*' * (idCard.length - startLength - endLength);
    
    return '$start$middle$end';
  }

  /// 银行卡号加星号处理
  /// [cardNumber] 银行卡号
  /// [startLength] 开头保留位数，默认4位
  /// [endLength] 结尾保留位数，默认4位
  static String maskBankCard(String cardNumber, {int startLength = 4, int endLength = 4}) {
    if (isEmpty(cardNumber) || cardNumber.length < startLength + endLength) {
      return cardNumber;
    }
    
    final start = cardNumber.substring(0, startLength);
    final end = cardNumber.substring(cardNumber.length - endLength);
    final middle = '*' * (cardNumber.length - startLength - endLength);
    
    return '$start$middle$end';
  }

  /// 邮箱加星号处理
  /// [email] 邮箱地址
  static String maskEmail(String email) {
    if (isEmpty(email) || !email.contains('@')) {
      return email;
    }
    
    final parts = email.split('@');
    if (parts.length != 2) {
      return email;
    }
    
    final username = parts[0];
    final domain = parts[1];
    
    if (username.length <= 2) {
      return email;
    }
    
    final maskedUsername = username.substring(0, 1) + 
                          '*' * (username.length - 2) + 
                          username.substring(username.length - 1);
    
    return '$maskedUsername@$domain';
  }

  /// 通用字符串加星号处理
  /// [str] 原字符串
  /// [startLength] 开头保留位数
  /// [endLength] 结尾保留位数
  /// [maskChar] 遮罩字符，默认为*
  static String mask(String str, int startLength, int endLength, {String maskChar = '*'}) {
    if (isEmpty(str) || str.length < startLength + endLength) {
      return str;
    }
    
    final start = str.substring(0, startLength);
    final end = str.substring(str.length - endLength);
    final middle = maskChar * (str.length - startLength - endLength);
    
    return '$start$middle$end';
  }

  /// 首字母大写
  /// [str] 要处理的字符串
  static String capitalize(String str) {
    if (isEmpty(str)) return str;
    return str[0].toUpperCase() + str.substring(1).toLowerCase();
  }

  /// 每个单词首字母大写
  /// [str] 要处理的字符串
  static String capitalizeWords(String str) {
    if (isEmpty(str)) return str;
    return str.split(' ').map((word) => capitalize(word)).join(' ');
  }

  /// 驼峰命名转下划线命名
  /// [str] 驼峰命名字符串
  static String camelToSnake(String str) {
    if (isEmpty(str)) return str;
    return str.replaceAllMapped(
      RegExp(r'[A-Z]'),
      (match) => '_${match.group(0)!.toLowerCase()}',
    );
  }

  /// 下划线命名转驼峰命名
  /// [str] 下划线命名字符串
  /// [firstUpperCase] 首字母是否大写，默认false
  static String snakeToCamel(String str, {bool firstUpperCase = false}) {
    if (isEmpty(str)) return str;
    
    final words = str.split('_');
    final result = StringBuffer();
    
    for (int i = 0; i < words.length; i++) {
      final word = words[i];
      if (word.isEmpty) continue;
      
      if (i == 0 && !firstUpperCase) {
        result.write(word.toLowerCase());
      } else {
        result.write(capitalize(word));
      }
    }
    
    return result.toString();
  }

  /// 移除所有空白字符
  /// [str] 要处理的字符串
  static String removeAllWhitespace(String str) {
    if (isEmpty(str)) return str;
    return str.replaceAll(RegExp(r'\s+'), '');
  }

  /// 压缩空白字符（多个连续空白字符替换为单个空格）
  /// [str] 要处理的字符串
  static String compressWhitespace(String str) {
    if (isEmpty(str)) return str;
    return str.replaceAll(RegExp(r'\s+'), ' ').trim();
  }

  /// 反转字符串
  /// [str] 要反转的字符串
  static String reverse(String str) {
    if (isEmpty(str)) return str;
    return str.split('').reversed.join('');
  }

  /// 判断字符串是否为回文
  /// [str] 要判断的字符串
  /// [ignoreCase] 是否忽略大小写，默认true
  /// [ignoreSpaces] 是否忽略空格，默认true
  static bool isPalindrome(String str, {bool ignoreCase = true, bool ignoreSpaces = true}) {
    if (isEmpty(str)) return true;
    
    String processed = str;
    if (ignoreCase) processed = processed.toLowerCase();
    if (ignoreSpaces) processed = processed.replaceAll(' ', '');
    
    return processed == reverse(processed);
  }

  /// 生成随机字符串
  /// [length] 字符串长度
  /// [includeNumbers] 是否包含数字，默认true
  /// [includeUppercase] 是否包含大写字母，默认true
  /// [includeLowercase] 是否包含小写字母，默认true
  /// [includeSymbols] 是否包含符号，默认false
  static String generateRandom(
    int length, {
    bool includeNumbers = true,
    bool includeUppercase = true,
    bool includeLowercase = true,
    bool includeSymbols = false,
  }) {
    const numbers = '0123456789';
    const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    const lowercase = 'abcdefghijklmnopqrstuvwxyz';
    const symbols = '!@#\$%^&*()_+-=[]{}|;:,.<>?';
    
    String chars = '';
    if (includeNumbers) chars += numbers;
    if (includeUppercase) chars += uppercase;
    if (includeLowercase) chars += lowercase;
    if (includeSymbols) chars += symbols;
    
    if (chars.isEmpty) return '';
    
    final random = Random();
    return List.generate(length, (index) => chars[random.nextInt(chars.length)]).join();
  }

  /// 截取字符串并添加省略号
  /// [str] 原字符串
  /// [maxLength] 最大长度
  /// [ellipsis] 省略号，默认为...
  static String truncate(String str, int maxLength, {String ellipsis = '...'}) {
    if (isEmpty(str) || str.length <= maxLength) return str;
    return str.substring(0, maxLength - ellipsis.length) + ellipsis;
  }

  /// 字符串转Base64
  /// [str] 要编码的字符串
  static String toBase64(String str) {
    if (isEmpty(str)) return str;
    return base64Encode(utf8.encode(str));
  }

  /// Base64转字符串
  /// [base64Str] Base64编码的字符串
  static String fromBase64(String base64Str) {
    if (isEmpty(base64Str)) return base64Str;
    try {
      return utf8.decode(base64Decode(base64Str));
    } catch (e) {
      return base64Str;
    }
  }

  /// 计算字符串的字节长度（UTF-8编码）
  /// [str] 要计算的字符串
  static int getByteLength(String str) {
    if (isEmpty(str)) return 0;
    return utf8.encode(str).length;
  }

  /// 按字节长度截取字符串
  /// [str] 原字符串
  /// [maxBytes] 最大字节数
  /// [ellipsis] 省略号，默认为...
  static String truncateByBytes(String str, int maxBytes, {String ellipsis = '...'}) {
    if (isEmpty(str)) return str;
    
    final bytes = utf8.encode(str);
    if (bytes.length <= maxBytes) return str;
    
    final ellipsisBytes = utf8.encode(ellipsis);
    final targetBytes = maxBytes - ellipsisBytes.length;
    
    if (targetBytes <= 0) return ellipsis;
    
    // 找到合适的截取点，避免截断UTF-8字符
    int cutPoint = targetBytes;
    while (cutPoint > 0) {
      try {
        final truncated = utf8.decode(bytes.sublist(0, cutPoint));
        return truncated + ellipsis;
      } catch (e) {
        cutPoint--;
      }
    }
    
    return ellipsis;
  }

  /// 移除字符串中的HTML标签
  /// [str] 包含HTML标签的字符串
  static String removeHtmlTags(String str) {
    if (isEmpty(str)) return str;
    return str.replaceAll(RegExp(r'<[^>]*>'), '');
  }

  /// 转义HTML特殊字符
  /// [str] 要转义的字符串
  static String escapeHtml(String str) {
    if (isEmpty(str)) return str;
    return str
        .replaceAll('&', '&amp;')
        .replaceAll('<', '&lt;')
        .replaceAll('>', '&gt;')
        .replaceAll('"', '&quot;')
        .replaceAll("'", '&#x27;');
  }

  /// 计算两个字符串的相似度（使用编辑距离算法）
  /// [str1] 第一个字符串
  /// [str2] 第二个字符串
  /// 返回值范围：0.0-1.0，1.0表示完全相同
  static double similarity(String str1, String str2) {
    if (str1 == str2) return 1.0;
    if (isEmpty(str1) && isEmpty(str2)) return 1.0;
    if (isEmpty(str1) || isEmpty(str2)) return 0.0;
    
    final maxLength = max(str1.length, str2.length);
    final distance = _levenshteinDistance(str1, str2);
    
    return 1.0 - (distance / maxLength);
  }

  /// 计算编辑距离（Levenshtein距离）
  static int _levenshteinDistance(String str1, String str2) {
    final m = str1.length;
    final n = str2.length;
    
    final dp = List.generate(m + 1, (i) => List.filled(n + 1, 0));
    
    for (int i = 0; i <= m; i++) {
      dp[i][0] = i;
    }
    
    for (int j = 0; j <= n; j++) {
      dp[0][j] = j;
    }
    
    for (int i = 1; i <= m; i++) {
      for (int j = 1; j <= n; j++) {
        if (str1[i - 1] == str2[j - 1]) {
          dp[i][j] = dp[i - 1][j - 1];
        } else {
          dp[i][j] = 1 + min(min(dp[i - 1][j], dp[i][j - 1]), dp[i - 1][j - 1]);
        }
      }
    }
    
    return dp[m][n];
  }
}
