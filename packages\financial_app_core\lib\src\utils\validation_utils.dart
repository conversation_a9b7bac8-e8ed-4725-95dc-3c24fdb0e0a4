import 'app_constants.dart';
import 'string_utils.dart';

/// 验证工具类。
/// 提供常用的数据验证功能，如手机号、邮箱、身份证等格式验证。
class ValidationUtils {
  // 私有构造函数，防止实例化
  ValidationUtils._();

  /// 验证手机号格式
  /// [phone] 手机号码
  /// [allowEmpty] 是否允许为空，默认false
  static bool isValidPhone(String? phone, {bool allowEmpty = false}) {
    if (StringUtils.isEmpty(phone)) {
      return allowEmpty;
    }
    
    return RegExp(AppConstants.phoneRegex).hasMatch(phone!);
  }

  /// 验证邮箱格式
  /// [email] 邮箱地址
  /// [allowEmpty] 是否允许为空，默认false
  static bool isValidEmail(String? email, {bool allowEmpty = false}) {
    if (StringUtils.isEmpty(email)) {
      return allowEmpty;
    }
    
    return RegExp(AppConstants.emailRegex).hasMatch(email!);
  }

  /// 验证身份证号格式
  /// [idCard] 身份证号
  /// [allowEmpty] 是否允许为空，默认false
  static bool isValidIdCard(String? idCard, {bool allowEmpty = false}) {
    if (StringUtils.isEmpty(idCard)) {
      return allowEmpty;
    }
    
    if (!RegExp(AppConstants.idCardRegex).hasMatch(idCard!)) {
      return false;
    }
    
    // 验证校验码
    return _validateIdCardChecksum(idCard);
  }

  /// 验证身份证校验码
  static bool _validateIdCardChecksum(String idCard) {
    if (idCard.length != 18) return false;
    
    const weights = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];
    const checkCodes = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'];
    
    int sum = 0;
    for (int i = 0; i < 17; i++) {
      final digit = int.tryParse(idCard[i]);
      if (digit == null) return false;
      sum += digit * weights[i];
    }
    
    final checkIndex = sum % 11;
    final expectedCheck = checkCodes[checkIndex];
    final actualCheck = idCard[17].toUpperCase();
    
    return expectedCheck == actualCheck;
  }

  /// 验证密码强度
  /// [password] 密码
  /// [minLength] 最小长度，默认8
  /// [requireUppercase] 是否需要大写字母，默认true
  /// [requireLowercase] 是否需要小写字母，默认true
  /// [requireNumbers] 是否需要数字，默认true
  /// [requireSymbols] 是否需要特殊符号，默认false
  static bool isValidPassword(
    String? password, {
    int minLength = 8,
    bool requireUppercase = true,
    bool requireLowercase = true,
    bool requireNumbers = true,
    bool requireSymbols = false,
  }) {
    if (StringUtils.isEmpty(password)) {
      return false;
    }
    
    final pwd = password!;
    
    // 检查长度
    if (pwd.length < minLength) {
      return false;
    }
    
    // 检查大写字母
    if (requireUppercase && !RegExp(r'[A-Z]').hasMatch(pwd)) {
      return false;
    }
    
    // 检查小写字母
    if (requireLowercase && !RegExp(r'[a-z]').hasMatch(pwd)) {
      return false;
    }
    
    // 检查数字
    if (requireNumbers && !RegExp(r'[0-9]').hasMatch(pwd)) {
      return false;
    }
    
    // 检查特殊符号
    if (requireSymbols && !RegExp(r'[!@#$%^&*()_+\-=\[\]{}|;:,.<>?]').hasMatch(pwd)) {
      return false;
    }
    
    return true;
  }

  /// 获取密码强度等级
  /// [password] 密码
  /// 返回值：0-弱，1-中等，2-强
  static int getPasswordStrength(String? password) {
    if (StringUtils.isEmpty(password)) {
      return 0;
    }
    
    final pwd = password!;
    int score = 0;
    
    // 长度评分
    if (pwd.length >= 8) score++;
    if (pwd.length >= 12) score++;
    
    // 字符类型评分
    if (RegExp(r'[a-z]').hasMatch(pwd)) score++;
    if (RegExp(r'[A-Z]').hasMatch(pwd)) score++;
    if (RegExp(r'[0-9]').hasMatch(pwd)) score++;
    if (RegExp(r'[!@#$%^&*()_+\-=\[\]{}|;:,.<>?]').hasMatch(pwd)) score++;
    
    // 复杂度评分
    if (pwd.length >= 16) score++;
    if (RegExp(r'(.)\1{2,}').hasMatch(pwd)) score--; // 连续重复字符扣分
    
    if (score <= 2) return 0; // 弱
    if (score <= 4) return 1; // 中等
    return 2; // 强
  }

  /// 验证银行卡号格式（Luhn算法）
  /// [cardNumber] 银行卡号
  /// [allowEmpty] 是否允许为空，默认false
  static bool isValidBankCard(String? cardNumber, {bool allowEmpty = false}) {
    if (StringUtils.isEmpty(cardNumber)) {
      return allowEmpty;
    }
    
    final card = cardNumber!.replaceAll(RegExp(r'\s+'), '');
    
    // 检查是否全为数字
    if (!RegExp(r'^\d+$').hasMatch(card)) {
      return false;
    }
    
    // 检查长度（一般为13-19位）
    if (card.length < 13 || card.length > 19) {
      return false;
    }
    
    // Luhn算法验证
    return _luhnCheck(card);
  }

  /// Luhn算法校验
  static bool _luhnCheck(String cardNumber) {
    int sum = 0;
    bool alternate = false;
    
    for (int i = cardNumber.length - 1; i >= 0; i--) {
      int digit = int.parse(cardNumber[i]);
      
      if (alternate) {
        digit *= 2;
        if (digit > 9) {
          digit = (digit % 10) + 1;
        }
      }
      
      sum += digit;
      alternate = !alternate;
    }
    
    return sum % 10 == 0;
  }

  /// 验证URL格式
  /// [url] URL地址
  /// [allowEmpty] 是否允许为空，默认false
  static bool isValidUrl(String? url, {bool allowEmpty = false}) {
    if (StringUtils.isEmpty(url)) {
      return allowEmpty;
    }
    
    try {
      final uri = Uri.parse(url!);
      return uri.hasScheme && (uri.scheme == 'http' || uri.scheme == 'https');
    } catch (e) {
      return false;
    }
  }

  /// 验证IP地址格式
  /// [ip] IP地址
  /// [allowEmpty] 是否允许为空，默认false
  static bool isValidIP(String? ip, {bool allowEmpty = false}) {
    if (StringUtils.isEmpty(ip)) {
      return allowEmpty;
    }
    
    final ipRegex = RegExp(
      r'^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$'
    );
    
    return ipRegex.hasMatch(ip!);
  }

  /// 验证数字格式
  /// [value] 要验证的值
  /// [allowEmpty] 是否允许为空，默认false
  /// [allowNegative] 是否允许负数，默认true
  /// [allowDecimal] 是否允许小数，默认true
  static bool isValidNumber(
    String? value, {
    bool allowEmpty = false,
    bool allowNegative = true,
    bool allowDecimal = true,
  }) {
    if (StringUtils.isEmpty(value)) {
      return allowEmpty;
    }
    
    String pattern = r'^\d+';
    
    if (allowNegative) {
      pattern = r'^-?\d+';
    }
    
    if (allowDecimal) {
      pattern += r'(\.\d+)?';
    }
    
    pattern += r'$';
    
    return RegExp(pattern).hasMatch(value!);
  }

  /// 验证整数格式
  /// [value] 要验证的值
  /// [allowEmpty] 是否允许为空，默认false
  /// [allowNegative] 是否允许负数，默认true
  static bool isValidInteger(
    String? value, {
    bool allowEmpty = false,
    bool allowNegative = true,
  }) {
    return isValidNumber(
      value,
      allowEmpty: allowEmpty,
      allowNegative: allowNegative,
      allowDecimal: false,
    );
  }

  /// 验证数字范围
  /// [value] 要验证的值
  /// [min] 最小值
  /// [max] 最大值
  /// [allowEmpty] 是否允许为空，默认false
  static bool isValidRange(
    String? value,
    num min,
    num max, {
    bool allowEmpty = false,
  }) {
    if (StringUtils.isEmpty(value)) {
      return allowEmpty;
    }
    
    final number = num.tryParse(value!);
    if (number == null) {
      return false;
    }
    
    return number >= min && number <= max;
  }

  /// 验证字符串长度
  /// [value] 要验证的字符串
  /// [minLength] 最小长度，默认0
  /// [maxLength] 最大长度，默认null（不限制）
  /// [allowEmpty] 是否允许为空，默认false
  static bool isValidLength(
    String? value, {
    int minLength = 0,
    int? maxLength,
    bool allowEmpty = false,
  }) {
    if (StringUtils.isEmpty(value)) {
      return allowEmpty;
    }
    
    final length = value!.length;
    
    if (length < minLength) {
      return false;
    }
    
    if (maxLength != null && length > maxLength) {
      return false;
    }
    
    return true;
  }

  /// 验证中文姓名格式
  /// [name] 姓名
  /// [allowEmpty] 是否允许为空，默认false
  static bool isValidChineseName(String? name, {bool allowEmpty = false}) {
    if (StringUtils.isEmpty(name)) {
      return allowEmpty;
    }
    
    // 中文姓名：2-10个中文字符，可能包含·（用于少数民族姓名）
    final nameRegex = RegExp(r'^[\u4e00-\u9fa5·]{2,10}$');
    return nameRegex.hasMatch(name!);
  }

  /// 验证用户名格式
  /// [username] 用户名
  /// [allowEmpty] 是否允许为空，默认false
  /// [minLength] 最小长度，默认3
  /// [maxLength] 最大长度，默认20
  static bool isValidUsername(
    String? username, {
    bool allowEmpty = false,
    int minLength = 3,
    int maxLength = 20,
  }) {
    if (StringUtils.isEmpty(username)) {
      return allowEmpty;
    }
    
    final name = username!;
    
    // 检查长度
    if (name.length < minLength || name.length > maxLength) {
      return false;
    }
    
    // 用户名只能包含字母、数字、下划线，且不能以数字开头
    final usernameRegex = RegExp(r'^[a-zA-Z][a-zA-Z0-9_]*$');
    return usernameRegex.hasMatch(name);
  }

  /// 验证QQ号格式
  /// [qq] QQ号
  /// [allowEmpty] 是否允许为空，默认false
  static bool isValidQQ(String? qq, {bool allowEmpty = false}) {
    if (StringUtils.isEmpty(qq)) {
      return allowEmpty;
    }
    
    // QQ号：5-11位数字，不能以0开头
    final qqRegex = RegExp(r'^[1-9][0-9]{4,10}$');
    return qqRegex.hasMatch(qq!);
  }

  /// 验证微信号格式
  /// [wechat] 微信号
  /// [allowEmpty] 是否允许为空，默认false
  static bool isValidWechat(String? wechat, {bool allowEmpty = false}) {
    if (StringUtils.isEmpty(wechat)) {
      return allowEmpty;
    }
    
    // 微信号：6-20位，字母开头，可包含字母、数字、下划线、减号
    final wechatRegex = RegExp(r'^[a-zA-Z][a-zA-Z0-9_-]{5,19}$');
    return wechatRegex.hasMatch(wechat!);
  }

  /// 验证车牌号格式
  /// [plateNumber] 车牌号
  /// [allowEmpty] 是否允许为空，默认false
  static bool isValidPlateNumber(String? plateNumber, {bool allowEmpty = false}) {
    if (StringUtils.isEmpty(plateNumber)) {
      return allowEmpty;
    }
    
    // 普通车牌：省份简称+字母+5位数字或字母
    // 新能源车牌：省份简称+字母+6位数字或字母（包含绿牌）
    final plateRegex = RegExp(
      r'^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z][A-HJ-NP-Z0-9]{4,5}[A-HJ-NP-Z0-9挂学警港澳]?$'
    );
    
    return plateRegex.hasMatch(plateNumber!);
  }
}
