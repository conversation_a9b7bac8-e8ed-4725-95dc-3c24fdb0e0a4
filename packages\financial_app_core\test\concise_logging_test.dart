import 'package:flutter_test/flutter_test.dart';
import 'package:financial_app_core/financial_app_core.dart';
import 'package:logger/logger.dart';

void main() {
  setUpAll(() {
    // 初始化 Flutter 绑定
    TestWidgetsFlutterBinding.ensureInitialized();
  });

  group('简洁日志测试', () {
    setUp(() async {
      // 每个测试前初始化日志系统
      await AppLogger.initialize(config: LogConfig.concise());
    });

    test('简洁模式应该隐藏调试日志', () async {
      // 切换到简洁模式
      await LogConfigManager.switchToConciseMode();

      final config = LogConfigManager.currentConfig;

      // 验证配置
      expect(config.showDebugLogs, false);
      expect(config.showTimestamps, false);
      expect(config.showModuleNames, true);
      expect(config.minLevel, LogLevel.info);
    });

    test('开发模式应该显示调试日志', () async {
      // 切换到开发模式
      await LogConfigManager.switchToDevelopmentMode();

      final config = LogConfigManager.currentConfig;

      // 验证配置
      expect(config.showDebugLogs, true);
      expect(config.showTimestamps, true);
      expect(config.showModuleNames, true);
      expect(config.minLevel, LogLevel.debug);
    });

    test('静默模式应该只显示警告和错误', () async {
      // 切换到静默模式
      await LogConfigManager.switchToQuietMode();

      final config = LogConfigManager.currentConfig;

      // 验证配置
      expect(config.showDebugLogs, false);
      expect(config.showTimestamps, false);
      expect(config.showModuleNames, false);
      expect(config.minLevel, LogLevel.warning);
    });

    test('动态切换调试日志', () async {
      // 初始状态
      await LogConfigManager.switchToConciseMode();
      expect(LogConfigManager.currentConfig.showDebugLogs, false);

      // 切换调试日志
      await LogConfigManager.toggleDebugLogs();
      expect(LogConfigManager.currentConfig.showDebugLogs, true);

      // 再次切换
      await LogConfigManager.toggleDebugLogs();
      expect(LogConfigManager.currentConfig.showDebugLogs, false);
    });

    test('日志级别过滤功能', () {
      // 在简洁模式下，调试日志应该被过滤
      // 这个测试通过配置验证来检查过滤逻辑
      final config = LogConfigManager.currentConfig;
      expect(config.showDebugLogs, false);
      expect(config.minLevel, LogLevel.info);
    });

    test('预设配置切换', () async {
      // 测试各种预设配置
      await LogConfigManager.switchToPreset('development');
      expect(LogConfigManager.currentConfig.showDebugLogs, true);

      await LogConfigManager.switchToPreset('concise');
      expect(LogConfigManager.currentConfig.showDebugLogs, false);

      await LogConfigManager.switchToPreset('quiet');
      expect(LogConfigManager.currentConfig.minLevel, LogLevel.warning);

      await LogConfigManager.switchToPreset('production');
      expect(LogConfigManager.currentConfig.minLevel, LogLevel.warning);
    });

    test('配置状态描述', () {
      final status = LogConfigManager.getConfigStatus();
      expect(status, contains('当前日志配置状态'));
      expect(status, contains('调试日志'));
      expect(status, contains('模块名称'));
      expect(status, contains('时间戳'));
    });

    test('CoreLogger 基本功能', () {
      final logger = CoreLogger();

      // 这些调用不应该抛出异常
      expect(() => logger.debug('测试调试日志'), returnsNormally);
      expect(() => logger.info('测试信息日志'), returnsNormally);
      expect(() => logger.warning('测试警告日志'), returnsNormally);
      expect(() => logger.error('测试错误日志'), returnsNormally);
    });

    test('日志打印器工厂', () {
      // 测试不同配置下的打印器创建
      final printer1 = LogPrinterFactory.createPrinter(
        showDebugLogs: false,
        showTimestamps: false,
        showModuleNames: false,
        structuredLogging: false,
        showStackTrace: false,
      );
      expect(printer1, isA<MinimalPrinter>());

      final printer2 = LogPrinterFactory.createPrinter(
        showDebugLogs: false,
        showTimestamps: true,
        showModuleNames: true,
        structuredLogging: false,
        showStackTrace: false,
      );
      expect(printer2, isA<SingleLinePrinter>());

      final printer3 = LogPrinterFactory.createPrinter(
        showDebugLogs: true,
        showTimestamps: true,
        showModuleNames: true,
        structuredLogging: false,
        showStackTrace: false,
      );
      expect(printer3, isA<PrettyPrinter>());
    });
  });

  group('日志打印器测试', () {
    test('简洁打印器创建', () {
      final printer = ConcisePrinter(showTime: false, showLevel: true);
      expect(printer, isA<ConcisePrinter>());
    });

    test('最小打印器创建', () {
      final printer = MinimalPrinter();
      expect(printer, isA<MinimalPrinter>());
    });

    test('单行打印器创建', () {
      final printer = SingleLinePrinter(
        showTime: false,
        showLevel: true,
        showModule: true,
      );
      expect(printer, isA<SingleLinePrinter>());
    });
  });
}
