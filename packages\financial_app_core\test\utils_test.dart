import 'package:flutter_test/flutter_test.dart';
import 'package:financial_app_core/financial_app_core.dart';

void main() {
  group('DateUtils Tests', () {
    test('formatDate should format date correctly', () {
      final date = DateTime(2024, 1, 1, 12, 30, 45);
      expect(DateUtils.formatDate(date), '2024-01-01');
      expect(DateUtils.formatDateTime(date), '2024-01-01 12:30:45');
    });

    test('formatRelativeTime should work correctly', () {
      final now = DateTime.now();
      final fiveMinutesAgo = now.subtract(const Duration(minutes: 5));
      expect(DateUtils.formatRelativeTime(fiveMinutesAgo), '5分钟前');
    });

    test('isToday should work correctly', () {
      expect(DateUtils.isToday(DateTime.now()), true);
      expect(
        DateUtils.isToday(DateTime.now().subtract(const Duration(days: 1))),
        false,
      );
    });

    test('timestamp conversion should work correctly', () {
      final now = DateTime.now();
      final timestamp = DateUtils.toTimestamp(now);
      final timestampSeconds = DateUtils.toTimestampSeconds(now);

      // 测试毫秒级时间戳转换
      expect(
        DateUtils.fromTimestamp(timestamp).millisecondsSinceEpoch,
        timestamp,
      );
      expect(DateUtils.timestampToDateString(timestamp), isA<String>());
      expect(DateUtils.timestampToDateTimeString(timestamp), isA<String>());
      expect(DateUtils.timestampToChineseDateString(timestamp), isA<String>());
      expect(DateUtils.timestampToRelativeTimeString(timestamp), isA<String>());

      // 测试秒级时间戳转换
      expect(
        DateUtils.fromTimestampSeconds(
          timestampSeconds,
        ).difference(now).inSeconds.abs(),
        lessThan(2),
      );
      expect(
        DateUtils.timestampSecondsToDateString(timestampSeconds),
        isA<String>(),
      );
      expect(
        DateUtils.timestampSecondsToDateTimeString(timestampSeconds),
        isA<String>(),
      );
    });
  });

  group('StringUtils Tests', () {
    test('isEmpty should work correctly', () {
      expect(StringUtils.isEmpty(null), true);
      expect(StringUtils.isEmpty(''), true);
      expect(StringUtils.isEmpty('test'), false);
    });

    test('maskPhone should mask phone number correctly', () {
      expect(StringUtils.maskPhone('13812345678'), '138****5678');
      expect(StringUtils.maskPhone('1381234567'), '138***4567');
    });

    test('maskEmail should mask email correctly', () {
      expect(StringUtils.maskEmail('<EMAIL>'), 'u**<EMAIL>');
      expect(StringUtils.maskEmail('<EMAIL>'), 't**<EMAIL>');
    });

    test('capitalize should work correctly', () {
      expect(StringUtils.capitalize('hello'), 'Hello');
      expect(StringUtils.capitalize('WORLD'), 'World');
    });

    test('camelToSnake should work correctly', () {
      expect(StringUtils.camelToSnake('userName'), 'user_name');
      expect(StringUtils.camelToSnake('firstName'), 'first_name');
    });

    test('snakeToCamel should work correctly', () {
      expect(StringUtils.snakeToCamel('user_name'), 'userName');
      expect(StringUtils.snakeToCamel('first_name'), 'firstName');
    });
  });

  group('NumberUtils Tests', () {
    test('formatNumber should format correctly', () {
      expect(NumberUtils.formatNumber(1234.567, decimalPlaces: 2), '1,234.57');
      expect(NumberUtils.formatNumber(1000, decimalPlaces: 0), '1,000');
    });

    test('formatPercentage should work correctly', () {
      expect(NumberUtils.formatPercentage(0.15), '15.00%');
      expect(NumberUtils.formatPercentage(0.15, showSign: true), '+15.00%');
      expect(NumberUtils.formatPercentage(-0.05, showSign: true), '-5.00%');
    });

    test('formatLargeNumber should work correctly', () {
      expect(NumberUtils.formatLargeNumber(1500), '1.5K');
      expect(NumberUtils.formatLargeNumber(1500000), '1.5M');
      expect(NumberUtils.formatLargeNumber(1500000000), '1.5B');
    });

    test('safeDivide should handle division by zero', () {
      expect(NumberUtils.safeDivide(10, 2), 5.0);
      expect(NumberUtils.safeDivide(10, 0), 0.0);
      expect(NumberUtils.safeDivide(10, 0, defaultValue: -1), -1.0);
    });

    test('isInRange should work correctly', () {
      expect(NumberUtils.isInRange(50, 0, 100), true);
      expect(NumberUtils.isInRange(150, 0, 100), false);
      expect(NumberUtils.isInRange(0, 0, 100), true);
      expect(NumberUtils.isInRange(100, 0, 100), true);
    });

    test('average should calculate correctly', () {
      expect(NumberUtils.average([1, 2, 3, 4, 5]), 3.0);
      expect(NumberUtils.average([10, 20, 30]), 20.0);
    });
  });

  group('ValidationUtils Tests', () {
    test('isValidPhone should validate phone numbers', () {
      expect(ValidationUtils.isValidPhone('13812345678'), true);
      expect(ValidationUtils.isValidPhone('12345678901'), false);
      expect(ValidationUtils.isValidPhone('1381234567'), false);
    });

    test('isValidEmail should validate emails', () {
      expect(ValidationUtils.isValidEmail('<EMAIL>'), true);
      expect(ValidationUtils.isValidEmail('<EMAIL>'), true);
      expect(ValidationUtils.isValidEmail('invalid-email'), false);
      expect(ValidationUtils.isValidEmail('user@'), false);
    });

    test('isValidPassword should validate passwords', () {
      expect(ValidationUtils.isValidPassword('Abc12345'), true);
      expect(
        ValidationUtils.isValidPassword('abc12345'),
        false,
      ); // no uppercase
      expect(
        ValidationUtils.isValidPassword('ABC12345'),
        false,
      ); // no lowercase
      expect(ValidationUtils.isValidPassword('Abcdefgh'), false); // no numbers
      expect(ValidationUtils.isValidPassword('Abc123'), false); // too short
    });

    test('getPasswordStrength should return correct strength', () {
      expect(ValidationUtils.getPasswordStrength('123'), 0); // weak
      expect(ValidationUtils.getPasswordStrength('Abc12345'), 1); // medium
      expect(ValidationUtils.getPasswordStrength('Abc123!@#456'), 2); // strong
    });

    test('isValidNumber should validate numbers', () {
      expect(ValidationUtils.isValidNumber('123'), true);
      expect(ValidationUtils.isValidNumber('123.45'), true);
      expect(ValidationUtils.isValidNumber('-123.45'), true);
      expect(ValidationUtils.isValidNumber('abc'), false);
    });

    test('isValidRange should validate number ranges', () {
      expect(ValidationUtils.isValidRange('50', 0, 100), true);
      expect(ValidationUtils.isValidRange('150', 0, 100), false);
      expect(ValidationUtils.isValidRange('abc', 0, 100), false);
    });
  });

  group('FormatUtils Tests', () {
    test('formatPhoneDisplay should format phone correctly', () {
      expect(
        FormatUtils.formatPhoneDisplay('13812345678', maskMiddle: false),
        '138 1234 5678',
      );
      expect(
        FormatUtils.formatPhoneDisplay('13812345678', maskMiddle: true),
        '138 **** 5678',
      );
    });

    test('formatAmountDisplay should format amount correctly', () {
      expect(FormatUtils.formatAmountDisplay(1234.56), '¥1,234.56');
      expect(
        FormatUtils.formatAmountDisplay(1234.56, showSymbol: false),
        '1,234.56',
      );
      expect(
        FormatUtils.formatAmountDisplay(1234.56, showSign: true),
        '¥+1,234.56',
      );
    });

    test('formatDistanceDisplay should format distance correctly', () {
      expect(FormatUtils.formatDistanceDisplay(500), '500m');
      expect(FormatUtils.formatDistanceDisplay(1500), '1.5km');
      expect(FormatUtils.formatDistanceDisplay(15000), '15km');
    });

    test('formatProgressDisplay should format progress correctly', () {
      expect(FormatUtils.formatProgressDisplay(75, 100), '75/100 (75.00%)');
      expect(
        FormatUtils.formatProgressDisplay(75, 100, showPercentage: false),
        '75/100',
      );
      expect(
        FormatUtils.formatProgressDisplay(75, 100, showFraction: false),
        '(75.00%)',
      );
    });
  });
}
