# 主题系统文档

## 概述

本应用实现了完整的主题配置系统，支持Light和Dark模式，使用Provider进行状态管理。主题系统包含以下特性：

- 🎨 **自定义主题配置** - 专为金融应用设计的主题
- 🌓 **Light/Dark模式** - 支持亮色、暗色和跟随系统
- 🎯 **多种主色调** - 8种预设主色调可选
- 💾 **持久化存储** - 主题设置自动保存
- 🔄 **实时切换** - 无需重启应用即可切换主题
- 📱 **响应式设计** - 适配不同屏幕尺寸

## 架构设计

### 核心组件

```
lib/
├── config/
│   └── app_theme.dart          # 主题配置文件
├── providers/
│   └── theme_provider.dart     # 主题状态管理
├── src/
│   ├── pages/settings/
│   │   ├── theme_settings_page.dart  # 主题设置页面
│   │   └── settings_page.dart        # 通用设置页面
│   └── widgets/
│       └── theme_switcher.dart       # 主题切换组件
└── app.dart                    # 应用入口配置
```

### 主要类说明

#### 1. AppTheme
主题配置类，提供完整的Light和Dark主题定义。

```dart
// 获取亮色主题
ThemeData lightTheme = AppTheme.lightTheme(primaryColor: Colors.blue);

// 获取暗色主题
ThemeData darkTheme = AppTheme.darkTheme(primaryColor: Colors.blue);
```

#### 2. ThemeProvider
主题状态管理类，使用Provider模式管理主题状态。

```dart
// 设置主题模式
await themeProvider.setThemeMode(ThemeMode.dark);

// 设置主色调
await themeProvider.setAccentColor('green');

// 切换自定义主题
await themeProvider.setUseCustomTheme(true);
```

#### 3. FinancialColors
金融应用专用颜色定义。

```dart
// 涨跌颜色
Color bullishColor = FinancialColors.bullish;  // 绿色
Color bearishColor = FinancialColors.bearish;  // 红色

// 风险等级颜色
Color lowRisk = FinancialColors.lowRisk;       // 低风险
Color highRisk = FinancialColors.highRisk;     // 高风险
```

## 使用指南

### 1. 基本配置

在`app.dart`中配置主题：

```dart
MaterialApp(
  theme: themeProvider.useCustomTheme 
      ? AppTheme.lightTheme(primaryColor: themeProvider.getPrimaryColor())
      : ThemeData.light(),
  darkTheme: themeProvider.useCustomTheme 
      ? AppTheme.darkTheme(primaryColor: themeProvider.getPrimaryColor())
      : ThemeData.dark(),
  themeMode: themeProvider.themeMode,
  // ...
)
```

### 2. 主题切换组件

使用`ThemeSwitcher`组件快速切换主题：

```dart
// 图标按钮样式
ThemeSwitcher(
  style: ThemeSwitcherStyle.iconButton,
)

// 列表项样式
ThemeSwitcher(
  style: ThemeSwitcherStyle.listTile,
  showLabel: true,
)

// 芯片样式
ThemeSwitcher(
  style: ThemeSwitcherStyle.chip,
)
```

### 3. 主题状态监听

使用`Consumer`监听主题变化：

```dart
Consumer<ThemeProvider>(
  builder: (context, themeProvider, child) {
    return Text(
      '当前主题: ${themeProvider.themeMode}',
      style: TextStyle(
        color: themeProvider.getPrimaryColor(),
      ),
    );
  },
)
```

### 4. 金融专用样式

使用金融应用专用的文本样式：

```dart
// 价格文本
Text(
  '¥123.45',
  style: FinancialTextStyles.priceText(
    fontSize: 16,
    color: FinancialColors.bullish,
  ),
)

// 百分比文本
Text(
  '+2.34%',
  style: FinancialTextStyles.percentageText(
    fontSize: 14,
    color: FinancialColors.bullish,
  ),
)

// 等宽数字文本
Text(
  '123,456.78',
  style: FinancialTextStyles.monospaceText(
    fontSize: 14,
    color: Theme.of(context).colorScheme.onSurface,
  ),
)
```

## 主题配置选项

### 主题模式

- **ThemeMode.system** - 跟随系统设置
- **ThemeMode.light** - 强制亮色模式
- **ThemeMode.dark** - 强制暗色模式

### 可用主色调

- 🔵 蓝色 (blue) - 默认
- 🟢 绿色 (green)
- 🟣 紫色 (purple)
- 🟠 橙色 (orange)
- 🔴 红色 (red)
- 🔷 青色 (teal)
- 🟦 靛蓝 (indigo)
- 🩷 粉色 (pink)

### 金融专用颜色

```dart
// 涨跌颜色
FinancialColors.bullish   // #00C851 (绿色)
FinancialColors.bearish   // #FF4444 (红色)
FinancialColors.neutral   // #757575 (灰色)

// 风险等级
FinancialColors.lowRisk    // #4CAF50 (低风险)
FinancialColors.mediumRisk // #FF9800 (中风险)
FinancialColors.highRisk   // #F44336 (高风险)

// 状态颜色
FinancialColors.success   // #4CAF50
FinancialColors.warning   // #FF9800
FinancialColors.error     // #F44336
FinancialColors.info      // #2196F3
```

## 最佳实践

### 1. 主题一致性
- 使用`Theme.of(context)`获取当前主题
- 避免硬编码颜色值
- 使用主题定义的颜色和样式

### 2. 响应式设计
```dart
// 根据主题亮度调整样式
final isDark = Theme.of(context).brightness == Brightness.dark;
final backgroundColor = isDark ? Colors.grey[900] : Colors.grey[100];
```

### 3. 无障碍支持
- 确保颜色对比度符合无障碍标准
- 提供语义化的颜色描述
- 支持系统字体大小设置

### 4. 性能优化
- 使用`Consumer`而不是`Provider.of`监听变化
- 避免在build方法中创建新的主题对象
- 合理使用`const`构造函数

## 扩展开发

### 添加新的主色调

1. 在`ThemeProvider.availableAccentColors`中添加新颜色名称
2. 在`getPrimaryColor()`方法中添加对应的颜色值
3. 在`getAccentColorName()`方法中添加显示名称

### 自定义主题组件

```dart
class CustomThemeWidget extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return Container(
          decoration: BoxDecoration(
            gradient: themeProvider.useCustomTheme
                ? FinancialColors.primaryGradient
                : null,
            color: themeProvider.useCustomTheme
                ? null
                : Theme.of(context).colorScheme.surface,
          ),
          child: child,
        );
      },
    );
  }
}
```

## 故障排除

### 常见问题

1. **主题切换不生效**
   - 检查是否正确使用`Consumer`或`Provider.of`
   - 确认`ThemeProvider`已在应用根部注册

2. **颜色显示异常**
   - 检查是否使用了正确的颜色属性
   - 确认主题配置是否正确加载

3. **持久化失败**
   - 检查`SharedPreferences`权限
   - 确认异步操作是否正确处理

### 调试技巧

```dart
// 打印当前主题信息
debugPrint('Current theme mode: ${themeProvider.themeMode}');
debugPrint('Use custom theme: ${themeProvider.useCustomTheme}');
debugPrint('Accent color: ${themeProvider.accentColor}');
```

## 更新日志

### v1.0.0
- ✅ 实现基础主题系统
- ✅ 支持Light/Dark模式切换
- ✅ 添加8种主色调选择
- ✅ 实现主题持久化存储
- ✅ 创建主题设置页面
- ✅ 添加金融专用颜色和样式
