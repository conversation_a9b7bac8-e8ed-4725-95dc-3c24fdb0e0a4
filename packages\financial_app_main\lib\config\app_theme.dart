import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// 应用主题配置
///
/// 提供完整的Light和Dark主题配置，支持：
/// - 自定义颜色方案
/// - 金融应用专用样式
/// - 响应式设计
/// - 无障碍支持
class AppTheme {
  /// 获取亮色主题 - 黑白风格
  static ThemeData lightTheme({Color? primaryColor}) {
    final seedColor = primaryColor ?? FinancialColors.primaryBlack;
    final colorScheme = ColorScheme.fromSeed(
      seedColor: seedColor,
      brightness: Brightness.light,
      surface: FinancialColors.lightBackground,
      onSurface: FinancialColors.primaryText,
      secondary: FinancialColors.lightGray,
      outline: FinancialColors.border,
    );

    return ThemeData(
      useMaterial3: true,
      colorScheme: colorScheme,
      brightness: Brightness.light,
      platform: TargetPlatform.iOS,

      // 应用栏主题 - 黑白风格
      appBarTheme: AppBarTheme(
        elevation: 0,
        centerTitle: true,
        backgroundColor: FinancialColors.lightBackground,
        foregroundColor: FinancialColors.primaryText,
        systemOverlayStyle: SystemUiOverlayStyle.dark,
        titleTextStyle: TextStyle(
          color: FinancialColors.primaryText,
          fontSize: 18,
          fontWeight: FontWeight.w600,
        ),
        iconTheme: IconThemeData(color: FinancialColors.primaryText),
        actionsIconTheme: IconThemeData(color: FinancialColors.primaryText),
      ),

      // 卡片主题 - 黑白风格
      cardTheme: CardThemeData(
        elevation: 2,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        color: FinancialColors.cardBackground,
        shadowColor: FinancialColors.primaryBlack.withValues(alpha: 0.1),
        surfaceTintColor: Colors.transparent,
      ),

      // 按钮主题 - 黑白风格
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          elevation: 2,
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          backgroundColor: FinancialColors.primaryBlack,
          foregroundColor: FinancialColors.primaryWhite,
          shadowColor: FinancialColors.primaryBlack.withValues(alpha: 0.3),
        ),
      ),

      // 文本按钮主题 - 黑白风格
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: FinancialColors.primaryBlack,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        ),
      ),

      // 轮廓按钮主题 - 黑白风格
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: FinancialColors.primaryBlack,
          side: BorderSide(color: FinancialColors.primaryBlack),
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        ),
      ),

      // 输入框主题 - 黑白风格
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: FinancialColors.lightBackground,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: FinancialColors.border),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: FinancialColors.border),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: FinancialColors.primaryBlack, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: FinancialColors.error),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: FinancialColors.error, width: 2),
        ),
        labelStyle: TextStyle(color: FinancialColors.secondaryText),
        hintStyle: TextStyle(color: FinancialColors.disabledText),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 12,
        ),
      ),

      // 底部导航栏主题
      bottomNavigationBarTheme: BottomNavigationBarThemeData(
        type: BottomNavigationBarType.fixed,
        backgroundColor: colorScheme.surface,
        selectedItemColor: colorScheme.primary,
        unselectedItemColor: colorScheme.onSurface.withValues(alpha: 0.6),
        elevation: 8,
      ),

      // 文本主题
      textTheme: _buildTextTheme(colorScheme),

      // 分割线主题
      dividerTheme: DividerThemeData(
        color: colorScheme.outline.withValues(alpha: 0.2),
        thickness: 1,
      ),

      // 列表瓦片主题
      listTileTheme: ListTileThemeData(
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),

      // 对话框主题
      dialogTheme: DialogThemeData(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        elevation: 8,
      ),

      // 浮动操作按钮主题
      floatingActionButtonTheme: FloatingActionButtonThemeData(
        elevation: 4,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      ),

      // 开关主题
      switchTheme: SwitchThemeData(
        thumbColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return colorScheme.primary;
          }
          return colorScheme.outline;
        }),
        trackColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return colorScheme.primary.withValues(alpha: 0.5);
          }
          return colorScheme.outline.withValues(alpha: 0.3);
        }),
      ),

      // 滑块主题
      sliderTheme: SliderThemeData(
        activeTrackColor: colorScheme.primary,
        inactiveTrackColor: colorScheme.primary.withValues(alpha: 0.3),
        thumbColor: colorScheme.primary,
        overlayColor: colorScheme.primary.withValues(alpha: 0.2),
      ),

      // 进度指示器主题 - 黑白风格
      progressIndicatorTheme: ProgressIndicatorThemeData(
        color: FinancialColors.primaryBlack, // 进度条为黑色
        linearTrackColor: FinancialColors.border, // 轨道为浅灰色
        circularTrackColor: FinancialColors.border, // 圆形轨道为浅灰色
      ),

      // 标签栏主题 - 黑白风格
      tabBarTheme: TabBarThemeData(
        labelColor: FinancialColors.primaryText, // 选中标签为黑色
        unselectedLabelColor: FinancialColors.secondaryText, // 未选中标签为灰色
        labelStyle: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
        unselectedLabelStyle: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.normal,
        ),
        indicator: UnderlineTabIndicator(
          borderSide: BorderSide(color: FinancialColors.primaryBlack, width: 2),
        ),
        indicatorSize: TabBarIndicatorSize.label,
      ),

      // 芯片主题
      chipTheme: ChipThemeData(
        backgroundColor: colorScheme.surface,
        selectedColor: colorScheme.primary.withValues(alpha: 0.2),
        labelStyle: TextStyle(color: colorScheme.onSurface),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      ),
    );
  }

  /// 获取暗色主题 - 黑白风格
  static ThemeData darkTheme({Color? primaryColor}) {
    final seedColor = primaryColor ?? FinancialColors.primaryBlack;
    final colorScheme = ColorScheme.fromSeed(
      seedColor: seedColor,
      brightness: Brightness.dark,
      surface: FinancialColors.darkBackground,
      onSurface: FinancialColors.darkPrimaryText,
      secondary: FinancialColors.darkGray,
      outline: FinancialColors.darkBorder,
    );

    return ThemeData(
      useMaterial3: true,
      colorScheme: colorScheme,
      brightness: Brightness.dark,
      platform: TargetPlatform.iOS,

      // 应用栏主题 - 黑白风格暗色模式
      appBarTheme: AppBarTheme(
        elevation: 0,
        centerTitle: true,
        backgroundColor: FinancialColors.darkBackground,
        foregroundColor: FinancialColors.darkPrimaryText,
        systemOverlayStyle: SystemUiOverlayStyle.light,
        titleTextStyle: TextStyle(
          color: FinancialColors.darkPrimaryText,
          fontSize: 18,
          fontWeight: FontWeight.w600,
        ),
        iconTheme: IconThemeData(color: FinancialColors.darkPrimaryText),
        actionsIconTheme: IconThemeData(color: FinancialColors.darkPrimaryText),
      ),

      // 卡片主题 - 黑白风格暗色模式
      cardTheme: CardThemeData(
        elevation: 4,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        color: FinancialColors.darkCardBackground,
        shadowColor: FinancialColors.primaryBlack.withValues(alpha: 0.3),
        surfaceTintColor: Colors.transparent,
      ),

      // 按钮主题 - 黑白风格暗色模式
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          elevation: 2,
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          backgroundColor: FinancialColors.primaryWhite,
          foregroundColor: FinancialColors.primaryBlack,
          shadowColor: FinancialColors.primaryWhite.withValues(alpha: 0.3),
        ),
      ),

      // 文本按钮主题 - 黑白风格暗色模式
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: FinancialColors.primaryWhite,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        ),
      ),

      // 轮廓按钮主题 - 黑白风格暗色模式
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: FinancialColors.primaryWhite,
          side: BorderSide(color: FinancialColors.primaryWhite),
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        ),
      ),

      // 输入框主题 - 黑白风格暗色模式
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: FinancialColors.darkCardBackground,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: FinancialColors.darkBorder),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: FinancialColors.darkBorder),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: FinancialColors.primaryWhite, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: FinancialColors.error),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: FinancialColors.error, width: 2),
        ),
        labelStyle: TextStyle(color: FinancialColors.darkSecondaryText),
        hintStyle: TextStyle(color: FinancialColors.darkSecondaryText),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 12,
        ),
      ),

      // 底部导航栏主题 - 黑白风格暗色模式
      bottomNavigationBarTheme: BottomNavigationBarThemeData(
        type: BottomNavigationBarType.fixed,
        backgroundColor: FinancialColors.darkBackground,
        selectedItemColor: FinancialColors.primaryWhite,
        unselectedItemColor: FinancialColors.darkSecondaryText,
        elevation: 8,
        selectedLabelStyle: const TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.w600,
        ),
        unselectedLabelStyle: const TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.normal,
        ),
      ),

      // 文本主题
      textTheme: _buildTextTheme(colorScheme),

      // 分割线主题 - 黑白风格暗色模式
      dividerTheme: DividerThemeData(
        color: FinancialColors.darkDivider,
        thickness: 1,
        space: 1,
      ),

      // 列表瓦片主题 - 黑白风格暗色模式
      listTileTheme: ListTileThemeData(
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        textColor: FinancialColors.darkPrimaryText,
        iconColor: FinancialColors.darkPrimaryText,
      ),

      // 对话框主题 - 黑白风格暗色模式
      dialogTheme: DialogThemeData(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        elevation: 8,
        backgroundColor: FinancialColors.darkCardBackground,
        titleTextStyle: TextStyle(
          color: FinancialColors.darkPrimaryText,
          fontSize: 20,
          fontWeight: FontWeight.w600,
        ),
        contentTextStyle: TextStyle(
          color: FinancialColors.darkSecondaryText,
          fontSize: 16,
        ),
      ),

      // 浮动操作按钮主题 - 黑白风格暗色模式
      floatingActionButtonTheme: FloatingActionButtonThemeData(
        elevation: 4,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        backgroundColor: FinancialColors.primaryWhite,
        foregroundColor: FinancialColors.primaryBlack,
      ),

      // 开关主题 - 黑白风格暗色模式
      switchTheme: SwitchThemeData(
        thumbColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return FinancialColors.primaryBlack;
          }
          return FinancialColors.darkSecondaryText;
        }),
        trackColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return FinancialColors.primaryWhite;
          }
          return FinancialColors.darkBorder;
        }),
        trackOutlineColor: WidgetStateProperty.resolveWith((states) {
          return FinancialColors.darkBorder;
        }),
      ),

      // 滑块主题 - 黑白风格暗色模式
      sliderTheme: SliderThemeData(
        activeTrackColor: FinancialColors.primaryWhite,
        inactiveTrackColor: FinancialColors.darkBorder,
        thumbColor: FinancialColors.primaryWhite,
        overlayColor: FinancialColors.primaryWhite.withValues(alpha: 0.1),
        valueIndicatorColor: FinancialColors.primaryWhite,
        valueIndicatorTextStyle: TextStyle(
          color: FinancialColors.primaryBlack,
          fontSize: 12,
        ),
      ),

      // 进度指示器主题 - 黑白风格暗色模式
      progressIndicatorTheme: ProgressIndicatorThemeData(
        color: FinancialColors.primaryWhite, // 进度条为白色
        linearTrackColor: FinancialColors.darkBorder, // 轨道为深灰色
        circularTrackColor: FinancialColors.darkBorder, // 圆形轨道为深灰色
      ),

      // 标签栏主题 - 黑白风格暗色模式
      tabBarTheme: TabBarThemeData(
        labelColor: FinancialColors.darkPrimaryText, // 选中标签为白色
        unselectedLabelColor: FinancialColors.darkSecondaryText, // 未选中标签为灰色
        labelStyle: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
        unselectedLabelStyle: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.normal,
        ),
        indicator: UnderlineTabIndicator(
          borderSide: BorderSide(color: FinancialColors.primaryWhite, width: 2),
        ),
        indicatorSize: TabBarIndicatorSize.label,
      ),

      // 芯片主题 - 黑白风格暗色模式
      chipTheme: ChipThemeData(
        backgroundColor: FinancialColors.darkGray,
        selectedColor: FinancialColors.primaryWhite,
        labelStyle: TextStyle(color: FinancialColors.darkPrimaryText),
        side: BorderSide(color: FinancialColors.darkBorder),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      ),
    );
  }

  /// 构建文本主题
  static TextTheme _buildTextTheme(ColorScheme colorScheme) {
    return TextTheme(
      // 显示文本
      displayLarge: TextStyle(
        fontSize: 57,
        fontWeight: FontWeight.w400,
        color: colorScheme.onSurface,
        height: 1.12,
      ),
      displayMedium: TextStyle(
        fontSize: 45,
        fontWeight: FontWeight.w400,
        color: colorScheme.onSurface,
        height: 1.16,
      ),
      displaySmall: TextStyle(
        fontSize: 36,
        fontWeight: FontWeight.w400,
        color: colorScheme.onSurface,
        height: 1.22,
      ),

      // 标题文本
      headlineLarge: TextStyle(
        fontSize: 32,
        fontWeight: FontWeight.w600,
        color: colorScheme.onSurface,
        height: 1.25,
      ),
      headlineMedium: TextStyle(
        fontSize: 28,
        fontWeight: FontWeight.w600,
        color: colorScheme.onSurface,
        height: 1.29,
      ),
      headlineSmall: TextStyle(
        fontSize: 24,
        fontWeight: FontWeight.w600,
        color: colorScheme.onSurface,
        height: 1.33,
      ),

      // 标题文本
      titleLarge: TextStyle(
        fontSize: 22,
        fontWeight: FontWeight.w500,
        color: colorScheme.onSurface,
        height: 1.27,
      ),
      titleMedium: TextStyle(
        fontSize: 16,
        fontWeight: FontWeight.w500,
        color: colorScheme.onSurface,
        height: 1.50,
      ),
      titleSmall: TextStyle(
        fontSize: 14,
        fontWeight: FontWeight.w500,
        color: colorScheme.onSurface,
        height: 1.43,
      ),

      // 标签文本
      labelLarge: TextStyle(
        fontSize: 14,
        fontWeight: FontWeight.w500,
        color: colorScheme.onSurface,
        height: 1.43,
      ),
      labelMedium: TextStyle(
        fontSize: 12,
        fontWeight: FontWeight.w500,
        color: colorScheme.onSurface,
        height: 1.33,
      ),
      labelSmall: TextStyle(
        fontSize: 11,
        fontWeight: FontWeight.w500,
        color: colorScheme.onSurface,
        height: 1.45,
      ),

      // 正文文本
      bodyLarge: TextStyle(
        fontSize: 16,
        fontWeight: FontWeight.w400,
        color: colorScheme.onSurface,
        height: 1.50,
      ),
      bodyMedium: TextStyle(
        fontSize: 14,
        fontWeight: FontWeight.w400,
        color: colorScheme.onSurface,
        height: 1.43,
      ),
      bodySmall: TextStyle(
        fontSize: 12,
        fontWeight: FontWeight.w400,
        color: colorScheme.onSurface,
        height: 1.33,
      ),
    );
  }
}

/// 金融应用专用颜色 - 黑白主色调配色方案
class FinancialColors {
  // 主品牌色 - 黑白色系
  static const Color primaryBlack = Color(0xFF000000); // 主黑色
  static const Color primaryWhite = Color(0xFFFFFFFF); // 主白色
  static const Color darkGray = Color(0xFF1A1A1A); // 深灰色
  static const Color lightGray = Color(0xFFF5F5F5); // 浅灰色

  // 涨跌颜色
  static const Color bullish = Color(0xFF00C851); // 绿色 - 上涨
  static const Color bearish = Color(0xFFFF4444); // 红色 - 下跌
  static const Color neutral = Color(0xFF666666); // 灰色 - 平盘

  // 背景色系 - 黑白风格
  static const Color lightBackground = Color(0xFFFFFFFF); // 白色背景
  static const Color darkBackground = Color(0xFF000000); // 黑色背景
  static const Color cardBackground = Color(0xFFFFFFFF); // 卡片背景
  static const Color darkCardBackground = Color(0xFF1A1A1A); // 深色卡片背景

  // 文本颜色 - 黑白风格
  static const Color primaryText = Color(0xFF000000); // 主要文本
  static const Color secondaryText = Color(0xFF666666); // 次要文本
  static const Color disabledText = Color(0xFFCCCCCC); // 禁用文本
  static const Color darkPrimaryText = Color(0xFFFFFFFF); // 深色主要文本
  static const Color darkSecondaryText = Color(0xFFCCCCCC); // 深色次要文本

  // 边框和分割线 - 黑白风格
  static const Color border = Color(0xFFE0E0E0); // 边框色
  static const Color darkBorder = Color(0xFF333333); // 深色边框
  static const Color divider = Color(0xFFF0F0F0); // 分割线
  static const Color darkDivider = Color(0xFF2A2A2A); // 深色分割线

  // 风险等级颜色
  static const Color lowRisk = Color(0xFF00C851); // 低风险 - 绿色
  static const Color mediumRisk = Color(0xFFFF9800); // 中风险 - 橙色
  static const Color highRisk = Color(0xFFFF4444); // 高风险 - 红色

  // 状态颜色
  static const Color success = Color(0xFF00C851); // 成功
  static const Color warning = Color(0xFFFF9800); // 警告
  static const Color error = Color(0xFFFF4444); // 错误
  static const Color info = Color(0xFF666666); // 信息

  // 图表颜色 - 黑白风格
  static const List<Color> chartColors = [
    Color(0xFF000000), // 黑色
    Color(0xFF666666), // 深灰
    Color(0xFF999999), // 中灰
    Color(0xFF00C851), // 绿色
    Color(0xFFFF4444), // 红色
    Color(0xFFFF9800), // 橙色
    Color(0xFF2196F3), // 蓝色
    Color(0xFF9C27B0), // 紫色
  ];

  // 渐变色 - 黑白风格
  static const LinearGradient bullishGradient = LinearGradient(
    colors: [Color(0xFF00C851), Color(0xFF4CAF50)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient bearishGradient = LinearGradient(
    colors: [Color(0xFFFF4444), Color(0xFFF44336)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient primaryGradient = LinearGradient(
    colors: [Color(0xFF000000), Color(0xFF333333)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  // 黑白特色渐变
  static const LinearGradient blackWhiteGradient = LinearGradient(
    colors: [Color(0xFF000000), Color(0xFF666666)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  // 深色模式渐变
  static const LinearGradient darkGradient = LinearGradient(
    colors: [Color(0xFF000000), Color(0xFF1A1A1A)],
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
  );
}

/// 金融应用专用文本样式
class FinancialTextStyles {
  // 价格文本样式
  static TextStyle priceText({
    required double fontSize,
    required Color color,
    FontWeight fontWeight = FontWeight.w600,
  }) {
    return TextStyle(
      fontSize: fontSize,
      fontWeight: fontWeight,
      color: color,
      fontFeatures: const [FontFeature.tabularFigures()],
    );
  }

  // 百分比文本样式
  static TextStyle percentageText({
    required double fontSize,
    required Color color,
    FontWeight fontWeight = FontWeight.w500,
  }) {
    return TextStyle(
      fontSize: fontSize,
      fontWeight: fontWeight,
      color: color,
      fontFeatures: const [FontFeature.tabularFigures()],
    );
  }

  // 数字文本样式（等宽字体）
  static TextStyle monospaceText({
    required double fontSize,
    required Color color,
    FontWeight fontWeight = FontWeight.w400,
  }) {
    return TextStyle(
      fontSize: fontSize,
      fontWeight: fontWeight,
      color: color,
      fontFamily: 'monospace',
      fontFeatures: const [FontFeature.tabularFigures()],
    );
  }

  /// 获取涨跌颜色
  static Color getTrendColor(double change, {bool isDark = false}) {
    if (change > 0) {
      return FinancialColors.bullish;
    } else if (change < 0) {
      return FinancialColors.bearish;
    } else {
      return isDark ? Colors.grey.shade400 : Colors.grey.shade600;
    }
  }

  /// 获取风险等级颜色
  static Color getRiskColor(String riskLevel) {
    switch (riskLevel.toLowerCase()) {
      case 'low':
      case '低':
        return FinancialColors.lowRisk;
      case 'medium':
      case '中':
        return FinancialColors.mediumRisk;
      case 'high':
      case '高':
        return FinancialColors.highRisk;
      default:
        return FinancialColors.neutral;
    }
  }

  /// 获取图表颜色
  static Color getChartColor(int index) {
    return FinancialColors.chartColors[index %
        FinancialColors.chartColors.length];
  }
}
