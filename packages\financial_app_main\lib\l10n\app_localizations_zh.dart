// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Chinese (`zh`).
class AppLocalizationsZh extends AppLocalizations {
  AppLocalizationsZh([String locale = 'zh']) : super(locale);

  @override
  String get appTitle => '金融交易App';

  @override
  String get welcomeMessage => '欢迎来到金融交易App！';

  @override
  String get login => '登录';

  @override
  String get register => '注册';

  @override
  String get trade => '交易';

  @override
  String get market => '市场';

  @override
  String get portfolio => '投资组合';

  @override
  String get notifications => '通知';

  @override
  String get settings => '设置';

  @override
  String get language => '语言';

  @override
  String get theme => '主题';
}
