import 'package:flutter/foundation.dart';
import 'package:financial_app_core/financial_app_core.dart';
import '../utils/main_logger.dart' as main_logger;
import '../utils/debug_log_controller.dart';

/// 日志系统管理器
///
/// 负责整个应用的日志系统初始化、配置管理和监控
class LoggingSystemManager {
  static final LoggingSystemManager _instance =
      LoggingSystemManager._internal();
  factory LoggingSystemManager() => _instance;
  LoggingSystemManager._internal();

  static LoggingSystemManager get instance => _instance;

  bool _isInitialized = false;

  /// 初始化日志系统
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // 1. 根据环境配置日志级别
      await _configureLogLevel();

      // 2. 初始化日志聚合器
      _initializeLogAggregator();

      // 3. 启动监控面板
      _startMonitoring();

      // 4. 初始化调试控制器
      DebugLogController.initialize();

      // 5. 设置错误处理
      _setupErrorHandling();

      _isInitialized = true;

      main_logger.MainLoggerUtils.logAppStartup(
        success: true,
        initializedModules: ['LoggingSystem', 'LogAggregator', 'LogMonitoring'],
      );

      // 6. 显示初始化信息
      _showInitializationInfo();
    } catch (e, stackTrace) {
      main_logger.MainLoggerUtils.logAppStartup(
        success: false,
        error: e.toString(),
      );

      main_logger.MainLoggerUtils.error(
        '日志系统初始化失败',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// 根据环境配置日志级别
  Future<void> _configureLogLevel() async {
    if (kDebugMode) {
      // 开发环境：显示所有日志，但隐藏堆栈跟踪
      await LogConfigManager.switchToDevelopmentMode();
      // 确保隐藏堆栈跟踪信息，避免重复显示
      await LogConfigManager.hideStackTrace();
      main_logger.MainLoggerUtils.info('🔧 开发环境：启用详细日志模式（已隐藏堆栈跟踪）');
    } else if (kProfileMode) {
      // 性能测试环境：显示重要日志
      await LogConfigManager.switchToConciseMode();
      main_logger.MainLoggerUtils.info('📊 性能测试环境：启用简洁日志模式');
    } else {
      // 生产环境：最小日志输出
      await LogConfigManager.switchToProductionMode();
      main_logger.MainLoggerUtils.info('🚀 生产环境：启用生产日志模式');
    }
  }

  /// 初始化日志聚合器
  void _initializeLogAggregator() {
    final aggregator = LogAggregator.instance;

    // 添加一些示例日志记录用于测试
    aggregator.addLogRecord(
      AppLogRecord(
        timestamp: DateTime.now(),
        module: 'Main',
        level: LogLevel.info,
        message: '日志聚合器初始化完成',
        metadata: {'component': 'LoggingSystemManager'},
      ),
    );

    main_logger.MainLoggerUtils.info('📊 日志聚合器初始化完成');
  }

  /// 启动监控面板
  void _startMonitoring() {
    final monitor = LogMonitoringDashboard.instance;

    // 添加告警回调
    monitor.addAlertCallback((alert) {
      _handleAlert(alert);
    });

    // 设置告警阈值
    monitor.setThreshold('error_rate_threshold', 5.0); // 错误率5%
    monitor.setThreshold('response_time_threshold', 2000); // 响应时间2秒

    // 启动监控
    monitor.startMonitoring();

    main_logger.MainLoggerUtils.info('🔍 日志监控面板启动完成');
  }

  /// 处理告警
  void _handleAlert(AlertInfo alert) {
    main_logger.MainLoggerUtils.warning(
      '🚨 收到日志告警',
      metadata: {
        'alert_type': alert.type.toString(),
        'severity': alert.severity.toString(),
        'message': alert.message,
        'timestamp': alert.timestamp.toIso8601String(),
      },
    );

    // 在开发环境中可以显示更详细的告警信息
    if (kDebugMode) {
      main_logger.MainLoggerUtils.debug('告警详细信息', metadata: alert.data);
    }

    // 可以在这里添加其他告警处理逻辑，如：
    // - 发送推送通知
    // - 记录到外部监控系统
    // - 触发自动恢复机制
  }

  /// 设置错误处理
  void _setupErrorHandling() {
    // 设置Flutter错误处理
    FlutterError.onError = (FlutterErrorDetails details) {
      main_logger.MainLoggerUtils.error(
        '❌ Flutter错误: ${details.exception}',
        error: details.exception,
        stackTrace: details.stack,
        metadata: {'error_type': 'FlutterError', 'library': details.library},
      );
    };

    // 设置平台错误处理
    PlatformDispatcher.instance.onError = (error, stack) {
      main_logger.MainLoggerUtils.error(
        '❌ 平台错误: $error',
        error: error,
        stackTrace: stack,
        metadata: {'error_type': 'PlatformError', 'source': 'Platform'},
      );
      return true;
    };

    main_logger.MainLoggerUtils.info('⚠️ 全局错误处理设置完成');
  }

  /// 显示初始化信息
  void _showInitializationInfo() {
    main_logger.MainLoggerUtils.info('📋 日志系统初始化完成');

    if (kDebugMode) {
      main_logger.MainLoggerUtils.info('🎯 开发环境快捷操作:');
      main_logger.MainLoggerUtils.info(
        '  • DebugLogController.toggleDebugMode() - 切换调试日志',
      );
      main_logger.MainLoggerUtils.info(
        '  • DebugLogController.hideDebugLogs() - 隐藏调试日志',
      );
      main_logger.MainLoggerUtils.info(
        '  • DebugLogController.showDebugLogs() - 显示调试日志',
      );
      main_logger.MainLoggerUtils.info(
        '  • LoggingSystemManager.instance.showSystemStatus() - 查看系统状态',
      );
      main_logger.MainLoggerUtils.info(
        '  • LoggingSystemManager.instance.exportLogs() - 导出日志',
      );
    }
  }

  /// 显示系统状态
  void showSystemStatus() {
    main_logger.MainLoggerUtils.info('📊 日志系统状态报告');

    // 显示当前配置
    LogConfigManager.printConfigStatus();

    // 显示统计信息
    LogConfigManager.printLogStatistics();

    // 显示监控状态
    final monitorStatus = LogMonitoringDashboard.instance.getMonitoringStatus();
    main_logger.MainLoggerUtils.info('监控状态', metadata: monitorStatus);

    // 显示缓存状态
    final bufferStatus = LogAggregator.instance.getBufferStatus();
    main_logger.MainLoggerUtils.info('缓存状态', metadata: bufferStatus);
  }

  /// 导出日志
  Future<String> exportLogs({
    String format = 'json',
    Duration? timeRange,
    List<String>? modules,
  }) async {
    final startTime = timeRange != null
        ? DateTime.now().subtract(timeRange)
        : DateTime.now().subtract(const Duration(hours: 24));

    final logs = await LogAggregator.instance.exportLogs(
      format: format,
      startTime: startTime,
      modules: modules,
    );

    main_logger.MainLoggerUtils.info(
      '📤 日志导出完成',
      metadata: {
        'format': format,
        'start_time': startTime.toIso8601String(),
        'modules': modules,
        'log_size': logs.length,
      },
    );

    return logs;
  }

  /// 切换日志模式
  Future<void> switchLogMode(String mode) async {
    switch (mode.toLowerCase()) {
      case 'development':
      case 'debug':
        await LogConfigManager.switchToDevelopmentMode();
        await DebugLogController.showDebugLogs();
        break;
      case 'concise':
      case 'simple':
        await LogConfigManager.switchToConciseMode();
        await DebugLogController.hideDebugLogs();
        break;
      case 'production':
      case 'prod':
        await LogConfigManager.switchToProductionMode();
        break;
      case 'quiet':
      case 'silent':
        await LogConfigManager.switchToQuietMode();
        await DebugLogController.switchToQuietMode();
        break;
      default:
        main_logger.MainLoggerUtils.warning('未知的日志模式: $mode');
        return;
    }

    main_logger.MainLoggerUtils.info(
      '✅ 日志模式已切换',
      metadata: {
        'new_mode': mode,
        'current_config': DebugLogController.getCurrentModeDescription(),
      },
    );
  }

  /// 重置日志系统
  Future<void> reset() async {
    main_logger.MainLoggerUtils.info('🔄 重置日志系统...');

    // 清除聚合器缓存
    LogAggregator.instance.clearLogs();

    // 重置统计信息
    LogConfigManager.resetStatistics();

    // 重新初始化
    _isInitialized = false;
    await initialize();

    main_logger.MainLoggerUtils.info('✅ 日志系统重置完成');
  }

  /// 获取性能报告
  Map<String, dynamic> getPerformanceReport() {
    return LogAggregator.instance.getPerformanceReport();
  }

  /// 获取错误分析报告
  Map<String, dynamic> getErrorAnalysisReport() {
    return LogAggregator.instance.getErrorAnalysisReport();
  }

  /// 检查系统健康状态
  Map<String, dynamic> checkSystemHealth() {
    final perfReport = getPerformanceReport();
    final errorReport = getErrorAnalysisReport();
    final bufferStatus = LogAggregator.instance.getBufferStatus();

    final summary = perfReport['summary'] as Map<String, dynamic>;
    final errorAnalysis = errorReport['error_analysis'] as Map<String, dynamic>;

    final errorRate =
        double.tryParse(
          (summary['error_rate'] as String).replaceAll('%', ''),
        ) ??
        0;

    final bufferUsage =
        double.tryParse(
          (bufferStatus['buffer_usage'] as String).replaceAll('%', ''),
        ) ??
        0;

    String healthStatus;
    if (errorRate > 10 || bufferUsage > 90) {
      healthStatus = 'critical';
    } else if (errorRate > 5 || bufferUsage > 70) {
      healthStatus = 'warning';
    } else {
      healthStatus = 'healthy';
    }

    return {
      'health_status': healthStatus,
      'error_rate': errorRate,
      'buffer_usage': bufferUsage,
      'total_logs': summary['total_logs'],
      'total_errors': summary['total_errors'],
      'error_patterns': errorAnalysis['total_error_patterns'],
      'recommendations': _getHealthRecommendations(
        healthStatus,
        errorRate,
        bufferUsage,
      ),
    };
  }

  /// 获取健康建议
  List<String> _getHealthRecommendations(
    String status,
    double errorRate,
    double bufferUsage,
  ) {
    final recommendations = <String>[];

    if (status == 'critical') {
      recommendations.add('🚨 系统状态严重，建议立即检查错误日志');
    }

    if (errorRate > 10) {
      recommendations.add('❌ 错误率过高，建议检查应用稳定性');
    } else if (errorRate > 5) {
      recommendations.add('⚠️ 错误率偏高，建议关注错误模式');
    }

    if (bufferUsage > 90) {
      recommendations.add('💾 日志缓存使用率过高，建议导出或清理日志');
    } else if (bufferUsage > 70) {
      recommendations.add('📊 日志缓存使用率较高，建议定期清理');
    }

    if (recommendations.isEmpty) {
      recommendations.add('✅ 系统运行良好，继续保持');
    }

    return recommendations;
  }

  /// 获取调试面板信息
  Map<String, dynamic> getDebugPanelInfo() {
    final debugInfo = DebugLogController.getDebugPanelInfo();
    final healthInfo = checkSystemHealth();

    return {
      ...debugInfo,
      'system_health': healthInfo,
      'quick_actions': [
        {
          'name': '系统状态',
          'action': 'showSystemStatus',
          'description': '查看详细的系统状态信息',
        },
        {'name': '导出日志', 'action': 'exportLogs', 'description': '导出最近24小时的日志'},
        {'name': '重置系统', 'action': 'reset', 'description': '清理缓存并重新初始化'},
      ],
    };
  }

  /// 执行调试操作
  Future<void> executeDebugAction(String action) async {
    switch (action) {
      case 'showSystemStatus':
        showSystemStatus();
        break;
      case 'exportLogs':
        await exportLogs();
        break;
      case 'reset':
        await reset();
        break;
      default:
        await DebugLogController.executeDebugAction(action);
        break;
    }
  }
}
