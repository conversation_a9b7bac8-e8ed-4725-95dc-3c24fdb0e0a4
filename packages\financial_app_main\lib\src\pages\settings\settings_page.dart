import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:financial_app_core/financial_app_core.dart';

import '../../../providers/theme_provider.dart';
import '../../../providers/locale_provider.dart';
import '../../widgets/theme_switcher.dart';
import 'theme_settings_page.dart';

/// 设置页面
///
/// 提供应用的各种设置选项：
/// - 主题设置
/// - 语言设置
/// - 通知设置
/// - 账户设置
/// - 关于信息
class SettingsPage extends StatelessWidget {
  const SettingsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('设置'),
        elevation: 0,
        actions: [
          // 快速主题切换按钮
          const ThemeSwitcher(style: ThemeSwitcherStyle.iconButton),
          const SizedBox(width: 8),
        ],
      ),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          // 外观设置
          _buildAppearanceSection(context),

          const SizedBox(height: 24),

          // 通用设置
          _buildGeneralSection(context),

          const SizedBox(height: 24),

          // 账户设置
          _buildAccountSection(context),

          const SizedBox(height: 24),

          // 关于设置
          _buildAboutSection(context),
        ],
      ),
    );
  }

  /// 构建外观设置区域
  Widget _buildAppearanceSection(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '外观设置',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 16),

            // 主题设置
            ListTile(
              leading: const Icon(Icons.palette),
              title: const Text('主题设置'),
              subtitle: Consumer<ThemeProvider>(
                builder: (context, themeProvider, child) {
                  return Text(
                    '当前: ${_getThemeModeText(themeProvider.themeMode)}',
                  );
                },
              ),
              trailing: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const ThemeColorPreview(size: 24),
                  const SizedBox(width: 8),
                  const Icon(Icons.chevron_right),
                ],
              ),
              onTap: () {
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => const ThemeSettingsPage(),
                  ),
                );
              },
            ),

            const Divider(),

            // 语言设置
            Consumer<LocaleProvider>(
              builder: (context, localeProvider, child) {
                return ListTile(
                  leading: const Icon(Icons.language),
                  title: const Text('语言设置'),
                  subtitle: Text(
                    '当前: ${_getLocaleText(localeProvider.locale)}',
                  ),
                  trailing: const Icon(Icons.chevron_right),
                  onTap: () {
                    _showLanguageDialog(context, localeProvider);
                  },
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  /// 构建通用设置区域
  Widget _buildGeneralSection(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '通用设置',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 16),

            // 通知设置
            ListTile(
              leading: const Icon(Icons.notifications),
              title: const Text('通知设置'),
              subtitle: const Text('管理推送通知和提醒'),
              trailing: const Icon(Icons.chevron_right),
              onTap: () {
                // TODO: 导航到通知设置页面
                ScaffoldMessenger.of(
                  context,
                ).showSnackBar(const SnackBar(content: Text('通知设置功能开发中')));
              },
            ),

            const Divider(),

            // 数据设置
            ListTile(
              leading: const Icon(Icons.storage),
              title: const Text('数据设置'),
              subtitle: const Text('缓存管理和数据同步'),
              trailing: const Icon(Icons.chevron_right),
              onTap: () {
                // TODO: 导航到数据设置页面
                ScaffoldMessenger.of(
                  context,
                ).showSnackBar(const SnackBar(content: Text('数据设置功能开发中')));
              },
            ),

            const Divider(),

            // 安全设置
            ListTile(
              leading: const Icon(Icons.security),
              title: const Text('安全设置'),
              subtitle: const Text('密码和生物识别'),
              trailing: const Icon(Icons.chevron_right),
              onTap: () {
                // TODO: 导航到安全设置页面
                ScaffoldMessenger.of(
                  context,
                ).showSnackBar(const SnackBar(content: Text('安全设置功能开发中')));
              },
            ),
          ],
        ),
      ),
    );
  }

  /// 构建账户设置区域
  Widget _buildAccountSection(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '账户设置',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 16),

            // 个人信息
            ListTile(
              leading: const Icon(Icons.person),
              title: const Text('个人信息'),
              subtitle: const Text('编辑个人资料和偏好'),
              trailing: const Icon(Icons.chevron_right),
              onTap: () {
                // TODO: 导航到个人信息页面
                ScaffoldMessenger.of(
                  context,
                ).showSnackBar(const SnackBar(content: Text('个人信息功能开发中')));
              },
            ),

            const Divider(),

            // 账户管理
            ListTile(
              leading: const Icon(Icons.account_circle),
              title: const Text('账户管理'),
              subtitle: const Text('登录状态和账户绑定'),
              trailing: const Icon(Icons.chevron_right),
              onTap: () {
                // TODO: 导航到账户管理页面
                ScaffoldMessenger.of(
                  context,
                ).showSnackBar(const SnackBar(content: Text('账户管理功能开发中')));
              },
            ),
          ],
        ),
      ),
    );
  }

  /// 构建关于设置区域
  Widget _buildAboutSection(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '关于',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 16),

            // 版本信息
            ListTile(
              leading: const Icon(Icons.info),
              title: const Text('版本信息'),
              subtitle: const Text('v1.0.0 (Build 1)'),
              trailing: const Icon(Icons.chevron_right),
              onTap: () {
                _showVersionDialog(context);
              },
            ),

            const Divider(),

            // 帮助与反馈
            ListTile(
              leading: const Icon(Icons.help),
              title: const Text('帮助与反馈'),
              subtitle: const Text('使用帮助和问题反馈'),
              trailing: const Icon(Icons.chevron_right),
              onTap: () {
                // TODO: 导航到帮助页面
                ScaffoldMessenger.of(
                  context,
                ).showSnackBar(const SnackBar(content: Text('帮助功能开发中')));
              },
            ),

            const Divider(),

            // 隐私政策
            ListTile(
              leading: const Icon(Icons.privacy_tip),
              title: const Text('隐私政策'),
              subtitle: const Text('了解我们如何保护您的隐私'),
              trailing: const Icon(Icons.chevron_right),
              onTap: () {
                // TODO: 导航到隐私政策页面
                ScaffoldMessenger.of(
                  context,
                ).showSnackBar(const SnackBar(content: Text('隐私政策功能开发中')));
              },
            ),
          ],
        ),
      ),
    );
  }

  /// 显示语言选择对话框
  void _showLanguageDialog(
    BuildContext context,
    LocaleProvider localeProvider,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('选择语言'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            RadioListTile<Locale>(
              title: const Text('简体中文'),
              value: const Locale('zh', 'CN'),
              groupValue: localeProvider.locale,
              onChanged: (value) {
                if (value != null) {
                  localeProvider.setLocale(value);
                  Navigator.of(context).pop();
                }
              },
            ),
            RadioListTile<Locale>(
              title: const Text('English'),
              value: const Locale('en', 'US'),
              groupValue: localeProvider.locale,
              onChanged: (value) {
                if (value != null) {
                  localeProvider.setLocale(value);
                  Navigator.of(context).pop();
                }
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
        ],
      ),
    );
  }

  /// 显示版本信息对话框
  void _showVersionDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('版本信息'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '金融应用',
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            const Text('版本: v1.0.0'),
            const Text('构建号: 1'),
            const Text('构建时间: 2024-01-01'),
            const SizedBox(height: 16),
            Text(
              '功能特性:',
              style: Theme.of(
                context,
              ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 8),
            const Text('• 高性能图表组件'),
            const Text('• 自定义主题系统'),
            const Text('• 多语言支持'),
            const Text('• 模块化架构'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  /// 获取主题模式文本
  String _getThemeModeText(ThemeMode themeMode) {
    switch (themeMode) {
      case ThemeMode.system:
        return '跟随系统';
      case ThemeMode.light:
        return '亮色模式';
      case ThemeMode.dark:
        return '暗色模式';
    }
  }

  /// 获取语言文本
  String _getLocaleText(Locale locale) {
    switch (locale.languageCode) {
      case 'zh':
        return '简体中文';
      case 'en':
        return 'English';
      default:
        return '简体中文';
    }
  }
}
