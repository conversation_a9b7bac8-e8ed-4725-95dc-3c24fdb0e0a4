import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:financial_app_core/financial_app_core.dart';
import 'dart:ui';

import '../../../providers/theme_provider.dart';
import '../../../config/app_theme.dart';
import '../../widgets/theme_color_picker.dart';

/// 主题设置页面
///
/// 提供完整的主题配置选项：
/// - Light/Dark/System模式切换
/// - 主色调选择
/// - 自定义主题开关
/// - 实时预览效果
class ThemeSettingsPage extends StatelessWidget {
  const ThemeSettingsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('主题设置'), elevation: 0),
      body: Consumer<ThemeProvider>(
        builder: (context, themeProvider, child) {
          return ListView(
            padding: const EdgeInsets.all(16),
            children: [
              // 主题模式选择
              _buildThemeModeSection(context, themeProvider),

              const SizedBox(height: 24),

              // 自定义主题开关
              _buildCustomThemeSection(context, themeProvider),

              const SizedBox(height: 24),

              // 主色调选择
              if (themeProvider.useCustomTheme) ...[
                _buildAccentColorSection(context, themeProvider),
                const SizedBox(height: 24),
              ],

              // 预览区域
              _buildPreviewSection(context, themeProvider),

              const SizedBox(height: 24),

              // 重置按钮
              _buildResetSection(context, themeProvider),
            ],
          );
        },
      ),
    );
  }

  /// 构建主题模式选择区域
  Widget _buildThemeModeSection(
    BuildContext context,
    ThemeProvider themeProvider,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '主题模式',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 16),

            // 系统跟随
            RadioListTile<ThemeMode>(
              title: const Text('跟随系统'),
              subtitle: const Text('根据系统设置自动切换'),
              value: ThemeMode.system,
              groupValue: themeProvider.themeMode,
              onChanged: (value) {
                if (value != null) {
                  themeProvider.setThemeMode(value);
                }
              },
            ),

            // 亮色模式
            RadioListTile<ThemeMode>(
              title: const Text('亮色模式'),
              subtitle: const Text('始终使用亮色主题'),
              value: ThemeMode.light,
              groupValue: themeProvider.themeMode,
              onChanged: (value) {
                if (value != null) {
                  themeProvider.setThemeMode(value);
                }
              },
            ),

            // 暗色模式
            RadioListTile<ThemeMode>(
              title: const Text('暗色模式'),
              subtitle: const Text('始终使用暗色主题'),
              value: ThemeMode.dark,
              groupValue: themeProvider.themeMode,
              onChanged: (value) {
                if (value != null) {
                  themeProvider.setThemeMode(value);
                }
              },
            ),
          ],
        ),
      ),
    );
  }

  /// 构建自定义主题区域
  Widget _buildCustomThemeSection(
    BuildContext context,
    ThemeProvider themeProvider,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '自定义主题',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 8),
            Text(
              '启用自定义主题以获得更好的视觉体验',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
              ),
            ),
            const SizedBox(height: 16),

            SwitchListTile(
              title: const Text('启用自定义主题'),
              subtitle: const Text('使用金融应用专用的主题设计'),
              value: themeProvider.useCustomTheme,
              onChanged: (value) {
                themeProvider.setUseCustomTheme(value);
              },
            ),
          ],
        ),
      ),
    );
  }

  /// 构建主色调选择区域
  Widget _buildAccentColorSection(
    BuildContext context,
    ThemeProvider themeProvider,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '主色调',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 16),

            Wrap(
              spacing: 12,
              runSpacing: 12,
              children: ThemeProvider.availableAccentColors.map((colorName) {
                final color = _getColorFromName(colorName);
                final isSelected = themeProvider.accentColor == colorName;

                return GestureDetector(
                  onTap: () {
                    themeProvider.setAccentColor(colorName);
                  },
                  child: Container(
                    width: 60,
                    height: 60,
                    decoration: BoxDecoration(
                      color: color,
                      shape: BoxShape.circle,
                      border: isSelected
                          ? Border.all(
                              color: Theme.of(context).colorScheme.primary,
                              width: 3,
                            )
                          : null,
                      boxShadow: [
                        BoxShadow(
                          color: color.withOpacity(0.3),
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: isSelected
                        ? Icon(Icons.check, color: Colors.white, size: 24)
                        : null,
                  ),
                );
              }).toList(),
            ),

            const SizedBox(height: 16),

            Text(
              '当前选择: ${themeProvider.getAccentColorName(themeProvider.accentColor)}',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: themeProvider.getPrimaryColor(),
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建预览区域
  Widget _buildPreviewSection(
    BuildContext context,
    ThemeProvider themeProvider,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '预览效果',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 16),

            // 按钮预览
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {},
                    child: const Text('主要按钮'),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: OutlinedButton(
                    onPressed: () {},
                    child: const Text('次要按钮'),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // 卡片预览
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('示例卡片', style: Theme.of(context).textTheme.titleSmall),
                  const SizedBox(height: 8),
                  Text(
                    '这是一个示例卡片，展示当前主题的效果。',
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                  const SizedBox(height: 12),

                  // 金融数据预览
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '股价',
                            style: Theme.of(context).textTheme.bodySmall,
                          ),
                          Text(
                            '¥123.45',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: FinancialColors.bullish,
                              fontFeatures: const [
                                FontFeature.tabularFigures(),
                              ],
                            ),
                          ),
                        ],
                      ),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          Text(
                            '涨跌幅',
                            style: Theme.of(context).textTheme.bodySmall,
                          ),
                          Text(
                            '+2.34%',
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                              color: FinancialColors.bullish,
                              fontFeatures: const [
                                FontFeature.tabularFigures(),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建重置区域
  Widget _buildResetSection(BuildContext context, ThemeProvider themeProvider) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '重置设置',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 8),
            Text(
              '将主题设置恢复到默认状态',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
              ),
            ),
            const SizedBox(height: 16),

            SizedBox(
              width: double.infinity,
              child: OutlinedButton.icon(
                onPressed: () {
                  _showResetDialog(context, themeProvider);
                },
                icon: const Icon(Icons.refresh),
                label: const Text('重置为默认设置'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 显示重置确认对话框
  void _showResetDialog(BuildContext context, ThemeProvider themeProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('重置主题设置'),
        content: const Text('确定要将主题设置恢复到默认状态吗？此操作无法撤销。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () {
              themeProvider.setThemeMode(ThemeMode.system);
              themeProvider.setUseCustomTheme(true);
              themeProvider.setAccentColor('black');
              Navigator.of(context).pop();

              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('主题设置已重置'),
                  duration: Duration(seconds: 2),
                ),
              );
            },
            child: const Text('重置'),
          ),
        ],
      ),
    );
  }

  /// 根据颜色名称获取颜色 - 黑白风格
  Color _getColorFromName(String colorName) {
    switch (colorName) {
      case 'black':
        return const Color(0xFF000000); // 纯黑色
      case 'darkGray':
        return const Color(0xFF1A1A1A); // 深灰色
      case 'gray':
        return const Color(0xFF666666); // 中灰色
      case 'lightGray':
        return const Color(0xFF999999); // 浅灰色
      case 'green':
        return const Color(0xFF00C851); // 绿色
      case 'red':
        return const Color(0xFFFF4444); // 红色
      case 'orange':
        return const Color(0xFFFF9800); // 橙色
      case 'blue':
        return const Color(0xFF2196F3); // 蓝色
      default:
        return const Color(0xFF000000); // 默认黑色
    }
  }
}
