import 'package:flutter/foundation.dart';
import 'package:financial_app_core/financial_app_core.dart';

/// 调试日志控制器
///
/// 提供在应用运行时快速切换日志显示模式的功能
class DebugLogController {
  static bool _isDebugMode = true;
  static final MainLogger _logger = MainLogger();

  /// 获取当前是否为调试模式
  static bool get isDebugMode => _isDebugMode;

  /// 初始化调试日志控制器
  static void initialize() {
    _logger.info('调试日志控制器初始化完成');
    _logger.info('当前模式: ${_isDebugMode ? "调试模式" : "简洁模式"}');

    // 默认隐藏堆栈跟踪信息，避免重复显示
    LogConfigManager.hideStackTrace();
    _logger.info('🎯 已隐藏堆栈跟踪信息，避免重复显示');
  }

  /// 切换调试日志显示模式
  static Future<void> toggleDebugMode() async {
    _isDebugMode = !_isDebugMode;

    if (_isDebugMode) {
      // 切换到开发模式 - 显示所有日志
      await LogConfigManager.switchToDevelopmentMode();
      _logger.info('🔧 已切换到调试模式 - 显示所有日志（包括绿色调试日志）');
    } else {
      // 切换到简洁模式 - 隐藏调试日志
      await LogConfigManager.switchToConciseMode();
      _logger.info('🎯 已切换到简洁模式 - 隐藏调试日志（绿色日志已隐藏）');
    }
  }

  /// 快速隐藏调试日志
  static Future<void> hideDebugLogs() async {
    if (_isDebugMode) {
      _isDebugMode = false;
      await LogConfigManager.switchToConciseMode();
      _logger.info('🎯 调试日志已隐藏 - 界面更简洁');
    }
  }

  /// 快速显示调试日志
  static Future<void> showDebugLogs() async {
    if (!_isDebugMode) {
      _isDebugMode = true;
      await LogConfigManager.switchToDevelopmentMode();
      _logger.info('🔧 调试日志已显示 - 便于问题排查');
    }
  }

  /// 切换到静默模式（只显示警告和错误）
  static Future<void> switchToQuietMode() async {
    _isDebugMode = false;
    await LogConfigManager.switchToQuietMode();
    _logger.warning('🔇 已切换到静默模式 - 只显示警告和错误');
  }

  /// 获取当前日志模式描述
  static String getCurrentModeDescription() {
    final config = LogConfigManager.currentConfig;

    if (config.showDebugLogs && config.minLevel == LogLevel.debug) {
      return '🔧 调试模式 - 显示所有日志';
    } else if (!config.showDebugLogs && config.minLevel == LogLevel.info) {
      return '🎯 简洁模式 - 隐藏调试日志';
    } else if (config.minLevel == LogLevel.warning) {
      return '🔇 静默模式 - 只显示警告和错误';
    } else {
      return '⚙️ 自定义模式';
    }
  }

  /// 打印当前模式状态
  static void printCurrentMode() {
    final mode = getCurrentModeDescription();
    _logger.info('当前日志模式: $mode');
  }

  /// 演示不同日志级别的输出
  static void demonstrateLogLevels() {
    _logger.info('📊 演示不同日志级别的输出:');

    // 使用不同模块的日志管理器进行演示
    final coreLogger = CoreLogger();
    // 注意：其他模块的日志管理器需要在相应模块中使用
    // final authLogger = AuthLogger();
    // final marketLogger = MarketLogger();

    _logger.debug('这是主模块的调试日志 (绿色)');
    coreLogger.debug('这是核心模块的调试日志 (绿色)');
    // authLogger.debug('这是认证模块的调试日志 (绿色)');
    // marketLogger.debug('这是市场模块的调试日志 (绿色)');

    _logger.info('这是主模块的信息日志 (蓝色)');
    coreLogger.info('这是核心模块的信息日志 (蓝色)');
    // authLogger.info('这是认证模块的信息日志 (蓝色)');
    // marketLogger.info('这是市场模块的信息日志 (蓝色)');

    _logger.warning('这是主模块的警告日志 (黄色)');
    coreLogger.warning('这是核心模块的警告日志 (黄色)');
    // authLogger.warning('这是认证模块的警告日志 (黄色)');
    // marketLogger.warning('这是市场模块的警告日志 (黄色)');

    _logger.error('这是主模块的错误日志 (红色)');
    coreLogger.error('这是核心模块的错误日志 (红色)');
    // authLogger.error('这是认证模块的错误日志 (红色)');
    // marketLogger.error('这是市场模块的错误日志 (红色)');
  }

  /// 提供快捷键提示
  static void showShortcutHelp() {
    _logger.info('📋 调试日志控制快捷操作:');
    _logger.info('  • DebugLogController.toggleDebugMode() - 切换调试日志显示');
    _logger.info('  • DebugLogController.hideDebugLogs() - 隐藏调试日志');
    _logger.info('  • DebugLogController.showDebugLogs() - 显示调试日志');
    _logger.info('  • DebugLogController.switchToQuietMode() - 静默模式');
    _logger.info('  • DebugLogController.demonstrateLogLevels() - 演示日志级别');
    _logger.info('  • DebugLogController.printCurrentMode() - 查看当前模式');
  }

  /// 在开发环境中提供调试面板信息
  static Map<String, dynamic> getDebugPanelInfo() {
    if (!kDebugMode) return {};

    final config = LogConfigManager.currentConfig;

    return {
      'current_mode': getCurrentModeDescription(),
      'debug_logs_visible': config.showDebugLogs,
      'min_log_level': config.minLevel.name,
      'module_names_visible': config.showModuleNames,
      'timestamps_visible': config.showTimestamps,
      'console_output': config.enableConsoleLogging,
      'file_output': config.enableFileLogging,
      'actions': [
        {
          'name': '切换调试日志',
          'action': 'toggleDebugMode',
          'description': '显示/隐藏绿色调试日志',
        },
        {
          'name': '简洁模式',
          'action': 'hideDebugLogs',
          'description': '隐藏调试日志，界面更简洁',
        },
        {
          'name': '调试模式',
          'action': 'showDebugLogs',
          'description': '显示所有日志，便于调试',
        },
        {
          'name': '静默模式',
          'action': 'switchToQuietMode',
          'description': '只显示警告和错误',
        },
      ],
    };
  }

  /// 执行调试面板操作
  static Future<void> executeDebugAction(String action) async {
    switch (action) {
      case 'toggleDebugMode':
        await toggleDebugMode();
        break;
      case 'hideDebugLogs':
        await hideDebugLogs();
        break;
      case 'showDebugLogs':
        await showDebugLogs();
        break;
      case 'switchToQuietMode':
        await switchToQuietMode();
        break;
      default:
        _logger.warning('未知的调试操作: $action');
        break;
    }
  }

  /// 自动检测并建议最佳日志模式
  static Future<void> suggestOptimalMode() async {
    final config = LogConfigManager.currentConfig;

    if (kDebugMode) {
      // 开发环境建议
      if (config.showDebugLogs) {
        _logger.info('💡 建议: 当前为调试模式，如果日志太多影响查看，可以调用 hideDebugLogs() 隐藏绿色调试日志');
      } else {
        _logger.info('💡 当前为简洁模式，界面清爽。如需调试可以调用 showDebugLogs() 显示详细日志');
      }
    } else {
      // 生产环境建议
      if (config.showDebugLogs || config.minLevel == LogLevel.debug) {
        _logger.warning('⚠️ 生产环境建议使用 LogConfig.production() 以提高性能');
        await LogConfigManager.switchToProductionMode();
      }
    }
  }
}
