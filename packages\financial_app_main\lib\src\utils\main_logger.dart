import 'package:financial_app_core/financial_app_core.dart';

/// 主应用模块专用日志管理器
///
/// 提供主应用模块特定的日志记录功能
class MainLogger extends ModuleLogger {
  static final MainLogger _instance = MainLogger._internal();
  factory MainLogger() => _instance;
  MainLogger._internal();

  @override
  String get moduleName => 'Main';

  // ==================== 主应用特定的日志方法 ====================

  /// 应用启动日志
  void logAppStartup({
    required bool success,
    Duration? startupDuration,
    List<String>? initializedModules,
    String? error,
  }) {
    final metadata = {
      'success': success,
      if (startupDuration != null)
        'startup_duration_ms': startupDuration.inMilliseconds,
      'initialized_modules': initializedModules,
      'modules_count': initializedModules?.length ?? 0,
      if (error != null) 'error': error,
      'timestamp': DateTime.now().toIso8601String(),
    };

    if (success) {
      info('应用启动成功', metadata: metadata);
      if (startupDuration != null) {
        logPerformance('app_startup', startupDuration, metadata: metadata);
      }
    } else {
      super.error('应用启动失败', metadata: metadata);
    }

    logBusinessEvent('app_startup', metadata);
  }

  /// 模块初始化日志
  void logModuleInitialization({
    required String moduleName,
    required bool success,
    Duration? initDuration,
    String? error,
    List<String>? dependencies,
  }) {
    final metadata = {
      'module_name': moduleName,
      'success': success,
      if (initDuration != null) 'init_duration_ms': initDuration.inMilliseconds,
      if (error != null) 'error': error,
      'dependencies': dependencies,
      'timestamp': DateTime.now().toIso8601String(),
    };

    if (success) {
      info('模块初始化成功: $moduleName', metadata: metadata);
      if (initDuration != null) {
        logPerformance('module_init', initDuration, metadata: metadata);
      }
    } else {
      super.error('模块初始化失败: $moduleName', metadata: metadata);
    }

    logBusinessEvent('module_initialization', metadata);
  }

  /// 页面导航日志
  void logNavigation({
    required String fromRoute,
    required String toRoute,
    String? userId,
    Map<String, dynamic>? routeParams,
    Duration? navigationDuration,
  }) {
    final metadata = {
      'from_route': fromRoute,
      'to_route': toRoute,
      'user_id': userId,
      'route_params': routeParams,
      if (navigationDuration != null)
        'navigation_duration_ms': navigationDuration.inMilliseconds,
      'timestamp': DateTime.now().toIso8601String(),
    };

    info('页面导航: $fromRoute -> $toRoute', metadata: metadata);

    if (navigationDuration != null) {
      logPerformance('page_navigation', navigationDuration, metadata: metadata);
    }

    logUserAction('page_navigation', userId, context: metadata);
  }

  /// 应用生命周期日志
  void logAppLifecycle({
    required String state, // 'resumed', 'paused', 'inactive', 'detached'
    Duration? sessionDuration,
    String? previousState,
  }) {
    final metadata = {
      'lifecycle_state': state,
      'previous_state': previousState,
      if (sessionDuration != null)
        'session_duration_ms': sessionDuration.inMilliseconds,
      'timestamp': DateTime.now().toIso8601String(),
    };

    info('应用生命周期变更: $state', metadata: metadata);
    logBusinessEvent('app_lifecycle', metadata);
  }

  /// 错误处理日志
  void logErrorHandling({
    required String errorType,
    required String errorMessage,
    String? errorSource,
    bool? recovered,
    String? recoveryAction,
    dynamic originalError,
    StackTrace? stackTrace,
  }) {
    final metadata = {
      'error_type': errorType,
      'error_source': errorSource,
      'recovered': recovered,
      'recovery_action': recoveryAction,
      'timestamp': DateTime.now().toIso8601String(),
    };

    if (recovered == true) {
      warning('错误已恢复: $errorType', metadata: metadata);
    } else {
      super.error(
        '错误处理: $errorType - $errorMessage',
        error: originalError,
        stackTrace: stackTrace,
        metadata: metadata,
      );
    }

    logBusinessEvent('error_handling', metadata);
  }

  /// 性能监控日志
  void logPerformanceMetrics({
    required String metricType, // 'memory', 'cpu', 'battery', 'network'
    required Map<String, dynamic> metrics,
    String? threshold,
    bool? alertTriggered,
  }) {
    final metadata = {
      'metric_type': metricType,
      'metrics': metrics,
      'threshold': threshold,
      'alert_triggered': alertTriggered,
      'timestamp': DateTime.now().toIso8601String(),
    };

    if (alertTriggered == true) {
      warning('性能告警: $metricType', metadata: metadata);
    } else {
      debug('性能指标: $metricType', metadata: metadata);
    }

    logBusinessEvent('performance_metrics', metadata);
  }

  /// 功能开关日志
  void logFeatureFlag({
    required String featureName,
    required bool enabled,
    String? reason,
    String? userId,
  }) {
    final metadata = {
      'feature_name': featureName,
      'enabled': enabled,
      'reason': reason,
      'user_id': userId,
      'timestamp': DateTime.now().toIso8601String(),
    };

    info('功能开关: $featureName (${enabled ? '启用' : '禁用'})', metadata: metadata);
    logBusinessEvent('feature_flag', metadata);
  }

  /// 配置变更日志
  void logConfigurationChange({
    required String configKey,
    required String operation, // 'create', 'update', 'delete'
    dynamic oldValue,
    dynamic newValue,
    String? source,
  }) {
    final metadata = {
      'config_key': configKey,
      'operation': operation,
      'old_value': oldValue?.toString(),
      'new_value': newValue?.toString(),
      'source': source,
      'timestamp': DateTime.now().toIso8601String(),
    };

    info('配置变更: $configKey ($operation)', metadata: metadata);
    logBusinessEvent('configuration_change', metadata);
  }

  /// 用户界面事件日志
  void logUIEvent({
    required String
    eventType, // 'button_click', 'scroll', 'swipe', 'long_press'
    required String elementId,
    String? screenName,
    String? userId,
    Map<String, dynamic>? eventData,
  }) {
    final metadata = {
      'event_type': eventType,
      'element_id': elementId,
      'screen_name': screenName,
      'user_id': userId,
      'event_data': eventData,
      'timestamp': DateTime.now().toIso8601String(),
    };

    debug('UI事件: $eventType ($elementId)', metadata: metadata);
    logUserAction(eventType, userId, context: metadata);
  }

  /// 网络状态变更日志
  void logNetworkStatusChange({
    required String fromStatus,
    required String toStatus,
    String? connectionType,
    bool? isConnected,
  }) {
    final metadata = {
      'from_status': fromStatus,
      'to_status': toStatus,
      'connection_type': connectionType,
      'is_connected': isConnected,
      'timestamp': DateTime.now().toIso8601String(),
    };

    info('网络状态变更: $fromStatus -> $toStatus', metadata: metadata);
    logBusinessEvent('network_status_change', metadata);
  }

  /// 主题切换日志
  void logThemeChange({
    required String fromTheme,
    required String toTheme,
    String? userId,
    bool? isSystemTheme,
  }) {
    final metadata = {
      'from_theme': fromTheme,
      'to_theme': toTheme,
      'user_id': userId,
      'is_system_theme': isSystemTheme,
      'timestamp': DateTime.now().toIso8601String(),
    };

    info('主题切换: $fromTheme -> $toTheme', metadata: metadata);
    logUserAction('theme_change', userId, context: metadata);
  }

  /// 语言切换日志
  void logLanguageChange({
    required String fromLanguage,
    required String toLanguage,
    String? userId,
  }) {
    final metadata = {
      'from_language': fromLanguage,
      'to_language': toLanguage,
      'user_id': userId,
      'timestamp': DateTime.now().toIso8601String(),
    };

    info('语言切换: $fromLanguage -> $toLanguage', metadata: metadata);
    logUserAction('language_change', userId, context: metadata);
  }

  /// 应用更新日志
  void logAppUpdate({
    required String fromVersion,
    required String toVersion,
    required bool success,
    String? updateType, // 'hot_reload', 'app_store', 'manual'
    Duration? updateDuration,
    String? error,
  }) {
    final metadata = {
      'from_version': fromVersion,
      'to_version': toVersion,
      'success': success,
      'update_type': updateType,
      if (updateDuration != null)
        'update_duration_ms': updateDuration.inMilliseconds,
      if (error != null) 'error': error,
      'timestamp': DateTime.now().toIso8601String(),
    };

    if (success) {
      info('应用更新成功: $fromVersion -> $toVersion', metadata: metadata);
    } else {
      super.error('应用更新失败: $fromVersion -> $toVersion', metadata: metadata);
    }

    logBusinessEvent('app_update', metadata);
  }

  /// 数据备份日志
  void logDataBackup({
    required String backupType, // 'auto', 'manual', 'cloud'
    required bool success,
    int? dataSize,
    Duration? backupDuration,
    String? error,
  }) {
    final metadata = {
      'backup_type': backupType,
      'success': success,
      'data_size_bytes': dataSize,
      if (backupDuration != null)
        'backup_duration_ms': backupDuration.inMilliseconds,
      if (error != null) 'error': error,
      'timestamp': DateTime.now().toIso8601String(),
    };

    if (success) {
      info('数据备份成功: $backupType', metadata: metadata);
      if (backupDuration != null) {
        logPerformance('data_backup', backupDuration, metadata: metadata);
      }
    } else {
      super.error('数据备份失败: $backupType', metadata: metadata);
    }

    logBusinessEvent('data_backup', metadata);
  }
}

/// 主应用模块日志工具类
class MainLoggerUtils {
  static final MainLogger _logger = MainLogger();

  // 基础日志方法
  static void debug(String message, {Map<String, dynamic>? metadata}) =>
      _logger.debug(message, metadata: metadata);

  static void info(String message, {Map<String, dynamic>? metadata}) =>
      _logger.info(message, metadata: metadata);

  static void warning(String message, {Map<String, dynamic>? metadata}) =>
      _logger.warning(message, metadata: metadata);

  static void error(
    String message, {
    dynamic error,
    StackTrace? stackTrace,
    Map<String, dynamic>? metadata,
  }) => _logger.error(
    message,
    error: error,
    stackTrace: stackTrace,
    metadata: metadata,
  );

  // 主应用特定方法
  static void logAppStartup({
    required bool success,
    Duration? startupDuration,
    List<String>? initializedModules,
    String? error,
  }) => _logger.logAppStartup(
    success: success,
    startupDuration: startupDuration,
    initializedModules: initializedModules,
    error: error,
  );

  static void logNavigation({
    required String fromRoute,
    required String toRoute,
    String? userId,
    Map<String, dynamic>? routeParams,
    Duration? navigationDuration,
  }) => _logger.logNavigation(
    fromRoute: fromRoute,
    toRoute: toRoute,
    userId: userId,
    routeParams: routeParams,
    navigationDuration: navigationDuration,
  );

  static void logUIEvent({
    required String eventType,
    required String elementId,
    String? screenName,
    String? userId,
    Map<String, dynamic>? eventData,
  }) => _logger.logUIEvent(
    eventType: eventType,
    elementId: elementId,
    screenName: screenName,
    userId: userId,
    eventData: eventData,
  );
}
