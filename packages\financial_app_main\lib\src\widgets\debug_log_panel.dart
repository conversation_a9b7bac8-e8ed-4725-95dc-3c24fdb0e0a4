import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import '../core/logging_system_manager.dart';
import '../utils/debug_log_controller.dart';

/// 调试日志控制面板
/// 
/// 在开发环境中提供快速的日志控制功能
class DebugLogPanel extends StatefulWidget {
  const DebugLogPanel({super.key});

  @override
  State<DebugLogPanel> createState() => _DebugLogPanelState();
}

class _DebugLogPanelState extends State<DebugLogPanel> {
  Map<String, dynamic> _debugInfo = {};
  bool _isExpanded = false;

  @override
  void initState() {
    super.initState();
    _loadDebugInfo();
  }

  void _loadDebugInfo() {
    if (kDebugMode) {
      setState(() {
        _debugInfo = LoggingSystemManager.instance.getDebugPanelInfo();
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (!kDebugMode) return const SizedBox.shrink();

    return Positioned(
      top: MediaQuery.of(context).padding.top + 10,
      right: 10,
      child: Material(
        elevation: 8,
        borderRadius: BorderRadius.circular(8),
        child: Container(
          constraints: const BoxConstraints(maxWidth: 300),
          decoration: BoxDecoration(
            color: Colors.black87,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildHeader(),
              if (_isExpanded) _buildContent(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    final currentMode = _debugInfo['current_mode'] ?? '未知模式';
    final healthStatus = _debugInfo['system_health']?['health_status'] ?? 'unknown';
    
    return InkWell(
      onTap: () {
        setState(() {
          _isExpanded = !_isExpanded;
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            _getHealthIcon(healthStatus),
            const SizedBox(width: 8),
            Flexible(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '日志控制',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    currentMode,
                    style: const TextStyle(
                      color: Colors.white70,
                      fontSize: 10,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(width: 8),
            Icon(
              _isExpanded ? Icons.expand_less : Icons.expand_more,
              color: Colors.white70,
              size: 16,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContent() {
    return Container(
      padding: const EdgeInsets.all(12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSystemHealth(),
          const SizedBox(height: 12),
          _buildQuickActions(),
          const SizedBox(height: 12),
          _buildLogModeActions(),
        ],
      ),
    );
  }

  Widget _buildSystemHealth() {
    final healthInfo = _debugInfo['system_health'] as Map<String, dynamic>? ?? {};
    final errorRate = healthInfo['error_rate'] ?? 0.0;
    final bufferUsage = healthInfo['buffer_usage'] ?? 0.0;
    final totalLogs = healthInfo['total_logs'] ?? 0;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '系统状态',
          style: TextStyle(
            color: Colors.white,
            fontSize: 12,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          '错误率: ${errorRate.toStringAsFixed(1)}%',
          style: TextStyle(
            color: errorRate > 5 ? Colors.red : Colors.green,
            fontSize: 10,
          ),
        ),
        Text(
          '缓存使用: ${bufferUsage.toStringAsFixed(1)}%',
          style: TextStyle(
            color: bufferUsage > 70 ? Colors.orange : Colors.green,
            fontSize: 10,
          ),
        ),
        Text(
          '总日志数: $totalLogs',
          style: const TextStyle(
            color: Colors.white70,
            fontSize: 10,
          ),
        ),
      ],
    );
  }

  Widget _buildQuickActions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '快速操作',
          style: TextStyle(
            color: Colors.white,
            fontSize: 12,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 4),
        Wrap(
          spacing: 4,
          runSpacing: 4,
          children: [
            _buildActionButton('状态', 'showSystemStatus', Colors.blue),
            _buildActionButton('导出', 'exportLogs', Colors.green),
            _buildActionButton('重置', 'reset', Colors.orange),
          ],
        ),
      ],
    );
  }

  Widget _buildLogModeActions() {
    final actions = _debugInfo['actions'] as List? ?? [];
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '日志模式',
          style: TextStyle(
            color: Colors.white,
            fontSize: 12,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 4),
        Wrap(
          spacing: 4,
          runSpacing: 4,
          children: actions.map<Widget>((action) {
            final name = action['name'] as String;
            final actionKey = action['action'] as String;
            
            Color color;
            switch (actionKey) {
              case 'toggleDebugMode':
                color = Colors.purple;
                break;
              case 'hideDebugLogs':
                color = Colors.indigo;
                break;
              case 'showDebugLogs':
                color = Colors.teal;
                break;
              case 'switchToQuietMode':
                color = Colors.grey;
                break;
              default:
                color = Colors.blue;
            }
            
            return _buildActionButton(name, actionKey, color);
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildActionButton(String label, String action, Color color) {
    return InkWell(
      onTap: () => _executeAction(action),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: color.withOpacity(0.2),
          border: Border.all(color: color, width: 1),
          borderRadius: BorderRadius.circular(4),
        ),
        child: Text(
          label,
          style: TextStyle(
            color: color,
            fontSize: 10,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }

  Widget _getHealthIcon(String status) {
    switch (status) {
      case 'healthy':
        return const Icon(Icons.check_circle, color: Colors.green, size: 16);
      case 'warning':
        return const Icon(Icons.warning, color: Colors.orange, size: 16);
      case 'critical':
        return const Icon(Icons.error, color: Colors.red, size: 16);
      default:
        return const Icon(Icons.help, color: Colors.grey, size: 16);
    }
  }

  Future<void> _executeAction(String action) async {
    try {
      await LoggingSystemManager.instance.executeDebugAction(action);
      
      // 刷新调试信息
      _loadDebugInfo();
      
      // 显示执行成功的反馈
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('操作执行成功: $action'),
            duration: const Duration(seconds: 1),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      // 显示错误反馈
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('操作执行失败: $e'),
            duration: const Duration(seconds: 2),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}

/// 日志控制面板的简化版本（浮动按钮）
class DebugLogFloatingButton extends StatelessWidget {
  const DebugLogFloatingButton({super.key});

  @override
  Widget build(BuildContext context) {
    if (!kDebugMode) return const SizedBox.shrink();

    return Positioned(
      bottom: 100,
      right: 16,
      child: FloatingActionButton.small(
        onPressed: () => _showLogControlDialog(context),
        backgroundColor: Colors.black87,
        child: const Icon(Icons.bug_report, color: Colors.white),
      ),
    );
  }

  void _showLogControlDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('日志控制'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.visibility),
              title: const Text('显示调试日志'),
              onTap: () {
                DebugLogController.showDebugLogs();
                Navigator.pop(context);
              },
            ),
            ListTile(
              leading: const Icon(Icons.visibility_off),
              title: const Text('隐藏调试日志'),
              onTap: () {
                DebugLogController.hideDebugLogs();
                Navigator.pop(context);
              },
            ),
            ListTile(
              leading: const Icon(Icons.swap_horiz),
              title: const Text('切换调试模式'),
              onTap: () {
                DebugLogController.toggleDebugMode();
                Navigator.pop(context);
              },
            ),
            ListTile(
              leading: const Icon(Icons.volume_off),
              title: const Text('静默模式'),
              onTap: () {
                DebugLogController.switchToQuietMode();
                Navigator.pop(context);
              },
            ),
            ListTile(
              leading: const Icon(Icons.info),
              title: const Text('系统状态'),
              onTap: () {
                LoggingSystemManager.instance.showSystemStatus();
                Navigator.pop(context);
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }
}
