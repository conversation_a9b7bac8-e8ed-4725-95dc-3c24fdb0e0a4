import 'package:flutter/material.dart';

import '../../providers/theme_provider.dart';
import '../../config/app_theme.dart';

/// 黑白主题色彩选择器
class ThemeColorPicker extends StatelessWidget {
  /// 当前选中的颜色
  final String selectedColor;
  
  /// 颜色改变回调
  final ValueChanged<String> onColorChanged;
  
  /// 是否显示标签
  final bool showLabels;

  const ThemeColorPicker({
    super.key,
    required this.selectedColor,
    required this.onColorChanged,
    this.showLabels = true,
  });

  @override
  Widget build(BuildContext context) {
    return Wrap(
      spacing: 12,
      runSpacing: 12,
      children: ThemeProvider.availableAccentColors.map((colorName) {
        final color = _getColorFromName(colorName);
        final isSelected = selectedColor == colorName;
        
        return GestureDetector(
          onTap: () => onColorChanged(colorName),
          child: Column(
            children: [
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: color,
                  shape: BoxShape.circle,
                  border: isSelected 
                      ? Border.all(
                          color: Theme.of(context).colorScheme.primary,
                          width: 3,
                        )
                      : Border.all(
                          color: Theme.of(context).colorScheme.outline.withOpacity(0.3),
                          width: 1,
                        ),
                  boxShadow: [
                    BoxShadow(
                      color: color.withOpacity(0.3),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: isSelected
                    ? const Icon(
                        Icons.check,
                        color: Colors.white,
                        size: 20,
                      )
                    : null,
              ),
              if (showLabels) ...[
                const SizedBox(height: 6),
                Text(
                  _getColorDisplayName(colorName),
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    fontSize: 10,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ],
          ),
        );
      }).toList(),
    );
  }

  /// 根据颜色名称获取颜色
  Color _getColorFromName(String colorName) {
    switch (colorName) {
      case 'black':
        return FinancialColors.primaryBlack;
      case 'darkGray':
        return FinancialColors.darkGray;
      case 'gray':
        return const Color(0xFF666666);
      case 'lightGray':
        return FinancialColors.lightGray;
      case 'green':
        return FinancialColors.bullish;
      case 'red':
        return FinancialColors.bearish;
      case 'orange':
        return FinancialColors.warning;
      case 'blue':
        return const Color(0xFF2196F3);
      default:
        return FinancialColors.primaryBlack;
    }
  }

  /// 获取颜色显示名称
  String _getColorDisplayName(String colorName) {
    switch (colorName) {
      case 'black':
        return '纯黑';
      case 'darkGray':
        return '深灰';
      case 'gray':
        return '中灰';
      case 'lightGray':
        return '浅灰';
      case 'green':
        return '绿色';
      case 'red':
        return '红色';
      case 'orange':
        return '橙色';
      case 'blue':
        return '蓝色';
      default:
        return '纯黑';
    }
  }
}
