import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../providers/theme_provider.dart';
import '../../config/app_theme.dart';

/// 黑白风格主题预览组件
/// 
/// 展示当前主题的各种颜色和样式效果
class ThemePreview extends StatelessWidget {
  /// 是否显示完整预览
  final bool showFullPreview;
  
  /// 预览高度
  final double? height;

  const ThemePreview({
    super.key,
    this.showFullPreview = false,
    this.height,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return Container(
          height: height,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            gradient: FinancialColors.blackWhiteGradient,
            borderRadius: BorderRadius.circular(12),
          ),
          child: showFullPreview 
              ? _buildFullPreview(context, themeProvider)
              : _buildSimplePreview(context, themeProvider),
        );
      },
    );
  }

  /// 构建简单预览
  Widget _buildSimplePreview(BuildContext context, ThemeProvider themeProvider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 标题
        Text(
          '黑白风格主题预览',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        
        const SizedBox(height: 12),
        
        // 颜色条
        Row(
          children: [
            _buildColorDot(FinancialColors.primaryBlack, '主色'),
            const SizedBox(width: 8),
            _buildColorDot(FinancialColors.bullish, '涨'),
            const SizedBox(width: 8),
            _buildColorDot(FinancialColors.bearish, '跌'),
            const SizedBox(width: 8),
            _buildColorDot(FinancialColors.warning, '警告'),
          ],
        ),
        
        const SizedBox(height: 12),
        
        // 当前主题信息
        Text(
          '当前: ${themeProvider.getAccentColorName(themeProvider.accentColor)}',
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Colors.white.withOpacity(0.9),
          ),
        ),
      ],
    );
  }

  /// 构建完整预览
  Widget _buildFullPreview(BuildContext context, ThemeProvider themeProvider) {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题区域
          _buildPreviewHeader(context),
          
          const SizedBox(height: 16),
          
          // 主色调展示
          _buildPrimaryColorsSection(context),
          
          const SizedBox(height: 16),
          
          // 功能色展示
          _buildFunctionalColorsSection(context),
          
          const SizedBox(height: 16),
          
          // 金融数据展示
          _buildFinancialDataSection(context),
          
          const SizedBox(height: 16),
          
          // 图表颜色展示
          _buildChartColorsSection(context),
        ],
      ),
    );
  }

  /// 构建预览标题
  Widget _buildPreviewHeader(BuildContext context) {
    return Row(
      children: [
        Container(
          width: 4,
          height: 24,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(2),
          ),
        ),
        const SizedBox(width: 12),
        Text(
          '黑白风格主题预览',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  /// 构建主色调区域
  Widget _buildPrimaryColorsSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '主色调',
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            _buildColorCard(FinancialColors.primaryBlack, '纯黑'),
            const SizedBox(width: 8),
            _buildColorCard(FinancialColors.darkGray, '深灰'),
            const SizedBox(width: 8),
            _buildColorCard(FinancialColors.lightGray, '浅灰'),
          ],
        ),
      ],
    );
  }

  /// 构建功能色区域
  Widget _buildFunctionalColorsSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '功能色',
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            _buildColorCard(FinancialColors.bullish, '上涨'),
            const SizedBox(width: 8),
            _buildColorCard(FinancialColors.bearish, '下跌'),
            const SizedBox(width: 8),
            _buildColorCard(FinancialColors.warning, '警告'),
            const SizedBox(width: 8),
            _buildColorCard(FinancialColors.success, '成功'),
          ],
        ),
      ],
    );
  }

  /// 构建金融数据区域
  Widget _buildFinancialDataSection(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '金融数据示例',
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          
          // 股价数据
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'BTC/USDT',
                    style: TextStyle(
                      color: Colors.white.withOpacity(0.8),
                      fontSize: 12,
                    ),
                  ),
                  Text(
                    '¥45,678.90',
                    style: FinancialTextStyles.priceText(
                      fontSize: 16,
                      color: Colors.white,
                    ),
                  ),
                ],
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    '24h涨跌',
                    style: TextStyle(
                      color: Colors.white.withOpacity(0.8),
                      fontSize: 12,
                    ),
                  ),
                  Text(
                    '+2.34%',
                    style: FinancialTextStyles.percentageText(
                      fontSize: 14,
                      color: FinancialColors.bullish,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建图表颜色区域
  Widget _buildChartColorsSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '图表配色',
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 6,
          runSpacing: 6,
          children: FinancialColors.chartColors.map((color) {
            return Container(
              width: 24,
              height: 24,
              decoration: BoxDecoration(
                color: color,
                shape: BoxShape.circle,
                border: Border.all(
                  color: Colors.white.withOpacity(0.3),
                  width: 1,
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  /// 构建颜色点
  Widget _buildColorDot(Color color, String label) {
    return Column(
      children: [
        Container(
          width: 20,
          height: 20,
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
            border: Border.all(
              color: Colors.white.withOpacity(0.3),
              width: 1,
            ),
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 10,
          ),
        ),
      ],
    );
  }

  /// 构建颜色卡片
  Widget _buildColorCard(Color color, String label) {
    return Expanded(
      child: Container(
        height: 40,
        decoration: BoxDecoration(
          color: color,
          borderRadius: BorderRadius.circular(6),
          border: Border.all(
            color: Colors.white.withOpacity(0.3),
            width: 1,
          ),
        ),
        child: Center(
          child: Text(
            label,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 11,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ),
    );
  }
}
