import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../providers/theme_provider.dart';

/// 主题切换器组件
///
/// 提供快速的主题切换功能：
/// - 支持图标按钮和列表项两种样式
/// - 自动显示当前主题状态
/// - 支持自定义图标和文本
class ThemeSwitcher extends StatelessWidget {
  /// 显示样式
  final ThemeSwitcherStyle style;

  /// 是否显示文本标签
  final bool showLabel;

  /// 自定义图标
  final IconData? customIcon;

  /// 自定义文本
  final String? customText;

  const ThemeSwitcher({
    super.key,
    this.style = ThemeSwitcherStyle.iconButton,
    this.showLabel = false,
    this.customIcon,
    this.customText,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        switch (style) {
          case ThemeSwitcherStyle.iconButton:
            return _buildIconButton(context, themeProvider);
          case ThemeSwitcherStyle.listTile:
            return _buildListTile(context, themeProvider);
          case ThemeSwitcherStyle.chip:
            return _buildChip(context, themeProvider);
        }
      },
    );
  }

  /// 构建图标按钮样式
  Widget _buildIconButton(BuildContext context, ThemeProvider themeProvider) {
    final icon = _getThemeIcon(themeProvider.themeMode);

    return IconButton(
      icon: Icon(customIcon ?? icon),
      tooltip: _getThemeTooltip(themeProvider.themeMode),
      onPressed: () => themeProvider.toggleThemeMode(),
    );
  }

  /// 构建列表项样式
  Widget _buildListTile(BuildContext context, ThemeProvider themeProvider) {
    final icon = _getThemeIcon(themeProvider.themeMode);
    final title = customText ?? _getThemeText(themeProvider.themeMode);

    return ListTile(
      leading: Icon(customIcon ?? icon),
      title: Text(title),
      subtitle: showLabel
          ? Text(_getThemeDescription(themeProvider.themeMode))
          : null,
      trailing: const Icon(Icons.chevron_right),
      onTap: () => _showThemeDialog(context, themeProvider),
    );
  }

  /// 构建芯片样式
  Widget _buildChip(BuildContext context, ThemeProvider themeProvider) {
    final icon = _getThemeIcon(themeProvider.themeMode);
    final text = customText ?? _getThemeText(themeProvider.themeMode);

    return ActionChip(
      avatar: Icon(customIcon ?? icon, size: 18),
      label: Text(text),
      onPressed: () => themeProvider.toggleThemeMode(),
    );
  }

  /// 显示主题选择对话框
  void _showThemeDialog(BuildContext context, ThemeProvider themeProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('选择主题'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            RadioListTile<ThemeMode>(
              title: const Text('跟随系统'),
              subtitle: const Text('根据系统设置自动切换'),
              value: ThemeMode.system,
              groupValue: themeProvider.themeMode,
              onChanged: (value) {
                if (value != null) {
                  themeProvider.setThemeMode(value);
                  Navigator.of(context).pop();
                }
              },
            ),
            RadioListTile<ThemeMode>(
              title: const Text('亮色模式'),
              subtitle: const Text('始终使用亮色主题'),
              value: ThemeMode.light,
              groupValue: themeProvider.themeMode,
              onChanged: (value) {
                if (value != null) {
                  themeProvider.setThemeMode(value);
                  Navigator.of(context).pop();
                }
              },
            ),
            RadioListTile<ThemeMode>(
              title: const Text('暗色模式'),
              subtitle: const Text('始终使用暗色主题'),
              value: ThemeMode.dark,
              groupValue: themeProvider.themeMode,
              onChanged: (value) {
                if (value != null) {
                  themeProvider.setThemeMode(value);
                  Navigator.of(context).pop();
                }
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
        ],
      ),
    );
  }

  /// 获取主题图标
  IconData _getThemeIcon(ThemeMode themeMode) {
    switch (themeMode) {
      case ThemeMode.system:
        return Icons.brightness_auto;
      case ThemeMode.light:
        return Icons.brightness_high;
      case ThemeMode.dark:
        return Icons.brightness_2;
    }
  }

  /// 获取主题文本
  String _getThemeText(ThemeMode themeMode) {
    switch (themeMode) {
      case ThemeMode.system:
        return '跟随系统';
      case ThemeMode.light:
        return '亮色模式';
      case ThemeMode.dark:
        return '暗色模式';
    }
  }

  /// 获取主题描述
  String _getThemeDescription(ThemeMode themeMode) {
    switch (themeMode) {
      case ThemeMode.system:
        return '根据系统设置自动切换';
      case ThemeMode.light:
        return '始终使用亮色主题';
      case ThemeMode.dark:
        return '始终使用暗色主题';
    }
  }

  /// 获取主题提示文本
  String _getThemeTooltip(ThemeMode themeMode) {
    switch (themeMode) {
      case ThemeMode.system:
        return '当前: 跟随系统';
      case ThemeMode.light:
        return '当前: 亮色模式';
      case ThemeMode.dark:
        return '当前: 暗色模式';
    }
  }
}

/// 主题切换器样式
enum ThemeSwitcherStyle {
  /// 图标按钮样式
  iconButton,

  /// 列表项样式
  listTile,

  /// 芯片样式
  chip,
}

/// 主题状态指示器
///
/// 显示当前主题状态的小组件
class ThemeStatusIndicator extends StatelessWidget {
  /// 是否显示文本
  final bool showText;

  /// 指示器大小
  final double size;

  const ThemeStatusIndicator({super.key, this.showText = true, this.size = 24});

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        final isDark = Theme.of(context).brightness == Brightness.dark;
        final icon = isDark ? Icons.brightness_2 : Icons.brightness_high;
        final text = isDark ? '暗色' : '亮色';

        return Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: size,
              color: Theme.of(context).colorScheme.primary,
            ),
            if (showText) ...[
              const SizedBox(width: 4),
              Text(
                text,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.primary,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ],
        );
      },
    );
  }
}

/// 主题颜色预览组件
///
/// 显示当前主题的主要颜色
class ThemeColorPreview extends StatelessWidget {
  /// 预览大小
  final double size;

  /// 是否显示边框
  final bool showBorder;

  const ThemeColorPreview({super.key, this.size = 32, this.showBorder = true});

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        final colorScheme = Theme.of(context).colorScheme;

        return Container(
          width: size,
          height: size,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [colorScheme.primary, colorScheme.secondary],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            shape: BoxShape.circle,
            border: showBorder
                ? Border.all(
                    color: colorScheme.outline.withOpacity(0.3),
                    width: 1,
                  )
                : null,
          ),
        );
      },
    );
  }
}
