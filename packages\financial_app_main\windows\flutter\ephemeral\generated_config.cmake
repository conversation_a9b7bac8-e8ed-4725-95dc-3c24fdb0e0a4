# Generated code do not commit.
file(TO_CMAKE_PATH "D:\\development\\fvm\\versions\\3.32.2" FLUTTER_ROOT)
file(TO_CMAKE_PATH "D:\\project\\app_all\\gemini\\financial_app_workspace\\packages\\financial_app_main" PROJECT_DIR)

set(FLUTTER_VERSION "1.0.0+1" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 1 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 0 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 0 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 1 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=D:\\development\\fvm\\versions\\3.32.2"
  "PROJECT_DIR=D:\\project\\app_all\\gemini\\financial_app_workspace\\packages\\financial_app_main"
  "FLUTTER_ROOT=D:\\development\\fvm\\versions\\3.32.2"
  "FLUTTER_EPHEMERAL_DIR=D:\\project\\app_all\\gemini\\financial_app_workspace\\packages\\financial_app_main\\windows\\flutter\\ephemeral"
  "PROJECT_DIR=D:\\project\\app_all\\gemini\\financial_app_workspace\\packages\\financial_app_main"
  "FLUTTER_TARGET=D:\\project\\app_all\\gemini\\financial_app_workspace\\packages\\financial_app_main\\lib\\main.dart"
  "DART_DEFINES=RkxVVFRFUl9WRVJTSU9OPTMuMzIuMg==,RkxVVFRFUl9DSEFOTkVMPXN0YWJsZQ==,RkxVVFRFUl9HSVRfVVJMPWh0dHBzOi8vZ2l0aHViLmNvbS9mbHV0dGVyL2ZsdXR0ZXIuZ2l0,RkxVVFRFUl9GUkFNRVdPUktfUkVWSVNJT049OGRlZmFhNzFhNw==,RkxVVFRFUl9FTkdJTkVfUkVWSVNJT049MTA5MTUwODkzOQ==,RkxVVFRFUl9EQVJUX1ZFUlNJT049My44LjE="
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=false"
  "PACKAGE_CONFIG=D:\\project\\app_all\\gemini\\financial_app_workspace\\packages\\financial_app_main\\.dart_tool\\package_config.json"
)
