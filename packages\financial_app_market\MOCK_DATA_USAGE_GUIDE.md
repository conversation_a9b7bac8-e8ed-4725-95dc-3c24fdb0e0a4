# K线Mock数据使用指南

## 📋 概述

本指南介绍如何使用K线Mock数据系统来替代真实接口数据，用于开发、测试和演示。

## 🔧 配置方式

### 方式1：修改依赖注入配置（推荐）

在 `packages/financial_app_market/lib/market_injection.dart` 文件中修改配置：

```dart
// 🔑 控制Mock数据的开关
locator.registerLazySingleton<MockDataConfig>(
  () => const MockDataConfig(
    enableMockData: true,  // 🔑 设置为 false 使用真实接口
    showMockDataLogs: true,
  ),
);
```

### 方式2：使用预定义配置

```dart
// 开发环境 - 启用Mock数据
locator.registerLazySingleton<MockDataConfig>(
  () => MockDataConfigs.development,
);

// 生产环境 - 使用真实接口
locator.registerLazySingleton<MockDataConfig>(
  () => MockDataConfigs.production,
);

// 测试环境 - 启用Mock数据但不显示日志
locator.registerLazySingleton<MockDataConfig>(
  () => MockDataConfigs.testing,
);
```

### 方式3：自定义配置

```dart
locator.registerLazySingleton<MockDataConfig>(
  () => const MockDataConfig(
    enableMockData: true,
    symbolBasePrices: {
      'BTC-USDT-SWAP': 50000.0,  // 自定义比特币价格
      'ETH-USDT-SWAP': 3000.0,   // 自定义以太坊价格
    },
    volatilitySettings: {
      '15m': 0.01,  // 1% 波动率
      '1h': 0.02,   // 2% 波动率
    },
  ),
);
```

## 🎯 配置参数说明

### 核心参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `enableMockData` | `bool` | `true` | **核心开关**：`true`=使用Mock数据，`false`=使用真实接口 |
| `showMockDataLogs` | `bool` | `true` | 是否在日志中显示Mock数据标识 |

### 价格配置

```dart
symbolBasePrices: {
  'BTC-USDT-SWAP': 45000.0,   // 比特币基础价格
  'ETH-USDT-SWAP': 2500.0,    // 以太坊基础价格
  'BNB-USDT-SWAP': 300.0,     // 币安币基础价格
  // ... 更多交易对
}
```

### 波动率配置

```dart
volatilitySettings: {
  '1m': 0.001,    // 0.1% - 1分钟波动率
  '15m': 0.005,   // 0.5% - 15分钟波动率
  '1h': 0.015,    // 1.5% - 1小时波动率
  '1d': 0.05,     // 5% - 1天波动率
  // ... 更多时间周期
}
```

## 🚀 使用场景

### 1. 开发阶段
```dart
// 启用Mock数据，方便开发调试
const MockDataConfig(enableMockData: true)
```

### 2. 演示阶段
```dart
// 使用高波动率配置，展示图表效果
MockDataConfigs.highVolatility
```

### 3. 测试阶段
```dart
// 启用Mock数据但不显示日志
MockDataConfigs.testing
```

### 4. 生产环境
```dart
// 使用真实接口数据
MockDataConfigs.production
```

## 📊 数据特性

### Mock数据生成特性

1. **价格连续性**：相邻K线价格连续，无异常跳跃
2. **OHLC逻辑性**：严格遵循开高低收价格逻辑
3. **时间准确性**：按照指定时间周期精确生成
4. **波动合理性**：基于配置的波动率生成真实的价格波动
5. **趋势模拟**：包含长期趋势和短期波动

### 技术指标友好

生成的Mock数据支持各种技术指标计算：
- 移动平均线（MA）
- 相对强弱指数（RSI）
- 布林带（BOLL）
- MACD指标
- 成交量指标

## 🔄 切换流程

### 从Mock数据切换到真实接口

1. 修改配置文件：
```dart
const MockDataConfig(enableMockData: false)
```

2. 重启应用
3. 验证真实接口数据正常

### 从真实接口切换到Mock数据

1. 修改配置文件：
```dart
const MockDataConfig(enableMockData: true)
```

2. 重启应用
3. 查看日志确认Mock数据生成

## 📝 日志示例

### Mock数据启用时的日志

```
🎭 Mock模式已启用，跳过真实接口调用，直接生成Mock数据 (接口1)
✅ Mock K线数据生成成功 (完全跳过真实接口)
```

### 真实接口启用时的日志

```
📊 Mock模式未启用，调用真实接口获取K线数据 (接口1)
✅ 初始K线数据获取成功
```

## ⚠️ 注意事项

1. **重启应用**：修改配置后需要重启应用才能生效
2. **数据一致性**：Mock数据每次生成都不同，不适合需要固定数据的测试
3. **性能考虑**：Mock数据生成速度快，但大量数据生成仍需考虑性能
4. **接口兼容**：Mock数据格式与真实接口完全兼容

## 🛠️ 故障排除

### 问题1：配置修改后没有生效
**解决方案**：确保重启了应用，配置修改需要重新初始化依赖注入

### 问题2：Mock数据价格异常
**解决方案**：检查 `symbolBasePrices` 配置，确保价格设置合理

### 问题3：图表显示异常波动
**解决方案**：调整 `volatilitySettings` 中对应时间周期的波动率

### 问题4：日志过多影响调试
**解决方案**：设置 `showMockDataLogs: false` 关闭Mock数据日志

## 📈 最佳实践

1. **开发阶段**：使用默认Mock配置，快速验证功能
2. **测试阶段**：使用低波动率配置，确保测试稳定性
3. **演示阶段**：使用高波动率配置，展示图表动态效果
4. **生产环境**：关闭Mock数据，使用真实接口

## 🔮 未来扩展

计划中的功能：
- [ ] 实时Mock数据更新
- [ ] 从文件加载Mock数据
- [ ] 更多技术指标模拟
- [ ] 市场事件模拟（如突发新闻影响）
