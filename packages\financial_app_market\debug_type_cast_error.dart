/// 🐛 调试类型转换错误
/// 最小化测试用例

import 'package:financial_app_market/src/data/models/trading_view_chart_data.dart';

void main() {
  print('🐛 开始调试类型转换错误...');
  
  try {
    // 测试1: 直接创建TradingViewChartDataModel
    print('\n📊 测试1: 直接创建TradingViewChartDataModel');
    final now = DateTime.now().millisecondsSinceEpoch;
    print('当前时间戳: $now (${now.runtimeType})');
    
    // 测试不同的时间周期
    final timeFrames = ['15m', '1h', '1d'];
    
    for (final timeFrame in timeFrames) {
      print('\n测试时间周期: $timeFrame');
      try {
        final chartData = TradingViewChartDataModel.fromHistoricalData(
          open: 45000.0,
          high: 45100.0,
          low: 44900.0,
          close: 45050.0,
          time: now,
          timeFrame: timeFrame,
          type: TradingViewDataType.candlestick,
        );
        
        print('✅ $timeFrame 转换成功');
        print('   time字段: ${chartData.time}');
        print('   time类型: ${chartData.time.runtimeType}');
        
      } catch (e, stackTrace) {
        print('❌ $timeFrame 转换失败: $e');
        print('堆栈跟踪: $stackTrace');
      }
    }
    
    // 测试2: 测试边界情况
    print('\n📊 测试2: 边界情况');
    
    // 测试很小的时间戳
    try {
      print('测试小时间戳: 1000');
      final chartData = TradingViewChartDataModel.fromHistoricalData(
        open: 100.0,
        high: 101.0,
        low: 99.0,
        close: 100.5,
        time: 1000,
        timeFrame: '15m',
        type: TradingViewDataType.candlestick,
      );
      print('✅ 小时间戳转换成功: ${chartData.time}');
    } catch (e) {
      print('❌ 小时间戳转换失败: $e');
    }
    
    // 测试很大的时间戳
    try {
      print('测试大时间戳: ${DateTime(2030).millisecondsSinceEpoch}');
      final bigTimestamp = DateTime(2030).millisecondsSinceEpoch;
      final chartData = TradingViewChartDataModel.fromHistoricalData(
        open: 100.0,
        high: 101.0,
        low: 99.0,
        close: 100.5,
        time: bigTimestamp,
        timeFrame: '15m',
        type: TradingViewDataType.candlestick,
      );
      print('✅ 大时间戳转换成功: ${chartData.time}');
    } catch (e) {
      print('❌ 大时间戳转换失败: $e');
    }
    
    print('\n🎉 所有测试完成！');
    
  } catch (e, stackTrace) {
    print('❌ 测试过程中发生错误: $e');
    print('完整堆栈跟踪: $stackTrace');
  }
}
