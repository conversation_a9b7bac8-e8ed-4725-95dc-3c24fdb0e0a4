import 'package:financial_app_market/src/config/mock_data_config.dart';
import 'package:financial_app_market/src/data/datasources/market_remote_datasource_impl.dart';
import 'package:financial_app_market/src/domain/usecases/get_line_data.dart';
import 'package:financial_trading_chart/financial_trading_chart.dart';
import 'package:get_it/get_it.dart';
import 'package:financial_app_core/financial_app_core.dart'; // 导入核心库
import 'src/data/datasources/market_remote_datasource.dart';
import 'src/data/datasources/market_websocket_datasource.dart';
import 'src/data/repositories/market_repository_impl.dart';
import 'src/domain/repositories/market_repository.dart';
import 'src/domain/usecases/get_market_ticker.dart';
import 'src/presentation/bloc/market_bloc.dart';

final GetIt locator = GetIt.instance;

Future<void> initMarketDependencies() async {
  // 🔧 注册Mock数据配置
  locator.registerLazySingleton<MockDataConfig>(
    () => const MockDataConfig(
      enableMockData: true, // 🔑 可以通过这里控制是否启用Mock数据
      showMockDataLogs: true,
    ),
  );

  // Data sources
  locator.registerLazySingleton<MarketRemoteDataSource>(
    () => MarketRemoteDataSourceImpl(
      apiService: locator<BaseApiService>(),
      mockConfig: locator<MockDataConfig>(),
    ),
  );

  locator.registerLazySingleton<MarketWebSocketDataSource>(() {
    final config = locator<AppConfig>(); // 获取配置
    return MarketWebSocketDataSourceImpl(websocketUrl: config.websocketUrl);
  });

  // Repositories
  locator.registerLazySingleton<MarketRepository>(
    () => MarketRepositoryImpl(
      remoteDataSource: locator<MarketRemoteDataSource>(),
      webSocketDataSource: locator<MarketWebSocketDataSource>(),
    ),
  );

  // Use cases
  locator.registerLazySingleton(
    () => GetMarketTicker(locator<MarketRepository>()),
  );
  locator.registerLazySingleton(
    () => GetKlineData(locator<MarketRepository>()),
  );
  locator.registerLazySingleton(
    () => GetMarketDepth(locator<MarketRepository>()),
  );
  locator.registerLazySingleton(
    () => GetAllMarketTickers(locator<MarketRepository>()),
  );
  locator.registerLazySingleton(() => GetLineData(locator<MarketRepository>()));

  // 注册ChartAPI
  locator.registerSingleton<ChartAPI>(ChartAPI.instance);
  // Blocs
  locator.registerFactory(() => MarketBloc(chartAPI: locator<ChartAPI>()));
}
