/// 🔧 K线Mock数据配置文件
/// 
/// 这个文件用于配置Mock数据的生成参数
/// 通过修改这里的配置，可以控制Mock数据的行为
/// 
/// 使用方法：
/// 1. 设置 enableMockData = true 启用Mock数据
/// 2. 设置 enableMockData = false 使用真实接口
/// 3. 可以自定义各交易对的基础价格和波动率

/// 🎯 Mock数据配置类
class MockDataConfig {
  /// 🔑 是否启用Mock数据
  /// true: 完全使用Mock数据，不调用真实接口
  /// false: 使用真实接口，Mock数据仅作为异常时的后备
  final bool enableMockData;
  
  /// Mock数据源类型
  final String mockDataSource;
  
  /// 各交易对的基础价格设置
  /// 可以根据实际需要调整价格
  final Map<String, double> symbolBasePrices;
  
  /// 不同时间周期的波动率设置
  /// 数值越大，价格波动越剧烈
  final Map<String, double> volatilitySettings;
  
  /// 是否启用实时更新（暂未实现）
  final bool enableRealtimeUpdate;
  
  /// 是否在日志中显示Mock数据标识
  final bool showMockDataLogs;

  const MockDataConfig({
    this.enableMockData = true, // 🔑 默认启用Mock数据
    this.mockDataSource = 'generator',
    this.symbolBasePrices = const {
      // 主流币种
      'BTC-USDT-SWAP': 45000.0,   // 比特币
      'ETH-USDT-SWAP': 2500.0,    // 以太坊
      'BNB-USDT-SWAP': 300.0,     // 币安币
      'ADA-USDT-SWAP': 0.5,       // 艾达币
      'SOL-USDT-SWAP': 100.0,     // Solana
      'DOGE-USDT-SWAP': 0.08,     // 狗狗币
      'XRP-USDT-SWAP': 0.6,       // 瑞波币
      'DOT-USDT-SWAP': 7.0,       // 波卡
      'AVAX-USDT-SWAP': 35.0,     // 雪崩
      'MATIC-USDT-SWAP': 0.9,     // Polygon
      
      // 测试用交易对
      'test-symbol': 50000.0,
      'BTCUSDT': 45000.0,
      'ETHUSDT': 2500.0,
    },
    this.volatilitySettings = const {
      // 时间周期越短，波动率越小
      '1s': 0.0005,   // 0.05% - 秒级波动很小
      '1m': 0.001,    // 0.1%  - 分钟级小幅波动
      '5m': 0.003,    // 0.3%  - 5分钟适中波动
      '15m': 0.005,   // 0.5%  - 15分钟常见波动
      '30m': 0.008,   // 0.8%  - 30分钟较大波动
      '1h': 0.015,    // 1.5%  - 小时级明显波动
      '4h': 0.025,    // 2.5%  - 4小时大幅波动
      '1d': 0.05,     // 5%    - 日线大幅波动
      '1w': 0.08,     // 8%    - 周线剧烈波动
      '1M': 0.15,     // 15%   - 月线极大波动
    },
    this.enableRealtimeUpdate = false,
    this.showMockDataLogs = true,
  });
  
  /// 获取交易对的基础价格
  double getBasePriceForSymbol(String symbol) {
    return symbolBasePrices[symbol] ?? symbolBasePrices['BTC-USDT-SWAP']!;
  }
  
  /// 获取时间周期的波动率
  double getVolatilityForTimeFrame(String timeFrame) {
    return volatilitySettings[timeFrame] ?? volatilitySettings['15m']!;
  }
  
  /// 创建开发环境配置（启用Mock数据）
  factory MockDataConfig.development() {
    return const MockDataConfig(
      enableMockData: true,
      showMockDataLogs: true,
    );
  }
  
  /// 创建生产环境配置（禁用Mock数据）
  factory MockDataConfig.production() {
    return const MockDataConfig(
      enableMockData: false,
      showMockDataLogs: false,
    );
  }
  
  /// 创建测试环境配置（启用Mock数据，但不显示日志）
  factory MockDataConfig.testing() {
    return const MockDataConfig(
      enableMockData: true,
      showMockDataLogs: false,
    );
  }
  
  /// 复制配置并修改部分参数
  MockDataConfig copyWith({
    bool? enableMockData,
    String? mockDataSource,
    Map<String, double>? symbolBasePrices,
    Map<String, double>? volatilitySettings,
    bool? enableRealtimeUpdate,
    bool? showMockDataLogs,
  }) {
    return MockDataConfig(
      enableMockData: enableMockData ?? this.enableMockData,
      mockDataSource: mockDataSource ?? this.mockDataSource,
      symbolBasePrices: symbolBasePrices ?? this.symbolBasePrices,
      volatilitySettings: volatilitySettings ?? this.volatilitySettings,
      enableRealtimeUpdate: enableRealtimeUpdate ?? this.enableRealtimeUpdate,
      showMockDataLogs: showMockDataLogs ?? this.showMockDataLogs,
    );
  }
  
  @override
  String toString() {
    return 'MockDataConfig('
        'enableMockData: $enableMockData, '
        'mockDataSource: $mockDataSource, '
        'symbolCount: ${symbolBasePrices.length}, '
        'timeFrameCount: ${volatilitySettings.length}, '
        'showLogs: $showMockDataLogs'
        ')';
  }
}

/// 🎯 预定义的Mock数据配置
class MockDataConfigs {
  /// 默认配置（启用Mock数据）
  static const MockDataConfig defaultConfig = MockDataConfig();
  
  /// 开发环境配置
  static const MockDataConfig development = MockDataConfig(
    enableMockData: true,
    showMockDataLogs: true,
  );
  
  /// 生产环境配置
  static const MockDataConfig production = MockDataConfig(
    enableMockData: false,
    showMockDataLogs: false,
  );
  
  /// 测试环境配置
  static const MockDataConfig testing = MockDataConfig(
    enableMockData: true,
    showMockDataLogs: false,
  );
  
  /// 高波动率配置（用于演示）
  static const MockDataConfig highVolatility = MockDataConfig(
    enableMockData: true,
    volatilitySettings: {
      '1s': 0.001,    // 0.1%
      '1m': 0.002,    // 0.2%
      '5m': 0.006,    // 0.6%
      '15m': 0.01,    // 1%
      '30m': 0.016,   // 1.6%
      '1h': 0.03,     // 3%
      '4h': 0.05,     // 5%
      '1d': 0.1,      // 10%
      '1w': 0.16,     // 16%
      '1M': 0.3,      // 30%
    },
  );
  
  /// 低波动率配置（稳定演示）
  static const MockDataConfig lowVolatility = MockDataConfig(
    enableMockData: true,
    volatilitySettings: {
      '1s': 0.0001,   // 0.01%
      '1m': 0.0005,   // 0.05%
      '5m': 0.0015,   // 0.15%
      '15m': 0.0025,  // 0.25%
      '30m': 0.004,   // 0.4%
      '1h': 0.0075,   // 0.75%
      '4h': 0.0125,   // 1.25%
      '1d': 0.025,    // 2.5%
      '1w': 0.04,     // 4%
      '1M': 0.075,    // 7.5%
    },
  );
}
