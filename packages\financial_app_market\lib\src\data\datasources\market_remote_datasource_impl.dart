import 'dart:math';
import 'package:dio/dio.dart';
import 'package:financial_app_core/financial_app_core.dart';
import 'package:financial_app_market/market_module.dart';
import 'package:financial_app_market/src/config/mock_data_config.dart';
import 'package:financial_app_market/src/data/datasources/market_remote_datasource.dart';
import 'package:financial_app_market/src/data/models/kline_data_model.dart';
import 'package:financial_app_market/src/data/models/line_data_model.dart';
import 'package:financial_app_market/src/data/models/trading_view_chart_data.dart';

/// 🎯 K线Mock数据生成器
class KlineMockDataGenerator {
  static final Random _random = Random();

  /// 时间周期映射
  static const Map<String, Duration> _timeFrameMap = {
    '1s': Duration(seconds: 1),
    '1m': Duration(minutes: 1),
    '5m': Duration(minutes: 5),
    '15m': Duration(minutes: 15),
    '30m': Duration(minutes: 30),
    '1h': Duration(hours: 1),
    '4h': Duration(hours: 4),
    '1d': Duration(days: 1),
    '1w': Duration(days: 7),
    '1M': Duration(days: 30),
  };

  /// 生成指定时间周期的Mock K线数据
  static List<LineDataModel> generateMockData({
    required String symbol,
    required String timeFrame,
    required int limit,
    DateTime? endTime,
    MockDataConfig? config,
  }) {
    final mockConfig = config ?? const MockDataConfig();
    final end = endTime ?? DateTime.now();
    final interval = _getIntervalDuration(timeFrame);
    final basePrice = mockConfig.getBasePriceForSymbol(symbol);
    final volatility = mockConfig.getVolatilityForTimeFrame(timeFrame);

    // 生成连续的K线数据
    final List<LineDataModel> result = [];
    double lastClose = basePrice;

    for (int i = 0; i < limit; i++) {
      final timestamp = end.subtract(
        Duration(milliseconds: interval.inMilliseconds * (limit - i - 1)),
      );

      final klineData = _generateSingleKlineData(
        timestamp: timestamp,
        previousClose: lastClose,
        basePrice: basePrice,
        volatility: volatility,
        index: i,
        timeFrame: timeFrame,
      );

      result.add(klineData);
      lastClose = klineData.close;
    }

    return result;
  }

  /// 获取时间间隔
  static Duration _getIntervalDuration(String timeFrame) {
    return _timeFrameMap[timeFrame] ?? const Duration(minutes: 15);
  }

  /// 生成单个K线数据
  static LineDataModel _generateSingleKlineData({
    required DateTime timestamp,
    required double previousClose,
    required double basePrice,
    required double volatility,
    required int index,
    required String timeFrame,
  }) {
    // 1. 生成趋势因子（长期走势）
    final trendFactor = _generateTrendFactor(index, timeFrame);

    // 2. 生成随机波动因子
    final randomFactor = (_random.nextDouble() - 0.5) * 2 * volatility;

    // 3. 生成技术分析因子（支撑阻力）
    final technicalFactor = _generateTechnicalFactor(
      previousClose,
      basePrice,
      index,
    );

    // 4. 计算开盘价（基于前一根K线收盘价）
    final open = previousClose * (1 + (randomFactor * 0.3));

    // 5. 计算收盘价
    final close = open * (1 + trendFactor + randomFactor + technicalFactor);

    // 6. 计算最高价和最低价
    final priceRange =
        (open - close).abs() * (1.2 + _random.nextDouble() * 0.8);
    final high = max(open, close) + priceRange * _random.nextDouble();
    final low = min(open, close) - priceRange * _random.nextDouble();

    return LineDataModel(
      open: _roundPrice(open),
      hight: _roundPrice(high), // 保持原有拼写
      low: _roundPrice(low),
      close: _roundPrice(close),
      time: timestamp.millisecondsSinceEpoch,
    );
  }

  /// 生成趋势因子
  static double _generateTrendFactor(int index, String timeFrame) {
    // 基于正弦波生成长期趋势
    final cyclePeriod = _getCyclePeriod(timeFrame);
    final phase = (index / cyclePeriod) * 2 * pi;
    return sin(phase) * 0.001; // 0.1%的趋势波动
  }

  /// 生成技术分析因子
  static double _generateTechnicalFactor(
    double currentPrice,
    double basePrice,
    int index,
  ) {
    // 模拟支撑阻力位
    final deviation = (currentPrice - basePrice) / basePrice;

    // 当价格偏离基准价格较多时，增加回归倾向
    if (deviation.abs() > 0.1) {
      return -deviation * 0.1; // 回归因子
    }

    // 模拟突破行为
    if (_random.nextDouble() < 0.05) {
      // 5%概率突破
      return (_random.nextDouble() - 0.5) * 0.02; // 2%的突破幅度
    }

    return 0.0;
  }

  /// 获取周期长度
  static double _getCyclePeriod(String timeFrame) {
    switch (timeFrame) {
      case '1s':
        return 60.0; // 1分钟周期
      case '1m':
        return 60.0; // 1小时周期
      case '5m':
        return 48.0; // 4小时周期
      case '15m':
        return 32.0; // 8小时周期
      case '30m':
        return 24.0; // 12小时周期
      case '1h':
        return 24.0; // 1天周期
      case '4h':
        return 21.0; // 3.5天周期
      case '1d':
        return 30.0; // 1月周期
      case '1w':
        return 52.0; // 1年周期
      case '1M':
        return 12.0; // 1年周期
      default:
        return 32.0;
    }
  }

  /// 价格四舍五入
  static double _roundPrice(double price) {
    if (price > 1000) {
      return (price * 100).round() / 100; // 保留2位小数
    } else if (price > 1) {
      return (price * 1000).round() / 1000; // 保留3位小数
    } else {
      return (price * 10000).round() / 10000; // 保留4位小数
    }
  }
}

class MarketRemoteDataSourceImpl implements MarketRemoteDataSource {
  static const String moduleName = 'MarketRemoteDataSourceImpl';
  final BaseApiService _apiService;

  /// 🔑 缓存每个交易对的最后时间戳，用于历史数据接口的after参数
  /// Key: "symbol_timeFrame", Value: 最后一条数据的时间戳
  final Map<String, int> _lastTimestampCache = {};

  /// 🔑 缓存初始数据的最后时间戳，用于第一次调用历史数据接口
  /// Key: "symbol_timeFrame", Value: 初始数据的最后一条数据时间戳
  final Map<String, int> _initialDataLastTimestamp = {};

  /// 🔧 Mock数据配置实例
  final MockDataConfig _mockConfig;

  MarketRemoteDataSourceImpl({
    required BaseApiService apiService,
    MockDataConfig? mockConfig,
  }) : _apiService = apiService,
       _mockConfig = mockConfig ?? const MockDataConfig();

  /// 🔑 清理时间戳缓存
  void clearTimestampCache({String? symbol, String? timeFrame}) {
    if (symbol != null && timeFrame != null) {
      // 清理特定交易对的缓存
      final cacheKey = '${symbol}_$timeFrame';
      _lastTimestampCache.remove(cacheKey);
      _initialDataLastTimestamp.remove(cacheKey);

      AppLogger.logModule(
        moduleName,
        LogLevel.info,
        '🧹 清理特定交易对的时间戳缓存',
        metadata: {'cache_key': cacheKey},
      );
    } else {
      // 清理所有缓存
      _lastTimestampCache.clear();
      _initialDataLastTimestamp.clear();

      AppLogger.logModule(moduleName, LogLevel.info, '🧹 清理所有时间戳缓存');
    }
  }

  /// 🔑 获取缓存状态信息
  Map<String, dynamic> getCacheStatus() {
    return {
      'last_timestamp_cache': Map.from(_lastTimestampCache),
      'initial_data_cache': Map.from(_initialDataLastTimestamp),
      'cache_count':
          _lastTimestampCache.length + _initialDataLastTimestamp.length,
    };
  }

  /// 🔧 数据格式兼容性处理工具
  /// 尝试从不同的数据格式中提取数值
  static double _safeExtractDouble(dynamic value, String fieldName) {
    try {
      if (value == null) return 0.0;
      if (value is num) return value.toDouble();
      if (value is String) return double.parse(value);
      throw FormatException(
        'Cannot convert $value to double for field $fieldName',
      );
    } catch (e) {
      AppLogger.logModule(
        moduleName,
        LogLevel.warning,
        '⚠️ 数值转换失败，使用默认值0.0',
        metadata: {
          'field': fieldName,
          'value': value,
          'value_type': value.runtimeType.toString(),
        },
      );
      return 0.0;
    }
  }

  /// 🔧 安全提取整数
  static int _safeExtractInt(dynamic value, String fieldName) {
    try {
      if (value == null) return 0;
      if (value is int) return value;
      if (value is double) return value.toInt();
      if (value is String) return int.parse(value);
      throw FormatException(
        'Cannot convert $value to int for field $fieldName',
      );
    } catch (e) {
      AppLogger.logModule(
        moduleName,
        LogLevel.warning,
        '⚠️ 整数转换失败，使用当前时间戳',
        metadata: {
          'field': fieldName,
          'value': value,
          'value_type': value.runtimeType.toString(),
        },
      );
      return DateTime.now().millisecondsSinceEpoch;
    }
  }

  /// 🔧 智能数据解析：支持数组和对象格式
  static LineDataModel _parseToLineDataModel(dynamic item) {
    if (item is List && item.length >= 5) {
      // 数组格式：[open, high, low, close, time]
      return LineDataModel(
        open: _safeExtractDouble(item[0], 'open'),
        hight: _safeExtractDouble(item[1], 'high'),
        low: _safeExtractDouble(item[2], 'low'),
        close: _safeExtractDouble(item[3], 'close'),
        time: _safeExtractInt(item[4], 'time'),
      );
    } else if (item is Map<String, dynamic>) {
      // 对象格式：{open: xx, high: xx, low: xx, close: xx, time: xx}
      return LineDataModel(
        open: _safeExtractDouble(item['open'] ?? item['o'], 'open'),
        hight: _safeExtractDouble(item['high'] ?? item['h'], 'high'),
        low: _safeExtractDouble(item['low'] ?? item['l'], 'low'),
        close: _safeExtractDouble(item['close'] ?? item['c'], 'close'),
        time: _safeExtractInt(
          item['time'] ?? item['t'] ?? item['timestamp'],
          'time',
        ),
      );
    } else {
      throw FormatException(
        'Unsupported data format: expected List or Map, got ${item.runtimeType} with content: $item',
      );
    }
  }

  /// 🔧 智能数据解析：支持数组和对象格式转换为TradingView格式
  static TradingViewChartDataModel _parseToTradingViewChartDataModel(
    dynamic item,
    String timeFrame,
    TradingViewDataType dataType,
  ) {
    double open, high, low, close;
    int time;

    if (item is List && item.length >= 5) {
      // 数组格式：[open, high, low, close, time]
      open = _safeExtractDouble(item[0], 'open');
      high = _safeExtractDouble(item[1], 'high');
      low = _safeExtractDouble(item[2], 'low');
      close = _safeExtractDouble(item[3], 'close');
      time = _safeExtractInt(item[4], 'time');
    } else if (item is Map<String, dynamic>) {
      // 对象格式：{open: xx, high: xx, low: xx, close: xx, time: xx}
      open = _safeExtractDouble(item['open'] ?? item['o'], 'open');
      high = _safeExtractDouble(item['high'] ?? item['h'], 'high');
      low = _safeExtractDouble(item['low'] ?? item['l'], 'low');
      close = _safeExtractDouble(item['close'] ?? item['c'], 'close');
      time = _safeExtractInt(
        item['time'] ?? item['t'] ?? item['timestamp'],
        'time',
      );
    } else {
      throw FormatException(
        'Unsupported data format: expected List or Map, got ${item.runtimeType} with content: $item',
      );
    }

    return TradingViewChartDataModel.fromHistoricalData(
      open: open,
      high: high,
      low: low,
      close: close,
      time: time,
      timeFrame: timeFrame,
      type: dataType,
    );
  }

  @override
  Future<List<KlineDataModel>> fetchKlineData({
    required String symbol,
    required String interval,
    int? startTime,
    int? endTime,
    int? limit,
  }) async {
    // 🔧 检查是否启用Mock数据 - 如果启用，直接返回Mock数据，不调用真实接口
    if (_mockConfig.enableMockData) {
      if (_mockConfig.showMockDataLogs) {
        AppLogger.logModule(
          moduleName,
          LogLevel.info,
          '🎭 Mock模式已启用，跳过真实接口调用，直接生成Mock数据 (fetchKlineData)',
          metadata: {
            'symbol': symbol,
            'interval': interval,
            'limit': limit,
            'start_time': startTime,
            'end_time': endTime,
            'mock_enabled': true,
            'skip_real_api': true,
            'data_source': 'mock_generator',
          },
        );
      }

      // 🎯 使用KlineMockDataGenerator生成Mock数据
      final finalLimit = limit ?? 100;
      final endTimeMs = endTime ?? DateTime.now().millisecondsSinceEpoch;

      final mockLineData = KlineMockDataGenerator.generateMockData(
        symbol: symbol,
        timeFrame: interval,
        limit: finalLimit,
        endTime: DateTime.fromMillisecondsSinceEpoch(endTimeMs),
        config: _mockConfig,
      );

      // 转换为KlineDataModel格式
      final result = mockLineData.map((lineData) {
        return KlineDataModel(
          openTime: lineData.time,
          open: lineData.open,
          high: lineData.hight, // 保持原有拼写
          low: lineData.low,
          close: lineData.close,
          volume: 1000.0, // 默认交易量
          closeTime: lineData.time + 60000, // 假设1分钟后关闭
          quoteAssetVolume: lineData.close * 1000.0,
          numberOfTrades: 100,
        );
      }).toList();

      // 🔍 调试：打印前几条数据
      if (_mockConfig.showMockDataLogs && result.isNotEmpty) {
        AppLogger.logModule(
          moduleName,
          LogLevel.info,
          '🔍 fetchKlineData生成的Mock数据样本',
          metadata: {
            'total_count': result.length,
            'first_item': {
              'openTime': result.first.openTime,
              'open': result.first.open,
              'high': result.first.high,
              'low': result.first.low,
              'close': result.first.close,
              'time_formatted': DateTime.fromMillisecondsSinceEpoch(
                result.first.openTime,
              ).toIso8601String(),
            },
            'last_item': {
              'openTime': result.last.openTime,
              'open': result.last.open,
              'high': result.last.high,
              'low': result.last.low,
              'close': result.last.close,
              'time_formatted': DateTime.fromMillisecondsSinceEpoch(
                result.last.openTime,
              ).toIso8601String(),
            },
          },
        );
      }

      return result;
    }

    final Map<String, dynamic> queryParams = {
      'symbol': symbol,
      'interval': interval,
    };
    if (startTime != null) queryParams['startTime'] = startTime;
    if (endTime != null) queryParams['endTime'] = endTime;
    if (limit != null) queryParams['limit'] = limit;

    try {
      // TODO: 替换为实际的 API 调用
      await Future.delayed(const Duration(seconds: 1)); // 模拟网络延迟

      // 🔧 如果Mock模式未启用，使用硬编码数据作为后备
      final List<List<dynamic>> mockKlineData = [
        [
          1678886400000,
          20000.0,
          20500.0,
          19800.0,
          20300.0,
          100.0,
          1678972799999,
          2030000.0,
          500,
        ], // 2023-03-15
        [
          1678972800000,
          20300.0,
          20800.0,
          20200.0,
          20700.0,
          120.0,
          1679059199999,
          2484000.0,
          600,
        ], // 2023-03-16
        [
          1679059200000,
          20700.0,
          21200.0,
          20600.0,
          21100.0,
          150.0,
          1679145599999,
          3165000.0,
          750,
        ], // 2023-03-17
        // ... 更多数据
      ];

      return mockKlineData.map((data) {
        return KlineDataModel(
          openTime: data[0] as int,
          open: data[1] as double,
          high: data[2] as double,
          low: data[3] as double,
          close: data[4] as double,
          volume: data[5] as double,
          closeTime: data[6] as int,
          quoteAssetVolume: data[7] as double,
          numberOfTrades: data[8] as int,
        );
      }).toList();

      // final response = await apiService.get('/market/kline', queryParameters: queryParams);
      // final List<dynamic> jsonList = response.data as List<dynamic>;
      // return jsonList.map((json) => KlineDataModel.fromJson(json as Map<String, dynamic>)).toList();
    } on DioException catch (e) {
      throw Exception('获取K线数据失败: ${e.message ?? e.type.name}');
    } catch (e) {
      throw Exception('获取K线数据时发生未知错误: ${e.toString()}');
    }
  }

  @override
  Future<List<MarketTickerModel>> fetchAllTickers() async {
    try {
      // TODO: 替换为实际的 API 调用
      await Future.delayed(const Duration(seconds: 1)); // 模拟网络延迟

      // 模拟所有交易对的行情数据
      final List<Map<String, dynamic>> mockTickers = [
        {
          "symbol": "BTCUSDT",
          "lastPrice": 62000.50,
          "openPrice": 61500.00,
          "highPrice": 62500.00,
          "lowPrice": 61000.00,
          "volume": 1500.0,
          "quoteVolume": 93000000.0,
          "priceChange": 500.50,
          "priceChangePercent": 0.81,
          "openTime": DateTime.now().millisecondsSinceEpoch - 86400000,
          "closeTime": DateTime.now().millisecondsSinceEpoch,
        },
        {
          "symbol": "ETHUSDT",
          "lastPrice": 3200.75,
          "openPrice": 3180.00,
          "highPrice": 3250.00,
          "lowPrice": 3150.00,
          "volume": 20000.0,
          "quoteVolume": 64000000.0,
          "priceChange": 20.75,
          "priceChangePercent": 0.65,
          "openTime": DateTime.now().millisecondsSinceEpoch - 86400000,
          "closeTime": DateTime.now().millisecondsSinceEpoch,
        },
        {
          "symbol": "BNBUSDT",
          "lastPrice": 600.00,
          "openPrice": 605.00,
          "highPrice": 610.00,
          "lowPrice": 595.00,
          "volume": 50000.0,
          "quoteVolume": 30000000.0,
          "priceChange": -5.00,
          "priceChangePercent": -0.83,
          "openTime": DateTime.now().millisecondsSinceEpoch - 86400000,
          "closeTime": DateTime.now().millisecondsSinceEpoch,
        },
      ];
      return mockTickers
          .map((json) => MarketTickerModel.fromJson(json))
          .toList();

      // final response = await apiService.get('/market/tickers');
      // final List<dynamic> jsonList = response.data as List<dynamic>;
      // return jsonList.map((json) => MarketTickerModel.fromJson(json as Map<String, dynamic>)).toList();
    } on DioException catch (e) {
      throw Exception('获取所有行情数据失败: ${e.message ?? e.type.name}');
    } catch (e) {
      throw Exception('获取所有行情数据时发生未知错误: ${e.toString()}');
    }
  }

  @override
  Future<List<LineDataModel>> candleStick({
    required String symbol,
    required String bar,
    required int limit,
    required int befor,
    required int t,
  }) async {
    // 🔧 检查是否启用Mock数据 - 如果启用，直接返回Mock数据，不调用真实接口
    if (_mockConfig.enableMockData) {
      if (_mockConfig.showMockDataLogs) {
        AppLogger.logModule(
          moduleName,
          LogLevel.info,
          '🎭 Mock模式已启用，跳过真实接口调用，直接生成Mock数据 (接口1)',
          metadata: {
            'symbol': symbol,
            'bar': bar,
            'limit': limit,
            'mock_enabled': true,
            'skip_real_api': true,
            'data_source': 'mock_generator',
          },
        );
      }

      // 🎯 直接生成Mock数据，不调用真实接口
      final mockData = KlineMockDataGenerator.generateMockData(
        symbol: symbol,
        timeFrame: bar,
        limit: limit,
        endTime: DateTime.fromMillisecondsSinceEpoch(t),
        config: _mockConfig,
      );

      if (_mockConfig.showMockDataLogs) {
        AppLogger.logModule(
          moduleName,
          LogLevel.info,
          '✅ Mock K线数据生成成功 (完全跳过真实接口)',
          metadata: {
            'symbol': symbol,
            'data_count': mockData.length,
            'first_price': mockData.isNotEmpty ? mockData.first.open : null,
            'last_price': mockData.isNotEmpty ? mockData.last.close : null,
            'data_source': 'mock_generator',
            'real_api_called': false,
          },
        );
      }

      return mockData;
    }

    // 🔑 Mock未启用时，调用真实接口
    try {
      AppLogger.logModule(
        moduleName,
        LogLevel.info,
        '📊 Mock模式未启用，调用真实接口获取K线数据 (接口1)',
        metadata: {
          'symbol': symbol,
          'bar': bar,
          'limit': limit,
          'befor': befor,
          't': t,
          'api_endpoint': '/quote-api/kline/v1/candlestick',
          'mock_enabled': false,
        },
      );

      var queryParameters = {
        'symbol': symbol,
        'bar': bar,
        'limit': limit,
        'befor': befor,
        't': t,
      };

      final response = await _apiService.get(
        '/quote-api/kline/v1/candlestick',
        queryParameters: queryParameters,
      );

      // 🔑 处理后端返回的数据格式
      // 先检查response.data的结构
      if (response['data'] == null) {
        throw Exception('响应数据为空');
      }

      final List<dynamic> dataList = response['data'] ?? [];

      // 🔧 检查是否有有效数据
      if (dataList.isEmpty) {
        AppLogger.logModule(
          moduleName,
          LogLevel.warning,
          '⚠️ 真实接口返回空数据',
          metadata: {'symbol': symbol, 'bar': bar, 'empty_response': true},
        );
        throw Exception('真实接口返回空数据');
      }

      AppLogger.logModule(
        moduleName,
        LogLevel.info,
        '📊 数据列表分析',
        metadata: {
          'dataList.length': dataList.length,
          'dataList.type': dataList.runtimeType.toString(),
          'first_item': dataList.isNotEmpty ? dataList.first : null,
          'first_item_type': dataList.isNotEmpty
              ? dataList.first.runtimeType.toString()
              : null,
        },
      );

      final List<LineDataModel> result = dataList.map((item) {
        AppLogger.logModule(
          moduleName,
          LogLevel.debug,
          '🔍 处理单个数据项',
          metadata: {
            'item': item,
            'item_type': item.runtimeType.toString(),
            'is_list': item is List,
            'item_length': item is List ? item.length : 'not_list',
          },
        );

        try {
          // 🔧 使用智能解析方法，支持多种数据格式
          return _parseToLineDataModel(item);
        } catch (e) {
          AppLogger.logModule(
            moduleName,
            LogLevel.error,
            '❌ 数据项转换失败',
            error: e,
            metadata: {'item': item, 'item_type': item.runtimeType.toString()},
          );
          rethrow;
        }
      }).toList();

      AppLogger.logModule(
        moduleName,
        LogLevel.info,
        '✅ 初始K线数据获取成功',
        metadata: {
          'symbol': symbol,
          'data_count': result.length,
          'api_endpoint': '/quote-api/kline/v1/candlestick',
        },
      );

      return result;
    } on DioException catch (e) {
      AppLogger.logModule(
        moduleName,
        LogLevel.error,
        '❌ 获取初始K线数据失败',
        error: e,
        metadata: {
          'api_endpoint': '/quote-api/kline/v1/candlestick',
          'symbol': symbol,
          'bar': bar,
        },
      );
      throw Exception('获取初始K线数据失败: ${e.message ?? e.type.name}');
    } catch (e) {
      AppLogger.logModule(
        moduleName,
        LogLevel.error,
        '❌ 获取初始K线数据时发生未知错误',
        error: e,
        metadata: {
          'api_endpoint': '/quote-api/kline/v1/candlestick',
          'symbol': symbol,
          'bar': bar,
        },
      );
      throw Exception('获取初始K线数据时发生未知错误: ${e.toString()}');
    }
  }

  @override
  Future<List<TradingViewChartDataModel>> getHistoricalChartData({
    required String symbol,
    required String timeFrame,
    required TradingViewDataType dataType,
    required range,
    int? limit,
  }) async {
    // 🔧 检查是否启用Mock数据 - 如果启用，直接返回Mock数据，不调用真实接口
    if (_mockConfig.enableMockData) {
      if (_mockConfig.showMockDataLogs) {
        AppLogger.logModule(
          moduleName,
          LogLevel.info,
          '🎭 Mock模式已启用，跳过真实接口调用，直接生成Mock历史数据 (接口2)',
          metadata: {
            'symbol': symbol,
            'time_frame': timeFrame,
            'data_type': dataType.toString(),
            'limit': limit,
            'mock_enabled': true,
            'skip_real_api': true,
            'data_source': 'mock_generator',
          },
        );
      }

      // 🎯 直接生成Mock历史数据，不调用真实接口
      return _generateMockHistoricalData(
        symbol: symbol,
        timeFrame: timeFrame,
        dataType: dataType,
        range: range,
        limit: limit,
      );
    }

    // 🔑 Mock未启用时，调用真实接口
    try {
      AppLogger.logModule(
        moduleName,
        LogLevel.info,
        '📊 Mock模式未启用，调用真实接口获取历史K线数据 (接口2)',
        metadata: {
          'symbol': symbol,
          'time_frame': timeFrame,
          'data_type': dataType.toString(),
          'limit': limit,
          'range': range.toString(),
          'api_endpoint': '/quote-api/kline/v1/history-candles',
          'mock_enabled': false,
        },
      );

      // 🔑 构建历史数据接口参数 - 智能选择after参数
      final cacheKey = '${symbol}_$timeFrame';
      int afterTimestamp;

      // 判断使用哪个时间戳作为after参数
      if (_lastTimestampCache.containsKey(cacheKey)) {
        // 如果有历史数据缓存，使用历史数据的最后时间戳
        afterTimestamp = _lastTimestampCache[cacheKey]!;
        AppLogger.logModule(
          moduleName,
          LogLevel.info,
          '📊 使用历史数据缓存的时间戳',
          metadata: {
            'cache_key': cacheKey,
            'after_timestamp': afterTimestamp,
            'after_time_readable': DateTime.fromMillisecondsSinceEpoch(
              afterTimestamp,
            ).toIso8601String(),
          },
        );
      } else if (_initialDataLastTimestamp.containsKey(cacheKey)) {
        // 如果没有历史数据缓存，但有初始数据缓存，使用初始数据的最后时间戳
        afterTimestamp = _initialDataLastTimestamp[cacheKey]!;
        AppLogger.logModule(
          moduleName,
          LogLevel.info,
          '📊 使用初始数据缓存的时间戳',
          metadata: {
            'cache_key': cacheKey,
            'after_timestamp': afterTimestamp,
            'after_time_readable': DateTime.fromMillisecondsSinceEpoch(
              afterTimestamp,
            ).toIso8601String(),
          },
        );
      } else {
        // 如果都没有缓存，使用range.to作为fallback
        afterTimestamp = range.to;
        AppLogger.logModule(
          moduleName,
          LogLevel.warning,
          '⚠️ 没有找到缓存时间戳，使用range.to作为fallback',
          metadata: {
            'cache_key': cacheKey,
            'after_timestamp': afterTimestamp,
            'after_time_readable': DateTime.fromMillisecondsSinceEpoch(
              afterTimestamp,
            ).toIso8601String(),
          },
        );
      }

      final queryParameters = {
        'symbol': symbol,
        'bar': timeFrame,
        'limit': limit ?? 100,
        'after': afterTimestamp,
        't': DateTime.now().millisecondsSinceEpoch,
      };

      final response = await _apiService.get(
        '/quote-api/kline/v1/history-candles',
        queryParameters: queryParameters,
      );

      AppLogger.logModule(
        moduleName,
        LogLevel.info,
        '✅ 接口2返回的原始数据',
        metadata: {
          'response.data': response.data,
          'response.data.type': response.data.runtimeType.toString(),
        },
      );

      // 🔑 处理后端返回的数据格式
      // 先检查response.data的结构
      if (response.data == null) {
        throw Exception('响应数据为空');
      }

      AppLogger.logModule(
        moduleName,
        LogLevel.info,
        '🔍 分析接口2响应数据结构',
        metadata: {
          'has_data_field': response.data.containsKey('data'),
          'response_keys': response.data.keys.toList(),
          'data_field_type': response.data['data']?.runtimeType.toString(),
          'data_field_value': response.data['data'],
        },
      );

      final List<dynamic> dataList = response.data['data'] ?? [];

      AppLogger.logModule(
        moduleName,
        LogLevel.info,
        '📊 接口2数据列表分析',
        metadata: {
          'dataList.length': dataList.length,
          'dataList.type': dataList.runtimeType.toString(),
          'first_item': dataList.isNotEmpty ? dataList.first : null,
          'first_item_type': dataList.isNotEmpty
              ? dataList.first.runtimeType.toString()
              : null,
        },
      );

      // 🔑 根据数据类型转换格式
      final chartData = dataList.map((item) {
        AppLogger.logModule(
          moduleName,
          LogLevel.debug,
          '🔍 接口2处理单个数据项',
          metadata: {
            'item': item,
            'item_type': item.runtimeType.toString(),
            'is_list': item is List,
            'item_length': item is List ? item.length : 'not_list',
          },
        );

        try {
          // 🔧 使用智能解析方法，支持多种数据格式
          return _parseToTradingViewChartDataModel(item, timeFrame, dataType);
        } catch (e) {
          AppLogger.logModule(
            moduleName,
            LogLevel.error,
            '❌ 接口2数据项转换失败',
            error: e,
            metadata: {'item': item, 'item_type': item.runtimeType.toString()},
          );
          rethrow;
        }
      }).toList();

      // 🔑 缓存历史数据的最后时间戳，用于下次调用历史数据接口
      if (chartData.isNotEmpty) {
        final cacheKey = '${symbol}_$timeFrame';
        final lastData = chartData.last;
        final lastTimestamp = lastData.timestamp.millisecondsSinceEpoch;

        _lastTimestampCache[cacheKey] = lastTimestamp;

        AppLogger.logModule(
          moduleName,
          LogLevel.info,
          '💾 缓存历史数据最后时间戳',
          metadata: {
            'cache_key': cacheKey,
            'last_timestamp': lastTimestamp,
            'last_time_readable': lastData.timestamp.toIso8601String(),
          },
        );
      }

      AppLogger.logModule(
        moduleName,
        LogLevel.info,
        '✅ 历史K线数据获取成功',
        metadata: {
          'symbol': symbol,
          'time_frame': timeFrame,
          'data_count': chartData.length,
          'data_type': dataType.toString(),
          'api_endpoint': '/quote-api/kline/v1/history-candles',
        },
      );

      return chartData;
    } on DioException catch (e) {
      AppLogger.logModule(
        moduleName,
        LogLevel.error,
        '❌ 获取历史K线数据失败',
        error: e,
        metadata: {
          'symbol': symbol,
          'time_frame': timeFrame,
          'api_endpoint': '/quote-api/kline/v1/history-candles',
        },
      );
      throw Exception('获取历史K线数据失败: ${e.message ?? e.type.name}');
    } catch (e) {
      AppLogger.logModule(
        moduleName,
        LogLevel.error,
        '❌ 获取历史K线数据时发生未知错误',
        error: e,
        metadata: {
          'symbol': symbol,
          'time_frame': timeFrame,
          'api_endpoint': '/quote-api/kline/v1/history-candles',
        },
      );
      throw Exception('获取历史K线数据时发生未知错误: ${e.toString()}');
    }
  }

  @override
  Future<List<TradingViewChartDataModel>> getInitialChartData({
    required String symbol,
    required String timeFrame,
    required TradingViewDataType dataType,
    int? limit,
  }) async {
    try {
      AppLogger.logModule(
        moduleName,
        LogLevel.info,
        '📊 获取初始图表数据 (接口1)',
        metadata: {
          'symbol': symbol,
          'time_frame': timeFrame,
          'data_type': dataType.toString(),
          'limit': limit,
          'api_interface': 'get_line_data',
        },
      );

      // 🔑 使用GetLineData接口（接口1）
      final response = await _apiService.get(
        '/quote-api/kline/v1/candlestick',
        queryParameters: {
          'symbol': symbol,
          'bar': timeFrame,
          'limit': limit ?? 100,
          'befor': DateTime.now().millisecondsSinceEpoch,
          't': DateTime.now().millisecondsSinceEpoch,
        },
      );

      // 🔑 处理后端返回的数据格式
      // response.data['data'] = [[open, high, low, close, time], ...]
      final List<dynamic> dataList = response.data['data'] ?? [];

      // 🔑 根据数据类型转换格式
      final chartData = dataList.map((item) {
        if (item is List && item.length >= 5) {
          final open = (item[0] as num).toDouble();
          final high = (item[1] as num).toDouble();
          final low = (item[2] as num).toDouble();
          final close = (item[3] as num).toDouble();
          final time = (item[4] as num).toInt();

          return TradingViewChartDataModel.fromHistoricalData(
            open: open,
            high: high,
            low: low,
            close: close,
            time: time,
            timeFrame: timeFrame,
            type: dataType,
          );
        } else {
          throw FormatException(
            'Invalid data format: expected List with 5 elements',
          );
        }
      }).toList();
      // 🔑 缓存初始数据的最后时间戳，用于历史数据接口的第一次调用
      if (chartData.isNotEmpty) {
        final cacheKey = '${symbol}_$timeFrame';
        final lastData = chartData.last;
        final lastTimestamp = lastData.timestamp.millisecondsSinceEpoch;

        _initialDataLastTimestamp[cacheKey] = lastTimestamp;

        AppLogger.logModule(
          moduleName,
          LogLevel.info,
          '💾 缓存初始数据最后时间戳',
          metadata: {
            'cache_key': cacheKey,
            'last_timestamp': lastTimestamp,
            'last_time_readable': lastData.timestamp.toIso8601String(),
          },
        );
      }

      AppLogger.logModule(
        'MarketRemoteDataSourceImpl',
        LogLevel.info,
        '✅ 初始图表数据获取成功',
        metadata: {
          'symbol': symbol,
          'time_frame': timeFrame,
          'data_count': chartData.length,
          'data_type': dataType.toString(),
          'api_interface': 'get_line_data',
        },
      );

      return chartData;
    } catch (e) {
      AppLogger.logModule(
        moduleName,
        LogLevel.error,
        '❌ 初始图表数据获取失败',
        error: e,
        metadata: {
          'symbol': symbol,
          'time_frame': timeFrame,
          'api_interface': 'get_line_data',
        },
      );
      throw Exception('获取初始图表数据失败: ${e.toString()}');
    }
  }

  /// 🎯 生成Mock历史数据
  List<TradingViewChartDataModel> _generateMockHistoricalData({
    required String symbol,
    required String timeFrame,
    required TradingViewDataType dataType,
    required dynamic range,
    int? limit,
  }) {
    try {
      AppLogger.logModule(
        moduleName,
        LogLevel.info,
        '🎭 生成Mock历史数据',
        metadata: {
          'symbol': symbol,
          'time_frame': timeFrame,
          'data_type': dataType.toString(),
          'limit': limit,
          'data_source': 'mock_generator',
        },
      );

      // 🔧 确保时间戳类型正确
      int endTimeMs;
      try {
        // range.to 应该是时间戳（毫秒）
        endTimeMs = range.to;
        if (endTimeMs <= 0) {
          endTimeMs = DateTime.now().millisecondsSinceEpoch;
        }
      } catch (e) {
        AppLogger.logModule(
          moduleName,
          LogLevel.warning,
          '⚠️ 时间戳转换失败，使用当前时间',
          error: e,
          metadata: {
            'range_to': range.to,
            'range_to_type': range.to.runtimeType.toString(),
          },
        );
        endTimeMs = DateTime.now().millisecondsSinceEpoch;
      }

      // 🔧 增加历史数据生成数量，确保有足够的数据供图表滑动
      // 根据时间范围计算合理的数据量，最少200条，最多1000条
      final timeRangeMs = range.to - range.from;
      final intervalMs = KlineMockDataGenerator._getIntervalDuration(
        timeFrame,
      ).inMilliseconds;
      final calculatedLimit = (timeRangeMs / intervalMs).ceil();
      final finalLimit = (limit ?? calculatedLimit).clamp(200, 1000);

      AppLogger.logModule(
        moduleName,
        LogLevel.info,
        '📊 计算历史数据生成数量',
        metadata: {
          'time_range_ms': timeRangeMs,
          'interval_ms': intervalMs,
          'calculated_limit': calculatedLimit,
          'requested_limit': limit,
          'final_limit': finalLimit,
        },
      );

      // 生成LineDataModel格式的Mock数据
      final mockLineData = KlineMockDataGenerator.generateMockData(
        symbol: symbol,
        timeFrame: timeFrame,
        limit: finalLimit,
        endTime: DateTime.fromMillisecondsSinceEpoch(endTimeMs),
        config: _mockConfig,
      );

      // 转换为TradingViewChartDataModel格式
      final chartData = <TradingViewChartDataModel>[];

      // 🔧 先尝试一个样本数据，确保格式正确
      if (mockLineData.isNotEmpty) {
        try {
          AppLogger.logModule(
            moduleName,
            LogLevel.info,
            '🧪 测试样本数据转换',
            metadata: {'timeFrame': timeFrame, 'dataType': dataType.toString()},
          );

          // 创建一个测试数据
          final testTime = DateTime.now().millisecondsSinceEpoch;
          final testData = TradingViewChartDataModel.fromHistoricalData(
            open: 100.0,
            high: 101.0,
            low: 99.0,
            close: 100.5,
            time: testTime,
            timeFrame: timeFrame,
            type: dataType,
          );

          AppLogger.logModule(
            moduleName,
            LogLevel.info,
            '✅ 测试样本数据转换成功',
            metadata: {
              'time': testData.time,
              'time_type': testData.time.runtimeType.toString(),
              'timestamp': testData.timestamp.toString(),
            },
          );
        } catch (e, stackTrace) {
          AppLogger.logModule(
            moduleName,
            LogLevel.error,
            '❌ 测试样本数据转换失败',
            error: e,
            metadata: {
              'timeFrame': timeFrame,
              'dataType': dataType.toString(),
              'stackTrace': stackTrace.toString(),
            },
          );
          // 继续执行，尝试真实数据
        }
      }

      // 处理真实数据
      for (int i = 0; i < mockLineData.length; i++) {
        final lineData = mockLineData[i];
        try {
          // 🔧 详细的类型检查和转换
          AppLogger.logModule(
            moduleName,
            LogLevel.debug,
            '🔍 转换Mock数据项 $i',
            metadata: {
              'open': lineData.open,
              'high': lineData.hight,
              'low': lineData.low,
              'close': lineData.close,
              'time': lineData.time,
              'time_type': lineData.time.runtimeType.toString(),
              'timeFrame': timeFrame,
              'dataType': dataType.toString(),
            },
          );

          // 🔧 特殊处理1d时间周期
          if (timeFrame == '1d') {
            // 对于1d时间周期，直接使用日期字符串格式
            final dateTime = DateTime.fromMillisecondsSinceEpoch(lineData.time);
            final year = dateTime.year;
            final month = dateTime.month.toString().padLeft(2, '0');
            final day = dateTime.day.toString().padLeft(2, '0');
            final dateStr = '$year-$month-$day';

            // 创建自定义的TradingViewChartDataModel
            final chartDataItem = TradingViewChartDataModel(
              time: dateStr,
              type: dataType,
              timestamp: dateTime,
              open: lineData.open,
              high: lineData.hight,
              low: lineData.low,
              close: lineData.close,
            );

            chartData.add(chartDataItem);
          } else {
            // 对于其他时间周期，使用正常的fromHistoricalData方法
            final chartDataItem = TradingViewChartDataModel.fromHistoricalData(
              open: lineData.open,
              high: lineData.hight,
              low: lineData.low,
              close: lineData.close,
              time: lineData.time,
              timeFrame: timeFrame,
              type: dataType,
            );

            chartData.add(chartDataItem);
          }
        } catch (e, stackTrace) {
          AppLogger.logModule(
            moduleName,
            LogLevel.error,
            '❌ Mock数据转换失败 (项目 $i)',
            error: e,
            metadata: {
              'index': i,
              'lineData_open': lineData.open,
              'lineData_high': lineData.hight,
              'lineData_low': lineData.low,
              'lineData_close': lineData.close,
              'lineData_time': lineData.time,
              'lineData_time_type': lineData.time.runtimeType.toString(),
              'timeFrame': timeFrame,
              'dataType': dataType.toString(),
              'stackTrace': stackTrace.toString(),
            },
          );

          // 🔧 不要抛出异常，而是跳过这个数据项
          AppLogger.logModule(moduleName, LogLevel.warning, '⚠️ 跳过无效数据项 $i');
        }
      }

      AppLogger.logModule(
        moduleName,
        LogLevel.info,
        '✅ Mock历史数据生成成功',
        metadata: {
          'symbol': symbol,
          'data_count': chartData.length,
          'data_source': 'mock_generator',
          'first_item_time': chartData.isNotEmpty ? chartData.first.time : null,
          'last_item_time': chartData.isNotEmpty ? chartData.last.time : null,
        },
      );

      return chartData;
    } catch (e) {
      AppLogger.logModule(
        moduleName,
        LogLevel.error,
        '❌ Mock历史数据生成失败',
        error: e,
      );
      return [];
    }
  }
}
