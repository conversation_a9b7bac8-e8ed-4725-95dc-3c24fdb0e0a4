/// 代表内层数组的 K 线数据模型
/// ["1751456641714", "130.0...", ...]
class LineDataModel {
  final double open;
  final double hight;
  final double low;
  final double close;
  final int time; // 时间戳

  LineDataModel({
    required this.open,
    required this.hight,
    required this.low,
    required this.close,
    required this.time,
  });

  // 这是关键：自定义一个工厂构造函数，从一个 List<String> 创建实例
  factory LineDataModel.fromList(List<dynamic> list) {
    // 安全检查，防止数组越界
    if (list.length < 5) {
      throw FormatException(
        "Invalid Kline data list. Expected 5 items, but got ${list.length}.",
      );
    }

    return LineDataModel(
      // 将 String 转换为对应的 int 或 double
      open: double.parse(list[0].toString()),
      hight: double.parse(list[1].toString()),
      low: double.parse(list[2].toString()),
      close: double.parse(list[3].toString()),
      time: int.parse(list[4].toString()),
    );
  }

  // (可选) 覆写 toString 方法，方便调试打印
  @override
  String toString() {
    return 'LineDataModel(time: ${DateTime.fromMillisecondsSinceEpoch(time)}, open: $open, hight: $hight, low: $low, close: $close)';
  }
}
