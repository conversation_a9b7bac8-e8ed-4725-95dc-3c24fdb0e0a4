import 'package:equatable/equatable.dart';
import 'package:financial_app_core/financial_app_core.dart';

/// TradingView数据类型枚举
enum TradingViewDataType {
  candlestick, // K线图
  line, // 折线图
}

class TradingViewChartDataModel extends Equatable {
  final String time; // TradingView使用的time字段
  final TradingViewDataType type; // 数据类型
  final DateTime timestamp; // 内部时间戳

  // K线图字段
  final double? open;
  final double? high;
  final double? low;
  final double? close;

  // 折线图字段
  final double? value;

  const TradingViewChartDataModel({
    required this.time,
    required this.type,
    required this.timestamp,
    this.open,
    this.high,
    this.low,
    this.close,
    this.value,
  });

  /// 从GetLineData响应创建K线图数据
  factory TradingViewChartDataModel.fromGetLineDataResponse({
    required dynamic response,
    required String timeFrame,
  }) {
    try {
      final timestampMs = _extractTimestamp(response);
      final time = _formatTimeForTradingView(timestampMs, timeFrame);

      return TradingViewChartDataModel(
        time: time,
        type: TradingViewDataType.candlestick,
        timestamp: DateTime.fromMillisecondsSinceEpoch(timestampMs),
        open: _extractDouble(response, ['open']),
        high: _extractDouble(response, ['high', 'hight']), // 兼容拼写错误
        low: _extractDouble(response, ['low']),
        close: _extractDouble(response, ['close']),
      );
    } catch (e) {
      AppLogger.logModule(
        'TradingViewChartDataModel',
        LogLevel.error,
        '❌ 从GetLineData响应创建K线图数据失败',
        error: e,
        metadata: {'response': response.toString()},
      );
      rethrow;
    }
  }

  /// 从GetLineData响应创建折线图数据
  factory TradingViewChartDataModel.fromGetLineDataResponseAsLine({
    required dynamic response,
    required String timeFrame,
  }) {
    try {
      final timestampMs = _extractTimestamp(response);
      final time = _formatTimeForTradingView(timestampMs, timeFrame);

      return TradingViewChartDataModel(
        time: time,
        type: TradingViewDataType.line,
        timestamp: DateTime.fromMillisecondsSinceEpoch(timestampMs),
        value: _extractDouble(response, ['close']), // 使用收盘价
      );
    } catch (e) {
      AppLogger.logModule(
        'TradingViewChartDataModel',
        LogLevel.error,
        '❌ 从GetLineData响应创建折线图数据失败',
        error: e,
        metadata: {'response': response.toString()},
      );
      rethrow;
    }
  }

  /// 从MarketApi响应创建K线图数据
  factory TradingViewChartDataModel.fromMarketApiResponse({
    required dynamic response,
    required String timeFrame,
  }) {
    try {
      final timestampMs = _extractTimestamp(response, [
        'openTime',
        'timestamp',
        'time',
      ]);
      final time = _formatTimeForTradingView(timestampMs, timeFrame);

      return TradingViewChartDataModel(
        time: time,
        type: TradingViewDataType.candlestick,
        timestamp: DateTime.fromMillisecondsSinceEpoch(timestampMs),
        open: _extractDouble(response, ['open']),
        high: _extractDouble(response, ['high']),
        low: _extractDouble(response, ['low']),
        close: _extractDouble(response, ['close']),
      );
    } catch (e) {
      AppLogger.logModule(
        'TradingViewChartDataModel',
        LogLevel.error,
        '❌ 从MarketApi响应创建K线图数据失败',
        error: e,
        metadata: {'response': response.toString()},
      );
      rethrow;
    }
  }

  /// 从MarketApi响应创建折线图数据
  factory TradingViewChartDataModel.fromMarketApiResponseAsLine({
    required dynamic response,
    required String timeFrame,
  }) {
    try {
      final timestampMs = _extractTimestamp(response, [
        'openTime',
        'timestamp',
        'time',
      ]);
      final time = _formatTimeForTradingView(timestampMs, timeFrame);

      return TradingViewChartDataModel(
        time: time,
        type: TradingViewDataType.line,
        timestamp: DateTime.fromMillisecondsSinceEpoch(timestampMs),
        value: _extractDouble(response, ['close']), // 使用收盘价
      );
    } catch (e) {
      AppLogger.logModule(
        'TradingViewChartDataModel',
        LogLevel.error,
        '❌ 从MarketApi响应创建折线图数据失败',
        error: e,
        metadata: {'response': response.toString()},
      );
      rethrow;
    }
  }

  /// 🔑 从历史数据创建图表数据（用于双接口）
  factory TradingViewChartDataModel.fromHistoricalData({
    required double open,
    required double high,
    required double low,
    required double close,
    required int time,
    required String timeFrame,
    required TradingViewDataType type,
  }) {
    try {
      final formattedTime = _formatTimeForTradingView(time, timeFrame);
      final timestamp = DateTime.fromMillisecondsSinceEpoch(time);

      switch (type) {
        case TradingViewDataType.candlestick:
          return TradingViewChartDataModel(
            time: formattedTime,
            type: TradingViewDataType.candlestick,
            timestamp: timestamp,
            open: open,
            high: high,
            low: low,
            close: close,
          );
        case TradingViewDataType.line:
          return TradingViewChartDataModel(
            time: formattedTime,
            type: TradingViewDataType.line,
            timestamp: timestamp,
            value: close, // 使用收盘价作为线图值
          );
      }
    } catch (e) {
      AppLogger.logModule(
        'TradingViewChartDataModel',
        LogLevel.error,
        '❌ 从历史数据创建图表数据失败',
        error: e,
        metadata: {
          'open': open,
          'high': high,
          'low': low,
          'close': close,
          'time': time,
          'timeFrame': timeFrame,
          'type': type.toString(),
        },
      );
      rethrow;
    }
  }

  /// 转换为TradingView格式
  Map<String, dynamic> toTradingViewFormat() {
    try {
      switch (type) {
        case TradingViewDataType.candlestick:
          if (open == null || high == null || low == null || close == null) {
            throw ArgumentError('K线图数据字段不完整');
          }
          return {
            'time': time,
            'open': open!,
            'high': high!,
            'low': low!,
            'close': close!,
          };
        case TradingViewDataType.line:
          if (value == null) {
            throw ArgumentError('折线图数据字段不完整');
          }
          return {'time': time, 'value': value!};
      }
    } catch (e) {
      AppLogger.logModule(
        'TradingViewChartDataModel',
        LogLevel.error,
        '❌ 转换为TradingView格式失败',
        error: e,
        metadata: {'type': type.toString()},
      );
      rethrow;
    }
  }

  /// 获取主要价格值
  double get primaryValue {
    switch (type) {
      case TradingViewDataType.candlestick:
        return close ?? 0.0;
      case TradingViewDataType.line:
        return value ?? 0.0;
    }
  }

  /// 验证数据完整性
  bool get isValid {
    switch (type) {
      case TradingViewDataType.candlestick:
        return open != null && high != null && low != null && close != null;
      case TradingViewDataType.line:
        return value != null;
    }
  }

  /// 为TradingView格式化时间
  static String _formatTimeForTradingView(int timestampMs, String timeFrame) {
    try {
      // 🔧 添加详细的类型检查和日志
      AppLogger.logModule(
        'TradingViewChartDataModel',
        LogLevel.debug,
        '🔍 格式化时间戳',
        metadata: {
          'timestampMs': timestampMs,
          'timestampMs_type': timestampMs.runtimeType.toString(),
          'timeFrame': timeFrame,
        },
      );

      final dateTime = DateTime.fromMillisecondsSinceEpoch(timestampMs);

      switch (timeFrame) {
        case '1s':
        case '1m':
        case '5m':
        case '15m':
        case '30m':
        case '1h':
        case '4h':
          // 分钟/小时级别：使用时间戳（秒）
          final timestampSeconds = timestampMs ~/ 1000;
          final result = timestampSeconds.toString();
          AppLogger.logModule(
            'TradingViewChartDataModel',
            LogLevel.debug,
            '✅ 时间戳格式化完成（秒级）',
            metadata: {
              'timestampSeconds': timestampSeconds,
              'result': result,
              'result_type': result.runtimeType.toString(),
            },
          );
          return result;
        case '1d':
        case '1w':
        case '1M':
          // 日/周/月级别：使用日期字符串
          final year = dateTime.year;
          final month = dateTime.month;
          final day = dateTime.day;

          // 🔧 明确的类型转换，避免类型推断问题
          final monthStr = month.toString();
          final dayStr = day.toString();

          // 确保padLeft的参数类型正确
          const String paddingChar = '0';
          final monthPadded = monthStr.padLeft(2, paddingChar);
          final dayPadded = dayStr.padLeft(2, paddingChar);

          final result = '$year-$monthPadded-$dayPadded';
          AppLogger.logModule(
            'TradingViewChartDataModel',
            LogLevel.debug,
            '✅ 时间戳格式化完成（日期）',
            metadata: {
              'year': year,
              'month': month,
              'day': day,
              'result': result,
              'result_type': result.runtimeType.toString(),
            },
          );
          return result;
        default:
          final timestampSeconds = timestampMs ~/ 1000;
          final result = timestampSeconds.toString();
          AppLogger.logModule(
            'TradingViewChartDataModel',
            LogLevel.debug,
            '✅ 时间戳格式化完成（默认）',
            metadata: {
              'timestampSeconds': timestampSeconds,
              'result': result,
              'result_type': result.runtimeType.toString(),
            },
          );
          return result;
      }
    } catch (e, stackTrace) {
      AppLogger.logModule(
        'TradingViewChartDataModel',
        LogLevel.error,
        '❌ 时间格式化失败',
        error: e,
        metadata: {
          'timestampMs': timestampMs,
          'timestampMs_type': timestampMs.runtimeType.toString(),
          'timeFrame': timeFrame,
          'stackTrace': stackTrace.toString(),
        },
      );
      // 🔧 确保异常情况下也返回正确的类型
      final fallbackSeconds = timestampMs ~/ 1000;
      return fallbackSeconds.toString();
    }
  }

  /// 提取时间戳
  static int _extractTimestamp(dynamic response, [List<String>? fields]) {
    final fieldsToTry = fields ?? ['time', 'timestamp', 'openTime'];

    for (final field in fieldsToTry) {
      try {
        if (response is Map<String, dynamic> && response.containsKey(field)) {
          final value = response[field];
          if (value is int) return value;
          if (value is String) return int.parse(value);
        } else if (response != null) {
          // 尝试通过反射获取字段
          try {
            final value = _getFieldValue(response, field);
            if (value is int) return value;
            if (value is String) return int.parse(value);
          } catch (_) {
            // 忽略反射错误，继续尝试下一个字段
          }
        }
      } catch (e) {
        // 忽略单个字段的错误，继续尝试下一个字段
        continue;
      }
    }

    // 如果所有字段都失败，返回当前时间戳
    AppLogger.logModule(
      'TradingViewChartDataModel',
      LogLevel.warning,
      '⚠️ 无法提取时间戳，使用当前时间',
      metadata: {'response': response.toString(), 'fields': fieldsToTry},
    );
    return DateTime.now().millisecondsSinceEpoch;
  }

  /// 提取double值
  static double _extractDouble(dynamic response, List<String> fields) {
    for (final field in fields) {
      try {
        if (response is Map<String, dynamic> && response.containsKey(field)) {
          final value = response[field];
          if (value is num) return value.toDouble();
          if (value is String) return double.parse(value);
        } else if (response != null) {
          // 尝试通过反射获取字段
          try {
            final value = _getFieldValue(response, field);
            if (value is num) return value.toDouble();
            if (value is String) return double.parse(value);
          } catch (_) {
            // 忽略反射错误，继续尝试下一个字段
          }
        }
      } catch (e) {
        // 忽略单个字段的错误，继续尝试下一个字段
        continue;
      }
    }

    AppLogger.logModule(
      'TradingViewChartDataModel',
      LogLevel.warning,
      '⚠️ 无法提取数值，返回0.0',
      metadata: {'response': response.toString(), 'fields': fields},
    );
    return 0.0;
  }

  /// 获取字段值（反射）
  static dynamic _getFieldValue(dynamic object, String fieldName) {
    try {
      switch (fieldName) {
        case 'open':
          return object.open;
        case 'high':
          return object.high;
        case 'hight':
          return object.hight; // 兼容拼写错误
        case 'low':
          return object.low;
        case 'close':
          return object.close;
        case 'value':
          return object.value;
        case 'volume':
          return object.volume;
        case 'timestamp':
          return object.timestamp;
        case 'time':
          return object.time;
        case 'openTime':
          return object.openTime;
        default:
          return null;
      }
    } catch (e) {
      return null;
    }
  }

  @override
  List<Object?> get props => [
    time,
    type,
    timestamp,
    open,
    high,
    low,
    close,
    value,
  ];

  @override
  String toString() {
    switch (type) {
      case TradingViewDataType.candlestick:
        return 'TradingViewChartDataModel.candlestick(time: $time, open: $open, high: $high, low: $low, close: $close)';
      case TradingViewDataType.line:
        return 'TradingViewChartDataModel.line(time: $time, value: $value)';
    }
  }
}

/// TradingView数据工具类
class TradingViewDataUtils {
  /// 批量转换GetLineData响应
  static List<TradingViewChartDataModel> convertGetLineDataResponse({
    required List<dynamic> responses,
    required String timeFrame,
    required TradingViewDataType dataType,
  }) {
    final results = <TradingViewChartDataModel>[];

    for (final response in responses) {
      try {
        switch (dataType) {
          case TradingViewDataType.candlestick:
            results.add(
              TradingViewChartDataModel.fromGetLineDataResponse(
                response: response,
                timeFrame: timeFrame,
              ),
            );
            break;
          case TradingViewDataType.line:
            results.add(
              TradingViewChartDataModel.fromGetLineDataResponseAsLine(
                response: response,
                timeFrame: timeFrame,
              ),
            );
            break;
        }
      } catch (e) {
        AppLogger.logModule(
          'TradingViewDataUtils',
          LogLevel.warning,
          '⚠️ 跳过无效数据项',
          error: e,
          metadata: {'response': response.toString()},
        );
        // 跳过无效数据项，继续处理下一个
      }
    }

    return results;
  }

  /// 批量转换MarketApi响应
  static List<TradingViewChartDataModel> convertMarketApiResponse({
    required List<dynamic> responses,
    required String timeFrame,
    required TradingViewDataType dataType,
  }) {
    final results = <TradingViewChartDataModel>[];

    for (final response in responses) {
      try {
        switch (dataType) {
          case TradingViewDataType.candlestick:
            results.add(
              TradingViewChartDataModel.fromMarketApiResponse(
                response: response,
                timeFrame: timeFrame,
              ),
            );
            break;
          case TradingViewDataType.line:
            results.add(
              TradingViewChartDataModel.fromMarketApiResponseAsLine(
                response: response,
                timeFrame: timeFrame,
              ),
            );
            break;
        }
      } catch (e) {
        AppLogger.logModule(
          'TradingViewDataUtils',
          LogLevel.warning,
          '⚠️ 跳过无效数据项',
          error: e,
          metadata: {'response': response.toString()},
        );
        // 跳过无效数据项，继续处理下一个
      }
    }

    return results;
  }

  /// 验证数据列表
  static List<TradingViewChartDataModel> validateAndFilter(
    List<TradingViewChartDataModel> data,
  ) {
    return data.where((item) => item.isValid).toList();
  }

  /// 按时间排序数据
  static List<TradingViewChartDataModel> sortByTime(
    List<TradingViewChartDataModel> data,
  ) {
    final sortedData = List<TradingViewChartDataModel>.from(data);
    sortedData.sort((a, b) => a.timestamp.compareTo(b.timestamp));
    return sortedData;
  }

  /// 去重数据（基于时间戳）
  static List<TradingViewChartDataModel> removeDuplicates(
    List<TradingViewChartDataModel> data,
  ) {
    final seen = <int>{};
    return data.where((item) {
      final timestamp = item.timestamp.millisecondsSinceEpoch;
      if (seen.contains(timestamp)) {
        return false;
      }
      seen.add(timestamp);
      return true;
    }).toList();
  }
}
