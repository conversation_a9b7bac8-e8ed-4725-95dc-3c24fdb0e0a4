import 'package:equatable/equatable.dart';

/// 图表可见范围模型
class VisibleRange extends Equatable {
  /// 起始位置
  final int from;
  
  /// 结束位置
  final int to;

  const VisibleRange({
    required this.from,
    required this.to,
  });

  /// 创建默认范围
  factory VisibleRange.defaultRange() {
    return const VisibleRange(from: 0, to: 100);
  }

  /// 创建指定大小的范围
  factory VisibleRange.withSize(int size, {int offset = 0}) {
    return VisibleRange(from: offset, to: offset + size);
  }

  /// 复制并修改范围
  VisibleRange copyWith({
    int? from,
    int? to,
  }) {
    return VisibleRange(
      from: from ?? this.from,
      to: to ?? this.to,
    );
  }

  /// 范围大小
  int get size => to - from;

  /// 验证范围是否有效
  bool get isValid => from >= 0 && to > from;

  /// 检查是否包含指定位置
  bool contains(int position) {
    return position >= from && position < to;
  }

  /// 检查是否与另一个范围重叠
  bool overlaps(VisibleRange other) {
    return from < other.to && to > other.from;
  }

  /// 扩展范围
  VisibleRange expand(int amount) {
    return VisibleRange(
      from: (from - amount).clamp(0, double.infinity).toInt(),
      to: to + amount,
    );
  }

  /// 移动范围
  VisibleRange shift(int offset) {
    return VisibleRange(
      from: (from + offset).clamp(0, double.infinity).toInt(),
      to: to + offset,
    );
  }

  @override
  List<Object?> get props => [from, to];

  @override
  String toString() {
    return 'VisibleRange(from: $from, to: $to, size: $size)';
  }
}
