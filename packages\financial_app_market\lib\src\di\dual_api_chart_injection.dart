import 'package:get_it/get_it.dart';
import 'package:financial_app_core/financial_app_core.dart';
import '../domain/usecases/get_initial_chart_data.dart';
import '../domain/usecases/get_historical_chart_data.dart';
import '../domain/repositories/market_repository.dart';
import '../data/repositories/market_repository_impl.dart';
import '../data/datasources/market_remote_datasource.dart';
import '../data/datasources/market_websocket_datasource.dart';
import '../presentation/bloc/market_bloc.dart';

/// 双接口图表依赖注入配置
class DualApiChartInjection {
  static const String moduleName = 'DualApiChartInjection';

  /// 注册双接口图表相关依赖
  static void registerDependencies(GetIt locator) {
    AppLogger.logModule(moduleName, LogLevel.info, '🔧 开始注册双接口图表依赖');

    try {
      // 🔑 注册双接口用例
      _registerUseCases(locator);

      // 🔑 注册增强的MarketBloc
      _registerBloc(locator);

      AppLogger.logModule(
        moduleName,
        LogLevel.info,
        '✅ 双接口图表依赖注册完成',
        metadata: {'registered_use_cases': 2, 'registered_blocs': 1},
      );
    } catch (e) {
      AppLogger.logModule(
        moduleName,
        LogLevel.error,
        '❌ 双接口图表依赖注册失败',
        error: e,
      );
      rethrow;
    }
  }

  /// 注册用例
  static void _registerUseCases(GetIt locator) {
    AppLogger.logModule(moduleName, LogLevel.info, '📋 注册双接口用例');

    // 获取初始图表数据用例
    locator.registerLazySingleton<GetInitialChartData>(() {
      AppLogger.logModule(
        moduleName,
        LogLevel.debug,
        '🏭 创建GetInitialChartData实例',
      );
      return GetInitialChartData(locator<MarketRepository>());
    });

    // 获取历史图表数据用例
    locator.registerLazySingleton<GetHistoricalChartData>(() {
      AppLogger.logModule(
        moduleName,
        LogLevel.debug,
        '🏭 创建GetHistoricalChartData实例',
      );
      return GetHistoricalChartData(locator<MarketRepository>());
    });

    AppLogger.logModule(moduleName, LogLevel.info, '✅ 双接口用例注册完成');
  }

  /// 注册Bloc
  static void _registerBloc(GetIt locator) {
    AppLogger.logModule(moduleName, LogLevel.info, '🧠 注册增强的MarketBloc');

    // 注册增强的MarketBloc（支持双接口）
    locator.registerFactory<MarketBloc>(() {
      AppLogger.logModule(
        moduleName,
        LogLevel.debug,
        '🏭 创建增强的MarketBloc实例',
        metadata: {
          'has_initial_use_case': locator.isRegistered<GetInitialChartData>(),
          'has_historical_use_case': locator
              .isRegistered<GetHistoricalChartData>(),
        },
      );

      return MarketBloc(
        getInitialChartData: locator<GetInitialChartData>(),
        getHistoricalChartData: locator<GetHistoricalChartData>(),
      );
    });

    AppLogger.logModule(moduleName, LogLevel.info, '✅ 增强的MarketBloc注册完成');
  }

  /// 验证依赖注册
  static bool validateDependencies(GetIt locator) {
    AppLogger.logModule(moduleName, LogLevel.info, '🔍 验证双接口图表依赖');

    final dependencies = [
      GetInitialChartData,
      GetHistoricalChartData,
      MarketBloc,
    ];

    final missingDependencies = <Type>[];

    for (final dependency in dependencies) {
      bool isRegistered = false;
      if (dependency == GetInitialChartData) {
        isRegistered = locator.isRegistered<GetInitialChartData>();
      } else if (dependency == GetHistoricalChartData) {
        isRegistered = locator.isRegistered<GetHistoricalChartData>();
      } else if (dependency == MarketBloc) {
        isRegistered = locator.isRegistered<MarketBloc>();
      }

      if (!isRegistered) {
        missingDependencies.add(dependency);
      }
    }

    if (missingDependencies.isNotEmpty) {
      AppLogger.logModule(
        moduleName,
        LogLevel.error,
        '❌ 双接口图表依赖验证失败',
        metadata: {
          'missing_dependencies': missingDependencies
              .map((e) => e.toString())
              .toList(),
        },
      );
      return false;
    }

    AppLogger.logModule(
      moduleName,
      LogLevel.info,
      '✅ 双接口图表依赖验证通过',
      metadata: {'validated_dependencies': dependencies.length},
    );

    return true;
  }

  /// 清理依赖
  static void unregisterDependencies(GetIt locator) {
    AppLogger.logModule(moduleName, LogLevel.info, '🗑️ 清理双接口图表依赖');

    try {
      final dependenciesToUnregister = [
        GetInitialChartData,
        GetHistoricalChartData,
        MarketBloc,
      ];

      for (final dependency in dependenciesToUnregister) {
        bool isRegistered = false;
        if (dependency == GetInitialChartData) {
          isRegistered = locator.isRegistered<GetInitialChartData>();
          if (isRegistered) {
            locator.unregister<GetInitialChartData>();
          }
        } else if (dependency == GetHistoricalChartData) {
          isRegistered = locator.isRegistered<GetHistoricalChartData>();
          if (isRegistered) {
            locator.unregister<GetHistoricalChartData>();
          }
        } else if (dependency == MarketBloc) {
          isRegistered = locator.isRegistered<MarketBloc>();
          if (isRegistered) {
            locator.unregister<MarketBloc>();
          }
        }

        if (isRegistered) {
          AppLogger.logModule(
            moduleName,
            LogLevel.debug,
            '🗑️ 已清理依赖',
            metadata: {'dependency': dependency.toString()},
          );
        }
      }

      AppLogger.logModule(moduleName, LogLevel.info, '✅ 双接口图表依赖清理完成');
    } catch (e) {
      AppLogger.logModule(
        moduleName,
        LogLevel.error,
        '❌ 双接口图表依赖清理失败',
        error: e,
      );
    }
  }

  /// 获取依赖信息
  static Map<String, dynamic> getDependencyInfo(GetIt locator) {
    return {
      'module_name': moduleName,
      'dependencies': {
        'GetInitialChartData': locator.isRegistered<GetInitialChartData>(),
        'GetHistoricalChartData': locator
            .isRegistered<GetHistoricalChartData>(),
        'MarketBloc': locator.isRegistered<MarketBloc>(),
      },
      'validation_status': validateDependencies(locator),
      'timestamp': DateTime.now().toIso8601String(),
    };
  }
}
