import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:financial_app_core/financial_app_core.dart';
import '../repositories/market_repository.dart';
import '../../data/models/trading_view_chart_data.dart';
import '../../data/models/visible_range.dart';

/// 获取历史图表数据用例
class GetHistoricalChartData {
  final MarketRepository _repository;

  GetHistoricalChartData(this._repository);

  @override
  Future<Either<Failure, List<TradingViewChartDataModel>>> call(
    GetHistoricalChartDataParams params,
  ) async {
    // 参数验证
    if (params.symbol.isEmpty) {
      return const Left(ValidationFailure('交易对不能为空'));
    }

    if (params.timeFrame.isEmpty) {
      return const Left(ValidationFailure('时间框架不能为空'));
    }

    if (params.range.from >= params.range.to) {
      return const Left(ValidationFailure('可见范围参数无效'));
    }

    if (params.limit != null && params.limit! <= 0) {
      return const Left(ValidationFailure('数据限制必须大于0'));
    }

    try {
      AppLogger.logModule(
        'GetHistoricalChartData',
        LogLevel.info,
        '🔄 开始获取历史图表数据',
        metadata: {
          'symbol': params.symbol,
          'time_frame': params.timeFrame,
          'data_type': params.dataType.toString(),
          'range': '${params.range.from}-${params.range.to}',
          'limit': params.limit,
          'use_case': 'get_historical_chart_data',
        },
      );

      final result = await _repository.getHistoricalChartData(
        symbol: params.symbol,
        timeFrame: params.timeFrame,
        dataType: params.dataType,
        range: params.range,
        limit: params.limit,
      );

      return result.fold(
        (failure) {
          AppLogger.logModule(
            'GetHistoricalChartData',
            LogLevel.error,
            '❌ 获取历史图表数据失败',
            metadata: {
              'symbol': params.symbol,
              'time_frame': params.timeFrame,
              'range': '${params.range.from}-${params.range.to}',
              'failure': failure.toString(),
            },
          );
          return Left(failure);
        },
        (data) {
          AppLogger.logModule(
            'GetHistoricalChartData',
            LogLevel.info,
            '✅ 获取历史图表数据成功',
            metadata: {
              'symbol': params.symbol,
              'time_frame': params.timeFrame,
              'data_count': data.length,
              'data_type': params.dataType.toString(),
              'range': '${params.range.from}-${params.range.to}',
            },
          );
          return Right(data);
        },
      );
    } catch (e) {
      AppLogger.logModule(
        'GetHistoricalChartData',
        LogLevel.error,
        '❌ 获取历史图表数据异常',
        error: e,
        metadata: {
          'symbol': params.symbol,
          'time_frame': params.timeFrame,
          'range': '${params.range.from}-${params.range.to}',
        },
      );
      return Left(UnknownFailure('获取历史图表数据异常: ${e.toString()}'));
    }
  }
}

/// 获取历史图表数据参数
class GetHistoricalChartDataParams extends Equatable {
  final String symbol;
  final String timeFrame;
  final TradingViewDataType dataType;
  final VisibleRange range;
  final int? limit;

  const GetHistoricalChartDataParams({
    required this.symbol,
    required this.timeFrame,
    required this.dataType,
    required this.range,
    this.limit,
  });

  /// 创建默认参数
  factory GetHistoricalChartDataParams.defaultParams({
    required String symbol,
    required String timeFrame,
    required VisibleRange range,
    TradingViewDataType dataType = TradingViewDataType.candlestick,
  }) {
    return GetHistoricalChartDataParams(
      symbol: symbol,
      timeFrame: timeFrame,
      dataType: dataType,
      range: range,
      limit: range.to - range.from + 50, // 默认多获取50条数据作为缓冲
    );
  }

  /// 复制并修改参数
  GetHistoricalChartDataParams copyWith({
    String? symbol,
    String? timeFrame,
    TradingViewDataType? dataType,
    VisibleRange? range,
    int? limit,
  }) {
    return GetHistoricalChartDataParams(
      symbol: symbol ?? this.symbol,
      timeFrame: timeFrame ?? this.timeFrame,
      dataType: dataType ?? this.dataType,
      range: range ?? this.range,
      limit: limit ?? this.limit,
    );
  }

  /// 验证参数
  bool get isValid {
    return symbol.isNotEmpty &&
        timeFrame.isNotEmpty &&
        range.from < range.to &&
        (limit == null || limit! > 0);
  }

  /// 计算数据范围大小
  int get rangeSize => range.to - range.from;

  @override
  List<Object?> get props => [symbol, timeFrame, dataType, range, limit];

  @override
  String toString() {
    return 'GetHistoricalChartDataParams(symbol: $symbol, timeFrame: $timeFrame, dataType: $dataType, range: ${range.from}-${range.to}, limit: $limit)';
  }
}
