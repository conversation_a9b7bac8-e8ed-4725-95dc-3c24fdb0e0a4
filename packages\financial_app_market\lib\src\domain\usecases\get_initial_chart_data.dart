import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:financial_app_core/financial_app_core.dart';
import '../repositories/market_repository.dart';
import '../../data/models/trading_view_chart_data.dart';

/// 获取初始图表数据用例
class GetInitialChartData {
  final MarketRepository _repository;

  GetInitialChartData(this._repository);

  @override
  Future<Either<Failure, List<TradingViewChartDataModel>>> call(
    GetInitialChartDataParams params,
  ) async {
    // 参数验证
    if (params.symbol.isEmpty) {
      return const Left(ValidationFailure('交易对不能为空'));
    }

    if (params.timeFrame.isEmpty) {
      return const Left(ValidationFailure('时间框架不能为空'));
    }

    if (params.limit != null && params.limit! <= 0) {
      return const Left(ValidationFailure('数据限制必须大于0'));
    }

    try {
      AppLogger.logModule(
        'GetInitialChartData',
        LogLevel.info,
        '🚀 开始获取初始图表数据',
        metadata: {
          'symbol': params.symbol,
          'time_frame': params.timeFrame,
          'data_type': params.dataType.toString(),
          'limit': params.limit,
          'use_case': 'get_initial_chart_data',
        },
      );

      final result = await _repository.getInitialChartData(
        symbol: params.symbol,
        timeFrame: params.timeFrame,
        dataType: params.dataType,
        limit: params.limit,
      );

      return result.fold(
        (failure) {
          AppLogger.logModule(
            'GetInitialChartData',
            LogLevel.error,
            '❌ 获取初始图表数据失败',
            metadata: {
              'symbol': params.symbol,
              'time_frame': params.timeFrame,
              'failure': failure.toString(),
            },
          );
          return Left(failure);
        },
        (data) {
          AppLogger.logModule(
            'GetInitialChartData',
            LogLevel.info,
            '✅ 获取初始图表数据成功',
            metadata: {
              'symbol': params.symbol,
              'time_frame': params.timeFrame,
              'data_count': data.length,
              'data_type': params.dataType.toString(),
            },
          );
          return Right(data);
        },
      );
    } catch (e) {
      AppLogger.logModule(
        'GetInitialChartData',
        LogLevel.error,
        '❌ 获取初始图表数据异常',
        error: e,
        metadata: {'symbol': params.symbol, 'time_frame': params.timeFrame},
      );
      return Left(UnknownFailure('获取初始图表数据异常: ${e.toString()}'));
    }
  }
}

/// 获取初始图表数据参数
class GetInitialChartDataParams extends Equatable {
  final String symbol;
  final String timeFrame;
  final TradingViewDataType dataType;
  final int? limit;

  const GetInitialChartDataParams({
    required this.symbol,
    required this.timeFrame,
    required this.dataType,
    this.limit,
  });

  /// 创建默认参数
  factory GetInitialChartDataParams.defaultParams({
    required String symbol,
    required String timeFrame,
    TradingViewDataType dataType = TradingViewDataType.candlestick,
  }) {
    return GetInitialChartDataParams(
      symbol: symbol,
      timeFrame: timeFrame,
      dataType: dataType,
      limit: 100, // 默认获取100条数据
    );
  }

  /// 复制并修改参数
  GetInitialChartDataParams copyWith({
    String? symbol,
    String? timeFrame,
    TradingViewDataType? dataType,
    int? limit,
  }) {
    return GetInitialChartDataParams(
      symbol: symbol ?? this.symbol,
      timeFrame: timeFrame ?? this.timeFrame,
      dataType: dataType ?? this.dataType,
      limit: limit ?? this.limit,
    );
  }

  /// 验证参数
  bool get isValid {
    return symbol.isNotEmpty &&
        timeFrame.isNotEmpty &&
        (limit == null || limit! > 0);
  }

  @override
  List<Object?> get props => [symbol, timeFrame, dataType, limit];

  @override
  String toString() {
    return 'GetInitialChartDataParams(symbol: $symbol, timeFrame: $timeFrame, dataType: $dataType, limit: $limit)';
  }
}
