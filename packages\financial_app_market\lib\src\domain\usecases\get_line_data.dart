// 获取K线图数据
import 'package:financial_app_market/src/data/models/line_data_model.dart';
import 'package:financial_app_market/src/domain/repositories/market_repository.dart';

class GetLineData {
  final MarketRepository repository;
  GetLineData(this.repository);
  Future<List<LineDataModel>> call({
    required String symbol,
    required String bar,
    required int limit,
    required int befor,
    required int t,
  }) {
    return repository.candleStick(
      symbol: symbol,
      bar: bar,
      limit: limit,
      befor: befor,
      t: t,
    );
  }
}
