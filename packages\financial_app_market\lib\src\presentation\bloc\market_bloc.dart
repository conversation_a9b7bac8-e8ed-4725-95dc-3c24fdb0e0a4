import 'dart:async';
import 'package:financial_app_market/src/data/models/visible_range.dart';
import 'package:financial_app_market/src/domain/usecases/get_line_data.dart';
import 'package:financial_app_market/src/domain/usecases/get_initial_chart_data.dart';
import 'package:financial_app_market/src/domain/usecases/get_historical_chart_data.dart';
import 'package:financial_app_market/src/data/models/trading_view_chart_data.dart';
import 'package:financial_app_market/src/data/models/visible_range.dart'
    as market_range;
import 'package:financial_app_market/src/domain/repositories/market_repository.dart';
import 'package:financial_trading_chart/financial_trading_chart.dart'
    hide TradingViewDataType;
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:financial_app_core/financial_app_core.dart';

import '../../utils/market_logger.dart';
import 'market_event.dart';
import 'market_state.dart';

/// 市场数据 Bloc
///
/// 管理股票市场数据，包括股票列表、实时行情、K线数据等
/// 🔑 增强版：支持双接口图表数据获取
class MarketBloc extends BaseBloc<MarketEvent, MarketState> {
  // 🔑 双接口用例
  final GetInitialChartData? _getInitialChartData;
  final GetHistoricalChartData? _getHistoricalChartData;

  // 订阅管理
  final Map<String, StreamSubscription> _subscriptions = {};
  final Set<String> _subscribedSymbols = {};

  // 缓存管理
  final Map<String, Map<String, dynamic>> _stockCache = {};
  final Map<String, Map<String, dynamic>> _quoteCache = {};

  // 🔑 双接口图表数据缓存
  final Map<String, List<TradingViewChartDataModel>> _chartDataCache = {};

  // 当前状态
  String? _currentMarket;
  List<Map<String, dynamic>> _currentStocks = [];
  String _sortBy = 'symbol';
  bool _sortAscending = true;

  // 🔑 双接口图表状态管理
  final ChartAPI _chartAPI;
  bool _isInitialDataLoaded = false;
  TradingViewDataType _currentChartType = TradingViewDataType.candlestick;
  String _currentSymbol = '';
  String _currentTimeFrame = '';

  MarketBloc({
    ChartAPI? chartAPI,
    GetInitialChartData? getInitialChartData,
    GetHistoricalChartData? getHistoricalChartData,
  }) : _chartAPI = chartAPI ?? ChartAPI.instance,
       _getInitialChartData = getInitialChartData,
       _getHistoricalChartData = getHistoricalChartData,
       super(const MarketInitial()) {
    // 注册事件处理器
    on<LoadStocksEvent>(_onLoadStocks);
    on<SearchStocksEvent>(_onSearchStocks);
    on<GetStockDetailEvent>(_onGetStockDetail);
    on<GetQuoteEvent>(_onGetQuote);
    on<SubscribeQuoteEvent>(_onSubscribeQuote);
    on<UnsubscribeQuoteEvent>(_onUnsubscribeQuote);
    on<GetKlineDataEvent>(_onGetKlineData);
    on<GetKlineDataEvent2>(_onGetKlineData2);
    on<ChangeTimeIntervalEvent>(_onChangeTimeInterval);
    on<RefreshMarketDataEvent>(_onRefreshMarketData);
    on<SwitchMarketEvent>(_onSwitchMarket);
    on<AddToWatchlistEvent>(_onAddToWatchlist);
    on<RemoveFromWatchlistEvent>(_onRemoveFromWatchlist);
    on<GetWatchlistEvent>(_onGetWatchlist);
    on<UpdateSortingEvent>(_onUpdateSorting);
    on<SetPriceAlertEvent>(_onSetPriceAlert);
    on<ClearMarketErrorEvent>(_onClearMarketError);
    on<QuoteUpdatedEvent>(_onQuoteUpdated);

    // 🔑 新增：双接口图表事件处理器
    on<InitializeChartEvent>(_onInitializeChart);
    on<SwitchChartTypeEvent>(_onSwitchChartType);
    on<ChangeSymbolEvent>(_onChangeSymbol);
    on<RefreshChartEvent>(_onRefreshChart);

    // 🔑 设置双接口数据提供者
    // if (_getInitialChartData != null && _getHistoricalChartData != null) {
    //   _setupDualApiChartDataProvider();
    // }
  }

  /// 🔑 设置双接口数据提供者
  // void _setupDualApiChartDataProvider() {
  //   _chartAPI.setGlobalDataProvider(_handleDualApiChartDataRequest);

  //   AppLogger.logModule(
  //     'MarketBloc',
  //     LogLevel.info,
  //     '🌐 设置双接口数据提供者',
  //     metadata: {
  //       'bloc_instance': hashCode,
  //       'chart_api_instance': _chartAPI.hashCode,
  //       'has_initial_use_case': _getInitialChartData != null,
  //       'has_historical_use_case': _getHistoricalChartData != null,
  //     },
  //   );
  // }

  /// 🔑 核心方法：处理双接口图表数据请求
  Future<List<ChartData>> _handleDualApiChartDataRequest(
    String symbol,
    String timeFrame,
    VisibleRange range,
  ) async {
    MarketLoggerUtils.info(
      '📊 处理双接口图表数据请求',
      metadata: {
        'symbol': symbol,
        'time_frame': timeFrame,
        'range': '${range.from}-${range.to}',
        'is_initial_loaded': _isInitialDataLoaded,
        'current_chart_type': _currentChartType.toString(),
        'current_symbol': _currentSymbol,
        'current_time_frame': _currentTimeFrame,
      },
    );

    try {
      // 🔑 智能接口选择逻辑
      if (!_isInitialDataLoaded ||
          symbol != _currentSymbol ||
          timeFrame != _currentTimeFrame) {
        // 使用接口1获取初始数据
        final tradingViewData = await _getInitialDataWithInterface1(
          symbol,
          timeFrame,
        );
        return _convertToChartData(tradingViewData);
      } else {
        // 使用接口2获取历史数据
        final tradingViewData = await _getHistoricalDataWithInterface2(
          symbol,
          timeFrame,
          range,
        );
        return _convertToChartData(tradingViewData);
      }
    } catch (e) {
      MarketLoggerUtils.error(
        '❌ 双接口图表数据请求异常',
        error: e,
        metadata: {
          'symbol': symbol,
          'time_frame': timeFrame,
          'range': '${range.from}-${range.to}',
        },
      );
      rethrow;
    }
  }

  /// 🔑 使用接口1获取初始数据
  Future<List<TradingViewChartDataModel>> _getInitialDataWithInterface1(
    String symbol,
    String timeFrame,
  ) async {
    MarketLoggerUtils.info(
      '🚀 使用接口1获取初始数据',
      metadata: {
        'symbol': symbol,
        'time_frame': timeFrame,
        'chart_type': _currentChartType.toString(),
        'api_interface': 'get_line_data',
      },
    );

    final result = await _getInitialChartData!(
      GetInitialChartDataParams(
        symbol: symbol,
        timeFrame: timeFrame,
        dataType: _currentChartType,
        limit: 100,
      ),
    );

    return result.fold(
      (failure) {
        MarketLoggerUtils.error(
          '❌ 接口1获取初始数据失败',
          metadata: {
            'symbol': symbol,
            'time_frame': timeFrame,
            'failure': failure.toString(),
            'api_interface': 'get_line_data',
          },
        );
        throw Exception(failure.message);
      },
      (data) {
        // 🔑 更新状态标记
        _isInitialDataLoaded = true;
        _currentSymbol = symbol;
        _currentTimeFrame = timeFrame;

        // 🔑 缓存数据
        final cacheKey = _generateCacheKey(
          symbol,
          timeFrame,
          _currentChartType,
        );
        _chartDataCache[cacheKey] = data;

        MarketLoggerUtils.info(
          '✅ 接口1获取初始数据成功',
          metadata: {
            'symbol': symbol,
            'time_frame': timeFrame,
            'data_count': data.length,
            'chart_type': _currentChartType.toString(),
            'api_interface': 'get_line_data',
            'cache_key': cacheKey,
          },
        );

        return data;
      },
    );
  }

  /// 🔑 使用接口2获取历史数据
  Future<List<TradingViewChartDataModel>> _getHistoricalDataWithInterface2(
    String symbol,
    String timeFrame,
    VisibleRange range,
  ) async {
    MarketLoggerUtils.info(
      '🔄 使用接口2获取历史数据',
      metadata: {
        'symbol': symbol,
        'time_frame': timeFrame,
        'range': '${range.from}-${range.to}',
        'chart_type': _currentChartType.toString(),
        'api_interface': 'market_api_service',
      },
    );

    // 🔑 转换VisibleRange类型
    final marketRange = market_range.VisibleRange(
      from: range.from,
      to: range.to,
    );

    final result = await _getHistoricalChartData!(
      GetHistoricalChartDataParams(
        symbol: symbol,
        timeFrame: timeFrame,
        dataType: _currentChartType,
        range: marketRange,
        limit: range.to - range.from + 50,
      ),
    );

    return result.fold(
      (failure) {
        MarketLoggerUtils.error(
          '❌ 接口2获取历史数据失败',
          metadata: {
            'symbol': symbol,
            'time_frame': timeFrame,
            'range': '${range.from}-${range.to}',
            'failure': failure.toString(),
            'api_interface': 'market_api_service',
          },
        );
        throw Exception(failure.message);
      },
      (data) {
        MarketLoggerUtils.info(
          '✅ 接口2获取历史数据成功',
          metadata: {
            'symbol': symbol,
            'time_frame': timeFrame,
            'range': '${range.from}-${range.to}',
            'data_count': data.length,
            'chart_type': _currentChartType.toString(),
            'api_interface': 'market_api_service',
          },
        );

        return data;
      },
    );
  }

  /// 🔑 生成缓存键
  String _generateCacheKey(
    String symbol,
    String timeFrame,
    TradingViewDataType chartType,
  ) {
    return '${symbol}_${timeFrame}_${chartType.toString()}';
  }

  /// 🔑 清理图表缓存
  void _clearChartCache({String? symbol, String? timeFrame}) {
    if (symbol != null && timeFrame != null) {
      // 清理特定交易对和时间框架的缓存
      _chartDataCache.removeWhere(
        (key, value) => key.startsWith('${symbol}_${timeFrame}_'),
      );
    } else if (symbol != null) {
      // 清理特定交易对的所有缓存
      _chartDataCache.removeWhere((key, value) => key.startsWith('${symbol}_'));
    } else {
      // 清理所有缓存
      _chartDataCache.clear();
    }

    MarketLoggerUtils.info(
      '🗑️ 清理图表缓存',
      metadata: {
        'symbol': symbol,
        'time_frame': timeFrame,
        'remaining_cache_count': _chartDataCache.length,
      },
    );
  }

  /// 🔑 转换TradingViewChartDataModel为ChartData
  List<ChartData> _convertToChartData(
    List<TradingViewChartDataModel> tradingViewData,
  ) {
    return tradingViewData.map((data) {
      switch (data.type) {
        case TradingViewDataType.candlestick:
          return KLineData(
            timestamp: data.timestamp,
            time: data.time,
            open: data.open ?? 0.0,
            high: data.high ?? 0.0,
            low: data.low ?? 0.0,
            close: data.close ?? 0.0,
            volume: 0.0, // 可以从原始数据中获取
          );
        case TradingViewDataType.line:
          return SimpleChartData(
            timestamp: data.timestamp,
            value: data.value ?? 0.0,
          );
      }
    }).toList();
  }

  @override
  Future<void> close() {
    // 清理所有订阅
    for (final subscription in _subscriptions.values) {
      subscription.cancel();
    }
    _subscriptions.clear();
    _subscribedSymbols.clear();
    return super.close();
  }

  /// 加载股票列表
  Future<void> _onLoadStocks(
    LoadStocksEvent event,
    Emitter<MarketState> emit,
  ) async {
    MarketLoggerUtils.info(
      '📈 加载股票列表',
      metadata: {'market': event.market, 'page': event.page},
    );

    if (event.page == 1) {
      emit(const MarketLoading());
    }

    try {
      // 模拟 API 调用
      await Future.delayed(const Duration(milliseconds: 500));

      final stocks = _generateMockStocks(
        event.market,
        event.page,
        event.pageSize,
      );

      if (event.page == 1) {
        _currentStocks = stocks;
        _currentMarket = event.market;
      } else {
        _currentStocks.addAll(stocks);
      }

      // 缓存股票数据
      for (final stock in stocks) {
        _stockCache[stock['symbol']] = stock;
      }

      emit(
        StocksLoaded(
          stocks: List.from(_currentStocks),
          hasMore: stocks.length == event.pageSize,
          currentPage: event.page,
          currentMarket: event.market,
        ),
      );
    } catch (e) {
      MarketLoggerUtils.error('❌ 加载股票列表失败', error: e);
      emit(
        MarketError(
          message: '加载股票列表失败: $e',
          errorType: MarketErrorType.network,
        ),
      );
    }
  }

  /// 搜索股票
  Future<void> _onSearchStocks(
    SearchStocksEvent event,
    Emitter<MarketState> emit,
  ) async {
    MarketLoggerUtils.info('🔍 搜索股票', metadata: {'keyword': event.keyword});

    if (event.keyword.trim().isEmpty) {
      emit(const StockSearchResults(results: [], keyword: ''));
      return;
    }

    emit(const MarketLoading());

    try {
      // 模拟搜索 API 调用
      await Future.delayed(const Duration(milliseconds: 300));

      final results = _searchMockStocks(event.keyword);

      emit(StockSearchResults(results: results, keyword: event.keyword));
    } catch (e) {
      MarketLoggerUtils.error('❌ 搜索股票失败', error: e);
      emit(
        MarketError(message: '搜索失败: $e', errorType: MarketErrorType.network),
      );
    }
  }

  /// 获取股票详情
  Future<void> _onGetStockDetail(
    GetStockDetailEvent event,
    Emitter<MarketState> emit,
  ) async {
    MarketLoggerUtils.info('📊 获取股票详情', metadata: {'symbol': event.symbol});

    emit(const MarketLoading());

    try {
      // 先检查缓存
      if (_stockCache.containsKey(event.symbol)) {
        final cachedStock = _stockCache[event.symbol]!;
        emit(StockDetailLoaded(stockDetail: cachedStock));
        return;
      }

      // 模拟 API 调用
      await Future.delayed(const Duration(milliseconds: 400));

      final stockDetail = _generateMockStockDetail(event.symbol);
      _stockCache[event.symbol] = stockDetail;

      emit(StockDetailLoaded(stockDetail: stockDetail));
    } catch (e) {
      MarketLoggerUtils.error('❌ 获取股票详情失败', error: e);
      emit(
        MarketError(
          message: '获取股票详情失败: $e',
          errorType: MarketErrorType.symbolNotFound,
        ),
      );
    }
  }

  /// 获取实时行情
  Future<void> _onGetQuote(
    GetQuoteEvent event,
    Emitter<MarketState> emit,
  ) async {
    MarketLoggerUtils.info('💹 获取实时行情', metadata: {'symbol': event.symbol});

    try {
      // 模拟 API 调用
      await Future.delayed(const Duration(milliseconds: 200));

      final quote = _generateMockQuote(event.symbol);
      _quoteCache[event.symbol] = quote;

      emit(
        QuoteLoaded(
          symbol: event.symbol,
          quote: quote,
          timestamp: DateTime.now(),
        ),
      );
    } catch (e) {
      MarketLoggerUtils.error('❌ 获取实时行情失败', error: e);
      emit(
        MarketError(
          message: '获取实时行情失败: $e',
          errorType: MarketErrorType.network,
        ),
      );
    }
  }

  /// 订阅实时行情
  Future<void> _onSubscribeQuote(
    SubscribeQuoteEvent event,
    Emitter<MarketState> emit,
  ) async {
    MarketLoggerUtils.info('🔔 订阅实时行情', metadata: {'symbol': event.symbol});

    if (_subscribedSymbols.contains(event.symbol)) {
      MarketLoggerUtils.warning(
        '⚠️ 已经订阅了交易对',
        metadata: {'symbol': event.symbol},
      );
      return;
    }

    try {
      // 模拟 WebSocket 订阅
      final subscription = _createMockQuoteStream(event.symbol).listen(
        (quoteData) {
          if (!isClosed) {
            add(QuoteUpdatedEvent(symbol: event.symbol, quoteData: quoteData));
          }
        },
        onError: (error) {
          MarketLoggerUtils.error(
            '❌ 实时行情订阅错误',
            error: error,
            metadata: {'symbol': event.symbol},
          );
          if (!isClosed) {
            emit(
              MarketError(
                message: '实时行情订阅失败: $error',
                errorType: MarketErrorType.subscription,
              ),
            );
          }
        },
      );

      _subscriptions[event.symbol] = subscription;
      _subscribedSymbols.add(event.symbol);

      emit(SubscriptionUpdated(symbol: event.symbol, isSubscribed: true));
    } catch (e) {
      MarketLoggerUtils.error(
        '❌ 订阅实时行情失败',
        error: e,
        metadata: {'symbol': event.symbol},
      );
      emit(
        MarketError(
          message: '订阅失败: $e',
          errorType: MarketErrorType.subscription,
        ),
      );
    }
  }

  /// 取消订阅实时行情
  Future<void> _onUnsubscribeQuote(
    UnsubscribeQuoteEvent event,
    Emitter<MarketState> emit,
  ) async {
    MarketLoggerUtils.info('🔕 取消订阅实时行情', metadata: {'symbol': event.symbol});

    final subscription = _subscriptions[event.symbol];
    if (subscription != null) {
      await subscription.cancel();
      _subscriptions.remove(event.symbol);
      _subscribedSymbols.remove(event.symbol);

      emit(SubscriptionUpdated(symbol: event.symbol, isSubscribed: false));
    }
  }

  /// 🔑 获取K线历史数据 (接口2)
  Future<void> _onGetKlineData(
    GetKlineDataEvent event,
    Emitter<MarketState> emit,
  ) async {
    MarketLoggerUtils.info(
      '📈 获取K线历史数据 (接口2)',
      metadata: {
        'symbol': event.symbol,
        'interval': event.interval,
        'start_time': event.startTime.toIso8601String(),
        'end_time': event.endTime.toIso8601String(),
        'api_endpoint': '/quote-api/kline/v1/history-candles',
      },
    );

    emit(const MarketLoading());

    try {
      // 🔑 调用真实的历史数据接口 (接口2)
      final marketRepository = locator<MarketRepository>();

      // 创建VisibleRange对象
      final range = VisibleRange(
        from: event.startTime.millisecondsSinceEpoch,
        to: event.endTime.millisecondsSinceEpoch,
      );

      // 调用历史数据接口
      final result = await marketRepository.getHistoricalChartData(
        symbol: event.symbol,
        timeFrame: event.interval,
        dataType: TradingViewDataType.candlestick,
        range: range,
        limit: 100,
      );

      // 🔑 处理Either结果并转换TradingViewChartDataModel为Map格式
      final klineData = result.fold(
        (failure) {
          throw Exception('获取历史数据失败: ${failure.toString()}');
        },
        (chartDataList) {
          return chartDataList.map((item) {
            return {
              'time': item.time,
              'open': item.open ?? 0.0,
              'high': item.high ?? 0.0,
              'low': item.low ?? 0.0,
              'close': item.close ?? 0.0,
              'timestamp': item.timestamp.millisecondsSinceEpoch,
              'volume': 0.0, // TradingViewChartDataModel没有volume字段
            };
          }).toList();
        },
      );

      MarketLoggerUtils.info(
        '✅ K线历史数据获取成功',
        metadata: {
          'symbol': event.symbol,
          'interval': event.interval,
          'data_count': klineData.length,
          'api_endpoint': '/quote-api/kline/v1/history-candles',
        },
      );

      emit(
        KlineDataLoaded(
          symbol: event.symbol,
          interval: event.interval,
          klineData: klineData,
          startTime: event.startTime,
          endTime: event.endTime,
        ),
      );
    } catch (e) {
      MarketLoggerUtils.error(
        '❌ 获取K线历史数据失败',
        error: e,
        metadata: {
          'symbol': event.symbol,
          'interval': event.interval,
          'api_endpoint': '/quote-api/kline/v1/history-candles',
        },
      );
      emit(
        MarketError(
          message: '获取K线历史数据失败: $e',
          errorType: MarketErrorType.network,
        ),
      );
    }
  }

  Future<void> _onGetKlineData2(
    GetKlineDataEvent2 event,
    Emitter<MarketState> emit,
  ) async {
    MarketLoggerUtils.info(
      '📈 获取K线初始化数据 (接口1)',
      metadata: {
        'symbol': event.symbol,
        'bar': event.bar,
        'api_endpoint': '/quote-api/kline/v1/candlestick',
      },
    );

    emit(const MarketLoading());

    try {
      // 🔑 调用真实的初始数据接口 (接口1)
      final getLineDataUseCase = locator<GetLineData>();
      final result = await getLineDataUseCase(
        symbol: event.symbol,
        bar: event.bar,
        limit: 100,
        befor: DateTime.now().millisecondsSinceEpoch,
        t: DateTime.now().millisecondsSinceEpoch,
      );

      // 🔑 转换数据格式为KLineData
      var list = result.map((e) {
        return KLineData(
          time: _formatTimeForInterval(e.time, event.bar),
          open: e.open,
          high: e.hight, // 注意：这里保持原有的拼写
          low: e.low,
          close: e.close,
          timestamp: DateTime.fromMillisecondsSinceEpoch(e.time),
          volume: 0.0, // LineDataModel没有volume字段，设为默认值
        );
      });

      MarketLoggerUtils.info(
        '✅ K线初始化数据获取成功',
        metadata: {
          'symbol': event.symbol,
          'bar': event.bar,
          'data_count': list.length,
          'api_endpoint': '/quote-api/kline/v1/candlestick',
        },
      );

      emit(
        KlineDataLoaded2(
          symbol: event.symbol,
          bar: event.bar,
          klineData: list.toList(),
          limit: 100,
          befor: DateTime.now().millisecondsSinceEpoch,
          t: DateTime.now().millisecondsSinceEpoch,
        ),
      );
    } catch (e) {
      MarketLoggerUtils.error(
        '❌ 获取K线初始化数据失败',
        error: e,
        metadata: {
          'symbol': event.symbol,
          'bar': event.bar,
          'api_endpoint': '/quote-api/kline/v1/candlestick',
        },
      );
      emit(
        MarketError(
          message: '获取K线初始化数据失败: $e',
          errorType: MarketErrorType.network,
        ),
      );
    }
  }

  /// 切换时间段
  Future<void> _onChangeTimeInterval(
    ChangeTimeIntervalEvent event,
    Emitter<MarketState> emit,
  ) async {
    MarketLoggerUtils.info(
      '⏰ 切换时间段',
      metadata: {
        'symbol': event.symbol,
        'interval': event.interval,
        'time_frame': event.timeFrame,
      },
    );

    emit(const MarketLoading());

    try {
      // 模拟 API 调用获取新时间段的数据
      final getLineDataUseCase = locator<GetLineData>();
      final result = await getLineDataUseCase(
        symbol: event.symbol,
        bar: event.interval,
        limit: 100,
        befor: 1750059224000,
        t: DateTime.now().millisecondsSinceEpoch,
      );

      // 转换数据格式
      MarketLoggerUtils.info(
        '📊 开始转换数据格式',
        metadata: {'data_count': result.length, 'time_frame': event.timeFrame},
      );

      var klineData = result.map((e) {
        final formattedTime = _formatTimeForInterval(e.time, event.timeFrame);
        final klineItem = KLineData(
          time: formattedTime,
          open: e.open,
          high: e.hight, // 保持原有的拼写（数据模型问题）
          low: e.low,
          close: e.close,
          timestamp: DateTime.fromMillisecondsSinceEpoch(e.time),
        );

        // 记录第一个数据项的详细信息
        if (result.indexOf(e) == 0) {
          MarketLoggerUtils.debug(
            '📊 第一个数据项详细信息',
            metadata: {
              'original_timestamp': e.time,
              'formatted_time': formattedTime,
              'ohlc': '${e.open}/${e.hight}/${e.low}/${e.close}',
              'open_valid': !e.open.isNaN && e.open.isFinite,
              'high_valid': !e.hight.isNaN && e.hight.isFinite,
              'low_valid': !e.low.isNaN && e.low.isFinite,
              'close_valid': !e.close.isNaN && e.close.isFinite,
              'timestamp_valid': e.time > 0,
              'formatted_time_valid': formattedTime.isNotEmpty,
            },
          );
        }

        return klineItem;
      }).toList();

      MarketLoggerUtils.info(
        '📊 数据转换完成',
        metadata: {'converted_count': klineData.length},
      );

      emit(
        TimeIntervalChanged(
          symbol: event.symbol,
          interval: event.interval,
          timeFrame: event.timeFrame,
          klineData: klineData,
          timestamp: DateTime.now(),
        ),
      );
    } catch (e) {
      MarketLoggerUtils.error('❌ 切换时间段失败', error: e);
      emit(
        MarketError(message: '切换时间段失败: $e', errorType: MarketErrorType.network),
      );
    }
  }

  /// 根据时间段格式化时间（为TradingView优化）
  String _formatTimeForInterval(int timestamp, String timeFrame) {
    final dateTime = DateTime.fromMillisecondsSinceEpoch(timestamp);

    switch (timeFrame) {
      case '1s':
      case '1m':
      case '5m':
      case '15m':
      case '30m':
      case '1h':
      case '4h':
        // 🔧 对于分钟/小时级别，TradingView需要时间戳（秒）
        return (timestamp ~/ 1000).toString();
      case '1d':
      case '1w':
        // 🔧 对于日/周级别，TradingView需要日期字符串 YYYY-MM-DD
        return DateUtils.formatDate(dateTime, format: 'yyyy-MM-dd');
      case '1M':
        // 🔧 对于月级别，TradingView需要日期字符串 YYYY-MM-DD（使用月初）
        return DateUtils.formatDate(dateTime, format: 'yyyy-MM-dd');
      default:
        // 🔧 默认使用时间戳（秒）
        return (timestamp ~/ 1000).toString();
    }
  }

  /// 刷新市场数据
  Future<void> _onRefreshMarketData(
    RefreshMarketDataEvent event,
    Emitter<MarketState> emit,
  ) async {
    MarketLoggerUtils.info('🔄 刷新市场数据');

    try {
      // 刷新当前股票列表
      if (_currentMarket != null) {
        add(LoadStocksEvent(market: _currentMarket, page: 1));
      }

      // 刷新已订阅的行情
      for (final symbol in _subscribedSymbols) {
        add(GetQuoteEvent(symbol: symbol));
      }

      emit(
        MarketDataRefreshed(
          refreshTime: DateTime.now(),
          updatedCount: _subscribedSymbols.length,
        ),
      );
    } catch (e) {
      MarketLoggerUtils.error('❌ 刷新市场数据失败', error: e);
      emit(
        MarketError(message: '刷新失败: $e', errorType: MarketErrorType.general),
      );
    }
  }

  /// 切换市场
  Future<void> _onSwitchMarket(
    SwitchMarketEvent event,
    Emitter<MarketState> emit,
  ) async {
    MarketLoggerUtils.info('🔄 切换市场', metadata: {'market': event.market});

    _currentMarket = event.market;
    add(LoadStocksEvent(market: event.market, page: 1));
  }

  /// 添加到自选股
  Future<void> _onAddToWatchlist(
    AddToWatchlistEvent event,
    Emitter<MarketState> emit,
  ) async {
    MarketLoggerUtils.info('⭐ 添加到自选股', metadata: {'symbol': event.symbol});

    try {
      // 模拟 API 调用
      await Future.delayed(const Duration(milliseconds: 200));

      emit(
        WatchlistOperationSuccess(
          symbol: event.symbol,
          operation: WatchlistOperation.add,
          message: '已添加到自选股',
        ),
      );
    } catch (e) {
      MarketLoggerUtils.error('❌ 添加到自选股失败', error: e);
      emit(
        MarketError(
          message: '添加到自选股失败: $e',
          errorType: MarketErrorType.general,
        ),
      );
    }
  }

  /// 从自选股移除
  Future<void> _onRemoveFromWatchlist(
    RemoveFromWatchlistEvent event,
    Emitter<MarketState> emit,
  ) async {
    MarketLoggerUtils.info('❌ 从自选股移除', metadata: {'symbol': event.symbol});

    try {
      // 模拟 API 调用
      await Future.delayed(const Duration(milliseconds: 200));

      emit(
        WatchlistOperationSuccess(
          symbol: event.symbol,
          operation: WatchlistOperation.remove,
          message: '已从自选股移除',
        ),
      );
    } catch (e) {
      MarketLoggerUtils.error('❌ 从自选股移除失败', error: e);
      emit(
        MarketError(
          message: '从自选股移除失败: $e',
          errorType: MarketErrorType.general,
        ),
      );
    }
  }

  /// 获取自选股列表
  Future<void> _onGetWatchlist(
    GetWatchlistEvent event,
    Emitter<MarketState> emit,
  ) async {
    MarketLoggerUtils.info('📋 获取自选股列表');

    emit(const MarketLoading());

    try {
      // 模拟 API 调用
      await Future.delayed(const Duration(milliseconds: 300));

      final watchlist = _generateMockWatchlist();

      emit(WatchlistLoaded(watchlist: watchlist));
    } catch (e) {
      MarketLoggerUtils.error('❌ 获取自选股列表失败', error: e);
      emit(
        MarketError(
          message: '获取自选股列表失败: $e',
          errorType: MarketErrorType.network,
        ),
      );
    }
  }

  /// 更新排序方式
  Future<void> _onUpdateSorting(
    UpdateSortingEvent event,
    Emitter<MarketState> emit,
  ) async {
    MarketLoggerUtils.info(
      '🔄 更新排序',
      metadata: {'sort_by': event.sortBy, 'ascending': event.ascending},
    );

    _sortBy = event.sortBy;
    _sortAscending = event.ascending;

    final sortedStocks = _sortStocks(
      _currentStocks,
      event.sortBy,
      event.ascending,
    );

    emit(
      SortingUpdated(
        sortBy: event.sortBy,
        ascending: event.ascending,
        sortedStocks: sortedStocks,
      ),
    );
  }

  /// 设置价格提醒
  Future<void> _onSetPriceAlert(
    SetPriceAlertEvent event,
    Emitter<MarketState> emit,
  ) async {
    MarketLoggerUtils.info(
      '🔔 设置价格提醒',
      metadata: {
        'symbol': event.symbol,
        'target_price': event.targetPrice,
        'alert_type': event.alertType,
      },
    );

    try {
      // 模拟 API 调用
      await Future.delayed(const Duration(milliseconds: 300));

      emit(
        PriceAlertSet(
          symbol: event.symbol,
          targetPrice: event.targetPrice,
          alertType: event.alertType,
          message: '价格提醒设置成功',
        ),
      );
    } catch (e) {
      MarketLoggerUtils.error('❌ 设置价格提醒失败', error: e);
      emit(
        MarketError(
          message: '设置价格提醒失败: $e',
          errorType: MarketErrorType.general,
        ),
      );
    }
  }

  /// 清除错误
  Future<void> _onClearMarketError(
    ClearMarketErrorEvent event,
    Emitter<MarketState> emit,
  ) async {
    if (state is MarketError) {
      emit(const MarketInitial());
    }
  }

  /// 实时行情更新
  Future<void> _onQuoteUpdated(
    QuoteUpdatedEvent event,
    Emitter<MarketState> emit,
  ) async {
    final previousQuote = _quoteCache[event.symbol];
    _quoteCache[event.symbol] = event.quoteData;

    emit(
      QuoteUpdated(
        symbol: event.symbol,
        quote: event.quoteData,
        timestamp: DateTime.now(),
        previousQuote: previousQuote,
      ),
    );
  }

  // 模拟数据生成方法
  List<Map<String, dynamic>> _generateMockStocks(
    String? market,
    int page,
    int pageSize,
  ) {
    // 实现模拟股票数据生成
    return List.generate(
      pageSize,
      (index) => {
        'symbol':
            '${market ?? 'SH'}${((page - 1) * pageSize + index + 1).toString().padLeft(6, '0')}',
        'name': '测试股票${(page - 1) * pageSize + index + 1}',
        'price': 10.0 + (index * 0.5),
        'change': (index % 3 - 1) * 0.1,
        'changePercent': (index % 3 - 1) * 1.0,
        'volume': 1000000 + index * 10000,
        'market': market ?? 'SH',
      },
    );
  }

  List<Map<String, dynamic>> _searchMockStocks(String keyword) {
    // 实现模拟搜索逻辑
    return [
      {
        'symbol': 'SH000001',
        'name': '上证指数',
        'price': 3000.0,
        'change': 10.0,
        'changePercent': 0.33,
      },
    ];
  }

  Map<String, dynamic> _generateMockStockDetail(String symbol) {
    // 实现模拟股票详情生成
    return {
      'symbol': symbol,
      'name': '测试股票',
      'price': 10.0,
      'change': 0.1,
      'changePercent': 1.0,
      'volume': 1000000,
      'marketCap': 10000000000,
      'pe': 15.0,
      'pb': 1.5,
    };
  }

  Map<String, dynamic> _generateMockQuote(String symbol) {
    // 实现模拟行情数据生成
    return {
      'symbol': symbol,
      'price': 10.0 + (DateTime.now().millisecond % 100) * 0.01,
      'volume': 1000000,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    };
  }

  Stream<Map<String, dynamic>> _createMockQuoteStream(String symbol) {
    // 实现模拟实时行情流
    return Stream.periodic(const Duration(seconds: 1), (count) {
      return _generateMockQuote(symbol);
    });
  }

  List<Map<String, dynamic>> _generateMockKlineData(
    String symbol,
    String interval,
    DateTime startTime,
    DateTime endTime,
  ) {
    // 实现模拟K线数据生成
    return [];
  }

  List<Map<String, dynamic>> _generateMockWatchlist() {
    // 实现模拟自选股列表生成
    return [];
  }

  List<Map<String, dynamic>> _sortStocks(
    List<Map<String, dynamic>> stocks,
    String sortBy,
    bool ascending,
  ) {
    // 实现股票排序逻辑
    final sortedStocks = List<Map<String, dynamic>>.from(stocks);
    sortedStocks.sort((a, b) {
      final aValue = a[sortBy];
      final bValue = b[sortBy];

      if (aValue == null || bValue == null) return 0;

      int comparison;
      if (aValue is String && bValue is String) {
        comparison = aValue.compareTo(bValue);
      } else if (aValue is num && bValue is num) {
        comparison = aValue.compareTo(bValue);
      } else {
        comparison = aValue.toString().compareTo(bValue.toString());
      }

      return ascending ? comparison : -comparison;
    });

    return sortedStocks;
  }

  // 🔑 双接口图表事件处理器

  /// 初始化图表
  Future<void> _onInitializeChart(
    InitializeChartEvent event,
    Emitter<MarketState> emit,
  ) async {
    MarketLoggerUtils.info(
      '🚀 初始化图表',
      metadata: {
        'symbol': event.symbol,
        'time_frame': event.timeFrame,
        'chart_type': event.chartType.toString(),
      },
    );

    try {
      // 重置状态
      _isInitialDataLoaded = false;
      _currentSymbol = '';
      _currentTimeFrame = '';
      _currentChartType = event.chartType;

      // 清理缓存
      _clearChartCache();

      // 设置新的参数
      _currentSymbol = event.symbol;
      _currentTimeFrame = event.timeFrame;

      emit(MarketLoading());

      MarketLoggerUtils.info(
        '✅ 图表初始化完成',
        metadata: {
          'symbol': event.symbol,
          'time_frame': event.timeFrame,
          'chart_type': event.chartType.toString(),
        },
      );

      emit(
        ChartInitialized(
          symbol: event.symbol,
          timeFrame: event.timeFrame,
          chartType: event.chartType,
        ),
      );
    } catch (e) {
      MarketLoggerUtils.error(
        '❌ 图表初始化失败',
        error: e,
        metadata: {'symbol': event.symbol, 'time_frame': event.timeFrame},
      );

      emit(
        MarketError(
          message: '图表初始化失败: ${e.toString()}',
          errorType: MarketErrorType.unknown,
        ),
      );
    }
  }

  /// 切换图表类型
  Future<void> _onSwitchChartType(
    SwitchChartTypeEvent event,
    Emitter<MarketState> emit,
  ) async {
    MarketLoggerUtils.info(
      '🔄 切换图表类型',
      metadata: {
        'old_type': _currentChartType.toString(),
        'new_type': event.chartType.toString(),
      },
    );

    try {
      _currentChartType = event.chartType;

      // 清理缓存以强制重新加载数据
      _clearChartCache(symbol: _currentSymbol, timeFrame: _currentTimeFrame);

      emit(ChartTypeChanged(chartType: event.chartType));

      MarketLoggerUtils.info(
        '✅ 图表类型切换完成',
        metadata: {'new_type': event.chartType.toString()},
      );
    } catch (e) {
      MarketLoggerUtils.error('❌ 切换图表类型失败', error: e);

      emit(
        MarketError(
          message: '切换图表类型失败: ${e.toString()}',
          errorType: MarketErrorType.unknown,
        ),
      );
    }
  }

  /// 切换交易对
  Future<void> _onChangeSymbol(
    ChangeSymbolEvent event,
    Emitter<MarketState> emit,
  ) async {
    MarketLoggerUtils.info(
      '🔄 切换交易对',
      metadata: {'old_symbol': _currentSymbol, 'new_symbol': event.symbol},
    );

    try {
      // 重置初始数据加载状态
      _isInitialDataLoaded = false;
      _currentSymbol = event.symbol;

      // 清理旧交易对的缓存
      _clearChartCache();

      emit(SymbolChanged(symbol: event.symbol));

      MarketLoggerUtils.info(
        '✅ 交易对切换完成',
        metadata: {'new_symbol': event.symbol},
      );
    } catch (e) {
      MarketLoggerUtils.error('❌ 切换交易对失败', error: e);

      emit(
        MarketError(
          message: '切换交易对失败: ${e.toString()}',
          errorType: MarketErrorType.unknown,
        ),
      );
    }
  }

  /// 刷新图表
  Future<void> _onRefreshChart(
    RefreshChartEvent event,
    Emitter<MarketState> emit,
  ) async {
    MarketLoggerUtils.info(
      '🔄 刷新图表',
      metadata: {
        'clear_cache': event.clearCache,
        'current_symbol': _currentSymbol,
        'current_time_frame': _currentTimeFrame,
      },
    );

    try {
      if (event.clearCache) {
        _clearChartCache();
        _isInitialDataLoaded = false;
      }

      emit(
        ChartRefreshed(
          symbol: _currentSymbol,
          timeFrame: _currentTimeFrame,
          clearCache: event.clearCache,
        ),
      );

      MarketLoggerUtils.info(
        '✅ 图表刷新完成',
        metadata: {'clear_cache': event.clearCache, 'symbol': _currentSymbol},
      );
    } catch (e) {
      MarketLoggerUtils.error('❌ 刷新图表失败', error: e);

      emit(
        MarketError(
          message: '刷新图表失败: ${e.toString()}',
          errorType: MarketErrorType.unknown,
        ),
      );
    }
  }
}
