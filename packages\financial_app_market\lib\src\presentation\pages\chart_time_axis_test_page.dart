import 'package:flutter/material.dart' hide DateUtils;
import 'package:financial_trading_chart/financial_trading_chart.dart';
import 'package:financial_app_core/financial_app_core.dart';
import '../widgets/time_interval_selector.dart';

/// 图表时间轴测试页面
class ChartTimeAxisTestPage extends StatefulWidget {
  const ChartTimeAxisTestPage({super.key});

  @override
  State<ChartTimeAxisTestPage> createState() => _ChartTimeAxisTestPageState();
}

class _ChartTimeAxisTestPageState extends State<ChartTimeAxisTestPage> {
  final GlobalKey<TradingChartWidgetState> _chartKey = GlobalKey();
  String _currentInterval = '15m';
  String _currentTimeFrame = '15m';
  List<KLineData> _currentData = [];

  @override
  void initState() {
    super.initState();
    _generateTestData();
  }

  void _onTimeIntervalChanged(String interval, String timeFrame) {
    setState(() {
      _currentInterval = interval;
      _currentTimeFrame = timeFrame;
    });

    // 重新生成数据并应用到图表
    _generateTestData();
  }

  void _generateTestData() {
    final now = DateTime.now();
    final testData = <KLineData>[];

    // 根据当前时间段生成不同间隔的数据
    Duration interval;
    int dataPoints;

    switch (_currentInterval) {
      case '1s':
        interval = const Duration(seconds: 1);
        dataPoints = 60; // 1分钟的数据
        break;
      case '1m':
        interval = const Duration(minutes: 1);
        dataPoints = 60; // 1小时的数据
        break;
      case '5m':
        interval = const Duration(minutes: 5);
        dataPoints = 48; // 4小时的数据
        break;
      case '15m':
        interval = const Duration(minutes: 15);
        dataPoints = 32; // 8小时的数据
        break;
      case '30m':
        interval = const Duration(minutes: 30);
        dataPoints = 24; // 12小时的数据
        break;
      case '1h':
        interval = const Duration(hours: 1);
        dataPoints = 24; // 1天的数据
        break;
      case '4h':
        interval = const Duration(hours: 4);
        dataPoints = 18; // 3天的数据
        break;
      case '1d':
        interval = const Duration(days: 1);
        dataPoints = 30; // 1个月的数据
        break;
      default:
        interval = const Duration(minutes: 15);
        dataPoints = 32;
    }

    // 生成测试数据
    for (int i = dataPoints - 1; i >= 0; i--) {
      final time = now.subtract(interval * i);
      final basePrice = 50000.0;
      final random = (time.millisecondsSinceEpoch % 10000) / 10000.0;

      final open = basePrice + (random * 1000) - 500;
      final close = open + ((random - 0.5) * 200);
      final high =
          [open, close].reduce((a, b) => a > b ? a : b) + (random * 100);
      final low =
          [open, close].reduce((a, b) => a < b ? a : b) - (random * 100);

      testData.add(
        KLineData(
          timestamp: time,
          time: _formatTimeForDisplay(time),
          open: open,
          high: high,
          low: low,
          close: close,
          volume: 1000000 + (random * 500000),
        ),
      );
    }

    setState(() {
      _currentData = testData;
    });

    // 设置到图表，传递timeFrame参数
    _chartKey.currentState?.setData(testData, timeFrame: _currentTimeFrame);

    debugPrint('📊 生成测试数据: ${testData.length} 个数据点');
    debugPrint('📅 时间范围: ${testData.first.time} 到 ${testData.last.time}');
    debugPrint('⏰ 时间格式: $_currentTimeFrame');
  }

  String _formatTimeForDisplay(DateTime dateTime) {
    switch (_currentTimeFrame) {
      case '1s':
        return DateUtils.formatDateTime(dateTime, format: 'HH:mm:ss');
      case '1m':
      case '5m':
      case '15m':
      case '30m':
        return DateUtils.formatDateTime(dateTime, format: 'HH:mm');
      case '1h':
      case '4h':
        return DateUtils.formatDateTime(dateTime, format: 'MM-dd HH:mm');
      case '1d':
        return DateUtils.formatDate(dateTime, format: 'MM-dd');
      case '1w':
        return DateUtils.formatDate(dateTime, format: 'MM-dd');
      case '1M':
        return DateUtils.formatDate(dateTime, format: 'yyyy-MM');
      default:
        return DateUtils.formatDate(dateTime);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('图表时间轴测试'),
        backgroundColor: Colors.blue,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _generateTestData,
            tooltip: '重新生成数据',
          ),
        ],
      ),
      body: Column(
        children: [
          // 信息面板
          Container(
            padding: const EdgeInsets.all(16),
            color: Colors.grey[100],
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    const Icon(Icons.info_outline, color: Colors.blue),
                    const SizedBox(width: 8),
                    const Text(
                      '当前设置',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text('时间间隔: $_currentInterval'),
                          Text('时间格式: $_currentTimeFrame'),
                          Text('数据点数: ${_currentData.length}'),
                        ],
                      ),
                    ),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '显示名称: ${TimeIntervalUtils.getIntervalDisplayName(_currentInterval)}',
                          ),
                          if (_currentData.isNotEmpty) ...[
                            Text('开始时间: ${_currentData.first.time}'),
                            Text('结束时间: ${_currentData.last.time}'),
                          ],
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // 时间段选择器
          Container(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  '选择时间段',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),
                TimeIntervalSelector(
                  selectedInterval: _currentInterval,
                  onIntervalChanged: _onTimeIntervalChanged,
                ),
              ],
            ),
          ),

          // 图表区域
          Expanded(
            child: Container(
              margin: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey),
                borderRadius: BorderRadius.circular(8),
              ),
              child: TradingChartWidget(key: _chartKey),
            ),
          ),

          // 控制面板
          Container(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                const Text(
                  '快速测试',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),
                Wrap(
                  spacing: 8,
                  runSpacing: 8,
                  children: [
                    _buildTestButton('1秒', '1s'),
                    _buildTestButton('1分', '1m'),
                    _buildTestButton('15分', '15m'),
                    _buildTestButton('1时', '1h'),
                    _buildTestButton('4时', '4h'),
                    _buildTestButton('1日', '1d'),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: _generateTestData,
                        icon: const Icon(Icons.refresh),
                        label: const Text('重新生成数据'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.green,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: () {
                          _showDataPreview();
                        },
                        icon: const Icon(Icons.preview),
                        label: const Text('预览数据'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.orange,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTestButton(String label, String interval) {
    final isSelected = _currentInterval == interval;
    return ElevatedButton(
      onPressed: () => _onTimeIntervalChanged(interval, interval),
      style: ElevatedButton.styleFrom(
        backgroundColor: isSelected ? Colors.blue : Colors.grey[300],
        foregroundColor: isSelected ? Colors.white : Colors.black,
      ),
      child: Text(label),
    );
  }

  void _showDataPreview() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('数据预览'),
        content: Container(
          width: double.maxFinite,
          height: 300,
          child: ListView.builder(
            itemCount: _currentData.length.clamp(0, 10),
            itemBuilder: (context, index) {
              final data = _currentData[index];
              return ListTile(
                dense: true,
                title: Text('时间: ${data.time}'),
                subtitle: Text(
                  'O: ${data.open.toStringAsFixed(2)} '
                  'H: ${data.high.toStringAsFixed(2)} '
                  'L: ${data.low.toStringAsFixed(2)} '
                  'C: ${data.close.toStringAsFixed(2)}',
                  style: const TextStyle(fontSize: 12),
                ),
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }
}
