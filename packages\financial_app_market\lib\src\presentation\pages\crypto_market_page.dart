import 'package:flutter/material.dart';

class CryptoMarketPage extends StatelessWidget {
  const CryptoMarketPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('加密货币市场'), centerTitle: true),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 搜索栏
              TextField(
                decoration: InputDecoration(
                  hintText: '搜索... 如 SPK 新币上线',
                  prefixIcon: const Icon(Icons.search),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
              const SizedBox(height: 16),

              // 顶部标签栏（市场、动态、牛人榜）
              DefaultTabController(
                length: 3,
                child: Column(
                  children: [
                    Container(
                      decoration: BoxDecoration(
                        color: Colors.grey[200],
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: TabBar(
                        labelColor: Colors.black,
                        unselectedLabelColor: Colors.grey,
                        tabs: const [
                          Tab(text: '市场'),
                          Tab(text: '动态'),
                          Tab(text: '牛人榜'),
                        ],
                      ),
                    ),
                    const SizedBox(height: 16),

                    // 市场数据概览
                    Row(
                      children: [
                        _buildMarketCard(
                          title: '市值',
                          value: '\$3.40万亿',
                          change: '+0.68%',
                          changeColor: Colors.green,
                        ),
                        const SizedBox(width: 16),
                        _buildMarketCard(
                          title: '成交额',
                          value: '\$748.79亿',
                          change: '-24.93%',
                          changeColor: Colors.red,
                        ),
                        const SizedBox(width: 16),
                        _buildMarketCard(
                          title: '市场占有率',
                          value: '61.87%',
                          change: 'Bitcoin',
                          changeColor: Colors.black,
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),

                    // GDP 提示
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.blue[50],
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Row(
                        children: [
                          const Icon(Icons.info, color: Colors.blue, size: 18),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              '季度环比 GDP 增长率最终值 (美国) 将于 6月26...',
                              style: TextStyle(color: Colors.blue[800]),
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 24),

                    // 合约分类标签
                    SingleChildScrollView(
                      scrollDirection: Axis.horizontal,
                      child: Row(
                        children: [
                          _buildCategoryChip('全部'),
                          _buildCategoryChip('主流币'),
                          _buildCategoryChip('新币种'),
                          _buildCategoryChip('人工智能'),
                          _buildCategoryChip('Solana'),
                        ],
                      ),
                    ),
                    const SizedBox(height: 16),

                    // 合约列表
                    DataTable(
                      columnSpacing: 16,
                      columns: const [
                        DataColumn(label: Text('名称')),
                        DataColumn(label: Text('成交额')),
                        DataColumn(label: Text('最新价')),
                        DataColumn(label: Text('今日涨跌')),
                      ],
                      rows: [
                        _buildDataRow(
                          name: 'BTCUSDT',
                          turnover: '\$56.14亿',
                          latestPrice: '105,928.3',
                          change: '+1.79%',
                          changeColor: Colors.green,
                        ),
                        _buildDataRow(
                          name: 'ETHUSDT',
                          turnover: '\$83.97亿',
                          latestPrice: '2,551.96',
                          change: '+2.41%',
                          changeColor: Colors.green,
                        ),
                        _buildDataRow(
                          name: 'SOLUSDT',
                          turnover: '\$10.91亿',
                          latestPrice: '147.58',
                          change: '+3.15%',
                          changeColor: Colors.green,
                        ),
                        _buildDataRow(
                          name: 'TONUSDT',
                          turnover: '\$1,755.26万',
                          latestPrice: '2.968',
                          change: '+1.61%',
                          changeColor: Colors.green,
                        ),
                        _buildDataRow(
                          name: 'DOGEUSDT',
                          turnover: '\$5.17亿',
                          latestPrice: '0.17004',
                          change: '+1.09%',
                          changeColor: Colors.green,
                        ),
                        _buildDataRow(
                          name: 'XRPUSDT',
                          turnover: '\$2.12亿',
                          latestPrice: '2.1667',
                          change: '+1.03%',
                          changeColor: Colors.green,
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),

      // 底部导航栏
      bottomNavigationBar: BottomNavigationBar(
        items: const [
          BottomNavigationBarItem(icon: Icon(Icons.home), label: '欧易'),
          BottomNavigationBarItem(icon: Icon(Icons.show_chart), label: '市场'),
          BottomNavigationBarItem(icon: Icon(Icons.swap_horiz), label: '交易'),
          BottomNavigationBarItem(icon: Icon(Icons.explore), label: '探索'),
          BottomNavigationBarItem(icon: Icon(Icons.wallet), label: '资产'),
        ],
        currentIndex: 1, // 模拟当前在“市场”页
        onTap: (index) {
          // 处理底部导航点击事件
        },
      ),
    );
  }

  Widget _buildMarketCard({
    required String title,
    required String value,
    required dynamic change,
    required Color changeColor,
  }) {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
          boxShadow: [
            BoxShadow(
              color: Colors.grey[300]!,
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: const TextStyle(color: Colors.grey, fontSize: 12),
            ),
            const SizedBox(height: 4),
            Text(
              value,
              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 4),
            Text(
              change.toString(),
              style: TextStyle(color: changeColor, fontSize: 12),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCategoryChip(String text) {
    return Padding(
      padding: const EdgeInsets.only(right: 8.0),
      child: Chip(
        label: Text(text),
        backgroundColor: Colors.grey[200],
        labelStyle: const TextStyle(color: Colors.black),
      ),
    );
  }

  DataRow _buildDataRow({
    required String name,
    required String turnover,
    required String latestPrice,
    required String change,
    required Color changeColor,
  }) {
    return DataRow(
      cells: [
        DataCell(Text(name)),
        DataCell(Text(turnover)),
        DataCell(Text(latestPrice)),
        DataCell(Text(change, style: TextStyle(color: changeColor))),
      ],
    );
  }
}

// 可在主入口或其他页面通过以下方式使用：
// void main() {
//   runApp(MaterialApp(
//     home: CryptoMarketPage(),
//   ));
// }
