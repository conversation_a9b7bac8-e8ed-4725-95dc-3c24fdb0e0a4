import 'package:financial_app_market/financial_app_market.dart';
import 'package:flutter/material.dart' hide DateUtils;
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:financial_trading_chart/financial_trading_chart.dart';
import 'package:financial_app_core/financial_app_core.dart';
import '../bloc/market_bloc.dart';

/// 专门调试15分钟图表的页面
class Debug15mChartPage extends StatefulWidget {
  const Debug15mChartPage({super.key});

  @override
  State<Debug15mChartPage> createState() => _Debug15mChartPageState();
}

class _Debug15mChartPageState extends State<Debug15mChartPage> {
  final GlobalKey<TradingChartWidgetState> _chartKey = GlobalKey();
  final String _symbol = 'BTC-USDT-SWAP';
  final String _timeFrame = '15m';
  List<String> _logs = [];
  bool _isLoading = false;
  List<KLineData>? _currentData;

  @override
  void initState() {
    super.initState();
    _addLog('页面初始化 - 交易对: $_symbol, 时间格式: $_timeFrame');
    _loadData();
  }

  void _addLog(String message) {
    setState(() {
      _logs.insert(
        0,
        '${DateTime.now().toString().substring(11, 19)} - $message',
      );
      if (_logs.length > 20) {
        _logs.removeLast();
      }
    });
    debugPrint('🐛 $message');
  }

  void _loadData() {
    _addLog('开始加载15分钟数据...');
    setState(() {
      _isLoading = true;
    });

    context.read<MarketBloc>().add(
      ChangeTimeIntervalEvent(
        symbol: _symbol,
        interval: _timeFrame,
        timeFrame: _timeFrame,
      ),
    );
  }

  void _testWithMockData() {
    _addLog('生成模拟15分钟数据进行测试');

    final now = DateTime.now();
    final mockData = <KLineData>[];

    // 生成24个15分钟数据点（6小时的数据）
    for (int i = 23; i >= 0; i--) {
      final time = now.subtract(Duration(minutes: 15 * i));
      final basePrice = 50000.0;
      final random = (time.millisecondsSinceEpoch % 10000) / 10000.0;

      final open = basePrice + (random * 1000) - 500;
      final close = open + ((random - 0.5) * 200);
      final high =
          [open, close].reduce((a, b) => a > b ? a : b) + (random * 100);
      final low =
          [open, close].reduce((a, b) => a < b ? a : b) - (random * 100);

      // 使用15分钟格式化时间
      final formattedTime = DateUtils.formatDateTime(time, format: 'HH:mm');

      mockData.add(
        KLineData(
          timestamp: time,
          time: formattedTime,
          open: open,
          high: high,
          low: low,
          close: close,
          volume: 1000000 + (random * 500000),
        ),
      );
    }

    setState(() {
      _currentData = mockData;
    });

    _addLog('生成了${mockData.length}个模拟数据点');
    _addLog('时间范围: ${mockData.first.time} ~ ${mockData.last.time}');

    // 设置到图表
    _chartKey.currentState?.setData(mockData, timeFrame: _timeFrame);
    _addLog('模拟数据已设置到图表');
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('15分钟图表调试'),
        backgroundColor: Colors.purple,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadData,
            tooltip: '重新加载',
          ),
          IconButton(
            icon: const Icon(Icons.science),
            onPressed: _testWithMockData,
            tooltip: '测试模拟数据',
          ),
        ],
      ),
      body: Column(
        children: [
          // 状态信息
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            color: Colors.purple[50],
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      _isLoading ? Icons.hourglass_empty : Icons.check_circle,
                      color: _isLoading ? Colors.orange : Colors.green,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      _isLoading ? '加载中...' : '就绪',
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Text('交易对: $_symbol'),
                Text('时间格式: $_timeFrame'),
                Text('预期时间轴: HH:mm (如: 14:30, 14:45, 15:00)'),
                if (_currentData != null) ...[
                  Text('当前数据点数: ${_currentData!.length}'),
                  if (_currentData!.isNotEmpty) ...[
                    Text(
                      '数据时间范围: ${_currentData!.first.time} ~ ${_currentData!.last.time}',
                    ),
                  ],
                ],
              ],
            ),
          ),

          // 操作按钮
          Container(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _loadData,
                    icon: const Icon(Icons.cloud_download),
                    label: const Text('加载真实数据'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _testWithMockData,
                    icon: const Icon(Icons.science),
                    label: const Text('测试模拟数据'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // 图表区域
          Expanded(
            flex: 2,
            child: Container(
              margin: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.purple, width: 2),
                borderRadius: BorderRadius.circular(8),
              ),
              child: BlocConsumer<MarketBloc, MarketState>(
                listener: (context, state) {
                  setState(() {
                    _isLoading = false;
                  });

                  if (state is TimeIntervalChanged) {
                    _addLog('✅ 收到TimeIntervalChanged事件');
                    _addLog('交易对: ${state.symbol}');
                    _addLog('时间间隔: ${state.interval}');
                    _addLog('时间格式: ${state.timeFrame}');

                    final data = state.klineData.cast<KLineData>();
                    _addLog('数据点数量: ${data.length}');

                    if (data.isNotEmpty) {
                      _addLog('第一个数据点:');
                      _addLog('  - 时间: ${data.first.time}');
                      _addLog('  - 时间戳: ${data.first.timestamp}');
                      _addLog(
                        '  - OHLC: ${data.first.open.toStringAsFixed(2)}/${data.first.high.toStringAsFixed(2)}/${data.first.low.toStringAsFixed(2)}/${data.first.close.toStringAsFixed(2)}',
                      );

                      _addLog('最后一个数据点:');
                      _addLog('  - 时间: ${data.last.time}');
                      _addLog('  - 时间戳: ${data.last.timestamp}');
                      _addLog(
                        '  - OHLC: ${data.last.open.toStringAsFixed(2)}/${data.last.high.toStringAsFixed(2)}/${data.last.low.toStringAsFixed(2)}/${data.last.close.toStringAsFixed(2)}',
                      );
                    } else {
                      _addLog('❌ 没有收到数据');
                    }

                    setState(() {
                      _currentData = data;
                    });

                    _chartKey.currentState?.setData(
                      data,
                      timeFrame: state.timeFrame,
                    );
                    _addLog('数据已设置到图表');
                  } else if (state is MarketError) {
                    _addLog('❌ 错误: ${state.message}');
                  } else if (state is MarketLoading) {
                    _addLog('⏳ 正在加载数据...');
                    setState(() {
                      _isLoading = true;
                    });
                  }
                },
                builder: (context, state) {
                  if (_isLoading) {
                    return const Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          CircularProgressIndicator(color: Colors.purple),
                          SizedBox(height: 16),
                          Text('加载15分钟图表数据中...'),
                        ],
                      ),
                    );
                  }

                  return TradingChartWidget(key: _chartKey);
                },
              ),
            ),
          ),

          // 日志区域
          Expanded(
            flex: 1,
            child: Container(
              margin: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                children: [
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(8),
                    decoration: const BoxDecoration(
                      color: Colors.grey,
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(8),
                        topRight: Radius.circular(8),
                      ),
                    ),
                    child: const Text(
                      '调试日志',
                      style: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  Expanded(
                    child: ListView.builder(
                      padding: const EdgeInsets.all(8),
                      itemCount: _logs.length,
                      itemBuilder: (context, index) {
                        return Padding(
                          padding: const EdgeInsets.symmetric(vertical: 1),
                          child: Text(
                            _logs[index],
                            style: const TextStyle(
                              fontSize: 11,
                              fontFamily: 'monospace',
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
          ),

          // 说明信息
          Container(
            padding: const EdgeInsets.all(16),
            color: Colors.grey[100],
            child: const Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('调试步骤：', style: TextStyle(fontWeight: FontWeight.bold)),
                SizedBox(height: 4),
                Text('1. 点击"加载真实数据"测试API数据', style: TextStyle(fontSize: 12)),
                Text('2. 点击"测试模拟数据"验证图表组件', style: TextStyle(fontSize: 12)),
                Text('3. 观察图表底部是否显示时间轴', style: TextStyle(fontSize: 12)),
                Text('4. 查看调试日志了解数据处理过程', style: TextStyle(fontSize: 12)),
                Text(
                  '5. 打开浏览器控制台查看JavaScript日志',
                  style: TextStyle(fontSize: 12),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
