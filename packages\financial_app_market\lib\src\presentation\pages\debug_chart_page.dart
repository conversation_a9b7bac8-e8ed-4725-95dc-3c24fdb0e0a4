import 'package:financial_app_market/financial_app_market.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:financial_trading_chart/financial_trading_chart.dart';
import 'package:financial_app_core/financial_app_core.dart';
import '../bloc/market_bloc.dart';

/// 调试图表页面 - 专门用于测试时间轴显示问题
class DebugChartPage extends StatefulWidget {
  const DebugChartPage({super.key});

  @override
  State<DebugChartPage> createState() => _DebugChartPageState();
}

class _DebugChartPageState extends State<DebugChartPage> {
  final GlobalKey<TradingChartWidgetState> _chartKey = GlobalKey();
  String _currentTimeFrame = '15m';
  List<String> _debugLogs = [];

  @override
  void initState() {
    super.initState();
    _addDebugLog('页面初始化，当前时间格式: $_currentTimeFrame');
    _loadInitialData();
  }

  void _addDebugLog(String message) {
    setState(() {
      _debugLogs.insert(
        0,
        '${DateTime.now().toString().substring(11, 19)} - $message',
      );
      if (_debugLogs.length > 20) {
        _debugLogs.removeLast();
      }
    });
    debugPrint('🐛 $message');
  }

  void _loadInitialData() {
    _addDebugLog('请求初始数据，时间格式: $_currentTimeFrame');
    context.read<MarketBloc>().add(
      GetKlineDataEvent2(symbol: 'test-symbol', bar: _currentTimeFrame),
    );
  }

  void _changeTimeFrame(String newTimeFrame) {
    _addDebugLog('切换时间格式: $_currentTimeFrame -> $newTimeFrame');
    setState(() {
      _currentTimeFrame = newTimeFrame;
    });

    context.read<MarketBloc>().add(
      ChangeTimeIntervalEvent(
        symbol: 'test-symbol',
        interval: newTimeFrame,
        timeFrame: newTimeFrame,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('图表时间轴调试'),
        backgroundColor: Colors.red,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadInitialData,
            tooltip: '重新加载数据',
          ),
          IconButton(
            icon: const Icon(Icons.clear),
            onPressed: () {
              setState(() {
                _debugLogs.clear();
              });
            },
            tooltip: '清空日志',
          ),
        ],
      ),
      body: Column(
        children: [
          // 当前状态显示
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            color: Colors.red[50],
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    const Icon(Icons.bug_report, color: Colors.red),
                    const SizedBox(width: 8),
                    const Text(
                      '调试信息',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Text('当前时间格式: $_currentTimeFrame'),
                Text(
                  '图表状态: ${_chartKey.currentState != null ? "已初始化" : "未初始化"}',
                ),
              ],
            ),
          ),

          // 时间格式快速切换
          Container(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  '快速切换时间格式',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),
                Wrap(
                  spacing: 8,
                  runSpacing: 8,
                  children: [
                    _buildTimeFrameButton('1s'),
                    _buildTimeFrameButton('1m'),
                    _buildTimeFrameButton('5m'),
                    _buildTimeFrameButton('15m'),
                    _buildTimeFrameButton('30m'),
                    _buildTimeFrameButton('1h'),
                    _buildTimeFrameButton('4h'),
                    _buildTimeFrameButton('1d'),
                  ],
                ),
              ],
            ),
          ),

          // 图表区域
          Expanded(
            flex: 2,
            child: Container(
              margin: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.red, width: 2),
                borderRadius: BorderRadius.circular(8),
              ),
              child: BlocConsumer<MarketBloc, MarketState>(
                listener: (context, state) {
                  if (state is KlineDataLoaded2) {
                    _addDebugLog(
                      '收到KlineDataLoaded2数据: ${state.klineData.length}个数据点',
                    );
                    _addDebugLog('数据时间格式: ${state.bar}');

                    final data = state.klineData.cast<KLineData>();
                    if (data.isNotEmpty) {
                      _addDebugLog('第一个数据点时间: ${data.first.time}');
                      _addDebugLog('最后一个数据点时间: ${data.last.time}');
                    }

                    _chartKey.currentState?.setData(
                      data,
                      timeFrame: _currentTimeFrame,
                    );
                    _addDebugLog('数据已设置到图表，timeFrame: $_currentTimeFrame');
                  } else if (state is TimeIntervalChanged) {
                    _addDebugLog(
                      '收到TimeIntervalChanged: ${state.interval} -> ${state.timeFrame}',
                    );
                    _addDebugLog('数据点数: ${state.klineData.length}');

                    final data = state.klineData.cast<KLineData>();
                    if (data.isNotEmpty) {
                      _addDebugLog('第一个数据点时间: ${data.first.time}');
                      _addDebugLog('最后一个数据点时间: ${data.last.time}');
                    }

                    _chartKey.currentState?.setData(
                      data,
                      timeFrame: state.timeFrame,
                    );
                    _addDebugLog('时间段切换完成');
                  } else if (state is MarketLoading) {
                    _addDebugLog('正在加载数据...');
                  } else if (state is MarketError) {
                    _addDebugLog('错误: ${state.message}');
                  }
                },
                builder: (context, state) {
                  if (state is MarketLoading) {
                    return const Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          CircularProgressIndicator(color: Colors.red),
                          SizedBox(height: 16),
                          Text('加载图表数据中...'),
                        ],
                      ),
                    );
                  }

                  return TradingChartWidget(key: _chartKey);
                },
              ),
            ),
          ),

          // 调试日志
          Expanded(
            flex: 1,
            child: Container(
              margin: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(8),
                    decoration: const BoxDecoration(
                      color: Colors.grey,
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(8),
                        topRight: Radius.circular(8),
                      ),
                    ),
                    child: const Text(
                      '调试日志',
                      style: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  Expanded(
                    child: ListView.builder(
                      padding: const EdgeInsets.all(8),
                      itemCount: _debugLogs.length,
                      itemBuilder: (context, index) {
                        return Padding(
                          padding: const EdgeInsets.symmetric(vertical: 2),
                          child: Text(
                            _debugLogs[index],
                            style: const TextStyle(
                              fontSize: 12,
                              fontFamily: 'monospace',
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
          ),

          // 操作按钮
          Container(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _loadInitialData,
                    icon: const Icon(Icons.refresh),
                    label: const Text('重新加载'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () {
                      _addDebugLog('手动检查图表状态');
                      final chartState = _chartKey.currentState;
                      if (chartState != null) {
                        _addDebugLog('图表组件状态正常');
                      } else {
                        _addDebugLog('图表组件状态异常');
                      }
                    },
                    icon: const Icon(Icons.search),
                    label: const Text('检查状态'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTimeFrameButton(String timeFrame) {
    final isSelected = _currentTimeFrame == timeFrame;
    return ElevatedButton(
      onPressed: () => _changeTimeFrame(timeFrame),
      style: ElevatedButton.styleFrom(
        backgroundColor: isSelected ? Colors.red : Colors.grey[300],
        foregroundColor: isSelected ? Colors.white : Colors.black,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      ),
      child: Text(timeFrame),
    );
  }
}
