import 'package:flutter/material.dart';
import 'debug_15m_chart_page.dart';
import 'debug_chart_page.dart';
import 'chart_time_axis_test_page.dart';
import 'time_axis_fix_test_page.dart';

/// 调试菜单页面 - 集中访问所有调试工具
class DebugMenuPage extends StatelessWidget {
  const DebugMenuPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('图表调试工具'),
        backgroundColor: Colors.deepPurple,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 标题
            const Text(
              '选择调试工具',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.deepPurple,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              '以下是专门用于调试图表时间轴显示问题的工具页面',
              style: TextStyle(fontSize: 16, color: Colors.grey),
            ),
            const SizedBox(height: 24),

            // 调试工具列表
            Expanded(
              child: ListView(
                children: [
                  _buildDebugCard(
                    context,
                    title: '15分钟图表专项调试',
                    subtitle: '专门调试15分钟时间格式的图表显示问题',
                    icon: Icons.schedule,
                    color: Colors.purple,
                    onTap: () => Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const Debug15mChartPage(),
                      ),
                    ),
                    features: [
                      '✅ 专门测试15分钟数据',
                      '✅ 模拟数据对比测试',
                      '✅ 详细的数据流程日志',
                      '✅ 实时状态监控',
                    ],
                  ),

                  const SizedBox(height: 16),

                  _buildDebugCard(
                    context,
                    title: '通用图表调试器',
                    subtitle: '通用的图表调试工具，支持所有时间格式',
                    icon: Icons.bug_report,
                    color: Colors.red,
                    onTap: () => Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const DebugChartPage(),
                      ),
                    ),
                    features: [
                      '✅ 支持所有时间格式',
                      '✅ 实时日志记录',
                      '✅ 状态检查功能',
                      '✅ 手动数据加载',
                    ],
                  ),

                  const SizedBox(height: 16),

                  _buildDebugCard(
                    context,
                    title: '时间轴修复测试',
                    subtitle: '测试时间轴修复方案的效果',
                    icon: Icons.access_time,
                    color: Colors.green,
                    onTap: () => Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const TimeAxisFixTestPage(),
                      ),
                    ),
                    features: [
                      '✅ 快速时间格式切换',
                      '✅ 预期格式说明',
                      '✅ 操作日志记录',
                      '✅ 修复效果验证',
                    ],
                  ),

                  const SizedBox(height: 16),

                  _buildDebugCard(
                    context,
                    title: '时间轴生成测试',
                    subtitle: '测试时间轴数据生成和显示',
                    icon: Icons.timeline,
                    color: Colors.blue,
                    onTap: () => Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const ChartTimeAxisTestPage(),
                      ),
                    ),
                    features: [
                      '✅ 自动生成测试数据',
                      '✅ 时间格式对比',
                      '✅ 数据预览功能',
                      '✅ 快速测试按钮',
                    ],
                  ),
                ],
              ),
            ),

            // 使用说明
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey[300]!),
              ),
              child: const Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.info_outline, color: Colors.blue),
                      SizedBox(width: 8),
                      Text(
                        '使用建议',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 8),
                  Text(
                    '1. 首先使用"15分钟图表专项调试"来定位具体问题',
                    style: TextStyle(fontSize: 14),
                  ),
                  Text(
                    '2. 如果问题不限于15分钟，使用"通用图表调试器"',
                    style: TextStyle(fontSize: 14),
                  ),
                  Text(
                    '3. 使用"时间轴修复测试"验证修复方案效果',
                    style: TextStyle(fontSize: 14),
                  ),
                  Text(
                    '4. 打开浏览器开发者工具查看JavaScript控制台日志',
                    style: TextStyle(fontSize: 14),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDebugCard(
    BuildContext context, {
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
    required List<String> features,
  }) {
    return Card(
      elevation: 4,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: color.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(icon, color: color, size: 24),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          title,
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          subtitle,
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                  Icon(
                    Icons.arrow_forward_ios,
                    color: Colors.grey[400],
                    size: 16,
                  ),
                ],
              ),
              const SizedBox(height: 12),
              ...features.map((feature) => Padding(
                    padding: const EdgeInsets.symmetric(vertical: 2),
                    child: Text(
                      feature,
                      style: const TextStyle(fontSize: 12),
                    ),
                  )),
            ],
          ),
        ),
      ),
    );
  }
}
