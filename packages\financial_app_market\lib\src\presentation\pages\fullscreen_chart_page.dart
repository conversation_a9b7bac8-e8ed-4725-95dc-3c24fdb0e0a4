import 'package:flutter/material.dart';

class FullScreenChartPage extends StatelessWidget {
  const FullScreenChartPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: SafeArea(
        child: Stack(
          children: [
            // 将 TradingChartWidget 放在这里
            // 注意：您可能需要一种方式来传递数据或状态到这个全屏图表
            Center(
              child: Text("全屏图表将在这里", style: TextStyle(color: Colors.white)),
            ),
            // 退出全屏按钮
            Positioned(
              top: 10,
              right: 10,
              child: IconButton(
                icon: Icon(Icons.fullscreen_exit, color: Colors.grey),
                onPressed: () => Navigator.of(context).pop(),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
