import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:financial_app_core/financial_app_core.dart'; // Import LoadingIndicator
import 'package:intl/intl.dart';

// 注意：这里仅为示例，实际K线图展示需要集成专业的图表库，例如 fl_chart。
// 由于 fl_chart 需要复杂的集成和配置，这里仅用一个简单的ListView来展示数据。
// 在实际项目中，您需要根据选择的图表库替换以下内容。

class KlineChartPage extends StatefulWidget {
  final String symbol;
  const KlineChartPage({super.key, required this.symbol});

  @override
  State<KlineChartPage> createState() => _KlineChartPageState();
}

class _KlineChartPageState extends State<KlineChartPage> {
  String _selectedInterval = '1h'; // 默认选择1小时K线

  @override
  void initState() {
    super.initState();
    _fetchKlineData();
  }

  Future<void> _fetchKlineData() async {
    // await Provider.of<MarketProvider>(context, listen: false).fetchKlineData(
    //   symbol: widget.symbol,
    //   interval: _selectedInterval,
    //   limit: 100, // 获取最近100条数据
    // );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('${widget.symbol} K线图'),
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(48.0),
          child: _buildIntervalSelector(),
        ),
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            // 这里放置 K线图表组件
            // 例如：CandlestickChart(data: marketProvider.klineData)
            Container(
              height: 300, // 图表占位符高度
              color: Colors.blueGrey.shade900,
              child: const Center(
                child: Text(
                  'K线图表占位符\n(此处将集成专业的图表库)',
                  textAlign: TextAlign.center,
                  style: TextStyle(color: Colors.white70, fontSize: 16),
                ),
              ),
            ),
            const SizedBox(height: 16),
            // 简单列表展示K线数据
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8.0),
              child: Text(
                '最近 $_selectedInterval K线数据列表',
                style: Theme.of(context).textTheme.titleMedium,
              ),
            ),
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: 5,
              itemBuilder: (context, index) {
                final kline = 1;
                final dateFormat = DateFormat('MM-dd HH:mm');
                // final openDateTime = DateTime.fromMillisecondsSinceEpoch(
                //   kline.openTime,
                // );
                // final bool isCloseUp = kline.close >= kline.open;
                // final Color candleColor = isCloseUp ? Colors.green : Colors.red;

                return Card(
                  margin: const EdgeInsets.symmetric(
                    vertical: 4.0,
                    horizontal: 8.0,
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(12.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Text('时间: ${dateFormat.format(openDateTime)}'),
                        // Text(
                        //   '开盘: ${kline.open.toStringAsFixed(2)}',
                        //   style: TextStyle(color: candleColor),
                        // ),
                        // Text(
                        //   '最高: ${kline.high.toStringAsFixed(2)}',
                        //   style: TextStyle(color: Colors.green),
                        // ),
                        // Text(
                        //   '最低: ${kline.low.toStringAsFixed(2)}',
                        //   style: TextStyle(color: Colors.red),
                        // ),
                        // Text(
                        //   '收盘: ${kline.close.toStringAsFixed(2)}',
                        //   style: TextStyle(
                        //     color: candleColor,
                        //     fontWeight: FontWeight.bold,
                        //   ),
                        // ),
                        // Text('成交量: ${kline.volume.toStringAsFixed(2)}'),
                      ],
                    ),
                  ),
                );
              },
            ),
          ],
        ),
      ),
      // body: Consumer<MarketProvider>(
      //   builder: (context, marketProvider, child) {
      //     if (marketProvider.isLoading) {
      //       return const Center(child: LoadingIndicator(message: '加载K线数据...'));
      //     }
      //     if (marketProvider.errorMessage != null) {
      //       return Center(
      //         child: Text(
      //           '错误: ${marketProvider.errorMessage}',
      //           style: TextStyle(color: Theme.of(context).colorScheme.error),
      //         ),
      //       );
      //     }
      //     if (marketProvider.klineData.isEmpty) {
      //       return const Center(child: Text('没有K线数据。'));
      //     }

      //     // 实际这里会是图表组件，例如 fl_chart
      //     // return ;
      //   },
      // ),
    );
  }

  Widget _buildIntervalSelector() {
    final List<String> intervals = [
      '1m',
      '5m',
      '15m',
      '30m',
      '1h',
      '4h',
      '1d',
      '1w',
      '1M',
    ];
    return Container(
      color: Theme.of(context).appBarTheme.backgroundColor,
      padding: const EdgeInsets.symmetric(horizontal: 8.0),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          children: intervals.map((interval) {
            return Padding(
              padding: const EdgeInsets.symmetric(horizontal: 4.0),
              child: ChoiceChip(
                label: Text(interval),
                selected: _selectedInterval == interval,
                selectedColor: Colors.blue.shade200,
                onSelected: (selected) {
                  if (selected) {
                    setState(() {
                      _selectedInterval = interval;
                      _fetchKlineData(); // 重新获取数据
                    });
                  }
                },
              ),
            );
          }).toList(),
        ),
      ),
    );
  }
}
