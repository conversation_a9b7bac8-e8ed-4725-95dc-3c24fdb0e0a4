import 'dart:async';
import 'dart:developer' as developer;
// dart:ffi 在Web平台不可用，暂时注释掉
// import 'dart:ffi' hide Size;

import 'package:financial_app_market/financial_app_market.dart';
import 'package:financial_app_market/src/data/models/line_data_model.dart';
import 'package:financial_app_market/src/presentation/pages/fullscreen_chart_page.dart';
import 'package:financial_app_market/src/presentation/pages/debug_menu_page.dart';
import 'package:financial_app_market/src/presentation/widgets/time_interval_selector.dart';
import 'package:financial_trading_chart/financial_trading_chart.dart'
    hide TradingViewDataType;
import 'package:financial_app_core/financial_app_core.dart';
import 'package:financial_ws_client/financial_ws_client.dart';
import 'package:flutter/material.dart' hide DateUtils;
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
// 🔑 使用financial_trading_chart模块的VisibleRange，避免冲突
import 'package:financial_trading_chart/src/data/chart_data_manager.dart'
    as chart_manager;

class KlineTradingPage extends StatefulWidget {
  const KlineTradingPage({super.key});

  @override
  State<KlineTradingPage> createState() => _KlineTradingPageState();
}

class _KlineTradingPageState extends State<KlineTradingPage>
    with TickerProviderStateMixin {
  // 🔑 模块名称常量
  static const String moduleName = 'KlineTradingPage';

  late TabController _topTabController;
  late TabController _bottomDataTabController;
  // 创建一个变量来控制滚动物理效果
  // 默认为可滚动
  ScrollPhysics _scrollPhysics = const AlwaysScrollableScrollPhysics();

  // --- 新增状态变量，用于管理交互 ---
  String _selectedTime = '15分';
  List<String> _selectedListIndicator = ['VOL'];

  // --- WebSocket 和图表相关状态 ---
  BusinessDataAdapter? _dataAdapter;
  StreamSubscription? _klineSubscription;
  StreamSubscription? _priceSubscription;
  List<KLineBusinessData> _klineData = [];
  PriceBusinessData? _currentPrice;
  bool _useCustomChart = false; // true: 自定义图表, false: TradingView
  bool _isConnected = false;
  String _currentSymbol = 'BTC-USDT-SWAP';
  String _currentInterval = '1m';
  String _currentTimeFrame = '1m'; // 当前时间段格式
  String? _klineSubscriberId;
  String? _priceSubscriberId;

  // 🔑 双接口图表相关状态
  TradingViewDataType _currentChartType = TradingViewDataType.candlestick;
  bool _isDualApiEnabled = true; // 🔧 临时禁用双接口模式，专注测试TradingView数据传递
  bool _isInitialDataLoaded = false; // 是否已加载初始数据
  String? _chartId; // 图表实例ID
  List<KLineData> _chartDataCache = []; // 图表数据缓存
  // ---

  @override
  void initState() {
    super.initState();
    AppLogger.logModule(
      moduleName,
      LogLevel.info,
      '🚀 KlineTradingPage initState开始',
    );

    _topTabController = TabController(length: 7, vsync: this);
    _bottomDataTabController = TabController(length: 4, vsync: this);
    _initializeWebSocket();
    _generateMockData();

    // 🔑 初始化双接口图表系统
    _initializeDualApiChart();

    // 🔑 加载初始图表数据（使用接口1）
    WidgetsBinding.instance.addPostFrameCallback((_) {
      AppLogger.logModule(
        moduleName,
        LogLevel.info,
        '📋 PostFrameCallback执行，开始加载初始数据',
      );
      _loadInitialChartData();
    });

    AppLogger.logModule(
      moduleName,
      LogLevel.info,
      '✅ KlineTradingPage initState完成',
    );
  }

  /// 🔑 初始化双接口图表系统
  void _initializeDualApiChart() {
    if (_isDualApiEnabled) {
      // 🔑 注册TradingChartWidget的key到ChartAPI
      ChartAPI.instance.registerTradingChartKey(tradingChartKey);

      // 设置全局数据提供者 - 处理图表滑动时的历史数据请求
      ChartAPI.instance.setGlobalDataProvider(_handleChartRangeChange);

      // 创建图表实例
      _chartId = ChartAPI.instance.createChart(
        symbol: _currentSymbol,
        timeFrame: _currentTimeFrame,
      );

      AppLogger.logModule(
        moduleName,
        LogLevel.info,
        '🔄 双接口图表系统初始化完成，图表ID: $_chartId',
      );

      // 🔑 确保桥接器设置 - 延迟执行以确保TradingChartWidget已经构建
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _setupChartBridge();
      });

      // 加载初始数据
      _loadInitialChartData();
    }
  }

  /// 🔑 设置图表桥接器
  void _setupChartBridge() {
    if (_chartId != null) {
      try {
        // 🔑 从ChartAPI获取控制器并设置桥接器
        final controller = ChartAPI.instance.getController(_chartId!);
        if (controller != null) {
          // 创建桥接器实例
          final bridge = ChartBridge(
            onDataRequest: (data) {
              developer.log('📊 图表请求数据: $data');
            },
            onChartEvent: (event) {
              developer.log('📊 图表事件: ${event.type}');
            },
            onError: (error) {
              developer.log('❌ 图表错误: $error');
            },
          );

          // 设置桥接器到控制器
          controller.setChartBridge(bridge);
          developer.log('✅ 图表桥接器设置成功，图表ID: $_chartId');
        } else {
          developer.log('⚠️ 无法获取图表控制器，将在稍后重试');
          // 如果控制器还没准备好，延迟重试
          Future.delayed(const Duration(milliseconds: 500), () {
            _setupChartBridge();
          });
        }
      } catch (e) {
        developer.log('❌ 设置图表桥接器失败: $e');
        // 重试设置桥接器
        Future.delayed(const Duration(milliseconds: 1000), () {
          _setupChartBridge();
        });
      }
    } else {
      developer.log('⚠️ 图表ID未准备好，将在稍后重试');
      // 如果图表ID还没准备好，延迟重试
      Future.delayed(const Duration(milliseconds: 500), () {
        _setupChartBridge();
      });
    }
  }

  /// 🔑 处理双接口模式的图表可见范围变化
  void _handleDualApiChartRangeChange(Map<String, dynamic> data) {
    try {
      AppLogger.logModule(moduleName, LogLevel.info, '📊 双接口模式图表可见范围变化: $data');

      // 🔑 直接调用ChartAPI处理可见范围变化
      ChartAPI.instance.handleVisibleRangeChanged(data);
    } catch (e, stackTrace) {
      AppLogger.logModule(
        moduleName,
        LogLevel.error,
        '❌ 处理双接口图表范围变化失败: $e\n堆栈跟踪: $stackTrace',
      );
    }
  }

  /// 🔑 处理传统图表的可见范围变化
  void _handleTraditionalChartRangeChange(Map<String, dynamic> data) {
    try {
      AppLogger.logModule(moduleName, LogLevel.info, '📊 传统图表可见范围变化: $data');

      // 🔧 修复数据类型转换问题
      dynamic fromValue = data['from'];
      dynamic toValue = data['to'];
      final currentDataCount = data['currentDataCount'] as int? ?? 0;

      // 处理不同的数据类型
      int? fromTimestamp;
      int? toTimestamp;

      if (fromValue is int) {
        fromTimestamp = fromValue;
      } else if (fromValue is double) {
        fromTimestamp = fromValue.toInt();
      } else if (fromValue is String) {
        fromTimestamp = int.tryParse(fromValue);
      }

      if (toValue is int) {
        toTimestamp = toValue;
      } else if (toValue is double) {
        toTimestamp = toValue.toInt();
      } else if (toValue is String) {
        toTimestamp = int.tryParse(toValue);
      }

      if (fromTimestamp == null || toTimestamp == null) {
        AppLogger.logModule(
          moduleName,
          LogLevel.warning,
          '⚠️ 可见范围参数无效: from=$fromValue, to=$toValue',
        );
        return;
      }

      AppLogger.logModule(
        moduleName,
        LogLevel.info,
        '📊 需要加载历史数据: from=$fromTimestamp, to=$toTimestamp, 当前数据量=$currentDataCount',
      );

      // 🔑 触发历史数据加载 - 使用接口2
      final startTime = DateTime.fromMillisecondsSinceEpoch(fromTimestamp);
      final endTime = DateTime.fromMillisecondsSinceEpoch(toTimestamp);

      AppLogger.logModule(
        moduleName,
        LogLevel.info,
        '📡 发起历史数据请求: ${startTime.toIso8601String()} - ${endTime.toIso8601String()}',
      );

      context.read<MarketBloc>().add(
        GetKlineDataEvent(
          symbol: _currentSymbol,
          interval: _currentTimeFrame,
          startTime: startTime,
          endTime: endTime,
        ),
      );
    } catch (e, stackTrace) {
      AppLogger.logModule(
        moduleName,
        LogLevel.error,
        '❌ 处理传统图表范围变化失败: $e\n堆栈跟踪: $stackTrace',
      );
    }
  }

  /// 🔑 传统方式添加历史数据
  void _addHistoricalDataToTraditionalChart(List<KLineData> data) {
    try {
      // 传统方式添加历史数据
      final chartWidget = tradingChartKey.currentState;
      AppLogger.logModule(moduleName, LogLevel.info, '📊 传统方式添加历史数据详情:');
      AppLogger.logModule(
        moduleName,
        LogLevel.info,
        '  - 图表组件状态: ${chartWidget != null}',
      );
      AppLogger.logModule(
        moduleName,
        LogLevel.info,
        '  - 历史数据数量: ${data.length}',
      );

      if (chartWidget != null) {
        AppLogger.logModule(
          moduleName,
          LogLevel.info,
          '📊 调用TradingChartWidget.addHistoricalData()...',
        );
        chartWidget.addHistoricalData(data, timeFrame: _currentTimeFrame);
        AppLogger.logModule(
          moduleName,
          LogLevel.info,
          '✅ TradingChartWidget.addHistoricalData()调用完成',
        );
      } else {
        AppLogger.logModule(
          moduleName,
          LogLevel.warning,
          '❌ TradingChartWidget状态为null，无法添加历史数据',
        );
        // 如果图表组件还没准备好，延迟重试
        Future.delayed(const Duration(milliseconds: 500), () {
          AppLogger.logModule(moduleName, LogLevel.info, '🔄 延迟重试传统方式添加历史数据');
          _addHistoricalDataToTraditionalChart(data);
        });
      }
    } catch (e) {
      AppLogger.logModule(moduleName, LogLevel.error, '❌ 传统方式历史数据添加失败: $e');
      AppLogger.logModule(
        moduleName,
        LogLevel.error,
        '❌ 错误堆栈: ${StackTrace.current}',
      );
    }
  }

  /// 🔑 传统方式更新图表
  void _updateTraditionalChart(List<KLineData> data) {
    try {
      // 传统方式更新图表
      final chartWidget = tradingChartKey.currentState;
      AppLogger.logModule(moduleName, LogLevel.info, '📊 传统方式更新图表详情:');
      AppLogger.logModule(
        moduleName,
        LogLevel.info,
        '  - 图表组件状态: ${chartWidget != null}',
      );
      AppLogger.logModule(
        moduleName,
        LogLevel.info,
        '  - 数据数量: ${data.length}',
      );
      AppLogger.logModule(
        moduleName,
        LogLevel.debug,
        '  - 时间格式: $_currentTimeFrame',
      );

      if (data.isNotEmpty) {
        AppLogger.logModule(
          moduleName,
          LogLevel.debug,
          '  - 第一条数据: ${data.first.toString()}',
        );
        AppLogger.logModule(
          moduleName,
          LogLevel.debug,
          '  - 最后一条数据: ${data.last.toString()}',
        );
      }

      if (chartWidget != null) {
        AppLogger.logModule(
          moduleName,
          LogLevel.info,
          '📊 调用TradingChartWidget.setData()...',
        );
        chartWidget.setData(data, timeFrame: _currentTimeFrame);
        AppLogger.logModule(
          moduleName,
          LogLevel.info,
          '✅ TradingChartWidget.setData()调用完成',
        );
      } else {
        AppLogger.logModule(
          moduleName,
          LogLevel.warning,
          '❌ TradingChartWidget状态为null，无法设置数据',
        );
        // 如果图表组件还没准备好，延迟重试
        Future.delayed(const Duration(milliseconds: 500), () {
          AppLogger.logModule(moduleName, LogLevel.info, '🔄 延迟重试传统方式更新图表');
          _updateTraditionalChart(data);
        });
      }
    } catch (e) {
      AppLogger.logModule(moduleName, LogLevel.error, '❌ 传统方式数据设置失败: $e');
      AppLogger.logModule(
        moduleName,
        LogLevel.error,
        '❌ 错误堆栈: ${StackTrace.current}',
      );
    }
  }

  /// 🔑 处理图表可见范围变化 - 当用户滑动图表时触发
  Future<List<ChartData>> _handleChartRangeChange(
    String symbol,
    String timeFrame,
    chart_manager.VisibleRange range,
  ) async {
    try {
      developer.log(
        '📊 图表滑动触发历史数据请求: $symbol, $timeFrame, 范围: ${range.from}-${range.to}',
      );

      // 🔑 图表滑动时，总是使用接口2获取历史数据
      final startTime = DateTime.fromMillisecondsSinceEpoch(range.from);
      final endTime = DateTime.fromMillisecondsSinceEpoch(range.to);

      context.read<MarketBloc>().add(
        GetKlineDataEvent(
          symbol: symbol,
          interval: timeFrame,
          startTime: startTime,
          endTime: endTime,
        ),
      );

      // 返回空数据，等待Bloc处理完成后通过状态更新
      return [];
    } catch (e) {
      developer.log('❌ 图表滑动数据获取失败: $e');
      return [];
    }
  }

  /// 🔑 加载初始数据 - 页面进入时调用接口1
  void _loadInitialChartData() {
    AppLogger.logModule(
      moduleName,
      LogLevel.info,
      '🔄 _loadInitialChartData被调用',
    );
    AppLogger.logModule(
      moduleName,
      LogLevel.debug,
      '📊 当前状态: _isInitialDataLoaded = $_isInitialDataLoaded',
    );

    if (!_isInitialDataLoaded) {
      AppLogger.logModule(
        moduleName,
        LogLevel.info,
        '📊 开始加载初始图表数据: $_currentSymbol, $_currentTimeFrame',
      );

      try {
        final marketBloc = context.read<MarketBloc>();
        AppLogger.logModule(
          moduleName,
          LogLevel.debug,
          '📊 获取MarketBloc成功: ${marketBloc.runtimeType}',
        );

        final event = GetKlineDataEvent2(
          symbol: _currentSymbol,
          bar: _currentTimeFrame,
        );
        AppLogger.logModule(
          moduleName,
          LogLevel.debug,
          '📊 创建事件: ${event.runtimeType}',
        );
        AppLogger.logModule(
          moduleName,
          LogLevel.debug,
          '📊 事件参数: symbol=${event.symbol}, bar=${event.bar}',
        );

        marketBloc.add(event);
        AppLogger.logModule(moduleName, LogLevel.info, '✅ 事件已发送到MarketBloc');

        _isInitialDataLoaded = true;
        AppLogger.logModule(moduleName, LogLevel.debug, '📊 标记初始数据已加载');
      } catch (e) {
        AppLogger.logModule(moduleName, LogLevel.error, '❌ 加载初始数据失败: $e');
        AppLogger.logModule(
          moduleName,
          LogLevel.error,
          '❌ 错误堆栈: ${StackTrace.current}',
        );
      }
    } else {
      AppLogger.logModule(moduleName, LogLevel.warning, '⚠️ 初始数据已加载，跳过重复加载');
    }
  }

  /// 🔑 合并历史数据到现有缓存中
  List<KLineData> _mergeHistoricalData(List<KLineData> newData) {
    // 创建一个Set来存储已有的时间戳，避免重复数据
    final existingTimestamps = _chartDataCache.map((e) => e.timestamp).toSet();

    // 过滤掉重复的数据
    final filteredNewData = newData
        .where((item) => !existingTimestamps.contains(item.timestamp))
        .toList();

    // 合并数据并按时间排序
    final mergedData = [..._chartDataCache, ...filteredNewData];
    mergedData.sort((a, b) => a.timestamp.compareTo(b.timestamp));

    // 更新缓存
    _chartDataCache = mergedData;

    developer.log(
      '📊 数据合并完成: 新增${filteredNewData.length}条，总计${mergedData.length}条',
    );

    return mergedData;
  }

  @override
  void dispose() {
    _topTabController.dispose();
    _bottomDataTabController.dispose();
    _klineSubscription?.cancel();
    _priceSubscription?.cancel();

    // 取消订阅
    if (_dataAdapter != null) {
      if (_klineSubscriberId != null) {
        _dataAdapter!.unsubscribe(
          'kline',
          _currentSymbol,
          parameters: {'interval': _currentInterval},
          subscriberId: _klineSubscriberId,
        );
      }
      if (_priceSubscriberId != null) {
        _dataAdapter!.unsubscribe(
          'ticker',
          _currentSymbol,
          subscriberId: _priceSubscriberId,
        );
      }
    }

    super.dispose();
  }

  // 初始化 WebSocket 连接
  void _initializeWebSocket() async {
    try {
      // 获取业务数据适配器实例
      _dataAdapter = BusinessDataProvider.maybeOf(context);

      if (_dataAdapter == null) {
        developer.log('❌ 无法获取业务数据适配器，使用模拟数据');
        _generateMockData();
        return;
      }

      // 生成唯一的订阅者ID
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      _klineSubscriberId = 'kline_page_$timestamp';
      _priceSubscriberId = 'price_page_$timestamp';

      // 订阅 K线数据
      _klineSubscription = _dataAdapter!
          .getKLineStream(
            _currentSymbol,
            _currentInterval,
            subscriberId: _klineSubscriberId,
          )
          .listen((klineData) {
            setState(() {
              _klineData = klineData;
              _isConnected = _dataAdapter?.isConnected ?? false;
            });
            developer.log('📈 收到 K线数据: ${klineData.length} 条');
          });

      // 订阅实时价格数据
      _priceSubscription = _dataAdapter!
          .getPriceStream(_currentSymbol, subscriberId: _priceSubscriberId)
          .listen((priceData) {
            setState(() {
              _currentPrice = priceData;
              _isConnected = _dataAdapter?.isConnected ?? false;
            });
            developer.log('💰 收到价格数据: ${priceData.price}');
          });

      developer.log('📊 业务数据适配器初始化完成');
    } catch (e) {
      developer.log('❌ 初始化 WebSocket 失败: $e');
      // 使用模拟数据作为后备
      _generateMockData();
    }
  }

  // 切换时间周期
  void _changeTimeInterval(String interval, {String? timeFrame}) {
    if (_currentInterval == interval) return;

    print('🔄 切换时间周期: $interval, timeFrame: $timeFrame');

    // 如果有WebSocket适配器，取消当前订阅
    if (_dataAdapter != null && _klineSubscriberId != null) {
      _dataAdapter!.unsubscribe(
        'kline',
        _currentSymbol,
        parameters: {'interval': _currentInterval},
        subscriberId: _klineSubscriberId,
      );
    }

    // 更新时间周期和时间格式
    setState(() {
      _currentInterval = interval;
      if (timeFrame != null) {
        _currentTimeFrame = timeFrame;
      }
    });

    // 总是通过Bloc请求数据（无论是否有WebSocket适配器）
    if (timeFrame != null) {
      print('📡 通过Bloc请求数据: $interval -> $timeFrame');
      context.read<MarketBloc>().add(
        ChangeTimeIntervalEvent(
          symbol: _currentSymbol,
          interval: interval,
          timeFrame: timeFrame,
        ),
      );
    }

    // 如果有WebSocket适配器，重新订阅新的时间周期数据
    if (_dataAdapter != null) {
      _klineSubscription?.cancel();
      _klineSubscription = _dataAdapter!
          .getKLineStream(
            _currentSymbol,
            _currentInterval,
            subscriberId: _klineSubscriberId,
          )
          .listen((klineData) {
            setState(() {
              _klineData = klineData;
            });
            developer.log('📈 切换到 $interval: ${klineData.length} 条数据');
          });

      developer.log('🔄 切换时间周期: $interval, 时间格式: $timeFrame');
    }
  }

  // 切换交易对
  void _changeSymbol(String symbol) {
    if (_currentSymbol == symbol || _dataAdapter == null) return;

    // 取消当前订阅
    if (_klineSubscriberId != null) {
      _dataAdapter!.unsubscribe(
        'kline',
        _currentSymbol,
        parameters: {'interval': _currentInterval},
        subscriberId: _klineSubscriberId,
      );
    }
    if (_priceSubscriberId != null) {
      _dataAdapter!.unsubscribe(
        'ticker',
        _currentSymbol,
        subscriberId: _priceSubscriberId,
      );
    }

    // 更新交易对
    _currentSymbol = symbol;

    // 重新订阅新的交易对数据
    _klineSubscription?.cancel();
    _klineSubscription = _dataAdapter!
        .getKLineStream(
          _currentSymbol,
          _currentInterval,
          subscriberId: _klineSubscriberId,
        )
        .listen((klineData) {
          setState(() {
            _klineData = klineData;
          });
          developer.log('📈 切换到 $symbol: ${klineData.length} 条数据');
        });

    // 重新订阅价格数据
    _priceSubscription?.cancel();
    _priceSubscription = _dataAdapter!
        .getPriceStream(_currentSymbol, subscriberId: _priceSubscriberId)
        .listen((priceData) {
          setState(() {
            _currentPrice = priceData;
          });
          developer.log('💰 切换到 $symbol 价格: ${priceData.price}');
        });

    developer.log('🔄 切换交易对: $symbol');
  }

  // 生成模拟数据（用于演示）
  void _generateMockData() {
    final mockData = List.generate(100, (i) {
      final timestamp = DateTime.now().subtract(Duration(minutes: 100 - i));
      final open = 50000.0 + (i * 10) + (i % 10) * 100;
      final high = 50500.0 + (i * 10) + (i % 10) * 100;
      final low = 49500.0 + (i * 10) + (i % 10) * 100;
      final close = 50200.0 + (i * 10) + (i % 10) * 100;
      final volume = 1000000.0 + (i * 10000);

      return KLineBusinessData(
        timestamp: timestamp,
        open: open,
        high: high,
        low: low,
        close: close,
        volume: volume,
        amount: volume * close,
        isComplete: true,
      );
    });

    setState(() {
      _klineData = mockData;
      _currentPrice = PriceBusinessData(
        symbol: _currentSymbol,
        price: mockData.last.close,
        priceChange: mockData.last.close - mockData.first.close,
        priceChangePercent:
            ((mockData.last.close - mockData.first.close) /
                mockData.first.close) *
            100,
        volume: mockData.map((e) => e.volume).reduce((a, b) => a + b),
        high24h: mockData.map((e) => e.high).reduce((a, b) => a > b ? a : b),
        low24h: mockData.map((e) => e.low).reduce((a, b) => a < b ? a : b),
        timestamp: DateTime.now(),
      );
    });
  }

  void _getKlineData() {
    context.read<MarketBloc>().add(
      GetKlineDataEvent2(
        symbol: 'test-symbol',
        bar: _currentTimeFrame,
      ), // 使用当前时间格式
    );
  }

  void _fetchKLineData({String interval = '15分'}) async {
    print('Fetching data for interval: $interval');

    // 将中文时间周期转换为标准格式
    String standardInterval;
    switch (interval) {
      case '1分':
        standardInterval = '1m';
        break;
      case '5分':
        standardInterval = '5m';
        break;
      case '15分':
        standardInterval = '15m';
        break;
      case '30分':
        standardInterval = '30m';
        break;
      case '1时':
        standardInterval = '1h';
        break;
      case '4时':
        standardInterval = '4h';
        break;
      case '1日':
        standardInterval = '1d';
        break;
      default:
        standardInterval = '15m';
    }

    // 切换时间周期
    _changeTimeInterval(standardInterval, timeFrame: standardInterval);
  }

  void _enterFullScreenChart() {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => const FullScreenChartPage()),
    );
  }

  // 🔑 显示图表类型选择弹窗 - 支持双接口模式
  void _showChartTypeDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: const Color(0xFF1E1E1E),
          title: const Text('选择图表类型', style: TextStyle(color: Colors.white)),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: Icon(
                  Icons.candlestick_chart,
                  color: _useCustomChart ? Colors.blue : Colors.grey,
                ),
                title: const Text(
                  '自定义图表',
                  style: TextStyle(color: Colors.white),
                ),
                subtitle: const Text(
                  '高性能原生图表，支持丰富的技术指标',
                  style: TextStyle(color: Colors.grey, fontSize: 12),
                ),
                trailing: _useCustomChart
                    ? const Icon(Icons.check, color: Colors.blue)
                    : null,
                onTap: () {
                  setState(() {
                    _useCustomChart = true;
                  });
                  Navigator.of(context).pop();
                },
              ),
              const Divider(color: Colors.grey),
              ListTile(
                leading: Icon(
                  Icons.web,
                  color: !_useCustomChart ? Colors.blue : Colors.grey,
                ),
                title: const Text(
                  'TradingView 图表 (双接口)',
                  style: TextStyle(color: Colors.white),
                ),
                subtitle: Text(
                  _isDualApiEnabled
                      ? '专业图表 + 智能双接口数据加载'
                      : '专业的 TradingView 图表组件',
                  style: const TextStyle(color: Colors.grey, fontSize: 12),
                ),
                trailing: !_useCustomChart
                    ? const Icon(Icons.check, color: Colors.blue)
                    : null,
                onTap: () {
                  setState(() {
                    _useCustomChart = false;
                    // 🔑 切换到TradingView时启用双接口
                    if (_isDualApiEnabled) {
                      _initializeDualApiChart();
                    }
                  });
                  Navigator.of(context).pop();
                },
              ),
              const Divider(color: Colors.grey),
              // 🔑 双接口模式开关
              SwitchListTile(
                title: const Text(
                  '双接口模式',
                  style: TextStyle(color: Colors.white),
                ),
                subtitle: const Text(
                  '智能选择最优数据接口',
                  style: TextStyle(color: Colors.grey, fontSize: 12),
                ),
                value: _isDualApiEnabled,
                activeColor: Colors.blue,
                onChanged: (value) {
                  setState(() {
                    _isDualApiEnabled = value;
                    if (value) {
                      _initializeDualApiChart();
                    }
                  });
                },
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('取消', style: TextStyle(color: Colors.grey)),
            ),
          ],
        );
      },
    );
  }

  // 显示时间段选择弹窗
  void _showTimeIntervalDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: const Color(0xFF1E1E1E),
          title: const Text('选择时间段', style: TextStyle(color: Colors.white)),
          content: Container(
            width: double.maxFinite,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text(
                  '请选择图表的时间间隔',
                  style: TextStyle(color: Colors.grey, fontSize: 14),
                ),
                const SizedBox(height: 16),
                // 使用网格布局的时间段选择器
                TimeIntervalGridSelector(
                  selectedInterval: _currentInterval,
                  onIntervalChanged: (interval, timeFrame) {
                    _changeTimeInterval(interval, timeFrame: timeFrame);
                    Navigator.of(context).pop(); // 选择后关闭弹窗
                  },
                ),
                const SizedBox(height: 16),
                const Divider(color: Colors.grey),
                const SizedBox(height: 8),
                // 或者使用下拉菜单版本
                Row(
                  children: [
                    const Text(
                      '快速选择: ',
                      style: TextStyle(color: Colors.white, fontSize: 14),
                    ),
                    Expanded(
                      child: TimeIntervalDropdownSelector(
                        selectedInterval: _currentInterval,
                        onIntervalChanged: (interval, timeFrame) {
                          _changeTimeInterval(interval, timeFrame: timeFrame);
                          Navigator.of(context).pop(); // 选择后关闭弹窗
                        },
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('取消', style: TextStyle(color: Colors.grey)),
            ),
          ],
        );
      },
    );
  }

  // 根据选择构建对应的图表组件
  Widget _buildSelectedChart() {
    developer.log('📊 构建图表组件:');
    developer.log('  - 使用自定义图表: $_useCustomChart');
    developer.log('  - 双接口启用: $_isDualApiEnabled');
    developer.log('  - 图表ID: $_chartId');

    if (_useCustomChart) {
      developer.log('📊 构建自定义图表组件');
      // 使用自定义图表
      return _buildCustomChart();
    } else {
      developer.log('📊 构建TradingView图表组件');
      // 使用 TradingView 图表
      return Container(
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey, width: 1),
          borderRadius: BorderRadius.circular(4),
        ),
        child: TradingChartWidget(
          key: tradingChartKey,
          onVisibleRangeChanged: _isDualApiEnabled
              ? _handleDualApiChartRangeChange
              : _handleTraditionalChartRangeChange,
        ),
      );
    }
  }

  // 构建自定义图表
  Widget _buildCustomChart() {
    if (_klineData.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(color: Colors.blue),
            SizedBox(height: 16),
            Text('加载图表数据中...', style: TextStyle(color: Colors.grey)),
          ],
        ),
      );
    }

    return CompositeChart(
      data: _convertToKLineData(_klineData),
      config: ChartConfig(
        theme: ChartTheme.professional(),
        showGrid: true,
        showCrosshair: true,
        showVolume: true,
        showIndicators: false,
        enableZoom: true,
        enablePan: true,
        enableRealTime: true,
        animationConfig: AnimationConfig(
          enableAnimations: true,
          duration: Duration(milliseconds: 300),
        ),
        gestureConfig: GestureConfig(
          enableDoubleTapZoom: true,
          enablePinchZoom: true,
          enableLongPressCrosshair: true,
          enablePanGesture: true,
        ),
      ),
      height: 300,
      showVolume: true,
      showIndicators: _selectedListIndicator.isNotEmpty,
      layoutConfig: const ChartLayoutConfig(
        showToolbar: false,
        showInfoPanel: false,
      ),
      onDataSelected: (data) {
        if (data != null) {
          developer.log('选中K线数据: ${data.timestamp} - ${data.close}');
        }
      },
      onZoomChanged: (zoom) {
        developer.log('缩放级别: $zoom');
      },
      onPanChanged: (pan) {
        developer.log('平移偏移: $pan');
      },
    );
  }

  // 转换业务数据为图表数据
  List<KLineData> _convertToKLineData(List<KLineBusinessData> businessData) {
    return businessData
        .map(
          (data) => KLineData(
            timestamp: data.timestamp,
            open: data.open,
            high: data.high,
            low: data.low,
            close: data.close,
            time: _formatTimeForCurrentInterval(data.timestamp),
            amount: data.amount,
          ),
        )
        .toList();
  }

  // 根据当前时间段格式化时间（为TradingView优化）
  String _formatTimeForCurrentInterval(DateTime timestamp) {
    switch (_currentTimeFrame) {
      case '1s':
      case '1m':
      case '5m':
      case '15m':
      case '30m':
      case '1h':
      case '4h':
        // 🔧 对于分钟/小时级别，TradingView需要时间戳（秒）
        return (timestamp.millisecondsSinceEpoch ~/ 1000).toString();
      case '1d':
      case '1w':
        // 🔧 对于日/周级别，TradingView需要日期字符串 YYYY-MM-DD
        return DateUtils.formatDate(timestamp, format: 'yyyy-MM-dd');
      case '1M':
        // 🔧 对于月级别，TradingView需要日期字符串 YYYY-MM-DD（使用月初）
        return DateUtils.formatDate(timestamp, format: 'yyyy-MM-dd');
      default:
        // 🔧 默认使用时间戳（秒）
        return (timestamp.millisecondsSinceEpoch ~/ 1000).toString();
    }
  }

  // 构建实时价格信息
  Widget _buildPriceInfo() {
    if (_currentPrice == null) {
      return Container(
        height: 20,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: Row(
          children: [
            Text(
              _currentSymbol,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(width: 16),
            const Text(
              '加载中...',
              style: TextStyle(color: Colors.grey, fontSize: 12),
            ),
          ],
        ),
      );
    }

    return Container(
      height: 20,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        children: [
          Text(
            _currentSymbol,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 14,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(width: 16),
          Text(
            '${_currentPrice!.price.toStringAsFixed(2)}',
            style: TextStyle(
              color: _currentPrice!.priceColor,
              fontSize: 14,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(width: 8),
          Text(
            '${_currentPrice!.priceChangePercent >= 0 ? '+' : ''}${_currentPrice!.priceChangePercent.toStringAsFixed(2)}%',
            style: TextStyle(color: _currentPrice!.priceColor, fontSize: 12),
          ),
          const Spacer(),
          Text(
            '24h Vol: ${(_currentPrice!.volume / 1000000).toStringAsFixed(1)}M',
            style: const TextStyle(color: Colors.grey, fontSize: 10),
          ),
        ],
      ),
    );
  }

  final Color appBackgroundColor = const Color(0xFF121212); // 使用一个具体的深色

  @override
  Widget build(BuildContext context) {
    return AnnotatedRegion(
      value: const SystemUiOverlayStyle(
        statusBarColor: Colors.black,
        statusBarIconBrightness: Brightness.light,
        systemNavigationBarColor: Colors.black,
        systemNavigationBarIconBrightness: Brightness.dark,
      ),
      child: Scaffold(
        backgroundColor: appBackgroundColor,
        body: Scaffold(
          backgroundColor: appBackgroundColor,
          appBar: AppBar(
            backgroundColor: appBackgroundColor,
            leading: null,
            automaticallyImplyLeading: false,
            title: Align(
              alignment: Alignment.centerLeft,
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  GestureDetector(
                    onTap: () => context.pop(),
                    child: const Padding(
                      padding: EdgeInsets.only(right: 6.0),
                      child: Icon(
                        Icons.arrow_back_ios,
                        size: 16,
                        color: Colors.white,
                      ),
                    ),
                  ),
                  const Text(
                    'BTCUSDT 永续',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(width: 4),
                  const Icon(Icons.arrow_drop_down, size: 20),
                ],
              ),
            ),
            titleTextStyle: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
            centerTitle: false,
            elevation: 0.0,
            actions: [
              IconButton(
                icon: const Icon(Icons.star_border, color: Colors.white),
                onPressed: () {},
              ),
              IconButton(
                icon: const Icon(
                  Icons.file_upload_outlined,
                  color: Colors.white,
                ),
                onPressed: () {},
              ),
              // 调试入口
              IconButton(
                icon: const Icon(Icons.bug_report, color: Colors.orange),
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const DebugMenuPage(),
                    ),
                  );
                },
                tooltip: '图表调试工具',
              ),
            ],
            bottom: PreferredSize(
              preferredSize: Size.fromHeight(30),
              child: Container(
                color: appBackgroundColor,
                child: TabBar(
                  // tabAlignment: TabAlignment.start,
                  controller: _topTabController,
                  isScrollable: false,
                  labelColor: Colors.white,
                  labelStyle: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                  unselectedLabelColor: Colors.grey,
                  indicatorColor: Colors.white,
                  indicatorSize: TabBarIndicatorSize.label,
                  indicator: const UnderlineTabIndicator(
                    borderSide: BorderSide(width: 3.0, color: Colors.white),
                    // 调整 insets，让指示器下移
                    // insets: EdgeInsets.only(bottom: 6.0),
                  ),
                  labelPadding: EdgeInsets.symmetric(horizontal: 8.0),
                  dividerColor: Colors.grey[800],
                  tabs: const [
                    Tab(text: '行情'),
                    Tab(text: '概况'),
                    Tab(text: '数据'),
                    Tab(text: '动态'),
                    Tab(text: '交易'),
                    Tab(text: '跟单'),
                    Tab(text: '策略'),
                  ],
                ),
              ),
            ),
          ),
          body: BlocConsumer<MarketBloc, MarketState>(
            listener: (context, state) {
              // 🔑 详细调试：检查BlocConsumer是否被触发
              AppLogger.logModule(
                moduleName,
                LogLevel.info,
                '🔔 BlocConsumer listener被触发，状态类型: ${state.runtimeType}',
              );

              // 🔑 双接口数据处理
              if (state is KlineDataLoaded2) {
                AppLogger.logModule(
                  moduleName,
                  LogLevel.info,
                  '✅ 确认进入KlineDataLoaded2分支',
                );
                AppLogger.logModule(
                  moduleName,
                  LogLevel.info,
                  '📊 收到KlineDataLoaded2数据: ${state.klineData.length}个数据点',
                );

                // 🔑 详细调试：检查原始数据格式
                AppLogger.logModule(
                  moduleName,
                  LogLevel.info,
                  '📊 原始数据类型: ${state.klineData.runtimeType}',
                );
                if (state.klineData.isNotEmpty) {
                  AppLogger.logModule(
                    moduleName,
                    LogLevel.info,
                    '📊 第一个原始数据项: ${state.klineData.first}',
                  );
                  AppLogger.logModule(
                    moduleName,
                    LogLevel.info,
                    '📊 第一个数据项类型: ${state.klineData.first.runtimeType}',
                  );
                }

                try {
                  AppLogger.logModule(
                    moduleName,
                    LogLevel.info,
                    '🔄 开始数据处理...',
                  );

                  // 🔑 智能数据处理 - 检查数据类型并相应处理
                  List<KLineData> data;

                  if (state.klineData.isNotEmpty) {
                    final firstItem = state.klineData.first;
                    AppLogger.logModule(
                      moduleName,
                      LogLevel.info,
                      '📊 检测到数据类型: ${firstItem.runtimeType}',
                    );

                    if (firstItem is KLineData) {
                      // 数据已经是KLineData类型，直接使用
                      AppLogger.logModule(
                        moduleName,
                        LogLevel.info,
                        '✅ 数据已经是KLineData类型，直接使用',
                      );
                      data = state.klineData.cast<KLineData>();
                    } else if (firstItem is LineDataModel) {
                      // 数据是LineDataModel类型，需要转换为KLineData
                      AppLogger.logModule(
                        moduleName,
                        LogLevel.info,
                        '🔄 数据是LineDataModel类型，开始转换...',
                      );
                      data = state.klineData
                          .map((item) {
                            final lineData = item as LineDataModel;
                            final timestamp =
                                DateTime.fromMillisecondsSinceEpoch(
                                  lineData.time,
                                );

                            return KLineData(
                              timestamp: timestamp,
                              time: lineData.time
                                  .toString(), // LineDataModel.time是int类型
                              open: lineData.open,
                              high: lineData
                                  .hight, // 注意：LineDataModel中是hight，不是high
                              low: lineData.low,
                              close: lineData.close,
                              volume: null, // LineDataModel没有volume字段
                            );
                          })
                          .cast<KLineData>()
                          .toList();
                    } else if (firstItem is Map<String, dynamic>) {
                      // 数据是Map格式，需要转换
                      AppLogger.logModule(
                        moduleName,
                        LogLevel.info,
                        '🔄 数据是Map格式，开始转换...',
                      );
                      data = state.klineData.map((item) {
                        AppLogger.logModule(
                          moduleName,
                          LogLevel.debug,
                          '📊 处理数据项: $item (类型: ${item.runtimeType})',
                        );

                        if (item is Map<String, dynamic>) {
                          // 🔑 安全的类型转换
                          final timestampValue = item['timestamp'];
                          DateTime timestamp;

                          if (timestampValue is int) {
                            timestamp = DateTime.fromMillisecondsSinceEpoch(
                              timestampValue,
                            );
                          } else if (timestampValue is String) {
                            // 如果是字符串，尝试解析为int
                            final timestampInt = int.tryParse(timestampValue);
                            if (timestampInt != null) {
                              timestamp = DateTime.fromMillisecondsSinceEpoch(
                                timestampInt,
                              );
                            } else {
                              // 如果解析失败，尝试作为ISO字符串解析
                              timestamp =
                                  DateTime.tryParse(timestampValue) ??
                                  DateTime.now();
                            }
                          } else {
                            timestamp = DateTime.now();
                          }

                          // 🔧 安全的类型转换，处理time字段可能是int或String的情况
                          String timeValue;
                          final timeField = item['time'];
                          if (timeField is String) {
                            timeValue = timeField;
                          } else if (timeField is int) {
                            timeValue = timeField.toString();
                          } else if (timeField is num) {
                            timeValue = timeField.toString();
                          } else {
                            // 如果time字段不存在或格式不正确，使用时间戳生成
                            timeValue = timestamp.millisecondsSinceEpoch
                                .toString();
                            AppLogger.logModule(
                              moduleName,
                              LogLevel.warning,
                              '⚠️ time字段类型不正确，使用时间戳: ${timeField?.runtimeType}',
                            );
                          }

                          final klineData = KLineData(
                            time: timeValue,
                            open: (item['open'] as num).toDouble(),
                            high: (item['high'] as num).toDouble(),
                            low: (item['low'] as num).toDouble(),
                            close: (item['close'] as num).toDouble(),
                            timestamp: timestamp,
                            volume: (item['volume'] as num?)?.toDouble() ?? 0.0,
                          );
                          AppLogger.logModule(
                            moduleName,
                            LogLevel.debug,
                            '📊 转换后的KLineData: ${klineData.toString()}',
                          );
                          return klineData;
                        } else {
                          AppLogger.logModule(
                            moduleName,
                            LogLevel.error,
                            '❌ 数据格式错误: 期望Map<String, dynamic>，实际${item.runtimeType}',
                          );
                          throw FormatException(
                            'Expected Map<String, dynamic>, got ${item.runtimeType}',
                          );
                        }
                      }).toList();
                    } else {
                      AppLogger.logModule(
                        moduleName,
                        LogLevel.error,
                        '❌ 未知数据格式: ${firstItem.runtimeType}',
                      );
                      throw FormatException(
                        'Unsupported data type: ${firstItem.runtimeType}',
                      );
                    }
                  } else {
                    AppLogger.logModule(
                      moduleName,
                      LogLevel.warning,
                      '⚠️ 数据列表为空',
                    );
                    data = [];
                  }

                  AppLogger.logModule(
                    moduleName,
                    LogLevel.info,
                    '✅ 数据处理完成，共${data.length}条数据',
                  );

                  if (data.isNotEmpty) {
                    AppLogger.logModule(
                      moduleName,
                      LogLevel.info,
                      '📊 第一个数据点: time=${data.first.time}, open=${data.first.open}',
                    );
                    AppLogger.logModule(
                      moduleName,
                      LogLevel.info,
                      '📊 最后一个数据点: time=${data.last.time}, close=${data.last.close}',
                    );
                  }
                  AppLogger.logModule(
                    moduleName,
                    LogLevel.debug,
                    '📊 当前时间格式: $_currentTimeFrame',
                  );

                  // 🔑 详细调试：检查图表状态
                  AppLogger.logModule(moduleName, LogLevel.info, '📊 图表状态检查:');
                  AppLogger.logModule(
                    moduleName,
                    LogLevel.info,
                    '  - 双接口启用: $_isDualApiEnabled',
                  );
                  AppLogger.logModule(
                    moduleName,
                    LogLevel.info,
                    '  - 使用自定义图表: $_useCustomChart',
                  );
                  AppLogger.logModule(
                    moduleName,
                    LogLevel.info,
                    '  - 图表ID: $_chartId',
                  );
                  AppLogger.logModule(
                    moduleName,
                    LogLevel.info,
                    '  - TradingChartWidget状态: ${tradingChartKey.currentState != null}',
                  );

                  // 🔑 强制更新状态变量以确保图表显示数据
                  setState(() {
                    // 将KLineData转换为KLineBusinessData
                    _klineData = data
                        .map(
                          (item) => KLineBusinessData(
                            timestamp: item.timestamp,
                            open: item.open,
                            high: item.high,
                            low: item.low,
                            close: item.close,
                            volume: item.volume ?? 0.0,
                            amount: (item.volume ?? 0.0) * item.close,
                            isComplete: true,
                          ),
                        )
                        .toList();
                  });
                  developer.log(
                    '📊 已更新_klineData状态变量，数据量: ${_klineData.length}',
                  );

                  // 🔑 如果启用双接口模式，通过ChartAPI更新数据
                  if (_isDualApiEnabled &&
                      !_useCustomChart &&
                      _chartId != null) {
                    developer.log('📊 使用双接口模式更新图表');

                    try {
                      // 将数据转换为ChartData格式并通过ChartAPI发送
                      final chartData = data
                          .map((item) => item as ChartData)
                          .toList();
                      developer.log('📊 双接口模式详情:');
                      developer.log(
                        '  - 转换为ChartData格式，数据量: ${chartData.length}',
                      );
                      developer.log('  - 图表ID: $_chartId');

                      if (chartData.isNotEmpty) {
                        developer.log(
                          '  - 第一条ChartData: ${chartData.first.toString()}',
                        );
                        developer.log(
                          '  - 最后一条ChartData: ${chartData.last.toString()}',
                        );
                      }

                      developer.log('📊 调用ChartAPI.updateChart()...');
                      ChartAPI.instance.updateChart(_chartId!, chartData);

                      // 缓存初始数据
                      _chartDataCache = data;

                      developer.log(
                        '✅ ChartAPI.updateChart()调用完成，数据量: ${chartData.length}',
                      );
                    } catch (e) {
                      developer.log('❌ 双接口数据更新失败: $e');
                      developer.log('❌ 错误堆栈: ${StackTrace.current}');
                      // 如果双接口失败，回退到传统方式
                      developer.log('📊 回退到传统方式更新图表');
                      _updateTraditionalChart(data);
                    }
                  } else {
                    AppLogger.logModule(
                      moduleName,
                      LogLevel.info,
                      '📊 使用传统方式更新图表',
                    );
                    AppLogger.logModule(
                      moduleName,
                      LogLevel.debug,
                      '📊 传统方式原因:',
                    );
                    AppLogger.logModule(
                      moduleName,
                      LogLevel.debug,
                      '  - 双接口启用: $_isDualApiEnabled',
                    );
                    AppLogger.logModule(
                      moduleName,
                      LogLevel.debug,
                      '  - 使用自定义图表: $_useCustomChart',
                    );
                    AppLogger.logModule(
                      moduleName,
                      LogLevel.debug,
                      '  - 图表ID: $_chartId',
                    );
                    _updateTraditionalChart(data);
                  }
                } catch (e, stackTrace) {
                  AppLogger.logModule(
                    moduleName,
                    LogLevel.error,
                    '❌ 数据转换失败: $e',
                  );
                  AppLogger.logModule(
                    moduleName,
                    LogLevel.error,
                    '❌ 错误堆栈: $stackTrace',
                  );
                }
              } else if (state is TimeIntervalChanged) {
                developer.log(
                  '📊 收到TimeIntervalChanged: ${state.interval} -> ${state.timeFrame}',
                );
                final data = state.klineData.cast<KLineData>();
                developer.log('📊 数据点数量: ${data.length}');
                if (data.isNotEmpty) {
                  developer.log(
                    '📊 第一个数据点: time=${data.first.time}, open=${data.first.open}',
                  );
                  developer.log(
                    '📊 最后一个数据点: time=${data.last.time}, close=${data.last.close}',
                  );
                }

                // 🔑 双接口模式处理时间段切换
                if (_isDualApiEnabled && !_useCustomChart && _chartId != null) {
                  final chartData = data
                      .map((item) => item as ChartData)
                      .toList();
                  ChartAPI.instance.updateChart(_chartId!, chartData);

                  // 更新缓存数据
                  _chartDataCache = data;

                  developer.log('📊 双接口时间段切换完成，数据量: ${chartData.length}');
                } else {
                  tradingChartKey.currentState?.setData(
                    data,
                    timeFrame: state.timeFrame,
                  );
                  developer.log('📊 时间段切换完成');
                }
              } else if (state is KlineDataLoaded) {
                // 🔑 处理历史数据加载（接口2）- 图表滑动时触发
                AppLogger.logModule(
                  moduleName,
                  LogLevel.info,
                  '📊 收到历史数据: ${state.klineData.length}个数据点',
                );

                if (_isDualApiEnabled && !_useCustomChart && _chartId != null) {
                  // 双接口模式处理
                  AppLogger.logModule(
                    moduleName,
                    LogLevel.info,
                    '🔄 双接口模式处理历史数据',
                  );
                  final historicalData = state.klineData
                      .map((item) {
                        // 假设klineData是Map<String, dynamic>格式，需要转换为KLineData
                        if (item is Map<String, dynamic>) {
                          // 🔧 安全的类型转换，处理time字段可能是int或String的情况
                          String timeValue;
                          final timeField = item['time'];
                          if (timeField is String) {
                            timeValue = timeField;
                          } else if (timeField is int) {
                            timeValue = timeField.toString();
                          } else if (timeField is num) {
                            timeValue = timeField.toString();
                          } else {
                            // 如果time字段不存在或格式不正确，使用时间戳生成
                            final timestampValue = item['timestamp'];
                            if (timestampValue is String) {
                              final parsedTime = DateTime.tryParse(
                                timestampValue,
                              );
                              timeValue =
                                  parsedTime?.millisecondsSinceEpoch
                                      .toString() ??
                                  DateTime.now().millisecondsSinceEpoch
                                      .toString();
                            } else {
                              timeValue = DateTime.now().millisecondsSinceEpoch
                                  .toString();
                            }
                            AppLogger.logModule(
                              moduleName,
                              LogLevel.warning,
                              '⚠️ time字段类型不正确，使用时间戳: ${timeField?.runtimeType}',
                            );
                          }

                          // 🔧 安全的时间戳解析，处理不同类型的timestamp字段
                          DateTime parsedTimestamp;
                          final timestampField = item['timestamp'];
                          if (timestampField is String) {
                            parsedTimestamp =
                                DateTime.tryParse(timestampField) ??
                                DateTime.now();
                          } else if (timestampField is int) {
                            parsedTimestamp =
                                DateTime.fromMillisecondsSinceEpoch(
                                  timestampField,
                                );
                          } else if (timestampField is num) {
                            parsedTimestamp =
                                DateTime.fromMillisecondsSinceEpoch(
                                  timestampField.toInt(),
                                );
                          } else {
                            parsedTimestamp = DateTime.now();
                            AppLogger.logModule(
                              moduleName,
                              LogLevel.warning,
                              '⚠️ timestamp字段类型不正确，使用当前时间: ${timestampField?.runtimeType}',
                            );
                          }

                          return KLineData(
                            timestamp: parsedTimestamp,
                            open: (item['open'] as num).toDouble(),
                            high: (item['high'] as num).toDouble(),
                            low: (item['low'] as num).toDouble(),
                            close: (item['close'] as num).toDouble(),
                            time: timeValue,
                            volume: (item['volume'] as num?)?.toDouble(),
                          );
                        }
                        return item as KLineData;
                      })
                      .cast<KLineData>()
                      .toList();

                  // 合并历史数据到缓存中（避免重复）
                  final mergedData = _mergeHistoricalData(historicalData);

                  // 🔑 使用addHistoricalData方法，保持当前可见范围
                  final chartData = historicalData
                      .map((item) => item as ChartData)
                      .toList();
                  ChartAPI.instance.addHistoricalData(_chartId!, chartData);

                  AppLogger.logModule(
                    moduleName,
                    LogLevel.info,
                    '📊 双接口历史数据已合并，总数据量: ${mergedData.length}',
                  );
                } else {
                  // 🔑 传统模式处理历史数据
                  AppLogger.logModule(
                    moduleName,
                    LogLevel.info,
                    '🔄 传统模式处理历史数据',
                  );

                  try {
                    // 智能数据处理 - 与KlineDataLoaded2相同的逻辑
                    List<KLineData> data;

                    if (state.klineData.isNotEmpty) {
                      final firstItem = state.klineData.first;
                      AppLogger.logModule(
                        moduleName,
                        LogLevel.info,
                        '📊 历史数据类型: ${firstItem.runtimeType}',
                      );

                      if (firstItem is KLineData) {
                        data = state.klineData.cast<KLineData>();
                      } else if (firstItem is LineDataModel) {
                        // 数据是LineDataModel类型，需要转换为KLineData
                        AppLogger.logModule(
                          moduleName,
                          LogLevel.info,
                          '🔄 历史数据是LineDataModel类型，开始转换...',
                        );
                        data = state.klineData
                            .map((item) {
                              final lineData = item as LineDataModel;
                              final timestamp =
                                  DateTime.fromMillisecondsSinceEpoch(
                                    lineData.time,
                                  );

                              return KLineData(
                                timestamp: timestamp,
                                time: lineData.time
                                    .toString(), // LineDataModel.time是int类型
                                open: lineData.open,
                                high: lineData
                                    .hight, // 注意：LineDataModel中是hight，不是high
                                low: lineData.low,
                                close: lineData.close,
                                volume: null, // LineDataModel没有volume字段
                              );
                            })
                            .cast<KLineData>()
                            .toList();
                      } else if (firstItem is Map<String, dynamic>) {
                        data = state.klineData.map((item) {
                          final mapItem = item as Map<String, dynamic>;
                          final timestampValue = mapItem['timestamp'];
                          DateTime timestamp;

                          if (timestampValue is int) {
                            timestamp = DateTime.fromMillisecondsSinceEpoch(
                              timestampValue,
                            );
                          } else if (timestampValue is String) {
                            final timestampInt = int.tryParse(timestampValue);
                            if (timestampInt != null) {
                              timestamp = DateTime.fromMillisecondsSinceEpoch(
                                timestampInt,
                              );
                            } else {
                              timestamp =
                                  DateTime.tryParse(timestampValue) ??
                                  DateTime.now();
                            }
                          } else {
                            timestamp = DateTime.now();
                          }

                          // 🔧 安全的类型转换，处理time字段可能是int或String的情况
                          String timeValue;
                          final timeField = mapItem['time'];
                          if (timeField is String) {
                            timeValue = timeField;
                          } else if (timeField is int) {
                            timeValue = timeField.toString();
                          } else if (timeField is num) {
                            timeValue = timeField.toString();
                          } else {
                            // 如果time字段不存在或格式不正确，使用时间戳生成
                            timeValue = timestamp.millisecondsSinceEpoch
                                .toString();
                            AppLogger.logModule(
                              moduleName,
                              LogLevel.warning,
                              '⚠️ time字段类型不正确，使用时间戳: ${timeField?.runtimeType}',
                            );
                          }

                          return KLineData(
                            time: timeValue,
                            open: (mapItem['open'] as num).toDouble(),
                            high: (mapItem['high'] as num).toDouble(),
                            low: (mapItem['low'] as num).toDouble(),
                            close: (mapItem['close'] as num).toDouble(),
                            timestamp: timestamp,
                            volume:
                                (mapItem['volume'] as num?)?.toDouble() ?? 0.0,
                          );
                        }).toList();
                      } else {
                        throw FormatException(
                          'Unsupported data type: ${firstItem.runtimeType}',
                        );
                      }
                    } else {
                      data = [];
                    }

                    AppLogger.logModule(
                      moduleName,
                      LogLevel.info,
                      '✅ 历史数据处理完成，共${data.length}条数据',
                    );

                    // 合并到现有数据并更新图表
                    if (data.isNotEmpty) {
                      final mergedData = _mergeHistoricalData(data);
                      AppLogger.logModule(
                        moduleName,
                        LogLevel.info,
                        '📊 历史数据已合并，总数据量: ${mergedData.length}',
                      );
                      // 🔑 对于历史数据，使用addHistoricalData方法
                      _addHistoricalDataToTraditionalChart(data);
                    }
                  } catch (e) {
                    AppLogger.logModule(
                      moduleName,
                      LogLevel.error,
                      '❌ 传统模式历史数据处理失败: $e',
                    );
                  }
                }
              }
            },
            builder: (context, state) {
              return _topTabBarView(state);
            },
          ),
        ),
      ),
    );
  }

  // =================================================================
  // =============== 以下是修改和优化的核心方法 ===============
  // =================================================================

  /// ### 核心修复：优化后的图表视图
  ///
  /// **修改点**：
  /// 1.  移除了内部错误的 `Expanded`，这是导致布局崩溃的直接原因。
  /// 2.  将 `TradingChartWidget` 直接包裹在 `Expanded` 中，使其能正确填充可用空间。
  /// 3.  为时间周期和指标选择器添加了交互逻辑。
  Widget _buildChartView(MarketState state) {
    // 定义时间周期和指标列表
    final timeIntervals = ['15分', '1时', '4时', '1日', '更多'];
    final indicators = ['VOL', 'MA', 'EMA', 'BOLL', 'SAR', 'MACD', 'KDJ'];

    return Column(
      children: [
        // 时间周期选择器 + 全屏按钮
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
          child: Row(
            children: [
              // 使用 Expanded 让时间选择器占据大部分空间，可以左右滚动
              Expanded(
                child: SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Row(
                    children: timeIntervals
                        .map(
                          (interval) => _buildTimeChip(
                            interval,
                            _selectedTime == interval,
                            () {
                              setState(() {
                                _selectedTime = interval;
                              });
                              // 当点击“更多”时，可以弹出一个菜单，这里暂时只更新状态
                              if (interval == '更多') {
                                _showTimeIntervalDialog();
                              } else {
                                _fetchKLineData(interval: interval);
                              }
                            },
                          ),
                        )
                        .toList(),
                  ),
                ),
              ),
              // WebSocket 连接状态指示器
              Container(
                width: 8,
                height: 8,
                decoration: BoxDecoration(
                  color: _isConnected ? Colors.green : Colors.red,
                  shape: BoxShape.circle,
                ),
              ),
              const SizedBox(width: 8),
              // 图表类型选择按钮
              IconButton(
                onPressed: _showChartTypeDialog,
                icon: Icon(
                  _useCustomChart ? Icons.candlestick_chart : Icons.web,
                  color: Colors.grey,
                ),
                tooltip: _useCustomChart ? '自定义图表' : 'TradingView图表',
              ),
              // 全屏按钮
              IconButton(
                onPressed: _enterFullScreenChart,
                icon: const Icon(Icons.fullscreen, color: Colors.grey),
              ),
            ],
          ),
        ),

        // --- 这是最关键的布局修复 ---
        // 将 TradingChartWidget 包裹在 Expanded 中，使其填充此 Column 中的所有剩余垂直空间
        // Expanded(child: TradingChartWidget(key: tradingChartKey)),
        Listener(
          onPointerDown: (event) {
            // 当用户开始触摸时，禁用滚动
            setState(() {
              developer.log("手指按下，禁止页面滚动");
              _scrollPhysics = const NeverScrollableScrollPhysics();
            });
          },
          onPointerUp: (event) {
            // 当用户停止触摸时，恢复滚动
            setState(() {
              developer.log("手指抬起，恢复页面滚动");
              _scrollPhysics = const AlwaysScrollableScrollPhysics();
            });
          },
          onPointerCancel: (event) {
            // 当手势取消时（例如电话打入），也恢复滚动
            setState(() {
              developer.log("手势取消，恢复页面滚动");
              _scrollPhysics = const AlwaysScrollableScrollPhysics();
            });
          },
          child: Column(
            children: [
              // 实时价格信息
              _buildPriceInfo(),
              // 图表
              SizedBox(height: 280, child: _buildSelectedChart()),
            ],
          ),
        ),
        // 指标选择器
        Container(
          color: Colors.black.withValues(alpha: 0.5),
          padding: const EdgeInsets.symmetric(vertical: 4.0),
          child: SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: indicators
                  .map(
                    (indicator) => _buildBottomIndicatorTab(
                      text: indicator,
                      isSelected: _selectedListIndicator.contains(indicator),
                      onTap: () => {
                        setState(() {
                          if (_selectedListIndicator.contains(indicator)) {
                            _selectedListIndicator.remove(indicator);
                          } else {
                            _selectedListIndicator.add(indicator);
                          }
                        }),
                        // 在这里可以添加调用 JS 来切换图表指标的逻辑
                        print('Selected indicator: $indicator'),
                      },
                    ),
                  )
                  .toList(),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildTimeChip(String text, bool isSelected, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 4.0),
        padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
        decoration: BoxDecoration(
          color: isSelected ? Colors.grey[850] : Colors.transparent,
          borderRadius: BorderRadius.circular(20.0),
        ),
        child: Text(
          text,
          style: TextStyle(
            color: isSelected ? Colors.white : Colors.grey,
            fontSize: 13,
          ),
        ),
      ),
    );
  }

  /// ### 构建资讯或公告栏
  Widget _buildAlertMessageSection() {
    // 在真实应用中，这条信息会从您的数据源获取，可能为 null
    const String alertMessage = 'Cork Protocol攻击者地址将1410枚ETH转移至Tornado...';

    // 如果没有信息，则不显示任何内容
    if (alertMessage.isEmpty) {
      return const SizedBox.shrink(); // 返回一个零尺寸的 widget
    }

    return GestureDetector(
      onTap: () {
        // 在这里可以处理点击事件，例如跳转到新闻详情页
        print('Alert message tapped!');
      },
      child: Container(
        // color: Colors.red,
        padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 0.0),
        child: Row(
          children: [
            // 左侧的小图标
            const Icon(Icons.campaign_outlined, color: Colors.white, size: 20),
            const SizedBox(width: 8),
            // 使用 Expanded 包裹 Text，使其能自动换行并填充剩余空间
            Expanded(
              child: Text(
                alertMessage,
                style: const TextStyle(color: Colors.white, fontSize: 13),
                overflow: TextOverflow.ellipsis, // 如果文字太长，用省略号代替
                maxLines: 1, // 只显示一行
              ),
            ),
            const Icon(Icons.close_rounded, color: Colors.white, size: 14),
          ],
        ),
      ),
    );
  }

  /// ### 构建顶部价格信息区
  /// 负责显示“最新价格”和“24小时最高/最低”等数据。
  Widget _buildTopInfoSection() {
    return Container(
      padding: const EdgeInsets.all(16.0),
      color: appBackgroundColor,
      child: Column(
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 左侧价格信息
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        const Text(
                          '最新价格',
                          style: TextStyle(color: Colors.grey, fontSize: 12),
                        ),
                        Icon(
                          Icons.arrow_drop_down,
                          color: Colors.grey,
                          size: 16,
                        ),
                      ],
                    ),
                    // const SizedBox(height: 8),
                    const Text(
                      '¥2,419.42',
                      style: TextStyle(
                        color: Color.fromARGB(255, 187, 75, 100),
                        fontSize: 26,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    // const SizedBox(height: 4),
                    Row(
                      children: [
                        Text(
                          '≈\$2,419.42',
                          style: TextStyle(color: Colors.white, fontSize: 14),
                        ),
                        const SizedBox(width: 8),
                        const Text(
                          '-0.72%',
                          style: TextStyle(
                            color: Color.fromARGB(255, 187, 75, 100),
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                    Row(
                      children: const [
                        Text(
                          '标记价格',
                          style: TextStyle(color: Colors.grey, fontSize: 12),
                        ),
                        SizedBox(width: 8),
                        Text(
                          '2,419.37',
                          style: TextStyle(color: Colors.white, fontSize: 12),
                        ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: const [
                        Icon(
                          Icons.local_fire_department,
                          color: Color.fromARGB(255, 242, 179, 66),
                          size: 16,
                        ),
                        Text(
                          'No.1 | 主流币 | Layer 1',
                          style: TextStyle(
                            color: Color.fromARGB(255, 242, 179, 66),
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              // const Spacer(), // 使用 Spacer 占据中间空间，将右侧内容推到最右边
              // 右侧数据统计
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    SizedBox(height: 20),
                    _hourTitleRow('24小时最高', '2,423.00'),
                    SizedBox(height: 4),
                    _hourTitleRow('24小时最低', '2,396.25'),
                    SizedBox(height: 4),
                    _hourTitleRow('24小时量(BTC)', '453.51万'),
                    SizedBox(height: 4),
                    _hourTitleRow('24小时量(USDT)', '1.10亿'),
                  ],
                ),
              ),
            ],
          ),

          // const SizedBox(height: 16),
        ],
      ),
    );
  }

  Widget _hourTitleRow(String title, String value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(title, style: TextStyle(color: Colors.grey, fontSize: 12)),
        Text(value, style: TextStyle(color: Colors.white, fontSize: 12)),
      ],
    );
  }

  Widget _buildBottomIndicatorTab({
    required String text,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 4.0),
        child: Column(
          children: [
            Text(
              text,
              style: TextStyle(color: isSelected ? Colors.white : Colors.grey),
            ),
          ],
        ),
      ),
    );
  }

  Widget _topTabBarView(MarketState state) {
    return TabBarView(
      controller: _topTabController,
      physics: _scrollPhysics,
      children: [
        Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                physics: _scrollPhysics,
                child: Column(
                  children: [
                    // --- 区域 1: 顶部固定信息区 ---
                    _buildTopInfoSection(),
                    // --- 新增区域: 资讯公告栏 ---
                    _buildAlertMessageSection(),
                    // --- 区域 2: 图表视图和数据选项卡 ---
                    _buildChartView(state),
                    // _buildDataTabBar(),
                    // _buildDataTabBarView(),
                  ],
                ),
              ),
            ),

            // // --- 区域 2: 图表视图和数据选项卡 ---
            // Expanded(flex: 6, child: _buildChartView()),
            // _buildDataTabBar(),
            // Expanded(flex: 4, child: _buildDataTabBarView()),
            _buildBottomTradeBar(),
          ],
        ),
        const Center(
          child: Text('概览内容', style: TextStyle(color: Colors.white)),
        ),
        const Center(
          child: Text('数据内容', style: TextStyle(color: Colors.white)),
        ),
        const Center(
          child: Text('动态内容', style: TextStyle(color: Colors.white)),
        ),
        const Center(
          child: Text('交易内容', style: TextStyle(color: Colors.white)),
        ),
        const Center(
          child: Text('跟单内容', style: TextStyle(color: Colors.white)),
        ),
        const Center(
          child: Text('策略内容', style: TextStyle(color: Colors.white)),
        ),
      ],
    );
  }

  Widget _buildDataTabBar() {
    return Container(
      color: appBackgroundColor,
      height: 40,
      child: TabBar(
        controller: _bottomDataTabController,
        labelColor: Colors.white,
        unselectedLabelColor: Colors.grey,
        indicatorColor: Colors.white,
        indicatorSize: TabBarIndicatorSize.label,
        labelStyle: const TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
        tabs: const [
          Tab(text: '订单'),
          Tab(text: '深度图'),
          Tab(text: '最新成交'),
          Tab(text: '交易规则'),
        ],
      ),
    );
  }

  Widget _buildDataTabBarView() {
    return TabBarView(
      controller: _bottomDataTabController,
      children: [
        _buildOrderBook(),
        const Center(
          child: Text('深度图内容', style: TextStyle(color: Colors.white)),
        ),
        const Center(
          child: Text('最新成交内容', style: TextStyle(color: Colors.white)),
        ),
        const Center(
          child: Text('交易规则内容', style: TextStyle(color: Colors.white)),
        ),
      ],
    );
  }

  // _buildOrderBook 方法的实现非常棒，使用了 ListView.builder，性能很好，无需修改。
  // 它能正常工作的原因是，它的父级 TabBarView -> Expanded(flex:4) 已经为其提供了明确的、有限的高度。
  Widget _buildOrderBook() {
    final buyOrders = List.generate(
      20,
      (i) => {'price': 2416.80 - i, 'amount': (i + 1) * 0.5},
    );
    final sellOrders = List.generate(
      20,
      (i) => {'price': 2416.81 + i, 'amount': (i + 1) * 0.6},
    );
    return Container(
      color: appBackgroundColor,
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      child: Row(
        children: [
          Expanded(
            child: ListView.builder(
              itemCount: buyOrders.length,
              itemBuilder: (context, index) {
                /* ... */
              },
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: ListView.builder(
              itemCount: sellOrders.length,
              itemBuilder: (context, index) {
                /* ... */
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomTradeBar() {
    return Container(
      color: const Color.fromARGB(255, 26, 26, 26),
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 16.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          _buildTradeButton(),
          _buildTradeIconButton(icon: Icons.view_timeline_outlined, text: '网格'),
          _buildTradeIconButton(
            icon: Icons.flip_camera_android_sharp,
            text: '现货',
          ),
          _buildTradeIconButton(
            icon: Icons.notification_add_outlined,
            text: '预警',
          ),
        ],
      ),
    );
  }

  Widget _buildTradeButton() {
    return Container(
      width: 130,
      height: 36,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(30.0),
      ),
      child: TextButton(
        onPressed: () {},
        child: const Text(
          '交易',
          style: TextStyle(
            color: Colors.black,
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  Widget _buildTradeIconButton({required IconData icon, required String text}) {
    return Column(
      children: [
        Icon(icon, color: Colors.white, size: 22),
        Text(text, style: const TextStyle(color: Colors.grey, fontSize: 10)),
      ],
    );
  }
}
