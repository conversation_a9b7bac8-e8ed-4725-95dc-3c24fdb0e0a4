import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:financial_app_market/src/data/models/market_depth_model.dart';
import 'package:financial_app_core/financial_app_core.dart'; // Import LoadingIndicator

// 注意：这里仅为示例，实际市场深度图展示需要集成专业的图表库。
// 由于图表集成较复杂，这里仅用简单的列表来展示买卖盘数据。

class MarketDepthPage extends StatefulWidget {
  final String symbol;
  const MarketDepthPage({super.key, required this.symbol});

  @override
  State<MarketDepthPage> createState() => _MarketDepthPageState();
}

class _MarketDepthPageState extends State<MarketDepthPage> {
  @override
  void initState() {
    super.initState();
    // 页面加载时订阅市场深度
    // WidgetsBinding.instance.addPostFrameCallback((_) {
    //   Provider.of<MarketProvider>(
    //     context,
    //     listen: false,
    //   ).subscribeToMarketDepth(widget.symbol);
    // });
  }

  @override
  void dispose() {
    // 页面销毁时取消订阅
    // Provider.of<MarketProvider>(
    //   context,
    //   listen: false,
    // ).unsubscribeFromMarketDepth();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('${widget.symbol} 市场深度')),
      body: SizedBox(height: 1),

      // Consumer<MarketProvider>(
      //   builder: (context, marketProvider, child) {
      //     if (marketProvider.isLoading) {
      //       return const Center(child: LoadingIndicator(message: '加载市场深度...'));
      //     }
      //     if (marketProvider.errorMessage != null) {
      //       return Center(
      //         child: Text(
      //           '错误: ${marketProvider.errorMessage}',
      //           style: TextStyle(color: Theme.of(context).colorScheme.error),
      //         ),
      //       );
      //     }
      //     if (marketProvider.currentMarketDepth == null ||
      //         (marketProvider.currentMarketDepth!.bids.isEmpty &&
      //             marketProvider.currentMarketDepth!.asks.isEmpty)) {
      //       return const Center(child: Text('没有市场深度数据。'));
      //     }

      //     final depth = marketProvider.currentMarketDepth!;
      //     final bids = depth.bids.reversed.toList(); // 买盘通常从高到低排列
      //     final asks = depth.asks; // 卖盘通常从低到高排列

      //     return Column(
      //       children: [
      //         Padding(
      //           padding: const EdgeInsets.all(8.0),
      //           child: Text(
      //             '最新更新ID: ${depth.lastUpdateId}',
      //             style: const TextStyle(fontSize: 12, color: Colors.grey),
      //           ),
      //         ),
      //         Expanded(
      //           child: Row(
      //             children: [
      //               // 买盘 (Bids)
      //               Expanded(
      //                 child: Column(
      //                   children: [
      //                     const Text(
      //                       '买盘 (Bids)',
      //                       style: TextStyle(
      //                         fontWeight: FontWeight.bold,
      //                         color: Colors.green,
      //                       ),
      //                     ),
      //                     const Divider(),
      //                     Expanded(
      //                       child: ListView.builder(
      //                         itemCount: bids.length,
      //                         itemBuilder: (context, index) {
      //                           final entry = bids[index];
      //                           return Padding(
      //                             padding: const EdgeInsets.symmetric(
      //                               vertical: 4.0,
      //                               horizontal: 8.0,
      //                             ),
      //                             child: Row(
      //                               mainAxisAlignment:
      //                                   MainAxisAlignment.spaceBetween,
      //                               children: [
      //                                 Text(
      //                                   entry.price.toStringAsFixed(
      //                                     widget.symbol.endsWith('USDT')
      //                                         ? 2
      //                                         : 4,
      //                                   ),
      //                                   style: const TextStyle(
      //                                     color: Colors.green,
      //                                     fontSize: 14,
      //                                   ),
      //                                 ),
      //                                 Text(
      //                                   entry.quantity.toStringAsFixed(8),
      //                                   style: const TextStyle(fontSize: 14),
      //                                 ),
      //                               ],
      //                             ),
      //                           );
      //                         },
      //                       ),
      //                     ),
      //                   ],
      //                 ),
      //               ),
      //               const VerticalDivider(width: 1),
      //               // 卖盘 (Asks)
      //               Expanded(
      //                 child: Column(
      //                   children: [
      //                     const Text(
      //                       '卖盘 (Asks)',
      //                       style: TextStyle(
      //                         fontWeight: FontWeight.bold,
      //                         color: Colors.red,
      //                       ),
      //                     ),
      //                     const Divider(),
      //                     Expanded(
      //                       child: ListView.builder(
      //                         itemCount: asks.length,
      //                         itemBuilder: (context, index) {
      //                           final entry = asks[index];
      //                           return Padding(
      //                             padding: const EdgeInsets.symmetric(
      //                               vertical: 4.0,
      //                               horizontal: 8.0,
      //                             ),
      //                             child: Row(
      //                               mainAxisAlignment:
      //                                   MainAxisAlignment.spaceBetween,
      //                               children: [
      //                                 Text(
      //                                   entry.quantity.toStringAsFixed(8),
      //                                   style: const TextStyle(fontSize: 14),
      //                                 ),
      //                                 Text(
      //                                   entry.price.toStringAsFixed(
      //                                     widget.symbol.endsWith('USDT')
      //                                         ? 2
      //                                         : 4,
      //                                   ),
      //                                   style: const TextStyle(
      //                                     color: Colors.red,
      //                                     fontSize: 14,
      //                                   ),
      //                                 ),
      //                               ],
      //                             ),
      //                           );
      //                         },
      //                       ),
      //                     ),
      //                   ],
      //                 ),
      //               ),
      //             ],
      //           ),
      //         ),
      //       ],
      //     );
      //   },
      // ),
    );
  }
}
