import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import 'package:financial_app_market/src/data/models/market_ticker_model.dart';
import 'package:financial_app_core/financial_app_core.dart'; // Import LoadingIndicator

class MarketOverviewPage extends StatefulWidget {
  const MarketOverviewPage({super.key});

  @override
  State<MarketOverviewPage> createState() => _MarketOverviewPageState();
}

class _MarketOverviewPageState extends State<MarketOverviewPage> {
  @override
  void initState() {
    super.initState();
    // 页面加载时获取所有行情数据
    // WidgetsBinding.instance.addPostFrameCallback((_) {
    //   Provider.of<MarketProvider>(
    //     context,
    //     listen: false,
    //   ).fetchAllMarketTickers();
    // });
  }

  Future<void> _refreshTickers() async {
    // await Provider.of<MarketProvider>(
    //   context,
    //   listen: false,
    // ).fetchAllMarketTickers();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('市场总览'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _refreshTickers,
          ),
        ],
      ),
      body: SizedBox(height: 1),

      // Consumer<MarketProvider>(
      //   builder: (context, marketProvider, child) {
      //     if (marketProvider.isLoading && marketProvider.allTickers.isEmpty) {
      //       return const Center(child: LoadingIndicator(message: '加载市场数据...'));
      //     }
      //     if (marketProvider.errorMessage != null) {
      //       return Center(
      //         child: Text(
      //           '错误: ${marketProvider.errorMessage}',
      //           style: TextStyle(color: Theme.of(context).colorScheme.error),
      //         ),
      //       );
      //     }
      //     if (marketProvider.allTickers.isEmpty) {
      //       return const Center(child: Text('没有可用的市场数据。'));
      //     }
      //     return ListView.builder(
      //       padding: const EdgeInsets.all(8.0),
      //       itemCount: marketProvider.allTickers.length,
      //       itemBuilder: (context, index) {
      //         final ticker = marketProvider.allTickers[index];
      //         final bool isPriceUp = ticker.priceChange > 0;
      //         final Color priceColor = isPriceUp
      //             ? Colors.green
      //             : (ticker.priceChange < 0 ? Colors.red : Colors.grey);

      //         return Card(
      //           margin: const EdgeInsets.symmetric(
      //             vertical: 8.0,
      //             horizontal: 4.0,
      //           ),
      //           shape: RoundedRectangleBorder(
      //             borderRadius: BorderRadius.circular(12.0),
      //           ),
      //           elevation: 3,
      //           child: InkWell(
      //             onTap: () {
      //               // 点击进入K线图页面
      //               GoRouter.of(context).push('/market/kline/${ticker.symbol}');
      //             },
      //             borderRadius: BorderRadius.circular(12.0),
      //             child: Padding(
      //               padding: const EdgeInsets.all(16.0),
      //               child: Row(
      //                 mainAxisAlignment: MainAxisAlignment.spaceBetween,
      //                 children: [
      //                   Column(
      //                     crossAxisAlignment: CrossAxisAlignment.start,
      //                     children: [
      //                       Text(
      //                         ticker.symbol,
      //                         style: const TextStyle(
      //                           fontWeight: FontWeight.bold,
      //                           fontSize: 18,
      //                         ),
      //                       ),
      //                       const SizedBox(height: 4),
      //                       Text(
      //                         '成交量: ${ticker.volume.toStringAsFixed(2)}',
      //                         style: const TextStyle(
      //                           fontSize: 12,
      //                           color: Colors.grey,
      //                         ),
      //                       ),
      //                     ],
      //                   ),
      //                   Column(
      //                     crossAxisAlignment: CrossAxisAlignment.end,
      //                     children: [
      //                       Text(
      //                         ticker.lastPrice.toStringAsFixed(
      //                           ticker.symbol.endsWith('USDT') ? 2 : 8,
      //                         ),
      //                         style: TextStyle(
      //                           fontWeight: FontWeight.bold,
      //                           fontSize: 18,
      //                           color: priceColor,
      //                         ),
      //                       ),
      //                       const SizedBox(height: 4),
      //                       Text(
      //                         '${ticker.priceChange > 0 ? '+' : ''}${ticker.priceChange.toStringAsFixed(2)} (${ticker.priceChangePercent.toStringAsFixed(2)}%)',
      //                         style: TextStyle(fontSize: 14, color: priceColor),
      //                       ),
      //                     ],
      //                   ),
      //                 ],
      //               ),
      //             ),
      //           ),
      //         );
      //       },
      //     );
      //   },
      // ),
    );
  }
}
