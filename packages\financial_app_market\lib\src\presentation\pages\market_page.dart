import 'package:financial_app_core/financial_app_core.dart';
import 'package:financial_app_market/src/presentation/widgets/CustomSearchDelegate.dart';
import 'package:financial_app_market/src/presentation/widgets/MarketHeaderInfo.dart';
import 'package:financial_app_market/src/presentation/widgets/RefreshableCryptoList.dart';
import 'package:financial_app_market/src/presentation/widgets/StickyDelegate.dart';
import 'package:financial_app_market/src/presentation/widgets/StickyTabBarDelegate.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:financial_app_assets/financial_app_assets.dart';
import 'dart:math' as math;

// --- App主框架，包含底部导航栏 ---
class MarketPage extends StatelessWidget {
  const MarketPage({super.key});

  @override
  Widget build(BuildContext context) {
    // 在这里可以添加逻辑来切换不同的主页面
    // 当前只显示 MarketScreen
    return const Scaffold(
      body: MarketScreen(),
      // bottomNavigationBar: CustomBottomNavBar(),
    );
  }
}

// --- 市场页面 (核心代码) ---
class MarketScreen extends StatefulWidget {
  const MarketScreen({super.key});

  @override
  State<MarketScreen> createState() => _MarketScreenState();
}

class _MarketScreenState extends State<MarketScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  // 2. 创建一个 TextEditingController 来管理输入框的文本
  final _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  // direction: 1 表示向右切换，-1 表示向左切换
  void _changeOuterTab(int direction) {
    print('direction:$direction');

    int newIndex = _tabController.index + direction;
    print('newIndex:$newIndex');
    // 边界检查，防止越界
    if (newIndex >= 0 && newIndex < _tabController.length) {
      _tabController.animateTo(newIndex);
    }
  }

  @override
  Widget build(BuildContext context) {
    return BaseScaffold(
      statusBarColor: Colors.white,
      statusBarIconBrightness: Brightness.dark,
      systemNavigationBarColor: Colors.white,
      systemNavigationBarIconBrightness: Brightness.light,
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        toolbarHeight: 0, // Hide default app bar
      ),
      body: SafeArea(
        child: Column(
          children: [
            // --- 顶部固定区域 ---
            _buildSearchBar(),
            _buildTabHeader(),
            // --- 可切换的内容区域 ---
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  MarketTabContent(onSwipeOut: _changeOuterTab),

                  // 第二个 Tab: 动态 (占位)
                  const Center(
                    child: Text('动态页面内容', style: TextStyle(fontSize: 24)),
                  ),

                  // 第三个 Tab: 牛人榜 (占位)
                  const Center(
                    child: Text('牛人榜页面内容', style: TextStyle(fontSize: 24)),
                  ),
                ],
              ),
            ),

            // --- 市场头部信息的小部件 ---
          ],
        ),
      ),
    );
  }

  // 4. 构建搜索输入框 Widget
  // Widget _buildSearchBar() {
  //   // 使用 SizedBox 来固定输入框的高度
  //   return SizedBox(
  //     height: 40,
  //     child: Padding(
  //       padding: const EdgeInsets.symmetric(horizontal: 15.0),
  //       child: TextField(
  //         controller: _searchController, // 关联 controller
  //         // 用户输入文本的样式
  //         style: const TextStyle(fontSize: 16, color: Colors.black),
  //         // 光标颜色
  //         cursorColor: Colors.grey,
  //         // 这是关键：配置输入框的装饰(外观)
  //         decoration: InputDecoration(
  //           // 提示文字（占位符）
  //           hintText: '🔥 SOL 当前热搜',
  //           // 提示文字的样式
  //           hintStyle: const TextStyle(fontSize: 16, color: Colors.black54),

  //           // 在输入框内部左侧添加搜索图标
  //           prefixIcon: const Icon(Icons.search, color: Colors.black54),

  //           // 设置背景色
  //           filled: true,
  //           fillColor: const Color(0xFFF5F5F5),

  //           // 设置边框样式，实现圆角效果并移除默认的下划线
  //           border: OutlineInputBorder(
  //             borderRadius: BorderRadius.circular(20.0),
  //             borderSide: BorderSide.none, // 移除边框线
  //           ),

  //           // 设置内容内边距，让文本和图标看起来更协调
  //           contentPadding: const EdgeInsets.symmetric(vertical: 0),
  //         ),
  //         // 当用户提交输入时（例如点击键盘上的完成按钮）会触发
  //         onSubmitted: (value) {
  //           // 在这里处理搜索逻辑
  //           print('用户搜索了: $value');
  //           // 可以弹出一个提示框来显示搜索内容
  //           ScaffoldMessenger.of(
  //             context,
  //           ).showSnackBar(SnackBar(content: Text('正在搜索: $value')));
  //         },
  //       ),
  //     ),
  //   );
  // }

  Widget _buildSearchBar() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 10),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 8),
        decoration: BoxDecoration(
          color: Colors.grey[100],
          borderRadius: BorderRadius.circular(25),
        ),
        child: const Row(
          children: [
            Icon(Icons.search, color: Colors.grey),
            SizedBox(width: 8),
            Text(
              'SPK 新市上线',
              style: TextStyle(color: Colors.grey, fontSize: 16),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTabHeader() {
    return TabBar(
      controller: _tabController, // 临时的
      tabs: const [
        Tab(text: '市场'),
        Tab(text: '动态'),
        Tab(text: '牛人榜'),
      ],
      padding: const EdgeInsets.symmetric(horizontal: 8),
      tabAlignment: TabAlignment.start,
      isScrollable: true,
      labelPadding: EdgeInsets.symmetric(horizontal: 10),
      labelStyle: TextStyle(
        fontSize: 22,
        fontWeight: FontWeight.bold,
        color: Colors.black,
      ),
      overlayColor: WidgetStateProperty.all(Colors.transparent),
      dividerColor: Colors.grey[200],
      unselectedLabelColor: Colors.grey,
      indicatorColor: Colors.grey,
      indicatorSize: TabBarIndicatorSize.label,
      indicator: const UnderlineTabIndicator(
        borderSide: BorderSide(width: 2.0, color: Colors.black),
        // 调整 insets，让指示器下移
        // insets: EdgeInsets.only(bottom: 6.0),
      ),
    );
  }
}

class MarketTabContent extends StatefulWidget {
  final void Function(int direction) onSwipeOut;

  const MarketTabContent({super.key, required this.onSwipeOut});

  @override
  State<MarketTabContent> createState() => _MarketTabContent();
}

class _MarketTabContent extends State<MarketTabContent>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  bool _isSwipeOutTriggered = false;

  @override
  void initState() {
    _tabController = TabController(length: 5, vsync: this);
    super.initState();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _onTap(BuildContext content) {
    print('点击');
    GoRouter.of(context).push('/market/kline');
  }

  @override
  Widget build(BuildContext context) {
    return NestedScrollView(
      headerSliverBuilder: (BuildContext context, bool innerBoxIsScrolled) {
        return <Widget>[
          SliverToBoxAdapter(child: MarketHeaderInfo()),

          SliverPersistentHeader(
            pinned: true,
            delegate: MainHeaderDelegate(
              child: Column(
                children: [
                  Container(
                    height: 45, // 设置TabBar的高度
                    color: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 8.0),
                    child: TabBar(
                      controller: _tabController,
                      tabs: [
                        _buildCustomTab('自选'),
                        _buildCustomTab('现货'),
                        _buildCustomTab('合约'),
                        _buildCustomTab('期权'),
                        _buildCustomTab('总览'),
                      ],
                      isScrollable: true,
                      padding: const EdgeInsets.symmetric(horizontal: 8.0),
                      tabAlignment: TabAlignment.center,
                      labelPadding: const EdgeInsets.symmetric(horizontal: 8.0),
                      dividerColor: Colors.transparent,
                      labelStyle: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: Colors.black,
                      ),
                      unselectedLabelStyle: const TextStyle(
                        fontWeight: FontWeight.w400,
                        color: Colors.grey,
                      ),
                      indicator: BoxDecoration(
                        color: Colors.grey[200],
                        borderRadius: BorderRadius.circular(20.0),
                      ),
                    ),
                  ),

                  _buildCoinListHeader(),
                ],
              ),
            ),
          ),
        ];
      },
      body: NotificationListener<ScrollNotification>(
        onNotification: (notification) {
          // 在拖动开始时，重置触发器
          if (notification is ScrollStartNotification) {
            _isSwipeOutTriggered = false;
          }

          // 判断是否是越界滚动通知
          if (notification is OverscrollNotification) {
            // 如果已经触发过，则直接返回，避免重复调用
            if (_isSwipeOutTriggered) return false;

            // notification.overscroll 是一个双精度值
            var overscroll = notification.overscroll;
            if (notification.overscroll > 5.0 &&
                _tabController.index == _tabController.length - 1) {
              widget.onSwipeOut(1); // 通知父级向右切换
              _isSwipeOutTriggered = true; // 标记已触发
            }
            // 在左边界（第一个页面）继续向右滑，overscroll 会是正数
            else if (notification.overscroll > 1.0 &&
                _tabController.index == 0) {
              widget.onSwipeOut(-1); // 通知父级向左切换
              _isSwipeOutTriggered = true; // 标记已触发
            }
          }

          // 必须返回 false，让原生滚动继续处理通知
          return false;
        },
        child: TabBarView(
          controller: _tabController,
          children: [
            RefreshableCryptoList(
              onTap: () {
                _onTap(context);
              },
            ),
            Text('现货', style: TextStyle(fontSize: 24)),
            const Center(child: Text('合约', style: TextStyle(fontSize: 24))),
            const Center(child: Text('期权', style: TextStyle(fontSize: 24))),
            const Center(child: Text('总览', style: TextStyle(fontSize: 24))),
          ],
        ),
      ),
    );
  }

  // --- 辅助方法，用于创建带样式的 Tab ---
  Widget _buildCustomTab(String text) {
    return Tab(
      child: Container(
        // 这个 margin 就是 Tab 之间的间距
        margin: const EdgeInsets.symmetric(horizontal: 0.0),
        // 这是 Tab 内部的 padding，控制文字和背景边缘的距离
        padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 2.0),
        child: Center(child: Text(text)),
      ),
    );
  }

  Widget _buildCoinListHeader() {
    return Container(
      color: Colors.white,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 2),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                Text(
                  '名称',
                  style: TextStyle(color: Colors.grey[600], fontSize: 10),
                ),
                SizedBox(
                  height: 23, // 这个高度决定了两个图标最终的总高度
                  width: 18, // 宽度和图标大小保持一致
                  child: Stack(
                    children: [
                      // 顶部的、向上指的箭头
                      Align(
                        alignment: Alignment.topCenter,
                        child: Transform.rotate(
                          angle: math.pi,
                          child: const Icon(
                            Icons.arrow_drop_down,
                            color: Colors.grey,
                            size: 18,
                          ),
                        ),
                      ),
                      // 底部的、向下指的箭头
                      Align(
                        alignment: Alignment.bottomCenter,
                        child: const Icon(
                          Icons.arrow_drop_down,
                          color: Colors.grey,
                          size: 18,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 5),
                Text(
                  '成交额',
                  style: TextStyle(color: Colors.grey[600], fontSize: 10),
                ),
                SizedBox(
                  height: 23, // 这个高度决定了两个图标最终的总高度
                  width: 18, // 宽度和图标大小保持一致
                  child: Stack(
                    children: [
                      // 顶部的、向上指的箭头
                      Align(
                        alignment: Alignment.topCenter,
                        child: Transform.rotate(
                          angle: math.pi,
                          child: const Icon(
                            Icons.arrow_drop_down,
                            color: Colors.grey,
                            size: 18,
                          ),
                        ),
                      ),
                      // 底部的、向下指的箭头
                      Align(
                        alignment: Alignment.bottomCenter,
                        child: const Icon(
                          Icons.arrow_drop_down,
                          color: Colors.grey,
                          size: 18,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            Row(
              children: [
                Text(
                  '最新价',
                  style: TextStyle(color: Colors.grey[600], fontSize: 10),
                ),
                SizedBox(
                  height: 23, // 这个高度决定了两个图标最终的总高度
                  width: 18, // 宽度和图标大小保持一致
                  child: Stack(
                    children: [
                      // 顶部的、向上指的箭头
                      Align(
                        alignment: Alignment.topCenter,
                        child: Transform.rotate(
                          angle: math.pi,
                          child: const Icon(
                            Icons.arrow_drop_down,
                            color: Colors.grey,
                            size: 18,
                          ),
                        ),
                      ),
                      // 底部的、向下指的箭头
                      Align(
                        alignment: Alignment.bottomCenter,
                        child: const Icon(
                          Icons.arrow_drop_down,
                          color: Colors.grey,
                          size: 18,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 20),
                Text(
                  '今日涨跌',
                  style: TextStyle(color: Colors.grey[600], fontSize: 10),
                ),
                SizedBox(
                  height: 23, // 这个高度决定了两个图标最终的总高度
                  width: 18, // 宽度和图标大小保持一致
                  child: Stack(
                    children: [
                      // 顶部的、向上指的箭头
                      Align(
                        alignment: Alignment.topCenter,
                        child: Transform.rotate(
                          angle: math.pi,
                          child: const Icon(
                            Icons.arrow_drop_down,
                            color: Colors.grey,
                            size: 18,
                          ),
                        ),
                      ),
                      // 底部的、向下指的箭头
                      Align(
                        alignment: Alignment.bottomCenter,
                        child: const Icon(
                          Icons.arrow_drop_down,
                          color: Colors.grey,
                          size: 18,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

class MainHeaderDelegate extends SliverPersistentHeaderDelegate {
  final Widget child;

  MainHeaderDelegate({required this.child});

  final double totalHeight = 75;

  @override
  double get minExtent => totalHeight;

  @override
  double get maxExtent => totalHeight;

  @override
  Widget build(
    BuildContext context,
    double shrinkOffset,
    bool overlapsContent,
  ) {
    // 直接返回我们组合好的 Column
    return child;
  }

  @override
  bool shouldRebuild(MainHeaderDelegate oldDelegate) {
    return child != oldDelegate.child;
  }
}
