import 'package:financial_app_market/src/presentation/widgets/CustomSearchDelegate.dart';
import 'package:financial_app_market/src/presentation/widgets/MainHeaderDelegate.dart';
import 'package:financial_app_market/src/presentation/widgets/MarketHeaderInfo.dart';
import 'package:financial_app_market/src/presentation/widgets/RefreshableCryptoList.dart';
import 'package:financial_app_market/src/presentation/widgets/StickyDelegate.dart';
import 'package:financial_app_market/src/presentation/widgets/StickyTabBarDelegate.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:financial_app_assets/financial_app_assets.dart';

class MarketPageOld extends StatefulWidget {
  const MarketPageOld({super.key});

  @override
  State<MarketPageOld> createState() => _MarketPageState();
}

class _MarketPageState extends State<MarketPageOld>
    with TickerProviderStateMixin {
  int _selectedIndex = 1; // Market tab is selected
  String _selectedTab = '合约'; // Default selected tab
  late TabController _tabController;
  final List<String> _tabs = ['全部', '主流币', '新币种', '人工智能', 'Solana', 'MEME'];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: _tabs.length, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _onItemTapped(int index) {
    setState(() {
      _selectedIndex = index;
    });
  }

  void _onTap(BuildContext content) {
    print('点击');
    GoRouter.of(context).push('/market/kline');
  }

  @override
  Widget build(BuildContext context) {
    return AnnotatedRegion(
      value: const SystemUiOverlayStyle(
        statusBarColor: Colors.white, // 状态栏背景色
        statusBarIconBrightness: Brightness.dark, // 状态栏图标颜色
        systemNavigationBarColor: Colors.white, // 导航栏背景色
        systemNavigationBarIconBrightness: Brightness.dark, // 导航栏图标颜色
      ),
      child: Scaffold(
        backgroundColor: Colors.white,
        appBar: AppBar(
          backgroundColor: Colors.white,
          elevation: 0,
          toolbarHeight: 0, // Hide default app bar
        ),
        body: NestedScrollView(
          headerSliverBuilder: (BuildContext context, bool innerBoxIsScrolled) {
            return <Widget>[
              SliverPersistentHeader(
                pinned: true,
                delegate: MainHeaderDelegate(
                  child: Container(
                    color: Colors.white,
                    child: Column(
                      children: [
                        // 1. 顶部的状态栏
                        _buildSearchBar(),
                        // 2. 搜索框
                        TabBar(
                          // 这个 TabBar 是假的，只用于显示在 AppBar 中
                          controller: TabController(
                            length: 3,
                            vsync: this,
                          ), // 临时的
                          tabs: const [
                            Tab(text: '市场'),
                            Tab(text: '动态'),
                            Tab(text: '牛人榜'),
                          ],
                          padding: const EdgeInsets.symmetric(horizontal: 8),
                          tabAlignment: TabAlignment.start,
                          isScrollable: true,
                          labelPadding: EdgeInsets.symmetric(horizontal: 10),
                          labelStyle: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.black,
                          ),
                          unselectedLabelColor: Colors.grey,
                          indicatorColor: Colors.grey,
                          indicatorSize: TabBarIndicatorSize.label,
                          indicator: const UnderlineTabIndicator(
                            borderSide: BorderSide(
                              width: 2.0,
                              color: Colors.black,
                            ),
                            // 调整 insets，让指示器下移
                            // insets: EdgeInsets.only(bottom: 6.0),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              // 1. 顶部的 AppBar，对应 "市场"、"动态"、"牛人榜"
              // SliverAppBar(
              //   // title: const Text(
              //   //   '市场',
              //   //   style: TextStyle(fontWeight: FontWeight.bold),
              //   // ),
              //   backgroundColor: Colors.white,
              //   elevation: 0,
              //   pinned: true, // 让 AppBar 在上滑时固定在顶部
              //   floating: false, // 下滑时 AppBar 会立即出现
              //   // forceElevated: innerBoxIsScrolled, // 当内部列表滚动时，给 AppBar 添加阴影
              //   // title: _buildSearchBar(),
              //   titleSpacing: 0,
              //   toolbarHeight: 0, // 因为没有 title，所以 toolbar 高度为0
              //   bottom: PreferredSize(
              //     preferredSize: Size.fromHeight(65.h),
              //     child: Column(
              //       children: [
              //         Container(
              //           color: Colors.white,
              //           child: ,
              //         ),
              //       ],
              //     ),
              //   ),
              // ),
              // 2. 页面上方的其他内容，例如您截图中的市值、成交额等
              SliverToBoxAdapter(child: MarketHeaderInfo()),
              // 3. 实现吸顶效果的关键：SliverPersistentHeader
              //    对应您图中的红色框部分 (自选、现货、合约...)
              SliverPersistentHeader(
                delegate: StickyDelegate(
                  child: Container(
                    color: Colors.white,
                    child: Column(
                      children: [
                        _buildTradeTypeTabs(),
                        _buildSubTradeTypeTabs(),
                        _buildCoinListHeader(),
                      ],
                    ),
                  ),
                  height: 125, // 设置吸顶区域的高度
                ),
                pinned: true, // 这是让 TabBar 吸顶的关键属性
              ),
            ];
          },
          body: TabBarView(
            controller: _tabController,
            // 每个 TabBarView 的子项都是一个可刷新的列表
            // 对应您图中的绿色框部分
            children: _tabs.map((String name) {
              return RefreshableCryptoList(
                tabName: name,
                onTap: () {
                  _onTap(context);
                },
              );
            }).toList(),
          ),
        ),
      ),
    );
  }

  Widget _buildSearchBar() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 10),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 8),
        decoration: BoxDecoration(
          color: Colors.grey[100],
          borderRadius: BorderRadius.circular(25),
        ),
        child: const Row(
          children: [
            Icon(Icons.search, color: Colors.grey),
            SizedBox(width: 8),
            Text(
              'SPK 新市上线',
              style: TextStyle(color: Colors.grey, fontSize: 16),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTabBar() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 10),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          _buildTabItem('市场', true),
          _buildTabItem('动态', false),
          _buildTabItem('牛人榜', false),
          const SizedBox(
            width: 50,
          ), // For alignment, adjusting based on actual image
        ],
      ),
    );
  }

  Widget _buildTabItem(String title, bool isSelected) {
    return Column(
      children: [
        Text(
          title,
          style: TextStyle(
            fontSize: 18,
            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
            color: isSelected ? Colors.black : Colors.grey[600],
          ),
        ),
        if (isSelected)
          Container(
            margin: const EdgeInsets.only(top: 5),
            height: 3,
            width: 30,
            decoration: BoxDecoration(
              color: Colors.black,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
      ],
    );
  }

  Widget _buildTradeTypeTabs() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 10),
      child: Row(
        children: [
          _buildTradeTab('自选'),
          const SizedBox(width: 15),
          _buildTradeTab('现货'),
          const SizedBox(width: 15),
          _buildTradeTab('合约'), // Selected
          const SizedBox(width: 15),
          _buildTradeTab('期权'),
          const SizedBox(width: 15),
          _buildTradeTab('总览'),
        ],
      ),
    );
  }

  Widget _buildTradeTab(String title) {
    return GestureDetector(
      onTap: () {
        //切换tab
        setState(() {
          _selectedTab = title;
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: _selectedTab == title ? Colors.grey[200] : Colors.transparent,
          borderRadius: BorderRadius.circular(5),
        ),
        child: Text(
          title,
          style: TextStyle(
            color: _selectedTab == title ? Colors.black : Colors.grey[700],
            fontWeight: _selectedTab == title
                ? FontWeight.bold
                : FontWeight.normal,
          ),
        ),
      ),
    );
  }

  Widget _buildSubTradeTypeTabs() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 10),
      child: Row(
        children: [
          _buildSubTab('全部', true),
          const SizedBox(width: 15),
          _buildSubTab('主流币', false),
          const SizedBox(width: 15),
          _buildSubTab('新币种', false),
          const SizedBox(width: 15),
          _buildSubTab('人工智能', false),
          const SizedBox(width: 15),
          _buildSubTab('Solana', false),
          const Spacer(),
          const Icon(Icons.menu, color: Colors.grey),
        ],
      ),
    );
  }

  Widget _buildSubTab(String title, bool isSelected) {
    return Text(
      title,
      style: TextStyle(
        color: isSelected ? Colors.black : Colors.grey[700],
        fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
        fontSize: 13,
      ),
    );
  }

  Widget _buildCoinListHeader() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 5),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            children: [
              Text(
                '名称',
                style: TextStyle(color: Colors.grey[600], fontSize: 10),
              ),
              const Icon(Icons.arrow_drop_down, color: Colors.grey, size: 18),
              const SizedBox(width: 5),
              Text(
                '成交额',
                style: TextStyle(color: Colors.grey[600], fontSize: 10),
              ),
              const Icon(Icons.arrow_drop_down, color: Colors.grey, size: 18),
            ],
          ),
          Row(
            children: [
              Text(
                '最新价',
                style: TextStyle(color: Colors.grey[600], fontSize: 10),
              ),
              const Icon(Icons.arrow_drop_down, color: Colors.grey, size: 18),
              const SizedBox(width: 20),
              Text(
                '今日涨跌',
                style: TextStyle(color: Colors.grey[600], fontSize: 10),
              ),
              const Icon(Icons.arrow_drop_down, color: Colors.grey, size: 18),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildBottomNavBar() {
    return BottomNavigationBar(
      items: <BottomNavigationBarItem>[
        BottomNavigationBarItem(
          icon: _selectedIndex == 0
              ? Image.asset('assets/home_selected.png', height: 24, width: 24)
              : Image.asset(
                  'assets/home_unselected.png',
                  height: 24,
                  width: 24,
                ),
          label: '欧易',
        ),
        BottomNavigationBarItem(
          icon: _selectedIndex == 1
              ? Image.asset('assets/market_selected.png', height: 24, width: 24)
              : Image.asset(
                  'assets/market_unselected.png',
                  height: 24,
                  width: 24,
                ),
          label: '市场',
        ),
        BottomNavigationBarItem(
          icon: _selectedIndex == 2
              ? Image.asset('assets/trade_selected.png', height: 24, width: 24)
              : Image.asset(
                  'assets/trade_unselected.png',
                  height: 24,
                  width: 24,
                ),
          label: '交易',
        ),
        BottomNavigationBarItem(
          icon: _selectedIndex == 3
              ? Image.asset(
                  'assets/explore_selected.png',
                  height: 24,
                  width: 24,
                )
              : Image.asset(
                  'assets/explore_unselected.png',
                  height: 24,
                  width: 24,
                ),
          label: '探索',
        ),
        BottomNavigationBarItem(
          icon: _selectedIndex == 4
              ? Image.asset('assets/assets_selected.png', height: 24, width: 24)
              : Image.asset(
                  'assets/assets_unselected.png',
                  height: 24,
                  width: 24,
                ),
          label: '资产',
        ),
      ],
      currentIndex: _selectedIndex,
      selectedItemColor: Colors.black,
      unselectedItemColor: Colors.grey,
      onTap: _onItemTapped,
      type: BottomNavigationBarType.fixed,
      selectedLabelStyle: const TextStyle(fontSize: 12),
      unselectedLabelStyle: const TextStyle(fontSize: 12),
      showUnselectedLabels: true,
      backgroundColor: Colors.white,
      elevation: 5,
    );
  }
}
