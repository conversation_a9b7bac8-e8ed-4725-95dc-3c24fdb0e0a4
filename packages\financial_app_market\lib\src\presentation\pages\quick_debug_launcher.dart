import 'package:flutter/material.dart';
import 'debug_15m_chart_page.dart';

/// 快速调试启动器 - 直接启动15分钟图表调试页面
class QuickDebugLauncher extends StatelessWidget {
  const QuickDebugLauncher({super.key});

  @override
  Widget build(BuildContext context) {
    // 直接返回15分钟调试页面
    return const Debug15mChartPage();
  }
}

/// 调试应用入口 - 可以独立运行
class DebugApp extends StatelessWidget {
  const DebugApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: '图表调试工具',
      theme: ThemeData(
        primarySwatch: Colors.purple,
        useMaterial3: true,
      ),
      home: const QuickDebugLauncher(),
      debugShowCheckedModeBanner: false,
    );
  }
}

// 可以独立运行的调试入口
void main() {
  runApp(const DebugApp());
}
