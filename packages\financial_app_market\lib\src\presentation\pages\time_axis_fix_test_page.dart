import 'package:financial_app_market/financial_app_market.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:financial_trading_chart/financial_trading_chart.dart';
import 'package:financial_app_core/financial_app_core.dart';
import '../bloc/market_bloc.dart';

/// 时间轴修复测试页面
class TimeAxisFixTestPage extends StatefulWidget {
  const TimeAxisFixTestPage({super.key});

  @override
  State<TimeAxisFixTestPage> createState() => _TimeAxisFixTestPageState();
}

class _TimeAxisFixTestPageState extends State<TimeAxisFixTestPage> {
  final GlobalKey<TradingChartWidgetState> _chartKey = GlobalKey();
  String _currentTimeFrame = '15m';
  List<String> _logs = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _addLog('页面初始化');
    _loadInitialData();
  }

  void _addLog(String message) {
    setState(() {
      _logs.insert(
        0,
        '${DateTime.now().toString().substring(11, 19)} - $message',
      );
      if (_logs.length > 15) {
        _logs.removeLast();
      }
    });
    debugPrint('📝 $message');
  }

  void _loadInitialData() {
    _addLog('加载初始数据，时间格式: $_currentTimeFrame');
    setState(() {
      _isLoading = true;
    });

    context.read<MarketBloc>().add(
      GetKlineDataEvent2(symbol: 'test-symbol', bar: _currentTimeFrame),
    );
  }

  void _changeTimeFrame(String newTimeFrame) {
    if (_currentTimeFrame == newTimeFrame) return;

    _addLog('切换时间格式: $_currentTimeFrame -> $newTimeFrame');
    setState(() {
      _currentTimeFrame = newTimeFrame;
      _isLoading = true;
    });

    context.read<MarketBloc>().add(
      ChangeTimeIntervalEvent(
        symbol: 'test-symbol',
        interval: newTimeFrame,
        timeFrame: newTimeFrame,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('时间轴修复测试'),
        backgroundColor: Colors.green,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadInitialData,
          ),
        ],
      ),
      body: Column(
        children: [
          // 状态信息
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            color: Colors.green[50],
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      _isLoading ? Icons.hourglass_empty : Icons.check_circle,
                      color: _isLoading ? Colors.orange : Colors.green,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      _isLoading ? '加载中...' : '就绪',
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Text('当前时间格式: $_currentTimeFrame'),
                Text('预期时间轴格式: ${_getExpectedFormat(_currentTimeFrame)}'),
              ],
            ),
          ),

          // 时间格式选择
          Container(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  '选择时间格式测试',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),
                Wrap(
                  spacing: 8,
                  runSpacing: 8,
                  children: [
                    _buildTimeFrameChip('1s', '秒级'),
                    _buildTimeFrameChip('1m', '1分钟'),
                    _buildTimeFrameChip('5m', '5分钟'),
                    _buildTimeFrameChip('15m', '15分钟'),
                    _buildTimeFrameChip('30m', '30分钟'),
                    _buildTimeFrameChip('1h', '1小时'),
                    _buildTimeFrameChip('4h', '4小时'),
                    _buildTimeFrameChip('1d', '1日'),
                  ],
                ),
              ],
            ),
          ),

          // 图表区域
          Expanded(
            flex: 3,
            child: Container(
              margin: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.green, width: 2),
                borderRadius: BorderRadius.circular(8),
              ),
              child: BlocConsumer<MarketBloc, MarketState>(
                listener: (context, state) {
                  setState(() {
                    _isLoading = false;
                  });

                  if (state is KlineDataLoaded2) {
                    _addLog(
                      '✅ 收到KlineDataLoaded2: ${state.klineData.length}个数据点',
                    );
                    final data = state.klineData.cast<KLineData>();
                    if (data.isNotEmpty) {
                      _addLog('时间范围: ${data.first.time} ~ ${data.last.time}');
                    }

                    _chartKey.currentState?.setData(
                      data,
                      timeFrame: _currentTimeFrame,
                    );
                    _addLog('数据已设置到图表');
                  } else if (state is TimeIntervalChanged) {
                    _addLog('✅ 收到TimeIntervalChanged: ${state.timeFrame}');
                    final data = state.klineData.cast<KLineData>();
                    if (data.isNotEmpty) {
                      _addLog('时间范围: ${data.first.time} ~ ${data.last.time}');
                    }

                    _chartKey.currentState?.setData(
                      data,
                      timeFrame: state.timeFrame,
                    );
                    _addLog('时间段切换完成');
                  } else if (state is MarketError) {
                    _addLog('❌ 错误: ${state.message}');
                  } else if (state is MarketLoading) {
                    _addLog('⏳ 正在加载...');
                    setState(() {
                      _isLoading = true;
                    });
                  }
                },
                builder: (context, state) {
                  if (_isLoading) {
                    return const Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          CircularProgressIndicator(color: Colors.green),
                          SizedBox(height: 16),
                          Text('加载图表数据中...'),
                        ],
                      ),
                    );
                  }

                  return TradingChartWidget(key: _chartKey);
                },
              ),
            ),
          ),

          // 日志区域
          Expanded(
            flex: 1,
            child: Container(
              margin: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                children: [
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(8),
                    decoration: const BoxDecoration(
                      color: Colors.grey,
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(8),
                        topRight: Radius.circular(8),
                      ),
                    ),
                    child: const Text(
                      '操作日志',
                      style: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  Expanded(
                    child: ListView.builder(
                      padding: const EdgeInsets.all(8),
                      itemCount: _logs.length,
                      itemBuilder: (context, index) {
                        return Padding(
                          padding: const EdgeInsets.symmetric(vertical: 1),
                          child: Text(
                            _logs[index],
                            style: const TextStyle(
                              fontSize: 12,
                              fontFamily: 'monospace',
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
          ),

          // 说明信息
          Container(
            padding: const EdgeInsets.all(16),
            color: Colors.grey[100],
            child: const Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('测试说明：', style: TextStyle(fontWeight: FontWeight.bold)),
                SizedBox(height: 4),
                Text('1. 点击不同时间格式按钮测试时间轴显示', style: TextStyle(fontSize: 12)),
                Text('2. 观察图表底部时间轴是否显示正确格式', style: TextStyle(fontSize: 12)),
                Text('3. 查看操作日志了解数据流程', style: TextStyle(fontSize: 12)),
                Text('4. 如果时间轴仍不显示，请检查浏览器控制台', style: TextStyle(fontSize: 12)),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTimeFrameChip(String timeFrame, String label) {
    final isSelected = _currentTimeFrame == timeFrame;
    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (_) => _changeTimeFrame(timeFrame),
      selectedColor: Colors.green[200],
      checkmarkColor: Colors.green[800],
    );
  }

  String _getExpectedFormat(String timeFrame) {
    switch (timeFrame) {
      case '1s':
        return 'HH:mm:ss (如: 14:30:25)';
      case '1m':
      case '5m':
      case '15m':
      case '30m':
        return 'HH:mm (如: 14:30)';
      case '1h':
      case '4h':
        return 'MM-dd HH:mm (如: 01-01 14:30)';
      case '1d':
        return 'MM-dd (如: 01-01)';
      default:
        return '默认格式';
    }
  }
}
