import 'package:flutter/material.dart';
import '../widgets/time_interval_selector.dart';

/// 时间段选择弹窗测试页面
class TimeIntervalDialogTestPage extends StatefulWidget {
  const TimeIntervalDialogTestPage({super.key});

  @override
  State<TimeIntervalDialogTestPage> createState() => _TimeIntervalDialogTestPageState();
}

class _TimeIntervalDialogTestPageState extends State<TimeIntervalDialogTestPage> {
  String _currentInterval = '1m';
  String _currentTimeFrame = '1m';

  // 显示时间段选择弹窗
  void _showTimeIntervalDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: const Color(0xFF1E1E1E),
          title: const Text(
            '选择时间段',
            style: TextStyle(color: Colors.white),
          ),
          content: Container(
            width: double.maxFinite,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text(
                  '请选择图表的时间间隔',
                  style: TextStyle(color: Colors.grey, fontSize: 14),
                ),
                const SizedBox(height: 16),
                // 使用网格布局的时间段选择器
                TimeIntervalGridSelector(
                  selectedInterval: _currentInterval,
                  onIntervalChanged: (interval, timeFrame) {
                    setState(() {
                      _currentInterval = interval;
                      _currentTimeFrame = timeFrame;
                    });
                    Navigator.of(context).pop(); // 选择后关闭弹窗
                    _showSuccessMessage(interval, timeFrame);
                  },
                ),
                const SizedBox(height: 16),
                const Divider(color: Colors.grey),
                const SizedBox(height: 8),
                // 或者使用下拉菜单版本
                Row(
                  children: [
                    const Text(
                      '快速选择: ',
                      style: TextStyle(color: Colors.white, fontSize: 14),
                    ),
                    Expanded(
                      child: TimeIntervalDropdownSelector(
                        selectedInterval: _currentInterval,
                        onIntervalChanged: (interval, timeFrame) {
                          setState(() {
                            _currentInterval = interval;
                            _currentTimeFrame = timeFrame;
                          });
                          Navigator.of(context).pop(); // 选择后关闭弹窗
                          _showSuccessMessage(interval, timeFrame);
                        },
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text(
                '取消',
                style: TextStyle(color: Colors.grey),
              ),
            ),
          ],
        );
      },
    );
  }

  void _showSuccessMessage(String interval, String timeFrame) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          '已选择时间段: ${TimeIntervalUtils.getIntervalDisplayName(interval)} ($timeFrame)',
        ),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('时间段选择弹窗测试'),
        backgroundColor: Colors.blue,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 当前状态显示
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '当前选择的时间段',
                      style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    Text('间隔代码: $_currentInterval'),
                    Text('时间格式: $_currentTimeFrame'),
                    Text('显示名称: ${TimeIntervalUtils.getIntervalDisplayName(_currentInterval)}'),
                    Text('简短名称: ${TimeIntervalUtils.getIntervalShortName(_currentInterval)}'),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        const Text('类型: '),
                        if (TimeIntervalUtils.isSecondInterval(_currentInterval))
                          const Chip(label: Text('秒级'), backgroundColor: Colors.red),
                        if (TimeIntervalUtils.isMinuteInterval(_currentInterval))
                          const Chip(label: Text('分钟级'), backgroundColor: Colors.orange),
                        if (TimeIntervalUtils.isHourInterval(_currentInterval))
                          const Chip(label: Text('小时级'), backgroundColor: Colors.blue),
                        if (TimeIntervalUtils.isDayInterval(_currentInterval))
                          const Chip(label: Text('日级'), backgroundColor: Colors.green),
                      ],
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 24),

            // 测试按钮
            const Text(
              '测试功能',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),

            // 模拟"更多"按钮
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _showTimeIntervalDialog,
                icon: const Icon(Icons.more_horiz),
                label: const Text('点击"更多"按钮'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
              ),
            ),

            const SizedBox(height: 16),

            // 其他测试按钮
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      setState(() {
                        _currentInterval = '1s';
                        _currentTimeFrame = '1s';
                      });
                    },
                    child: const Text('设为1秒'),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      setState(() {
                        _currentInterval = '15m';
                        _currentTimeFrame = '15m';
                      });
                    },
                    child: const Text('设为15分'),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      setState(() {
                        _currentInterval = '1d';
                        _currentTimeFrame = '1d';
                      });
                    },
                    child: const Text('设为1日'),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 24),

            // 说明文档
            const Card(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '功能说明',
                      style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                    ),
                    SizedBox(height: 8),
                    Text('1. 点击"更多"按钮会弹出时间段选择弹窗'),
                    Text('2. 弹窗包含网格布局和下拉菜单两种选择方式'),
                    Text('3. 选择时间段后会自动关闭弹窗并显示成功消息'),
                    Text('4. 支持从1秒到1月的完整时间段范围'),
                    Text('5. 实时显示当前选择的时间段信息'),
                  ],
                ),
              ),
            ),

            const Spacer(),

            // 底部信息
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Column(
                children: [
                  Text(
                    '这个页面演示了在KlineTradingPage中点击"更多"按钮时',
                    style: TextStyle(fontSize: 12, color: Colors.grey),
                  ),
                  Text(
                    '显示TimeIntervalDropdownSelector的完整功能',
                    style: TextStyle(fontSize: 12, color: Colors.grey),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
