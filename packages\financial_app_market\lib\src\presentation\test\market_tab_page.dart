import 'package:flutter/material.dart';

class MarketTabPage extends StatefulWidget {
  final void Function(int direction) onSwipeOut;

  const MarketTabPage({super.key, required this.onSwipeOut});

  @override
  State<MarketTabPage> createState() => _MarketTabPageState();
}

class _MarketTabPageState extends State<MarketTabPage>
    with SingleTickerProviderStateMixin {
  late final TabController _marketTabController;
  bool _isSwipeOutTriggered = false;

  @override
  void initState() {
    super.initState();
    _marketTabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _marketTabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: <Widget>[
        Container(
          color: Colors.indigo,
          child: TabBar(
            controller: _marketTabController,
            tabs: const <Widget>[
              Tab(text: '自选'),
              Tab(text: '现货'),
              Tab(text: '合约'),
            ],
          ),
        ),
        Expanded(
          child: NotificationListener<ScrollNotification>(
            onNotification: (notification) {
              // 在拖动开始时，重置触发器
              if (notification is ScrollStartNotification) {
                _isSwipeOutTriggered = false;
              }

              // 判断是否是越界滚动通知
              if (notification is OverscrollNotification) {
                // 如果已经触发过，则直接返回，避免重复调用
                if (_isSwipeOutTriggered) return false;

                // notification.overscroll 是一个双精度值，表示“溢出”的滚动量
                // 在右边界（最后一个页面）继续向左滑，overscroll 会是负数
                var overscroll = notification.overscroll;
                print('notification.overscroll:$overscroll----');
                if (notification.overscroll > 5.0 &&
                    _marketTabController.index ==
                        _marketTabController.length - 1) {
                  print("✅ [Overscroll] 在'合约'页面继续向左滑动！");
                  widget.onSwipeOut(1); // 通知父级向右切换
                  _isSwipeOutTriggered = true; // 标记已触发
                }
                // 在左边界（第一个页面）继续向右滑，overscroll 会是正数
                else if (notification.overscroll > 1.0 &&
                    _marketTabController.index == 0) {
                  print("✅ [Overscroll] 在'自选'页面继续向右滑动！");
                  widget.onSwipeOut(-1); // 通知父级向左切换
                  _isSwipeOutTriggered = true; // 标记已触发
                }
              }

              // 必须返回 false，让原生滚动继续处理通知
              return false;
            },
            child: TabBarView(
              controller: _marketTabController,
              children: const <Widget>[
                Center(child: Text('自选列表', style: TextStyle(fontSize: 20))),
                Center(child: Text('现货行情', style: TextStyle(fontSize: 20))),
                Center(child: Text('合约数据', style: TextStyle(fontSize: 20))),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
