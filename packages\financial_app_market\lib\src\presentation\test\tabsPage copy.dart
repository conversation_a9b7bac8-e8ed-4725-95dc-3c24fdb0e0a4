// nested_tab_bar_page.dart

import 'package:flutter/material.dart';
import 'market_tab_page.dart';

class NestedTabBarPage extends StatefulWidget {
  const NestedTabBarPage({super.key});

  @override
  State<NestedTabBarPage> createState() => _NestedTabBarPageState();
}

class _NestedTabBarPageState extends State<NestedTabBarPage>
    with SingleTickerProviderStateMixin {
  late final TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  // 1. 定义一个方法来改变外层 Tab 的索引
  // direction: 1 表示向右切换，-1 表示向左切换
  void _changeOuterTab(int direction) {
    print('direction:$direction');

    int newIndex = _tabController.index + direction;
    print('newIndex:$newIndex');
    // 边界检查，防止越界
    if (newIndex >= 0 && newIndex < _tabController.length) {
      _tabController.animateTo(newIndex);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('联动滑动 TabBar'),
        bottom: TabBar(
          controller: _tabController,
          tabs: const <Widget>[
            Tab(text: '市场'),
            Tab(text: '牛人'),
            Tab(text: '动态'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: <Widget>[
          // 2. 将方法作为回调函数传递给 MarketTabPage
          MarketTabPage(onSwipeOut: _changeOuterTab),
          const Center(child: Text('牛人页面内容', style: TextStyle(fontSize: 24))),
          const Center(child: Text('动态页面内容', style: TextStyle(fontSize: 24))),
        ],
      ),
    );
  }
}
