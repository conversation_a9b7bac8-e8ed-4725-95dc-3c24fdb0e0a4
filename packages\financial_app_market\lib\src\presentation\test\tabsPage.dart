import 'package:flutter/material.dart';

// --- 数据模型 ---
class CryptoData {
  final String iconPath;
  final String name;
  final String pair;
  final double volume;
  final String volumeUnit;
  final double price;
  final double usdPrice;
  final double change;

  const CryptoData({
    required this.iconPath,
    required this.name,
    required this.pair,
    required this.volume,
    required this.volumeUnit,
    required this.price,
    required this.usdPrice,
    required this.change,
  });
}

// --- App主框架，包含底部导航栏 ---
class MainScreen1 extends StatelessWidget {
  const MainScreen1({super.key});

  @override
  Widget build(BuildContext context) {
    // 在这里可以添加逻辑来切换不同的主页面
    // 当前只显示 MarketScreen
    return const Scaffold(
      body: MarketScreen(),
      // bottomNavigationBar: CustomBottomNavBar(),
    );
  }
}

// --- 市场页面 (核心代码) ---
class MarketScreen extends StatefulWidget {
  const MarketScreen({super.key});

  @override
  State<MarketScreen> createState() => _MarketScreenState();
}

class _MarketScreenState extends State<MarketScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Column(
        children: [
          // --- 顶部固定区域 ---
          _buildStaticHeader(),

          // --- 可切换的内容区域 ---
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                // 第一个 Tab: 市场 (包含自己的可滚动内容和吸顶二级TabBar)
                MarketTabContent(),

                // 第二个 Tab: 动态 (占位)
                const Center(
                  child: Text('动态页面内容', style: TextStyle(fontSize: 24)),
                ),

                // 第三个 Tab: 牛人榜 (占位)
                const Center(
                  child: Text('牛人榜页面内容', style: TextStyle(fontSize: 24)),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // 构建顶部固定的搜索框和一级TabBar
  Widget _buildStaticHeader() {
    return Container(
      color: Colors.white, // 背景色确保不会透明
      child: Column(
        children: [
          // 搜索框
          Padding(
            padding: const EdgeInsets.fromLTRB(16.0, 16.0, 16.0, 8.0),
            child: Container(
              padding: const EdgeInsets.symmetric(
                horizontal: 16.0,
                vertical: 8.0,
              ),
              decoration: BoxDecoration(
                color: Colors.grey[200],
                borderRadius: BorderRadius.circular(30.0),
              ),
              child: Row(
                children: const [
                  Icon(Icons.search, color: Colors.grey),
                  SizedBox(width: 8),
                  Text('🔥', style: TextStyle(fontSize: 16)),
                  SizedBox(width: 4),
                  Text('SOL', style: TextStyle(fontWeight: FontWeight.bold)),
                  SizedBox(width: 8),
                  Text('当前热搜', style: TextStyle(color: Colors.grey)),
                ],
              ),
            ),
          ),
          // 一级 TabBar
          TabBar(
            controller: _tabController,
            tabs: const [
              Tab(text: '市场'),
              Tab(text: '动态'),
              Tab(text: '牛人榜'),
            ],
            labelStyle: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
            unselectedLabelStyle: const TextStyle(fontSize: 16),
            labelColor: Colors.black,
            unselectedLabelColor: Colors.grey[600],
            indicator: const UnderlineTabIndicator(
              borderSide: BorderSide(width: 3.0, color: Colors.black),
              insets: EdgeInsets.symmetric(horizontal: 40.0),
            ),
          ),
        ],
      ),
    );
  }
}

// --- 市场Tab的专属内容Widget ---
class MarketTabContent extends StatelessWidget {
  MarketTabContent({super.key});

  final List<CryptoData> cryptoList = [
    const CryptoData(
      iconPath: 'btc',
      name: 'BTC',
      pair: 'USDT',
      volume: 5.3,
      volumeUnit: '亿',
      price: 107332,
      usdPrice: 107378.2,
      change: 0.01,
    ),
    const CryptoData(
      iconPath: 'eth',
      name: 'ETH',
      pair: 'USDT',
      volume: 4.18,
      volumeUnit: '亿',
      price: 2442.59,
      usdPrice: 2443.64,
      change: 0.39,
    ),
    const CryptoData(
      iconPath: 'okb',
      name: 'OKB',
      pair: 'USDT',
      volume: 332.68,
      volumeUnit: '万',
      price: 49.95,
      usdPrice: 49.9715,
      change: -0.12,
    ),
    const CryptoData(
      iconPath: 'bnb',
      name: 'BNB',
      pair: 'USDT',
      volume: 447.87,
      volumeUnit: '万',
      price: 645.1,
      usdPrice: 645.38,
      change: 0.00,
    ),
    const CryptoData(
      iconPath: 'doge',
      name: 'DOGE',
      pair: 'USDT',
      volume: 5354.5,
      volumeUnit: '万',
      price: 0.16095,
      usdPrice: 0.16102,
      change: 0.54,
    ),
    const CryptoData(
      iconPath: 'xrp',
      name: 'XRP',
      pair: 'USDT',
      volume: 4450.5,
      volumeUnit: '万',
      price: 2.0901,
      usdPrice: 2.0910,
      change: -1.40,
    ),
    const CryptoData(
      iconPath: 'sol',
      name: 'SOL',
      pair: 'USDT',
      volume: 2130.5,
      volumeUnit: '万',
      price: 40.75,
      usdPrice: 40.76,
      change: -2.50,
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return CustomScrollView(
      slivers: [
        // Sliver 1: 市场总览卡片
        _buildSliverStatsCards(),

        // Sliver 2: 公告栏
        _buildSliverAnnouncementBar(),

        // Sliver 3: 二级 TabBar (吸顶)
        SliverPersistentHeader(
          pinned: true,
          delegate: _SliverTabBarDelegate(
            child: Container(
              color: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 8.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  _buildSecondaryTab('自选', isSelected: true),
                  _buildSecondaryTab('现货'),
                  _buildSecondaryTab('合约'),
                  _buildSecondaryTab('期权'),
                  _buildSecondaryTab('总览'),
                ],
              ),
            ),
            height: 50.0,
          ),
        ),

        // Sliver 4: 列表头
        SliverToBoxAdapter(
          child: Container(
            padding: const EdgeInsets.symmetric(
              horizontal: 16.0,
              vertical: 8.0,
            ),
            color: Colors.white,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                _buildListHeader('名称 / 成交额'),
                const Spacer(),
                _buildListHeader('最新价'),
                const SizedBox(width: 60),
                _buildListHeader('今日涨跌', alignment: TextAlign.right),
              ],
            ),
          ),
        ),

        // Sliver 5: 加密货币列表
        SliverList(
          delegate: SliverChildBuilderDelegate(
            (context, index) => CryptoListItem(data: cryptoList[index]),
            childCount: cryptoList.length,
          ),
        ),
      ],
    );
  }

  // --- 以下是 MarketTabContent 的辅助构建方法 ---
  Widget _buildSliverStatsCards() {
    return SliverToBoxAdapter(
      child: Padding(
        padding: const EdgeInsets.fromLTRB(16.0, 16.0, 16.0, 0),
        child: Row(
          children: [
            _buildStatsCard('市值', '\$3.40万亿', '-0.73%', false),
            const SizedBox(width: 12),
            _buildStatsCard('成交额', '\$853.12亿', '-14.38%', false),
            const SizedBox(width: 12),
            _buildStatsCard('市场占有率', '62.82%', 'Bitcoin', true),
          ],
        ),
      ),
    );
  }

  Widget _buildSliverAnnouncementBar() {
    return SliverToBoxAdapter(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
          decoration: BoxDecoration(
            color: Colors.grey[100],
            borderRadius: BorderRadius.circular(10.0),
          ),
          child: Row(
            children: const [
              Icon(Icons.campaign_outlined, color: Colors.grey),
              SizedBox(width: 8),
              Expanded(
                child: Text(
                  '非农业就业人数(美国)将于7月3日发布',
                  style: TextStyle(fontSize: 13),
                ),
              ),
              Icon(Icons.arrow_forward_ios, size: 14, color: Colors.grey),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatsCard(
    String title,
    String value,
    String change,
    bool isDominance,
  ) {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.all(16.0),
        decoration: BoxDecoration(
          color: Colors.grey[100],
          borderRadius: BorderRadius.circular(10.0),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: TextStyle(color: Colors.grey[600], fontSize: 13),
            ),
            const SizedBox(height: 8),
            Text(
              value,
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            isDominance
                ? Row(
                    children: const [
                      CircleAvatar(
                        backgroundColor: Colors.orange,
                        radius: 7,
                        child: Text(
                          'B',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 9,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      SizedBox(width: 4),
                      Text('Bitcoin', style: TextStyle(fontSize: 12)),
                    ],
                  )
                : Text(
                    change,
                    style: TextStyle(
                      color: change.startsWith('-') ? Colors.red : Colors.green,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
          ],
        ),
      ),
    );
  }

  Widget _buildSecondaryTab(String text, {bool isSelected = false}) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: isSelected ? Colors.grey[200] : Colors.transparent,
        borderRadius: BorderRadius.circular(20),
      ),
      child: Text(
        text,
        style: TextStyle(
          color: Colors.black,
          fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
        ),
      ),
    );
  }

  Widget _buildListHeader(String text, {TextAlign alignment = TextAlign.left}) {
    return Text(
      text,
      textAlign: alignment,
      style: TextStyle(color: Colors.grey[500], fontSize: 12),
    );
  }
}

// --- 自定义 SliverPersistentHeaderDelegate (用于吸顶) ---
class _SliverTabBarDelegate extends SliverPersistentHeaderDelegate {
  final Widget child;
  final double height;

  _SliverTabBarDelegate({required this.child, required this.height});

  @override
  Widget build(
    BuildContext context,
    double shrinkOffset,
    bool overlapsContent,
  ) {
    return SizedBox.expand(child: child);
  }

  @override
  double get maxExtent => height;

  @override
  double get minExtent => height;

  @override
  bool shouldRebuild(covariant _SliverTabBarDelegate oldDelegate) {
    return child != oldDelegate.child || height != oldDelegate.height;
  }
}

// --- 单个加密货币列表项 ---
class CryptoListItem extends StatelessWidget {
  final CryptoData data;
  const CryptoListItem({super.key, required this.data});

  Widget _getIcon(String name) {
    switch (name.toLowerCase()) {
      case 'btc':
        return const CircleAvatar(
          backgroundColor: Colors.orange,
          radius: 18,
          child: Text(
            'B',
            style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
          ),
        );
      case 'eth':
        return const CircleAvatar(
          backgroundColor: Color(0xFF627EEA),
          radius: 18,
          child: Text(
            'E',
            style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
          ),
        );
      case 'okb':
        return const CircleAvatar(
          backgroundColor: Colors.black,
          radius: 18,
          child: Text(
            'O',
            style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
          ),
        );
      case 'bnb':
        return const CircleAvatar(
          backgroundColor: Color(0xFFF0B90B),
          radius: 18,
          child: Text(
            'B',
            style: TextStyle(color: Colors.black, fontWeight: FontWeight.bold),
          ),
        );
      case 'doge':
        return const CircleAvatar(
          backgroundColor: Color(0xFFC3A634),
          radius: 18,
          child: Text(
            'D',
            style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
          ),
        );
      case 'xrp':
        return const CircleAvatar(
          backgroundColor: Colors.black,
          radius: 18,
          child: Text(
            'X',
            style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
          ),
        );
      default:
        return CircleAvatar(
          backgroundColor: Colors.grey,
          radius: 18,
          child: Text(
            name.substring(0, 1),
            style: const TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
        );
    }
  }

  @override
  Widget build(BuildContext context) {
    final changeColor = data.change > 0
        ? Colors.green
        : (data.change < 0 ? Colors.red : Colors.grey[700]);
    final changeText = data.change == 0
        ? '0.00%'
        : (data.change > 0
              ? '+${data.change.toStringAsFixed(2)}%'
              : '${data.change.toStringAsFixed(2)}%');

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
      child: Row(
        children: [
          _getIcon(data.name),
          const SizedBox(width: 12),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.baseline,
                textBaseline: TextBaseline.alphabetic,
                children: [
                  Text(
                    data.name,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                  Text(
                    ' / ${data.pair}',
                    style: TextStyle(color: Colors.grey[600], fontSize: 12),
                  ),
                  const SizedBox(width: 4),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 4,
                      vertical: 1,
                    ),
                    color: Colors.grey[200],
                    child: const Text(
                      '10x',
                      style: TextStyle(fontSize: 10, color: Colors.grey),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 4),
              Text(
                '\$${data.volume.toStringAsFixed(2)}${data.volumeUnit}',
                style: TextStyle(color: Colors.grey[600], fontSize: 12),
              ),
            ],
          ),
          const Spacer(),
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                data.price.toStringAsFixed(data.price > 10 ? 0 : 4),
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                '\$${data.usdPrice.toStringAsFixed(data.usdPrice > 10 ? 2 : 5)}',
                style: TextStyle(color: Colors.grey[600], fontSize: 12),
              ),
            ],
          ),
          const SizedBox(width: 16),
          Container(
            width: 80,
            padding: const EdgeInsets.symmetric(vertical: 8),
            decoration: BoxDecoration(
              color: changeColor,
              borderRadius: BorderRadius.circular(4),
            ),
            child: Text(
              changeText,
              textAlign: TextAlign.center,
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

// --- 自定义底部导航栏 ---
class CustomBottomNavBar extends StatelessWidget {
  const CustomBottomNavBar({super.key});

  @override
  Widget build(BuildContext context) {
    return BottomAppBar(
      color: Colors.white,
      elevation: 8.0,
      child: SizedBox(
        height: 60.0,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            _buildNavItem(Icons.home_outlined, '欧易'),
            _buildNavItem(Icons.bar_chart, '市场', isSelected: true),
            _buildTradeButton(),
            _buildNavItem(Icons.explore_outlined, '探索'),
            _buildNavItem(Icons.account_balance_wallet_outlined, '资产'),
          ],
        ),
      ),
    );
  }

  Widget _buildNavItem(IconData icon, String label, {bool isSelected = false}) {
    final color = isSelected ? Colors.black : Colors.grey[600];
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(icon, color: color),
        const SizedBox(height: 4),
        Text(label, style: TextStyle(color: color, fontSize: 10)),
      ],
    );
  }

  Widget _buildTradeButton() {
    return Container(
      width: 50,
      height: 50,
      decoration: const BoxDecoration(
        shape: BoxShape.circle,
        color: Colors.black,
      ),
      child: const Icon(Icons.swap_horiz, color: Colors.white, size: 30),
    );
  }
}
