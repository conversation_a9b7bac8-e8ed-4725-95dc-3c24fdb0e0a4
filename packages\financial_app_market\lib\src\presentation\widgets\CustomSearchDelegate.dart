import 'package:flutter/material.dart';

class CustomSearchDelegate extends SliverPersistentHeaderDelegate {
  final Widget child;
  final double height;

  CustomSearchDelegate({required this.child, required this.height});

  @override
  double get minExtent => height;

  @override
  double get maxExtent => height;

  @override
  Widget build(
    BuildContext context,
    double shrinkOffset,
    bool overlapsContent,
  ) {
    return child;
  }

  @override
  bool shouldRebuild(CustomSearchDelegate oldDelegate) {
    return child != oldDelegate.child || height != oldDelegate.height;
  }
}
