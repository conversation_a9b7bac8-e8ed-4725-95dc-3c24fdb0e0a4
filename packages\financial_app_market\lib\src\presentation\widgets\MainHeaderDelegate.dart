import 'package:flutter/material.dart';

class MainHeaderDelegate extends SliverPersistentHeaderDelegate {
  final Widget child;

  MainHeaderDelegate({required this.child});

  // TabBar 的标准高度是 48，搜索框给了 64 (8 padding + 48 height + 8 padding)
  // 所以总高度是 48 + 64 = 112
  final double totalHeight = 112.0;

  @override
  double get minExtent => totalHeight;

  @override
  double get maxExtent => totalHeight;

  @override
  Widget build(
    BuildContext context,
    double shrinkOffset,
    bool overlapsContent,
  ) {
    // 直接返回我们组合好的 Column
    return child;
  }

  @override
  bool shouldRebuild(MainHeaderDelegate oldDelegate) {
    return child != oldDelegate.child;
  }
}
