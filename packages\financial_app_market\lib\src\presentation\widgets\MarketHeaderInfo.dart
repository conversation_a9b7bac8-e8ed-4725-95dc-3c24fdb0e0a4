// 市场头部信息的小部件
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class MarketHeaderInfo extends StatelessWidget {
  const MarketHeaderInfo({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
      color: Colors.white,
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              _buildOverviewCard(
                title: '市值',
                value: '3.40万亿',
                change: '+0.68%',
                changeColor: Colors.green,
              ),
              SizedBox(width: 4),
              _buildOverviewCard(
                title: '成交额',
                value: '748.79亿',
                change: '-24.93%',
                changeColor: Colors.red,
              ),
              SizedBox(width: 4),
              _buildOverviewCard(
                title: '市场占有率',
                value: '61.87%',
                subtitle: 'Bitcoin',
                showIcon: true,
              ),
            ],
          ),
          const SizedBox(height: 4),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
            decoration: BoxDecoration(
              color: const Color.fromARGB(255, 246, 246, 246),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Icon(Icons.rss_feed, color: Colors.orange, size: 20),
                const SizedBox(width: 8),
                const Expanded(
                  child: Text(
                    '季度环比 GDP 增长率终值(美国) 将于 6月26日...',
                    style: TextStyle(color: Colors.black, fontSize: 12),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                const Icon(Icons.chevron_right, color: Colors.grey),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOverviewCard({
    required String title,
    required String value,
    String? change,
    Color? changeColor,
    String? subtitle,
    bool showIcon = false,
  }) {
    return Expanded(
      flex: 1,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
        decoration: BoxDecoration(
          color: const Color.fromARGB(255, 246, 246, 246),
          borderRadius: BorderRadius.circular(5),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: TextStyle(color: Colors.grey[600], fontSize: 14),
            ),
            const SizedBox(height: 5),
            Text(
              value,
              style: const TextStyle(
                color: Colors.black,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            if (change != null)
              Text(change, style: TextStyle(color: changeColor, fontSize: 12)),
            if (subtitle != null)
              Row(
                children: [
                  if (showIcon)
                    const Padding(
                      padding: EdgeInsets.only(right: 4.0),
                      child: Icon(
                        Icons.currency_bitcoin,
                        size: 14,
                        color: Colors.orange,
                      ),
                    ),
                  Text(
                    subtitle,
                    style: TextStyle(color: Colors.grey[600], fontSize: 12),
                  ),
                ],
              ),
          ],
        ),
      ),
    );
  }
}
