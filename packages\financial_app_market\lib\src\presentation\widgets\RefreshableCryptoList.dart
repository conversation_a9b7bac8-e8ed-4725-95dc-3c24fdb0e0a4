// 封装了下拉刷新的列表组件
import 'dart:math';

import 'package:financial_app_assets/financial_app_assets.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:easy_refresh/easy_refresh.dart';
import 'package:financial_app_market/src/presentation/widgets/pull_style_header.dart';

class RefreshableCryptoList extends StatefulWidget {
  final String tabName;
  final Function onTap;

  const RefreshableCryptoList({
    super.key,
    this.tabName = "",
    required this.onTap,
  });

  @override
  State<RefreshableCryptoList> createState() => _RefreshableCryptoListState();
}

class _RefreshableCryptoListState extends State<RefreshableCryptoList>
    with AutomaticKeepAliveClientMixin {
  List<Map<String, dynamic>> _cryptoData = [];

  final EasyRefreshController _controller = EasyRefreshController(
    controlFinishRefresh: true,
  );

  @override
  void initState() {
    super.initState();
    _generateData();
  }

  @override
  void dispose() {
    _controller.dispose(); // 释放控制器
    super.dispose();
  }

  // 生成模拟数据
  void _generateData() {
    final random = Random();
    _cryptoData = List.generate(20, (index) {
      return {
        'name': 'BTCUSDT',
        'symbol': 'SYM',
        'price': (random.nextDouble() * 1000).toStringAsFixed(4),
        'change': (random.nextDouble() * 10 - 5).toStringAsFixed(2),
      };
    });
  }

  // 模拟网络请求和刷新
  Future<void> _handleRefresh() async {
    // 等待2秒钟模拟网络延迟
    await Future.delayed(const Duration(seconds: 2));
    setState(() {
      _generateData();
    });
    // 3. 告诉控制器刷新已完成
    _controller.finishRefresh(IndicatorResult.success);
  }

  // 使用 AutomaticKeepAliveClientMixin 并返回 true 来保持 Tab 切换时的状态
  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context); // 必须调用 super.build
    // RefreshIndicator 用于实现下拉刷新功能
    // 它只包裹内部的 ListView，因此刷新范围就是您图中的绿色框
    return EasyRefresh(
      controller: _controller,
      onRefresh: _handleRefresh,
      header: VideoStyleHeader(),
      child: ListView.builder(
        // 1. 让 ListView 的高度收缩，只包裹其内容的高度
        shrinkWrap: true,
        // 2. 确保即使列表内容不满一屏，它也始终可以被拖动，
        //    这样 EasyRefresh 才能接收到“过度滚动”事件来触发刷新。
        physics: const AlwaysScrollableScrollPhysics(),
        itemCount: _cryptoData.length,
        itemBuilder: (context, index) {
          final item = _cryptoData[index];
          final change = double.parse(item['change']);
          final changeColor = change >= 0
              ? Colors.greenAccent
              : Colors.redAccent;
          return _buildCoinRow(
            icon: 'assets/images/coins/btc.png', // 替换为实际的图标路径
            name: item['name'],
            volume: '${item['price']}',
            price: '${item['price']}',
            usdPrice: (double.parse(item['price']) * 100).toStringAsFixed(2),
            change: '${item['change']}%',
            isPositive: change >= 0,
            onTap: widget.onTap,
          );
        },
      ),
    );
  }
}

Widget _buildCoinRow({
  required String icon,
  required String name,
  required String volume,
  required String price,
  required String usdPrice,
  required String change,
  required bool isPositive,
  required Function onTap,
}) {
  return GestureDetector(
    onTap: () {
      onTap();
      // GoRouter.of(context).push('/market/kline');
    },
    child: Padding(
      padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 10),
      child: Row(
        children: [
          CircleAvatar(
            radius: 15,
            backgroundColor: Colors.transparent,
            child: AppImages.image(
              icon,
            ), // Make sure to add these images to your assets folder
          ),
          const SizedBox(width: 10),
          Expanded(
            flex: 2,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      name,
                      style: const TextStyle(
                        fontWeight: FontWeight.w500,
                        fontSize: 15,
                        color: Colors.black,
                      ),
                    ),
                    const SizedBox(width: 5),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 4,
                        vertical: 2,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.grey[200],
                        borderRadius: BorderRadius.circular(3),
                      ),
                      child: Text(
                        '永续',
                        style: TextStyle(color: Colors.grey[600], fontSize: 10),
                      ),
                    ),
                  ],
                ),
                Text(
                  '\$$volume',
                  style: TextStyle(color: Colors.grey[600], fontSize: 12),
                ),
              ],
            ),
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                price,
                // maxLines: 1,
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 13,
                  color: Colors.black,
                ),
              ),
              Text(
                '\$$usdPrice',
                style: TextStyle(color: Colors.grey[600], fontSize: 12),
              ),
            ],
          ),
          const SizedBox(width: 10),
          Expanded(
            flex: 1,
            child: Align(
              alignment: Alignment.center,
              child: Container(
                width: 300,
                height: 32,
                alignment: Alignment.center,
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 6,
                ),
                decoration: BoxDecoration(
                  color: isPositive
                      ? const Color.fromARGB(255, 52, 187, 101)
                      : const Color.fromARGB(255, 230, 77, 108),
                  borderRadius: BorderRadius.circular(5),
                ),
                child: Text(
                  change,
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.w500,
                    fontSize: 13,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    ),
  );
}
