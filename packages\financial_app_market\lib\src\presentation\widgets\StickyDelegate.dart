// 自定义的 SliverPersistentHeaderDelegate，用于创建吸顶的 Widget
import 'package:flutter/material.dart';

class StickyDelegate extends SliverPersistentHeaderDelegate {
  final Widget child;
  final Color? stickyColor;
  final double height;

  StickyDelegate({this.stickyColor, required this.child, this.height = 48.0});

  @override
  double get minExtent => height;

  @override
  double get maxExtent => height;

  @override
  Widget build(
    BuildContext context,
    double shrinkOffset,
    bool overlapsContent,
  ) {
    return Container(
      color: stickyColor, // 设置吸顶时的背景色
      child: child,
    );
  }

  @override
  bool shouldRebuild(SliverPersistentHeaderDelegate oldDelegate) {
    return false;
  }
}
