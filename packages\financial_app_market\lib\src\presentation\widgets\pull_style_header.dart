import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:easy_refresh/easy_refresh.dart';

/// 适配 easy_refresh v3.4.0 的自定义 Header
class VideoStyleHeader extends Header {
  const VideoStyleHeader({super.triggerOffset = 38.0, super.clamping = false});

  @override
  Widget build(BuildContext context, IndicatorState state) {
    // build 方法签名已修正，直接将 state 传递下去
    return _VideoStyleHeaderWidget(state: state, triggerOffset: triggerOffset);
  }
}

class _VideoStyleHeaderWidget extends StatefulWidget {
  final IndicatorState state;
  final double triggerOffset;

  const _VideoStyleHeaderWidget({
    required this.state,
    required this.triggerOffset,
  });

  @override
  State<_VideoStyleHeaderWidget> createState() =>
      _VideoStyleHeaderWidgetState();
}

class _VideoStyleHeaderWidgetState extends State<_VideoStyleHeaderWidget>
    with SingleTickerProviderStateMixin {
  late final AnimationController _animationController;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // 从 widget.state 直接获取状态和偏移量
    final mode = widget.state.mode;
    // 修正属性名：pulledExtent -> offset
    final offset = widget.state.offset;

    // --- 动画控制逻辑 ---
    if (mode == IndicatorMode.processing || mode == IndicatorMode.ready) {
      if (!_animationController.isAnimating) {
        _animationController.repeat();
      }
    } else {
      if (_animationController.isAnimating) {
        _animationController.stop();
      }
      final rotation = math.min(offset / widget.triggerOffset, 1.0) * 2.0;
      _animationController.value = rotation;
    }

    // --- UI 构建部分 ---
    Widget body;
    // 修正枚举值：IndicatorMode.failed -> IndicatorResult.fail
    if (mode == IndicatorMode.processed &&
        widget.state.result == IndicatorResult.fail) {
      body = const Icon(Icons.close, color: Colors.red, size: 28);
    } else {
      body = RotationTransition(
        turns: _animationController,
        child: _buildGridIcon(),
      );
    }

    return SizedBox(
      // 高度跟随下拉距离变化 (offset)
      height: offset,
      child: Center(child: body),
    );
  }

  Widget _buildGridIcon() {
    return SizedBox(
      width: 28,
      height: 28,
      child: GridView.count(
        physics: const NeverScrollableScrollPhysics(),
        crossAxisCount: 2,
        mainAxisSpacing: 2,
        crossAxisSpacing: 2,
        children: List.generate(4, (index) {
          return Container(color: Color.fromARGB(255, 52, 187, 101));
        }),
      ),
    );
  }
}
