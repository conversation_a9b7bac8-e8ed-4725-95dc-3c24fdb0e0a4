import 'package:flutter/material.dart';

/// 时间段选择器组件
class TimeIntervalSelector extends StatelessWidget {
  final String selectedInterval;
  final Function(String interval, String timeFrame) onIntervalChanged;

  const TimeIntervalSelector({
    super.key,
    required this.selectedInterval,
    required this.onIntervalChanged,
  });

  // 时间段配置
  static const List<Map<String, String>> _intervals = [
    {'label': '1秒', 'interval': '1s', 'timeFrame': '1s'},
    {'label': '1分', 'interval': '1m', 'timeFrame': '1m'},
    {'label': '5分', 'interval': '5m', 'timeFrame': '5m'},
    {'label': '15分', 'interval': '15m', 'timeFrame': '15m'},
    {'label': '30分', 'interval': '30m', 'timeFrame': '30m'},
    {'label': '1时', 'interval': '1h', 'timeFrame': '1h'},
    {'label': '4时', 'interval': '4h', 'timeFrame': '4h'},
    {'label': '1日', 'interval': '1d', 'timeFrame': '1d'},
    {'label': '1周', 'interval': '1w', 'timeFrame': '1w'},
    {'label': '1月', 'interval': '1M', 'timeFrame': '1M'},
  ];

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 40,
      padding: const EdgeInsets.symmetric(horizontal: 8),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: _intervals.length,
        itemBuilder: (context, index) {
          final interval = _intervals[index];
          final isSelected = selectedInterval == interval['interval'];

          return Padding(
            padding: const EdgeInsets.symmetric(horizontal: 4),
            child: _buildIntervalButton(
              label: interval['label']!,
              interval: interval['interval']!,
              timeFrame: interval['timeFrame']!,
              isSelected: isSelected,
            ),
          );
        },
      ),
    );
  }

  Widget _buildIntervalButton({
    required String label,
    required String interval,
    required String timeFrame,
    required bool isSelected,
  }) {
    return GestureDetector(
      onTap: () => onIntervalChanged(interval, timeFrame),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected ? Colors.blue : Colors.transparent,
          borderRadius: BorderRadius.circular(4),
          border: Border.all(
            color: isSelected ? Colors.blue : Colors.grey.withOpacity(0.3),
            width: 1,
          ),
        ),
        child: Text(
          label,
          style: TextStyle(
            color: isSelected ? Colors.white : Colors.grey[600],
            fontSize: 12,
            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
          ),
        ),
      ),
    );
  }
}

/// 时间段选择器（网格布局版本）
class TimeIntervalGridSelector extends StatelessWidget {
  final String selectedInterval;
  final Function(String interval, String timeFrame) onIntervalChanged;

  const TimeIntervalGridSelector({
    super.key,
    required this.selectedInterval,
    required this.onIntervalChanged,
  });

  // 分组的时间段配置
  static const List<List<Map<String, String>>> _intervalGroups = [
    [
      {'label': '1秒', 'interval': '1s', 'timeFrame': '1s'},
      {'label': '1分', 'interval': '1m', 'timeFrame': '1m'},
      {'label': '5分', 'interval': '5m', 'timeFrame': '5m'},
    ],
    [
      {'label': '15分', 'interval': '15m', 'timeFrame': '15m'},
      {'label': '30分', 'interval': '30m', 'timeFrame': '30m'},
      {'label': '1时', 'interval': '1h', 'timeFrame': '1h'},
    ],
    [
      {'label': '4时', 'interval': '4h', 'timeFrame': '4h'},
      {'label': '1日', 'interval': '1d', 'timeFrame': '1d'},
      {'label': '1周', 'interval': '1w', 'timeFrame': '1w'},
    ],
    [
      {'label': '1月', 'interval': '1M', 'timeFrame': '1M'},
      {'label': '', 'interval': '', 'timeFrame': ''}, // 占位符
      {'label': '', 'interval': '', 'timeFrame': ''}, // 占位符
    ],
  ];

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(8),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: _intervalGroups.map((group) {
          return Padding(
            padding: const EdgeInsets.symmetric(vertical: 4),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: group.map((interval) {
                final isSelected = selectedInterval == interval['interval'];
                return Expanded(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 4),
                    child: _buildIntervalButton(
                      label: interval['label']!,
                      interval: interval['interval']!,
                      timeFrame: interval['timeFrame']!,
                      isSelected: isSelected,
                    ),
                  ),
                );
              }).toList(),
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildIntervalButton({
    required String label,
    required String interval,
    required String timeFrame,
    required bool isSelected,
  }) {
    return GestureDetector(
      onTap: () => onIntervalChanged(interval, timeFrame),
      child: Container(
        height: 36,
        decoration: BoxDecoration(
          color: isSelected ? Colors.blue : Colors.grey[100],
          borderRadius: BorderRadius.circular(6),
          border: Border.all(
            color: isSelected ? Colors.blue : Colors.grey.withOpacity(0.3),
            width: 1,
          ),
        ),
        child: Center(
          child: Text(
            label,
            style: TextStyle(
              color: isSelected ? Colors.white : Colors.grey[700],
              fontSize: 13,
              fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
            ),
          ),
        ),
      ),
    );
  }
}

/// 时间段选择器（下拉菜单版本）
class TimeIntervalDropdownSelector extends StatelessWidget {
  final String selectedInterval;
  final Function(String interval, String timeFrame) onIntervalChanged;

  const TimeIntervalDropdownSelector({
    super.key,
    required this.selectedInterval,
    required this.onIntervalChanged,
  });

  static const List<Map<String, String>> _intervals = [
    {'label': '1秒钟', 'interval': '1s', 'timeFrame': '1s'},
    {'label': '1分钟', 'interval': '1m', 'timeFrame': '1m'},
    {'label': '5分钟', 'interval': '5m', 'timeFrame': '5m'},
    {'label': '15分钟', 'interval': '15m', 'timeFrame': '15m'},
    {'label': '30分钟', 'interval': '30m', 'timeFrame': '30m'},
    {'label': '1小时', 'interval': '1h', 'timeFrame': '1h'},
    {'label': '4小时', 'interval': '4h', 'timeFrame': '4h'},
    {'label': '1天', 'interval': '1d', 'timeFrame': '1d'},
    {'label': '1周', 'interval': '1w', 'timeFrame': '1w'},
    {'label': '1月', 'interval': '1M', 'timeFrame': '1M'},
  ];

  @override
  Widget build(BuildContext context) {
    final selectedItem = _intervals.firstWhere(
      (item) => item['interval'] == selectedInterval,
      orElse: () => _intervals[0],
    );

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.withValues(alpha: 0.3)),
        borderRadius: BorderRadius.circular(6),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<String>(
          value: selectedInterval,
          icon: const Icon(Icons.arrow_drop_down, size: 20),
          style: const TextStyle(color: Colors.black87, fontSize: 14),
          onChanged: (String? newValue) {
            if (newValue != null) {
              final item = _intervals.firstWhere(
                (item) => item['interval'] == newValue,
              );
              onIntervalChanged(item['interval']!, item['timeFrame']!);
            }
          },
          items: _intervals.map<DropdownMenuItem<String>>((item) {
            return DropdownMenuItem<String>(
              value: item['interval'],
              child: Text(item['label']!),
            );
          }).toList(),
        ),
      ),
    );
  }
}

/// 时间段信息工具类
class TimeIntervalUtils {
  /// 获取时间段的显示名称
  static String getIntervalDisplayName(String interval) {
    const intervalNames = {
      '1s': '1秒钟',
      '1m': '1分钟',
      '5m': '5分钟',
      '15m': '15分钟',
      '30m': '30分钟',
      '1h': '1小时',
      '4h': '4小时',
      '1d': '1天',
      '1w': '1周',
      '1M': '1月',
    };
    return intervalNames[interval] ?? interval;
  }

  /// 获取时间段的简短名称
  static String getIntervalShortName(String interval) {
    const intervalNames = {
      '1s': '1秒',
      '1m': '1分',
      '5m': '5分',
      '15m': '15分',
      '30m': '30分',
      '1h': '1时',
      '4h': '4时',
      '1d': '1日',
      '1w': '1周',
      '1M': '1月',
    };
    return intervalNames[interval] ?? interval;
  }

  /// 判断是否为秒级别的时间段
  static bool isSecondInterval(String interval) {
    return ['1s'].contains(interval);
  }

  /// 判断是否为分钟级别的时间段
  static bool isMinuteInterval(String interval) {
    return ['1m', '5m', '15m', '30m'].contains(interval);
  }

  /// 判断是否为小时级别的时间段
  static bool isHourInterval(String interval) {
    return ['1h', '4h'].contains(interval);
  }

  /// 判断是否为日级别的时间段
  static bool isDayInterval(String interval) {
    return ['1d', '1w', '1M'].contains(interval);
  }
}
