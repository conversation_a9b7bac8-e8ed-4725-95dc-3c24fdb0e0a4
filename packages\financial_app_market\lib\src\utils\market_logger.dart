import 'package:financial_app_core/financial_app_core.dart';

/// 市场数据模块专用日志管理器
///
/// 提供市场数据模块特定的日志记录功能
class MarketLogger extends ModuleLogger {
  static final MarketLogger _instance = MarketLogger._internal();
  factory MarketLogger() => _instance;
  MarketLogger._internal();

  @override
  String get moduleName => 'Market';

  // ==================== 市场数据特定的日志方法 ====================

  /// 股票数据获取日志
  void logStockDataFetch({
    required List<String> symbols,
    required bool success,
    String? dataSource,
    Duration? duration,
    String? error,
  }) {
    final metadata = {
      'symbols': symbols,
      'symbols_count': symbols.length,
      'success': success,
      'data_source': dataSource ?? 'primary',
      if (duration != null) 'fetch_duration_ms': duration.inMilliseconds,
      if (error != null) 'error': error,
      'timestamp': DateTime.now().toIso8601String(),
    };

    if (success) {
      info('股票数据获取成功', metadata: metadata);
      if (duration != null) {
        logPerformance('stock_data_fetch', duration, metadata: metadata);
      }
    } else {
      warning('股票数据获取失败', metadata: metadata);
    }

    logBusinessEvent('stock_data_fetch', metadata);
  }

  /// K线数据获取日志
  void logKlineDataFetch({
    required String symbol,
    required String timeframe,
    required bool success,
    int? dataPoints,
    Duration? duration,
    String? error,
  }) {
    final metadata = {
      'symbol': symbol,
      'timeframe': timeframe,
      'success': success,
      'data_points': dataPoints,
      if (duration != null) 'fetch_duration_ms': duration.inMilliseconds,
      if (error != null) 'error': error,
      'timestamp': DateTime.now().toIso8601String(),
    };

    if (success) {
      debug('K线数据获取成功: $symbol ($timeframe)', metadata: metadata);
      if (duration != null) {
        logPerformance('kline_data_fetch', duration, metadata: metadata);
      }
    } else {
      warning('K线数据获取失败: $symbol ($timeframe)', metadata: metadata);
    }

    logBusinessEvent('kline_data_fetch', metadata);
  }

  /// 实时行情订阅日志
  void logRealtimeSubscription({
    required String symbol,
    required String action, // 'subscribe', 'unsubscribe'
    required bool success,
    String? reason,
  }) {
    final metadata = {
      'symbol': symbol,
      'action': action,
      'success': success,
      if (reason != null) 'reason': reason,
      'timestamp': DateTime.now().toIso8601String(),
    };

    if (success) {
      info(
        '实时行情${action == 'subscribe' ? '订阅' : '取消订阅'}成功: $symbol',
        metadata: metadata,
      );
    } else {
      warning(
        '实时行情${action == 'subscribe' ? '订阅' : '取消订阅'}失败: $symbol',
        metadata: metadata,
      );
    }

    logBusinessEvent('realtime_subscription', metadata);
  }

  /// 市场数据更新日志
  void logMarketDataUpdate({
    required String symbol,
    required Map<String, dynamic> priceData,
    String? source,
  }) {
    final metadata = {
      'symbol': symbol,
      'price': priceData['price'],
      'change': priceData['change'],
      'change_percent': priceData['changePercent'],
      'volume': priceData['volume'],
      'source': source ?? 'websocket',
      'timestamp': DateTime.now().toIso8601String(),
    };

    debug('市场数据更新: $symbol', metadata: metadata);
  }

  /// 数据源切换日志
  void logDataSourceSwitch({
    required String fromSource,
    required String toSource,
    required String reason,
    List<String>? affectedSymbols,
  }) {
    final metadata = {
      'from_source': fromSource,
      'to_source': toSource,
      'reason': reason,
      'affected_symbols': affectedSymbols,
      'affected_count': affectedSymbols?.length ?? 0,
      'timestamp': DateTime.now().toIso8601String(),
    };

    warning('数据源切换: $fromSource -> $toSource', metadata: metadata);
    logBusinessEvent('data_source_switch', metadata);
  }

  /// 市场状态变更日志
  void logMarketStatusChange({
    required String market,
    required String fromStatus,
    required String toStatus,
    DateTime? statusTime,
  }) {
    final metadata = {
      'market': market,
      'from_status': fromStatus,
      'to_status': toStatus,
      'status_time':
          statusTime?.toIso8601String() ?? DateTime.now().toIso8601String(),
      'timestamp': DateTime.now().toIso8601String(),
    };

    info('市场状态变更: $market ($fromStatus -> $toStatus)', metadata: metadata);
    logBusinessEvent('market_status_change', metadata);
  }

  /// 数据质量检查日志
  void logDataQualityCheck({
    required String symbol,
    required bool passed,
    List<String>? issues,
    Map<String, dynamic>? metrics,
  }) {
    final metadata = {
      'symbol': symbol,
      'quality_check_passed': passed,
      'issues': issues,
      'issues_count': issues?.length ?? 0,
      'metrics': metrics,
      'timestamp': DateTime.now().toIso8601String(),
    };

    if (passed) {
      debug('数据质量检查通过: $symbol', metadata: metadata);
    } else {
      warning('数据质量检查失败: $symbol', metadata: metadata);
    }

    logBusinessEvent('data_quality_check', metadata);
  }

  /// 缓存操作日志
  void logCacheOperation({
    required String operation, // 'hit', 'miss', 'set', 'invalidate'
    required String key,
    String? reason,
    Duration? duration,
  }) {
    final metadata = {
      'operation': operation,
      'cache_key': key,
      'reason': reason,
      if (duration != null) 'operation_duration_ms': duration.inMilliseconds,
      'timestamp': DateTime.now().toIso8601String(),
    };

    switch (operation) {
      case 'hit':
        debug('缓存命中: $key', metadata: metadata);
        break;
      case 'miss':
        debug('缓存未命中: $key', metadata: metadata);
        break;
      case 'set':
        debug('缓存设置: $key', metadata: metadata);
        break;
      case 'invalidate':
        info('缓存失效: $key', metadata: metadata);
        break;
      default:
        debug('缓存操作: $operation ($key)', metadata: metadata);
    }
  }

  /// WebSocket 连接日志
  void logWebSocketEvent({
    required String
    event, // 'connected', 'disconnected', 'error', 'reconnecting'
    String? url,
    String? reason,
    int? reconnectAttempt,
  }) {
    final metadata = {
      'event': event,
      'url': url,
      'reason': reason,
      'reconnect_attempt': reconnectAttempt,
      'timestamp': DateTime.now().toIso8601String(),
    };

    switch (event) {
      case 'connected':
        info('WebSocket 连接成功', metadata: metadata);
        break;
      case 'disconnected':
        warning('WebSocket 连接断开', metadata: metadata);
        break;
      case 'error':
        error('WebSocket 连接错误', metadata: metadata);
        break;
      case 'reconnecting':
        info('WebSocket 重连中', metadata: metadata);
        break;
      default:
        info('WebSocket 事件: $event', metadata: metadata);
    }

    logBusinessEvent('websocket_$event', metadata);
  }

  /// 股票搜索日志
  void logStockSearch({
    required String query,
    required int resultCount,
    Duration? searchDuration,
    String? searchType,
  }) {
    final metadata = {
      'query': query,
      'result_count': resultCount,
      'search_type': searchType ?? 'symbol',
      if (searchDuration != null)
        'search_duration_ms': searchDuration.inMilliseconds,
      'timestamp': DateTime.now().toIso8601String(),
    };

    info('股票搜索: "$query" (${resultCount}个结果)', metadata: metadata);

    if (searchDuration != null) {
      logPerformance('stock_search', searchDuration, metadata: metadata);
    }

    logUserAction('stock_search', null, context: metadata);
  }

  /// 市场指数更新日志
  void logIndexUpdate({
    required String indexName,
    required Map<String, dynamic> indexData,
    String? source,
  }) {
    final metadata = {
      'index_name': indexName,
      'value': indexData['value'],
      'change': indexData['change'],
      'change_percent': indexData['changePercent'],
      'source': source ?? 'api',
      'timestamp': DateTime.now().toIso8601String(),
    };

    debug('市场指数更新: $indexName', metadata: metadata);
  }

  /// 数据同步日志
  void logDataSync({
    required String syncType, // 'full', 'incremental'
    required bool success,
    int? recordsProcessed,
    Duration? syncDuration,
    String? error,
  }) {
    final metadata = {
      'sync_type': syncType,
      'success': success,
      'records_processed': recordsProcessed,
      if (syncDuration != null) 'sync_duration_ms': syncDuration.inMilliseconds,
      if (error != null) 'error': error,
      'timestamp': DateTime.now().toIso8601String(),
    };

    if (success) {
      info('数据同步完成: $syncType', metadata: metadata);
      if (syncDuration != null) {
        logPerformance('data_sync', syncDuration, metadata: metadata);
      }
    } else {
      super.error('数据同步失败: $syncType', metadata: metadata);
    }

    logBusinessEvent('data_sync', metadata);
  }
}

/// 市场数据模块日志工具类
class MarketLoggerUtils {
  static final MarketLogger _logger = MarketLogger();

  // 基础日志方法
  static void debug(String message, {Map<String, dynamic>? metadata}) =>
      _logger.debug(message, metadata: metadata);

  static void info(String message, {Map<String, dynamic>? metadata}) =>
      _logger.info(message, metadata: metadata);

  static void warning(String message, {Map<String, dynamic>? metadata}) =>
      _logger.warning(message, metadata: metadata);

  static void error(
    String message, {
    dynamic error,
    StackTrace? stackTrace,
    Map<String, dynamic>? metadata,
  }) => _logger.error(
    message,
    error: error,
    stackTrace: stackTrace,
    metadata: metadata,
  );

  // 市场数据特定方法
  static void logStockDataFetch({
    required List<String> symbols,
    required bool success,
    String? dataSource,
    Duration? duration,
    String? error,
  }) => _logger.logStockDataFetch(
    symbols: symbols,
    success: success,
    dataSource: dataSource,
    duration: duration,
    error: error,
  );

  static void logKlineDataFetch({
    required String symbol,
    required String timeframe,
    required bool success,
    int? dataPoints,
    Duration? duration,
    String? error,
  }) => _logger.logKlineDataFetch(
    symbol: symbol,
    timeframe: timeframe,
    success: success,
    dataPoints: dataPoints,
    duration: duration,
    error: error,
  );

  static void logWebSocketEvent({
    required String event,
    String? url,
    String? reason,
    int? reconnectAttempt,
  }) => _logger.logWebSocketEvent(
    event: event,
    url: url,
    reason: reason,
    reconnectAttempt: reconnectAttempt,
  );
}
