import 'package:flutter_test/flutter_test.dart';
import 'package:financial_app_market/src/data/models/trading_view_chart_data.dart';
import 'package:financial_app_market/src/data/models/visible_range.dart';
import 'package:financial_app_market/src/presentation/bloc/market_event.dart';
import 'package:financial_app_market/src/presentation/bloc/market_state.dart';

void main() {
  group('双接口图表集成测试', () {
    group('TradingViewChartDataModel 测试', () {
      test('应该正确创建K线图数据模型', () {
        // Arrange
        final mockResponse = {
          'time': 1640995200000, // 2022-01-01 00:00:00
          'open': 100.0,
          'high': 110.0,
          'low': 95.0,
          'close': 105.0,
        };

        // Act
        final result = TradingViewChartDataModel.fromGetLineDataResponse(
          response: mockResponse,
          timeFrame: '15m',
        );

        // Assert
        expect(result.type, TradingViewDataType.candlestick);
        expect(result.open, 100.0);
        expect(result.high, 110.0);
        expect(result.low, 95.0);
        expect(result.close, 105.0);
        expect(result.isValid, true);
      });

      test('应该正确创建折线图数据模型', () {
        // Arrange
        final mockResponse = {'time': 1640995200000, 'close': 105.0};

        // Act
        final result = TradingViewChartDataModel.fromGetLineDataResponseAsLine(
          response: mockResponse,
          timeFrame: '1h',
        );

        // Assert
        expect(result.type, TradingViewDataType.line);
        expect(result.value, 105.0);
        expect(result.isValid, true);
      });

      test('应该正确转换为TradingView格式', () {
        // Arrange
        final model = TradingViewChartDataModel(
          time: '1640995200',
          type: TradingViewDataType.candlestick,
          timestamp: DateTime.fromMillisecondsSinceEpoch(1640995200000),
          open: 100.0,
          high: 110.0,
          low: 95.0,
          close: 105.0,
        );

        // Act
        final result = model.toTradingViewFormat();

        // Assert
        expect(result['time'], '1640995200');
        expect(result['open'], 100.0);
        expect(result['high'], 110.0);
        expect(result['low'], 95.0);
        expect(result['close'], 105.0);
      });
    });

    group('VisibleRange 测试', () {
      test('应该正确创建可见范围', () {
        // Arrange & Act
        const range = VisibleRange(from: 0, to: 100);

        // Assert
        expect(range.from, 0);
        expect(range.to, 100);
        expect(range.size, 100);
        expect(range.isValid, true);
      });

      test('应该正确验证范围', () {
        // Arrange & Act
        const validRange = VisibleRange(from: 0, to: 100);
        const invalidRange = VisibleRange(from: 100, to: 0);

        // Assert
        expect(validRange.isValid, true);
        expect(invalidRange.isValid, false);
      });

      test('应该正确检查包含关系', () {
        // Arrange
        const range = VisibleRange(from: 10, to: 90);

        // Act & Assert
        expect(range.contains(50), true);
        expect(range.contains(5), false);
        expect(range.contains(95), false);
      });
    });

    group('图表事件测试', () {
      test('应该正确创建初始化图表事件', () {
        // Arrange & Act
        const event = InitializeChartEvent(
          symbol: 'BTCUSDT',
          timeFrame: '15m',
          chartType: TradingViewDataType.candlestick,
        );

        // Assert
        expect(event.symbol, 'BTCUSDT');
        expect(event.timeFrame, '15m');
        expect(event.chartType, TradingViewDataType.candlestick);
      });

      test('应该正确创建切换图表类型事件', () {
        // Arrange & Act
        const event = SwitchChartTypeEvent(chartType: TradingViewDataType.line);

        // Assert
        expect(event.chartType, TradingViewDataType.line);
      });
    });

    group('图表状态测试', () {
      test('应该正确创建图表初始化状态', () {
        // Arrange & Act
        const state = ChartInitialized(
          symbol: 'BTCUSDT',
          timeFrame: '15m',
          chartType: TradingViewDataType.candlestick,
        );

        // Assert
        expect(state.symbol, 'BTCUSDT');
        expect(state.timeFrame, '15m');
        expect(state.chartType, TradingViewDataType.candlestick);
      });

      test('应该正确创建图表类型切换状态', () {
        // Arrange & Act
        const state = ChartTypeChanged(chartType: TradingViewDataType.line);

        // Assert
        expect(state.chartType, TradingViewDataType.line);
      });
    });

    group('TradingViewDataUtils 工具测试', () {
      test('应该正确批量转换GetLineData响应', () {
        // Arrange
        final responses = [
          {
            'time': 1640995200000,
            'open': 100.0,
            'high': 110.0,
            'low': 95.0,
            'close': 105.0,
          },
          {
            'time': 1640995260000,
            'open': 105.0,
            'high': 115.0,
            'low': 100.0,
            'close': 110.0,
          },
        ];

        // Act
        final result = TradingViewDataUtils.convertGetLineDataResponse(
          responses: responses,
          timeFrame: '15m',
          dataType: TradingViewDataType.candlestick,
        );

        // Assert
        expect(result.length, 2);
        expect(result.every((item) => item.isValid), true);
      });

      test('应该正确验证和过滤数据', () {
        // Arrange
        final data = [
          TradingViewChartDataModel(
            time: '1640995200',
            type: TradingViewDataType.candlestick,
            timestamp: DateTime.fromMillisecondsSinceEpoch(1640995200000),
            open: 100.0,
            high: 110.0,
            low: 95.0,
            close: 105.0,
          ),
          TradingViewChartDataModel(
            time: '1640995260',
            type: TradingViewDataType.candlestick,
            timestamp: DateTime.fromMillisecondsSinceEpoch(1640995260000),
            open: null, // 无效数据
            high: 110.0,
            low: 95.0,
            close: 105.0,
          ),
        ];

        // Act
        final result = TradingViewDataUtils.validateAndFilter(data);

        // Assert
        expect(result.length, 1); // 只有一个有效数据
        expect(result.first.isValid, true);
      });
    });
  });
}
          TradingViewChartDataModel(
            time: '1640995200',
            type: TradingViewDataType.candlestick,
            timestamp: DateTime.fromMillisecondsSinceEpoch(1640995200000),
            open: 100.0,
            high: 110.0,
            low: 95.0,
            close: 105.0,
          ),
        ];

        when(
          mockRepository.getInitialChartData(
            symbol: any,
            timeFrame: any,
            dataType: any,
            limit: any,
          ),
        ).thenAnswer((_) async => Right(mockData));

        final params = GetInitialChartDataParams(
          symbol: 'BTCUSDT',
          timeFrame: '15m',
          dataType: TradingViewDataType.candlestick,
          limit: 100,
        );

        // Act
        final result = await getInitialChartData(params);

        // Assert
        expect(result.isRight(), true);
        result.fold((failure) => fail('应该返回成功结果'), (data) {
          expect(data.length, 1);
          expect(data.first.type, TradingViewDataType.candlestick);
          expect(data.first.isValid, true);
        });
      });

      test('应该处理参数验证错误', () async {
        // Arrange
        final params = GetInitialChartDataParams(
          symbol: '', // 空交易对
          timeFrame: '15m',
          dataType: TradingViewDataType.candlestick,
        );

        // Act
        final result = await getInitialChartData(params);

        // Assert
        expect(result.isLeft(), true);
        result.fold(
          (failure) => expect(failure, isA<ValidationFailure>()),
          (data) => fail('应该返回验证错误'),
        );
      });
    });

    group('GetHistoricalChartData 用例测试', () {
      test('应该成功获取历史图表数据', () async {
        // Arrange
        final mockData = [
          TradingViewChartDataModel(
            time: '1640995200',
            type: TradingViewDataType.candlestick,
            timestamp: DateTime.fromMillisecondsSinceEpoch(1640995200000),
            open: 100.0,
            high: 110.0,
            low: 95.0,
            close: 105.0,
          ),
        ];

        when(
          mockRepository.getHistoricalChartData(
            symbol: anyNamed('symbol'),
            timeFrame: anyNamed('timeFrame'),
            dataType: anyNamed('dataType'),
            range: anyNamed('range'),
            limit: anyNamed('limit'),
          ),
        ).thenAnswer((_) async => Right(mockData));

        final params = GetHistoricalChartDataParams(
          symbol: 'BTCUSDT',
          timeFrame: '15m',
          dataType: TradingViewDataType.candlestick,
          range: const VisibleRange(from: 0, to: 100),
        );

        // Act
        final result = await getHistoricalChartData(params);

        // Assert
        expect(result.isRight(), true);
        result.fold((failure) => fail('应该返回成功结果'), (data) {
          expect(data.length, 1);
          expect(data.first.symbol, 'BTCUSDT');
        });
      });
    });

    group('MarketBloc 双接口集成测试', () {
      test('应该正确初始化图表', () async {
        // Arrange
        final event = InitializeChartEvent(
          symbol: 'BTCUSDT',
          timeFrame: '15m',
          chartType: TradingViewDataType.candlestick,
        );

        // Act
        marketBloc.add(event);

        // Assert
        await expectLater(
          marketBloc.stream,
          emitsInOrder([isA<MarketLoading>(), isA<ChartInitialized>()]),
        );
      });

      test('应该正确切换图表类型', () async {
        // Arrange
        final event = SwitchChartTypeEvent(chartType: TradingViewDataType.line);

        // Act
        marketBloc.add(event);

        // Assert
        await expectLater(marketBloc.stream, emits(isA<ChartTypeChanged>()));
      });

      test('应该正确切换交易对', () async {
        // Arrange
        final event = ChangeSymbolEvent(symbol: 'ETHUSDT');

        // Act
        marketBloc.add(event);

        // Assert
        await expectLater(marketBloc.stream, emits(isA<SymbolChanged>()));
      });

      test('应该正确刷新图表', () async {
        // Arrange
        final event = RefreshChartEvent(clearCache: true);

        // Act
        marketBloc.add(event);

        // Assert
        await expectLater(marketBloc.stream, emits(isA<ChartRefreshed>()));
      });
    });

    group('TradingViewDataUtils 工具测试', () {
      test('应该正确批量转换GetLineData响应', () {
        // Arrange
        final responses = [
          {
            'time': 1640995200000,
            'open': 100.0,
            'high': 110.0,
            'low': 95.0,
            'close': 105.0,
          },
          {
            'time': 1640995260000,
            'open': 105.0,
            'high': 115.0,
            'low': 100.0,
            'close': 110.0,
          },
        ];

        // Act
        final result = TradingViewDataUtils.convertGetLineDataResponse(
          responses: responses,
          timeFrame: '15m',
          dataType: TradingViewDataType.candlestick,
        );

        // Assert
        expect(result.length, 2);
        expect(result.every((item) => item.isValid), true);
      });

      test('应该正确验证和过滤数据', () {
        // Arrange
        final data = [
          TradingViewChartDataModel(
            time: '1640995200',
            type: TradingViewDataType.candlestick,
            timestamp: DateTime.fromMillisecondsSinceEpoch(1640995200000),
            open: 100.0,
            high: 110.0,
            low: 95.0,
            close: 105.0,
          ),
          TradingViewChartDataModel(
            time: '1640995260',
            type: TradingViewDataType.candlestick,
            timestamp: DateTime.fromMillisecondsSinceEpoch(1640995260000),
            open: null, // 无效数据
            high: 110.0,
            low: 95.0,
            close: 105.0,
          ),
        ];

        // Act
        final result = TradingViewDataUtils.validateAndFilter(data);

        // Assert
        expect(result.length, 1); // 只有一个有效数据
        expect(result.first.isValid, true);
      });
    });
  });
}
