/// 🧪 测试日期格式化
/// 专门测试_formatTimeForTradingView方法

void main() {
  print('🧪 开始测试日期格式化...');
  
  try {
    // 测试不同的时间戳和时间周期
    final testCases = [
      {
        'timestamp': DateTime.now().millisecondsSinceEpoch,
        'timeFrame': '15m',
        'description': '当前时间 + 15分钟',
      },
      {
        'timestamp': DateTime.now().millisecondsSinceEpoch,
        'timeFrame': '1d',
        'description': '当前时间 + 1天',
      },
      {
        'timestamp': DateTime(2023, 12, 25).millisecondsSinceEpoch,
        'timeFrame': '1d',
        'description': '圣诞节 + 1天',
      },
      {
        'timestamp': 1000000000000, // 2001年9月9日
        'timeFrame': '1d',
        'description': '固定时间戳 + 1天',
      },
    ];
    
    for (final testCase in testCases) {
      final timestamp = testCase['timestamp'] as int;
      final timeFrame = testCase['timeFrame'] as String;
      final description = testCase['description'] as String;
      
      print('\n测试用例: $description');
      print('  时间戳: $timestamp');
      print('  时间周期: $timeFrame');
      
      try {
        // 手动实现_formatTimeForTradingView的逻辑
        final dateTime = DateTime.fromMillisecondsSinceEpoch(timestamp);
        print('  DateTime: $dateTime');
        
        String result;
        switch (timeFrame) {
          case '1s':
          case '1m':
          case '5m':
          case '15m':
          case '30m':
          case '1h':
          case '4h':
            // 分钟/小时级别：使用时间戳（秒）
            final timestampSeconds = timestamp ~/ 1000;
            result = timestampSeconds.toString();
            print('  结果 (秒级): $result');
            break;
          case '1d':
          case '1w':
          case '1M':
            // 日/周/月级别：使用日期字符串
            final year = dateTime.year;
            final month = dateTime.month;
            final day = dateTime.day;
            
            print('  年: $year (${year.runtimeType})');
            print('  月: $month (${month.runtimeType})');
            print('  日: $day (${day.runtimeType})');
            
            // 测试toString转换
            final monthStr = month.toString();
            final dayStr = day.toString();
            print('  月字符串: $monthStr (${monthStr.runtimeType})');
            print('  日字符串: $dayStr (${dayStr.runtimeType})');
            
            // 测试padLeft
            const String paddingChar = '0';
            print('  填充字符: $paddingChar (${paddingChar.runtimeType})');
            
            final monthPadded = monthStr.padLeft(2, paddingChar);
            final dayPadded = dayStr.padLeft(2, paddingChar);
            print('  月填充后: $monthPadded (${monthPadded.runtimeType})');
            print('  日填充后: $dayPadded (${dayPadded.runtimeType})');
            
            result = '$year-$monthPadded-$dayPadded';
            print('  结果 (日期): $result');
            break;
          default:
            final timestampSeconds = timestamp ~/ 1000;
            result = timestampSeconds.toString();
            print('  结果 (默认): $result');
        }
        
        print('✅ 格式化成功: $result');
        
      } catch (e, stackTrace) {
        print('❌ 格式化失败: $e');
        print('堆栈跟踪: $stackTrace');
      }
    }
    
    print('\n🎉 所有测试完成！');
    
  } catch (e, stackTrace) {
    print('❌ 测试过程中发生错误: $e');
    print('完整堆栈跟踪: $stackTrace');
  }
}
