/// 🧪 Mock数据测试文件
/// 用于测试Mock数据生成是否正常工作

import 'package:financial_app_market/src/config/mock_data_config.dart';
import 'package:financial_app_market/src/data/datasources/market_remote_datasource_impl.dart';
import 'package:financial_app_market/src/data/models/trading_view_chart_data.dart';
import 'package:financial_app_market/src/data/models/visible_range.dart';

void main() async {
  print('🧪 开始测试Mock数据生成...');

  try {
    // 0. 首先测试时间戳格式化
    print('\n⏰ 测试时间戳格式化...');
    final testTimestamp = DateTime.now().millisecondsSinceEpoch;
    print('   测试时间戳: $testTimestamp (${testTimestamp.runtimeType})');

    // 直接调用_formatTimeForTradingView方法进行测试
    // 注意：这是私有方法，我们通过TradingViewChartDataModel.fromHistoricalData间接测试

    // 1. 测试KlineMockDataGenerator
    print('\n📊 测试K线Mock数据生成器...');
    final mockData = KlineMockDataGenerator.generateMockData(
      symbol: 'BTC-USDT-SWAP',
      timeFrame: '15m',
      limit: 3, // 减少数量以便调试
      endTime: DateTime.now(),
      config: const MockDataConfig(),
    );

    print('✅ K线Mock数据生成成功，数量: ${mockData.length}');
    if (mockData.isNotEmpty) {
      final first = mockData.first;
      print(
        '   第一条数据: open=${first.open}, high=${first.hight}, low=${first.low}, close=${first.close}, time=${first.time}',
      );
      print('   时间类型: ${first.time.runtimeType}');
      print('   时间值检查: ${first.time > 0 ? '有效' : '无效'}');
    }

    // 2. 逐步测试TradingViewChartDataModel转换
    print('\n🔄 测试TradingView数据转换...');
    if (mockData.isNotEmpty) {
      final lineData = mockData.first;
      print('   准备转换数据:');
      print('     open: ${lineData.open} (${lineData.open.runtimeType})');
      print('     high: ${lineData.hight} (${lineData.hight.runtimeType})');
      print('     low: ${lineData.low} (${lineData.low.runtimeType})');
      print('     close: ${lineData.close} (${lineData.close.runtimeType})');
      print('     time: ${lineData.time} (${lineData.time.runtimeType})');
      print('     timeFrame: 15m');
      print('     type: TradingViewDataType.candlestick');

      print('\n   开始调用 TradingViewChartDataModel.fromHistoricalData...');

      final chartData = TradingViewChartDataModel.fromHistoricalData(
        open: lineData.open,
        high: lineData.hight,
        low: lineData.low,
        close: lineData.close,
        time: lineData.time,
        timeFrame: '15m',
        type: TradingViewDataType.candlestick,
      );

      print('✅ TradingView数据转换成功');
      print('   time字段: ${chartData.time} (类型: ${chartData.time.runtimeType})');
      print('   timestamp: ${chartData.timestamp}');
    }

    // 3. 测试历史数据生成
    print('\n📈 测试历史数据生成...');
    final range = VisibleRange(
      from: DateTime.now()
          .subtract(const Duration(hours: 24))
          .millisecondsSinceEpoch,
      to: DateTime.now().millisecondsSinceEpoch,
    );

    print('   时间范围: ${range.from} - ${range.to}');
    print(
      '   范围类型: from=${range.from.runtimeType}, to=${range.to.runtimeType}',
    );

    // 模拟_generateMockHistoricalData方法的逻辑
    final endTimeMs = range.to;
    final mockLineData = KlineMockDataGenerator.generateMockData(
      symbol: 'BTC-USDT-SWAP',
      timeFrame: '15m',
      limit: 5,
      endTime: DateTime.fromMillisecondsSinceEpoch(endTimeMs),
      config: const MockDataConfig(),
    );

    final chartDataList = <TradingViewChartDataModel>[];
    for (int i = 0; i < mockLineData.length; i++) {
      final lineData = mockLineData[i];
      print(
        '   处理数据项 $i: time=${lineData.time} (${lineData.time.runtimeType})',
      );

      final chartDataItem = TradingViewChartDataModel.fromHistoricalData(
        open: lineData.open,
        high: lineData.hight,
        low: lineData.low,
        close: lineData.close,
        time: lineData.time,
        timeFrame: '15m',
        type: TradingViewDataType.candlestick,
      );

      chartDataList.add(chartDataItem);
      print(
        '   转换成功: time=${chartDataItem.time} (${chartDataItem.time.runtimeType})',
      );
    }

    print('✅ 历史数据生成成功，数量: ${chartDataList.length}');

    print('\n🎉 所有测试通过！');
  } catch (e, stackTrace) {
    print('❌ 测试失败: $e');
    print('堆栈跟踪: $stackTrace');
  }
}
