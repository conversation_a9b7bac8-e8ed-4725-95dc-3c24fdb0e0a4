import 'package:financial_app_notification/notification_router.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:financial_app_notification/src/data/models/notification_model.dart';
import 'package:financial_app_notification/src/domain/providers/notification_provider.dart';
import 'package:intl/intl.dart';

class NotificationDetailPage extends StatefulWidget {
  final String notificationId;

  const NotificationDetailPage({super.key, required this.notificationId});

  @override
  State<NotificationDetailPage> createState() => _NotificationDetailPageState();
}

class _NotificationDetailPageState extends State<NotificationDetailPage> {
  NotificationModel? _notification;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadNotification();
    });
  }

  void _loadNotification() {
    final provider = Provider.of<NotificationProvider>(context, listen: false);
    _notification = provider.getNotificationById(widget.notificationId);
    // 如果是从外部 deep link 直接进入此页面，可能需要重新获取数据
    if (_notification == null) {
      // 实际应用中这里可能需要调用 API 重新获取单个通知详情
      // provider.fetchNotificationById(widget.notificationId);
      // 暂时使用模拟数据
      _notification = NotificationModel(
        id: widget.notificationId,
        title: '通知详情 (模拟)',
        content:
            '这是ID为 ${widget.notificationId} 的通知详情内容。如果直接通过 deep link 打开，数据可能是模拟的。',
        type: NotificationType.other,
        timestamp: DateTime.now().millisecondsSinceEpoch,
        isRead: true, // 详情页默认为已读
      );
    }
    // 确保进入详情页后标记为已读
    if (_notification != null && !_notification!.isRead) {
      provider.markAsRead(widget.notificationId);
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_notification == null) {
      return Scaffold(
        appBar: AppBar(title: const Text('通知详情')),
        body: const Center(child: Text('通知未找到或正在加载...')),
      );
    }

    final dateFormat = DateFormat('yyyy年MM月dd日 HH:mm:ss');

    return Scaffold(
      appBar: AppBar(title: const Text('通知详情')),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              _notification!.title,
              style: Theme.of(
                context,
              ).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Text(
              '时间: ${dateFormat.format(DateTime.fromMillisecondsSinceEpoch(_notification!.timestamp))}',
              style: const TextStyle(fontSize: 14, color: Colors.grey),
            ),
            const SizedBox(height: 16),
            Text(
              _notification!.content,
              style: Theme.of(context).textTheme.bodyLarge,
            ),
            const SizedBox(height: 20),
            // 可以在这里根据 deepLinkData 提供跳转按钮
            if (_notification!.deepLinkData != null)
              ElevatedButton(
                onPressed: () {
                  final path = NotificationRouter.buildDeepLinkPath(
                    _notification!.deepLinkData!,
                  );
                  GoRouter.of(context).go(path); // 使用 go 导航
                },
                child: const Text('前往相关页面'),
              ),
          ],
        ),
      ),
    );
  }
}
