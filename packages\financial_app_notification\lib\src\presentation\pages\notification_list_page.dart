import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import 'package:financial_app_core/financial_app_core.dart';
import 'package:financial_app_notification/src/data/models/notification_model.dart';
import 'package:financial_app_notification/src/domain/providers/notification_provider.dart';
import 'package:intl/intl.dart';

class NotificationListPage extends StatefulWidget {
  const NotificationListPage({super.key});

  @override
  State<NotificationListPage> createState() => _NotificationListPageState();
}

class _NotificationListPageState extends State<NotificationListPage> {
  bool _showUnreadOnly = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _fetchNotifications();
    });
  }

  Future<void> _fetchNotifications() async {
    await Provider.of<NotificationProvider>(
      context,
      listen: false,
    ).fetchNotifications(onlyUnread: _showUnreadOnly);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('消息通知'),
        actions: [
          IconButton(
            icon: Icon(
              _showUnreadOnly ? Icons.visibility_off : Icons.visibility,
            ),
            onPressed: () {
              setState(() {
                _showUnreadOnly = !_showUnreadOnly;
              });
              _fetchNotifications();
            },
            tooltip: _showUnreadOnly ? '显示所有消息' : '只显示未读消息',
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _fetchNotifications,
          ),
        ],
      ),
      body: Consumer<NotificationProvider>(
        builder: (context, notificationProvider, child) {
          if (notificationProvider.isLoading &&
              notificationProvider.notifications.isEmpty) {
            return const Center(child: LoadingIndicator(message: '加载消息...'));
          }
          if (notificationProvider.errorMessage != null) {
            return Center(
              child: Text(
                '错误: ${notificationProvider.errorMessage}',
                style: TextStyle(color: Theme.of(context).colorScheme.error),
              ),
            );
          }
          if (notificationProvider.notifications.isEmpty) {
            return const Center(child: Text('暂无通知。'));
          }

          return ListView.builder(
            padding: const EdgeInsets.all(8.0),
            itemCount: notificationProvider.notifications.length,
            itemBuilder: (context, index) {
              final notification = notificationProvider.notifications[index];
              final dateFormat = DateFormat('yyyy-MM-dd HH:mm');

              return Card(
                margin: const EdgeInsets.symmetric(vertical: 6.0),
                elevation: notification.isRead ? 1 : 3,
                color: notification.isRead
                    ? Colors.grey.shade100
                    : Colors.white,
                child: InkWell(
                  onTap: () {
                    // 标记为已读并跳转到详情页
                    if (!notification.isRead) {
                      notificationProvider.markAsRead(notification.id);
                    }
                    GoRouter.of(
                      context,
                    ).push('/notifications/${notification.id}');
                  },
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(
                              _getNotificationIcon(notification.type),
                              color: _getNotificationIconColor(
                                notification.type,
                              ),
                              size: 20,
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                notification.title,
                                style: TextStyle(
                                  fontWeight: notification.isRead
                                      ? FontWeight.normal
                                      : FontWeight.bold,
                                  fontSize: 16,
                                  color: notification.isRead
                                      ? Colors.grey.shade700
                                      : Colors.black87,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            if (!notification.isRead)
                              Container(
                                margin: const EdgeInsets.only(left: 8),
                                width: 10,
                                height: 10,
                                decoration: const BoxDecoration(
                                  color: Colors.red,
                                  shape: BoxShape.circle,
                                ),
                              ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Text(
                          notification.content,
                          style: TextStyle(
                            fontSize: 14,
                            color: notification.isRead
                                ? Colors.grey
                                : Colors.black54,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(height: 8),
                        Align(
                          alignment: Alignment.bottomRight,
                          child: Text(
                            dateFormat.format(
                              DateTime.fromMillisecondsSinceEpoch(
                                notification.timestamp,
                              ),
                            ),
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey.shade600,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              );
            },
          );
        },
      ),
    );
  }

  IconData _getNotificationIcon(NotificationType type) {
    switch (type) {
      case NotificationType.system:
        return Icons.campaign;
      case NotificationType.trade:
        return Icons.bar_chart;
      case NotificationType.account:
        return Icons.account_balance_wallet;
      case NotificationType.security:
        return Icons.security;
      case NotificationType.other:
        return Icons.info_outline;
    }
  }

  Color _getNotificationIconColor(NotificationType type) {
    switch (type) {
      case NotificationType.system:
        return Colors.blue;
      case NotificationType.trade:
        return Colors.green;
      case NotificationType.account:
        return Colors.orange;
      case NotificationType.security:
        return Colors.red;
      case NotificationType.other:
        return Colors.grey;
    }
  }
}
