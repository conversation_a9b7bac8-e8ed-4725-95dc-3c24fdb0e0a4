import 'package:financial_app_core/financial_app_core.dart';

/// 通知模块专用日志管理器
/// 
/// 提供通知模块特定的日志记录功能
class NotificationLogger extends ModuleLogger {
  static final NotificationLogger _instance = NotificationLogger._internal();
  factory NotificationLogger() => _instance;
  NotificationLogger._internal();

  @override
  String get moduleName => 'Notification';

  // ==================== 通知特定的日志方法 ====================

  /// 推送通知发送日志
  void logPushNotificationSent({
    required String userId,
    required String notificationType,
    required bool success,
    String? notificationId,
    String? title,
    String? body,
    Map<String, dynamic>? payload,
    String? error,
  }) {
    final metadata = {
      'user_id': userId,
      'notification_type': notificationType,
      'notification_id': notificationId,
      'success': success,
      'title': title,
      'body': body,
      'payload': payload,
      if (error != null) 'error': error,
      'timestamp': DateTime.now().toIso8601String(),
    };

    if (success) {
      info('推送通知发送成功', metadata: metadata);
    } else {
      warning('推送通知发送失败', metadata: metadata);
    }

    logBusinessEvent('push_notification_sent', metadata);
  }

  /// 通知点击日志
  void logNotificationClicked({
    required String userId,
    required String notificationId,
    required String notificationType,
    String? action,
    Map<String, dynamic>? context,
  }) {
    final metadata = {
      'user_id': userId,
      'notification_id': notificationId,
      'notification_type': notificationType,
      'action': action ?? 'open',
      'context': context,
      'timestamp': DateTime.now().toIso8601String(),
    };

    info('用户点击通知', metadata: metadata);
    logUserAction('notification_clicked', userId, context: metadata);
  }

  /// 通知权限管理日志
  void logNotificationPermission({
    required String action, // 'request', 'granted', 'denied', 'revoked'
    required bool success,
    String? platform,
    String? reason,
  }) {
    final metadata = {
      'action': action,
      'success': success,
      'platform': platform,
      'reason': reason,
      'timestamp': DateTime.now().toIso8601String(),
    };

    if (success) {
      info('通知权限操作成功: $action', metadata: metadata);
    } else {
      warning('通知权限操作失败: $action', metadata: metadata);
    }

    logBusinessEvent('notification_permission', metadata);
  }

  /// 通知订阅管理日志
  void logNotificationSubscription({
    required String userId,
    required String subscriptionType, // 'price_alert', 'news', 'trade_update'
    required String action, // 'subscribe', 'unsubscribe'
    required bool success,
    Map<String, dynamic>? settings,
  }) {
    final metadata = {
      'user_id': userId,
      'subscription_type': subscriptionType,
      'action': action,
      'success': success,
      'settings': settings,
      'timestamp': DateTime.now().toIso8601String(),
    };

    if (success) {
      info('通知订阅操作成功: $action $subscriptionType', metadata: metadata);
    } else {
      warning('通知订阅操作失败: $action $subscriptionType', metadata: metadata);
    }

    logBusinessEvent('notification_subscription', metadata);
  }

  /// 价格提醒日志
  void logPriceAlert({
    required String userId,
    required String symbol,
    required String alertType, // 'price_above', 'price_below', 'change_percent'
    required double triggerValue,
    required double currentValue,
    required bool triggered,
    String? alertId,
  }) {
    final metadata = {
      'user_id': userId,
      'symbol': symbol,
      'alert_type': alertType,
      'trigger_value': triggerValue,
      'current_value': currentValue,
      'triggered': triggered,
      'alert_id': alertId,
      'timestamp': DateTime.now().toIso8601String(),
    };

    if (triggered) {
      info('价格提醒触发: $symbol', metadata: metadata);
    } else {
      debug('价格提醒检查: $symbol', metadata: metadata);
    }

    logBusinessEvent('price_alert', metadata);
  }

  /// 通知模板管理日志
  void logNotificationTemplate({
    required String templateId,
    required String operation, // 'create', 'update', 'delete', 'use'
    required bool success,
    String? templateType,
    Map<String, dynamic>? templateData,
  }) {
    final metadata = {
      'template_id': templateId,
      'operation': operation,
      'success': success,
      'template_type': templateType,
      'template_data': templateData,
      'timestamp': DateTime.now().toIso8601String(),
    };

    if (success) {
      info('通知模板操作成功: $operation', metadata: metadata);
    } else {
      warning('通知模板操作失败: $operation', metadata: metadata);
    }

    logBusinessEvent('notification_template', metadata);
  }

  /// 批量通知发送日志
  void logBatchNotification({
    required List<String> userIds,
    required String notificationType,
    required int successCount,
    required int failureCount,
    Duration? processingTime,
    String? batchId,
  }) {
    final metadata = {
      'batch_id': batchId,
      'notification_type': notificationType,
      'total_users': userIds.length,
      'success_count': successCount,
      'failure_count': failureCount,
      'success_rate': successCount / userIds.length,
      if (processingTime != null) 'processing_time_ms': processingTime.inMilliseconds,
      'timestamp': DateTime.now().toIso8601String(),
    };

    info('批量通知发送完成', metadata: metadata);
    
    if (processingTime != null) {
      logPerformance('batch_notification', processingTime, metadata: metadata);
    }

    logBusinessEvent('batch_notification', metadata);
  }

  /// 通知渠道状态日志
  void logNotificationChannel({
    required String channelType, // 'push', 'email', 'sms', 'in_app'
    required String status, // 'active', 'inactive', 'error'
    String? reason,
    Map<String, dynamic>? metrics,
  }) {
    final metadata = {
      'channel_type': channelType,
      'status': status,
      'reason': reason,
      'metrics': metrics,
      'timestamp': DateTime.now().toIso8601String(),
    };

    switch (status) {
      case 'active':
        info('通知渠道正常: $channelType', metadata: metadata);
        break;
      case 'inactive':
        warning('通知渠道停用: $channelType', metadata: metadata);
        break;
      case 'error':
        error('通知渠道错误: $channelType', metadata: metadata);
        break;
      default:
        info('通知渠道状态: $channelType ($status)', metadata: metadata);
    }

    logHealthCheck(channelType, status == 'active', 
      details: reason, metrics: metrics);
  }

  /// 通知统计日志
  void logNotificationStats({
    required String period, // 'hourly', 'daily', 'weekly'
    required Map<String, int> stats,
    DateTime? periodStart,
    DateTime? periodEnd,
  }) {
    final metadata = {
      'period': period,
      'period_start': periodStart?.toIso8601String(),
      'period_end': periodEnd?.toIso8601String(),
      'stats': stats,
      'total_notifications': stats.values.fold(0, (sum, count) => sum + count),
      'timestamp': DateTime.now().toIso8601String(),
    };

    info('通知统计: $period', metadata: metadata);
    logBusinessEvent('notification_stats', metadata);
  }

  /// 通知设备管理日志
  void logDeviceToken({
    required String userId,
    required String action, // 'register', 'update', 'remove'
    required bool success,
    String? deviceId,
    String? platform,
    String? token,
    String? error,
  }) {
    final metadata = {
      'user_id': userId,
      'action': action,
      'success': success,
      'device_id': deviceId,
      'platform': platform,
      'token_length': token?.length,
      if (error != null) 'error': error,
      'timestamp': DateTime.now().toIso8601String(),
    };

    if (success) {
      info('设备令牌操作成功: $action', metadata: metadata);
    } else {
      warning('设备令牌操作失败: $action', metadata: metadata);
    }

    logBusinessEvent('device_token', metadata);
  }

  /// 通知调度日志
  void logNotificationSchedule({
    required String scheduleId,
    required String action, // 'create', 'update', 'cancel', 'execute'
    required bool success,
    DateTime? scheduledTime,
    String? notificationType,
    String? error,
  }) {
    final metadata = {
      'schedule_id': scheduleId,
      'action': action,
      'success': success,
      'scheduled_time': scheduledTime?.toIso8601String(),
      'notification_type': notificationType,
      if (error != null) 'error': error,
      'timestamp': DateTime.now().toIso8601String(),
    };

    if (success) {
      info('通知调度操作成功: $action', metadata: metadata);
    } else {
      warning('通知调度操作失败: $action', metadata: metadata);
    }

    logBusinessEvent('notification_schedule', metadata);
  }
}

/// 通知模块日志工具类
class NotificationLoggerUtils {
  static final NotificationLogger _logger = NotificationLogger();

  // 基础日志方法
  static void debug(String message, {Map<String, dynamic>? metadata}) =>
      _logger.debug(message, metadata: metadata);

  static void info(String message, {Map<String, dynamic>? metadata}) =>
      _logger.info(message, metadata: metadata);

  static void warning(String message, {Map<String, dynamic>? metadata}) =>
      _logger.warning(message, metadata: metadata);

  static void error(String message, {
    dynamic error,
    StackTrace? stackTrace,
    Map<String, dynamic>? metadata,
  }) => _logger.error(message, error: error, stackTrace: stackTrace, metadata: metadata);

  // 通知特定方法
  static void logPushNotificationSent({
    required String userId,
    required String notificationType,
    required bool success,
    String? notificationId,
    String? title,
    String? body,
    Map<String, dynamic>? payload,
    String? error,
  }) => _logger.logPushNotificationSent(
    userId: userId,
    notificationType: notificationType,
    success: success,
    notificationId: notificationId,
    title: title,
    body: body,
    payload: payload,
    error: error,
  );

  static void logNotificationClicked({
    required String userId,
    required String notificationId,
    required String notificationType,
    String? action,
    Map<String, dynamic>? context,
  }) => _logger.logNotificationClicked(
    userId: userId,
    notificationId: notificationId,
    notificationType: notificationType,
    action: action,
    context: context,
  );

  static void logPriceAlert({
    required String userId,
    required String symbol,
    required String alertType,
    required double triggerValue,
    required double currentValue,
    required bool triggered,
    String? alertId,
  }) => _logger.logPriceAlert(
    userId: userId,
    symbol: symbol,
    alertType: alertType,
    triggerValue: triggerValue,
    currentValue: currentValue,
    triggered: triggered,
    alertId: alertId,
  );
}
