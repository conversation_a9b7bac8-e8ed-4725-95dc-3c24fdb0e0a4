import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import 'package:financial_app_portfolio/src/data/models/asset_model.dart';
import 'package:financial_app_portfolio/src/domain/providers/portfolio_provider.dart';
import 'package:financial_app_core/financial_app_core.dart'; // Import LoadingIndicator

class PortfolioOverviewPage extends StatefulWidget {
  const PortfolioOverviewPage({super.key});

  @override
  State<PortfolioOverviewPage> createState() => _PortfolioOverviewPageState();
}

class _PortfolioOverviewPageState extends State<PortfolioOverviewPage> {
  @override
  void initState() {
    super.initState();
    // 页面加载时获取投资组合概览和资产列表
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<PortfolioProvider>(
        context,
        listen: false,
      ).fetchPortfolioSummary();
      Provider.of<PortfolioProvider>(context, listen: false).fetchAssets();
    });
  }

  Future<void> _refreshData() async {
    await Provider.of<PortfolioProvider>(
      context,
      listen: false,
    ).fetchPortfolioSummary();
    await Provider.of<PortfolioProvider>(context, listen: false).fetchAssets();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('我的投资组合'),
        actions: [
          IconButton(icon: const Icon(Icons.refresh), onPressed: _refreshData),
          IconButton(
            icon: const Icon(Icons.history),
            onPressed: () {
              GoRouter.of(context).push('/portfolio/transactions');
            },
          ),
        ],
      ),
      body: Consumer<PortfolioProvider>(
        builder: (context, portfolioProvider, child) {
          if (portfolioProvider.isLoading &&
              portfolioProvider.summary == null) {
            return const Center(
              child: LoadingIndicator(message: '加载投资组合数据...'),
            );
          }
          if (portfolioProvider.errorMessage != null) {
            return Center(
              child: Text(
                '错误: ${portfolioProvider.errorMessage}',
                style: TextStyle(color: Theme.of(context).colorScheme.error),
              ),
            );
          }
          if (portfolioProvider.summary == null) {
            return const Center(child: Text('没有可用的投资组合概览数据。'));
          }

          final summary = portfolioProvider.summary!;
          final assets = portfolioProvider.assets;
          final bool isProfit = summary.totalProfitLoss >= 0;
          final Color profitColor = isProfit ? Colors.green : Colors.red;

          return SingleChildScrollView(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 投资组合概览卡片
                Card(
                  elevation: 4,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12.0),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(20.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          '总资产价值',
                          style: TextStyle(fontSize: 16, color: Colors.grey),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          '${summary.totalAssetValue.toStringAsFixed(2)} ${summary.valuationCurrency}',
                          style: const TextStyle(
                            fontSize: 28,
                            fontWeight: FontWeight.bold,
                            color: Colors.blueAccent,
                          ),
                        ),
                        const SizedBox(height: 16),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const Text(
                                  '总盈亏',
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: Colors.grey,
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  '${isProfit ? '+' : ''}${summary.totalProfitLoss.toStringAsFixed(2)} ${summary.valuationCurrency}',
                                  style: TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                    color: profitColor,
                                  ),
                                ),
                              ],
                            ),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.end,
                              children: [
                                const Text(
                                  '盈亏百分比',
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: Colors.grey,
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  '${isProfit ? '+' : ''}${summary.totalProfitLossPercent.toStringAsFixed(2)}%',
                                  style: TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                    color: profitColor,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                        // 这里可以集成图表，例如资产分布饼图
                        const SizedBox(height: 20),
                        const Text(
                          '资产分布 (占位符)',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Container(
                          height: 150,
                          color: Colors.blueGrey.shade100,
                          child: Center(
                            child: Text(
                              summary.assetDistribution.entries
                                  .map(
                                    (e) =>
                                        '${e.key}: ${(e.value * 100).toStringAsFixed(1)}%',
                                  )
                                  .join(', '),
                              textAlign: TextAlign.center,
                              style: const TextStyle(color: Colors.black54),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 30),
                // 资产列表
                Text(
                  '我的资产 (${assets.length})',
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
                const Divider(),
                if (assets.isEmpty)
                  const Center(child: Text('没有可用的资产。'))
                else
                  ListView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: assets.length,
                    itemBuilder: (context, index) {
                      final asset = assets[index];
                      return Card(
                        margin: const EdgeInsets.symmetric(vertical: 8.0),
                        child: Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                asset.currency,
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 18,
                                ),
                              ),
                              const SizedBox(height: 8),
                              Text(
                                '可用余额: ${asset.availableBalance.toStringAsFixed(asset.currency == 'USDT' ? 2 : 8)}',
                              ),
                              Text(
                                '冻结余额: ${asset.frozenBalance.toStringAsFixed(asset.currency == 'USDT' ? 2 : 8)}',
                              ),
                              if (asset.valuationInUsdt != null)
                                Text(
                                  '估值: ${asset.valuationInUsdt!.toStringAsFixed(2)} USDT',
                                ),
                            ],
                          ),
                        ),
                      );
                    },
                  ),
              ],
            ),
          );
        },
      ),
    );
  }
}
