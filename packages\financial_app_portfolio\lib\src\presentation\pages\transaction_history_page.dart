import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:financial_app_portfolio/src/data/models/transaction_record_model.dart';
import 'package:financial_app_portfolio/src/domain/providers/portfolio_provider.dart';
import 'package:financial_app_core/financial_app_core.dart'; // Import LoadingIndicator
import 'package:intl/intl.dart'; // For date formatting

class TransactionHistoryPage extends StatefulWidget {
  const TransactionHistoryPage({super.key});

  @override
  State<TransactionHistoryPage> createState() => _TransactionHistoryPageState();
}

class _TransactionHistoryPageState extends State<TransactionHistoryPage> {
  String? _selectedCurrency;
  TransactionType? _selectedType;
  DateTime? _startDate;
  DateTime? _endDate;

  @override
  void initState() {
    super.initState();
    // 页面加载时获取资金流水
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _fetchTransactionHistory();
    });
  }

  Future<void> _fetchTransactionHistory() async {
    await Provider.of<PortfolioProvider>(
      context,
      listen: false,
    ).fetchTransactionHistory(
      currency: _selectedCurrency,
      type: _selectedType,
      startTime: _startDate?.millisecondsSinceEpoch,
      endTime: _endDate?.millisecondsSinceEpoch,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('资金流水'),
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: _showFilterDialog,
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _fetchTransactionHistory,
          ),
        ],
      ),
      body: Consumer<PortfolioProvider>(
        builder: (context, portfolioProvider, child) {
          if (portfolioProvider.isLoading &&
              portfolioProvider.transactionHistory.isEmpty) {
            return const Center(child: LoadingIndicator(message: '加载资金流水...'));
          }
          if (portfolioProvider.errorMessage != null) {
            return Center(
              child: Text(
                '错误: ${portfolioProvider.errorMessage}',
                style: TextStyle(color: Theme.of(context).colorScheme.error),
              ),
            );
          }
          if (portfolioProvider.transactionHistory.isEmpty) {
            return const Center(child: Text('没有资金流水记录。'));
          }

          return ListView.builder(
            padding: const EdgeInsets.all(8.0),
            itemCount: portfolioProvider.transactionHistory.length,
            itemBuilder: (context, index) {
              final record = portfolioProvider.transactionHistory[index];
              final dateFormat = DateFormat('yyyy-MM-dd HH:mm:ss');
              final recordDateTime = DateTime.fromMillisecondsSinceEpoch(
                record.timestamp,
              );
              final isNegative =
                  record.amount < 0 ||
                  record.type == TransactionType.withdrawal ||
                  record.type == TransactionType.commission;
              final Color amountColor = isNegative
                  ? Colors.red.shade700
                  : Colors.green.shade700;

              return Card(
                margin: const EdgeInsets.symmetric(vertical: 8.0),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            '${_getTranslatedTransactionType(record.type)} - ${record.currency}',
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                            ),
                          ),
                          Text(
                            '${isNegative ? '' : '+'}${record.amount.toStringAsFixed(8)}',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                              color: amountColor,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Text('时间: ${dateFormat.format(recordDateTime)}'),
                      Text(
                        '状态: ${_getTranslatedTransactionStatus(record.status)}',
                      ),
                      if (record.description != null &&
                          record.description!.isNotEmpty)
                        Text('描述: ${record.description!}'),
                      if (record.relatedOrderId != null &&
                          record.relatedOrderId!.isNotEmpty)
                        Text('关联订单ID: ${record.relatedOrderId!}'),
                    ],
                  ),
                ),
              );
            },
          );
        },
      ),
    );
  }

  String _getTranslatedTransactionType(TransactionType type) {
    switch (type) {
      case TransactionType.deposit:
        return '充值';
      case TransactionType.withdrawal:
        return '提现';
      case TransactionType.trade:
        return '交易';
      case TransactionType.commission:
        return '手续费';
      case TransactionType.transfer:
        return '划转';
      case TransactionType.other:
        return '其他';
    }
  }

  String _getTranslatedTransactionStatus(TransactionStatus status) {
    switch (status) {
      case TransactionStatus.pending:
        return '进行中';
      case TransactionStatus.completed:
        return '已完成';
      case TransactionStatus.failed:
        return '失败';
      case TransactionStatus.cancelled:
        return '已取消';
    }
  }

  Future<void> _showFilterDialog() async {
    final result = await showDialog<Map<String, dynamic>>(
      context: context,
      builder: (BuildContext context) {
        String? tempCurrency = _selectedCurrency;
        TransactionType? tempType = _selectedType;
        DateTime? tempStartDate = _startDate;
        DateTime? tempEndDate = _endDate;

        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12.0),
              ),
              title: const Text('筛选资金流水'),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    DropdownButtonFormField<String>(
                      decoration: const InputDecoration(labelText: '币种'),
                      value: tempCurrency,
                      items: ['BTC', 'ETH', 'USDT', 'BNB', null]
                          .map(
                            (e) => DropdownMenuItem(
                              value: e,
                              child: Text(e ?? '所有币种'),
                            ),
                          )
                          .toList(),
                      onChanged: (value) {
                        setState(() {
                          tempCurrency = value;
                        });
                      },
                    ),
                    const SizedBox(height: 16),
                    DropdownButtonFormField<TransactionType>(
                      decoration: const InputDecoration(labelText: '类型'),
                      value: tempType,
                      items: [null, ...TransactionType.values]
                          .map(
                            (e) => DropdownMenuItem(
                              value: e,
                              child: Text(
                                e == null
                                    ? '所有类型'
                                    : _getTranslatedTransactionType(e),
                              ),
                            ),
                          )
                          .toList(),
                      onChanged: (value) {
                        setState(() {
                          tempType = value;
                        });
                      },
                    ),
                    const SizedBox(height: 16),
                    ListTile(
                      title: Text(
                        '开始日期: ${tempStartDate != null ? DateFormat('yyyy-MM-dd').format(tempStartDate!) : '选择日期'}',
                      ),
                      trailing: const Icon(Icons.calendar_today),
                      onTap: () async {
                        final picked = await showDatePicker(
                          context: context,
                          initialDate: tempStartDate ?? DateTime.now(),
                          firstDate: DateTime(2020),
                          lastDate: DateTime.now(),
                        );
                        if (picked != null) {
                          setState(() {
                            tempStartDate = picked;
                          });
                        }
                      },
                    ),
                    ListTile(
                      title: Text(
                        '结束日期: ${tempEndDate != null ? DateFormat('yyyy-MM-dd').format(tempEndDate!) : '选择日期'}',
                      ),
                      trailing: const Icon(Icons.calendar_today),
                      onTap: () async {
                        final picked = await showDatePicker(
                          context: context,
                          initialDate: tempEndDate ?? DateTime.now(),
                          firstDate: DateTime(2020),
                          lastDate: DateTime.now(),
                        );
                        if (picked != null) {
                          setState(() {
                            tempEndDate = picked;
                          });
                        }
                      },
                    ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop(); // 取消
                  },
                  child: const Text('取消'),
                ),
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop({
                      'currency': tempCurrency,
                      'type': tempType,
                      'startDate': tempStartDate,
                      'endDate': tempEndDate,
                    });
                  },
                  child: const Text('确定'),
                ),
              ],
            );
          },
        );
      },
    );

    if (result != null) {
      setState(() {
        _selectedCurrency = result['currency'];
        _selectedType = result['type'];
        _startDate = result['startDate'];
        _endDate = result['endDate'];
      });
      _fetchTransactionHistory(); // 应用筛选并重新获取数据
    }
  }
}
