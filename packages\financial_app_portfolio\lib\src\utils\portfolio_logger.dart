import 'package:financial_app_core/financial_app_core.dart';

/// 投资组合模块专用日志管理器
/// 
/// 提供投资组合模块特定的日志记录功能
class PortfolioLogger extends ModuleLogger {
  static final PortfolioLogger _instance = PortfolioLogger._internal();
  factory PortfolioLogger() => _instance;
  PortfolioLogger._internal();

  @override
  String get moduleName => 'Portfolio';

  // ==================== 投资组合特定的日志方法 ====================

  /// 投资组合创建日志
  void logPortfolioCreated({
    required String userId,
    required String portfolioId,
    required String portfolioName,
    required bool success,
    String? portfolioType,
    double? initialValue,
    String? error,
  }) {
    final metadata = {
      'user_id': userId,
      'portfolio_id': portfolioId,
      'portfolio_name': portfolioName,
      'portfolio_type': portfolioType,
      'initial_value': initialValue,
      'success': success,
      if (error != null) 'error': error,
      'timestamp': DateTime.now().toIso8601String(),
    };

    if (success) {
      info('投资组合创建成功: $portfolioName', metadata: metadata);
    } else {
      warning('投资组合创建失败: $portfolioName', metadata: metadata);
    }

    logBusinessEvent('portfolio_created', metadata);
  }

  /// 持仓添加日志
  void logPositionAdded({
    required String userId,
    required String portfolioId,
    required String symbol,
    required double quantity,
    required double price,
    required bool success,
    String? positionId,
    String? error,
  }) {
    final metadata = {
      'user_id': userId,
      'portfolio_id': portfolioId,
      'position_id': positionId,
      'symbol': symbol,
      'quantity': quantity,
      'price': price,
      'total_value': quantity * price,
      'success': success,
      if (error != null) 'error': error,
      'timestamp': DateTime.now().toIso8601String(),
    };

    if (success) {
      info('持仓添加成功: $symbol', metadata: metadata);
    } else {
      warning('持仓添加失败: $symbol', metadata: metadata);
    }

    logBusinessEvent('position_added', metadata);
  }

  /// 持仓更新日志
  void logPositionUpdated({
    required String userId,
    required String portfolioId,
    required String positionId,
    required String symbol,
    Map<String, dynamic>? oldValues,
    Map<String, dynamic>? newValues,
    required bool success,
    String? updateType, // 'quantity', 'price', 'both'
  }) {
    final metadata = {
      'user_id': userId,
      'portfolio_id': portfolioId,
      'position_id': positionId,
      'symbol': symbol,
      'update_type': updateType,
      'old_values': oldValues,
      'new_values': newValues,
      'success': success,
      'timestamp': DateTime.now().toIso8601String(),
    };

    if (success) {
      info('持仓更新成功: $symbol', metadata: metadata);
    } else {
      warning('持仓更新失败: $symbol', metadata: metadata);
    }

    logBusinessEvent('position_updated', metadata);
  }

  /// 投资组合估值计算日志
  void logPortfolioValuation({
    required String userId,
    required String portfolioId,
    required double totalValue,
    required double dayChange,
    required double dayChangePercent,
    required int positionsCount,
    Duration? calculationTime,
    Map<String, dynamic>? breakdown,
  }) {
    final metadata = {
      'user_id': userId,
      'portfolio_id': portfolioId,
      'total_value': totalValue,
      'day_change': dayChange,
      'day_change_percent': dayChangePercent,
      'positions_count': positionsCount,
      'breakdown': breakdown,
      if (calculationTime != null) 'calculation_time_ms': calculationTime.inMilliseconds,
      'timestamp': DateTime.now().toIso8601String(),
    };

    info('投资组合估值计算完成', metadata: metadata);
    
    if (calculationTime != null) {
      logPerformance('portfolio_valuation', calculationTime, metadata: metadata);
    }

    logBusinessEvent('portfolio_valuation', metadata);
  }

  /// 投资组合分析日志
  void logPortfolioAnalysis({
    required String userId,
    required String portfolioId,
    required String analysisType, // 'risk', 'performance', 'allocation', 'diversification'
    required Map<String, dynamic> analysisResult,
    Duration? analysisTime,
    String? timeframe,
  }) {
    final metadata = {
      'user_id': userId,
      'portfolio_id': portfolioId,
      'analysis_type': analysisType,
      'analysis_result': analysisResult,
      'timeframe': timeframe,
      if (analysisTime != null) 'analysis_time_ms': analysisTime.inMilliseconds,
      'timestamp': DateTime.now().toIso8601String(),
    };

    info('投资组合分析完成: $analysisType', metadata: metadata);
    
    if (analysisTime != null) {
      logPerformance('portfolio_analysis', analysisTime, metadata: metadata);
    }

    logBusinessEvent('portfolio_analysis', metadata);
  }

  /// 资产配置变更日志
  void logAssetAllocationChange({
    required String userId,
    required String portfolioId,
    required Map<String, double> oldAllocation,
    required Map<String, double> newAllocation,
    required String reason, // 'rebalance', 'trade', 'market_change'
  }) {
    final metadata = {
      'user_id': userId,
      'portfolio_id': portfolioId,
      'old_allocation': oldAllocation,
      'new_allocation': newAllocation,
      'reason': reason,
      'allocation_change': _calculateAllocationChange(oldAllocation, newAllocation),
      'timestamp': DateTime.now().toIso8601String(),
    };

    info('资产配置变更: $reason', metadata: metadata);
    logBusinessEvent('asset_allocation_change', metadata);
  }

  /// 投资组合重平衡日志
  void logPortfolioRebalance({
    required String userId,
    required String portfolioId,
    required bool success,
    required List<Map<String, dynamic>> rebalanceActions,
    String? rebalanceStrategy,
    double? totalTransactionCost,
    Duration? rebalanceTime,
    String? error,
  }) {
    final metadata = {
      'user_id': userId,
      'portfolio_id': portfolioId,
      'success': success,
      'rebalance_strategy': rebalanceStrategy,
      'actions_count': rebalanceActions.length,
      'rebalance_actions': rebalanceActions,
      'total_transaction_cost': totalTransactionCost,
      if (rebalanceTime != null) 'rebalance_time_ms': rebalanceTime.inMilliseconds,
      if (error != null) 'error': error,
      'timestamp': DateTime.now().toIso8601String(),
    };

    if (success) {
      info('投资组合重平衡成功', metadata: metadata);
      if (rebalanceTime != null) {
        logPerformance('portfolio_rebalance', rebalanceTime, metadata: metadata);
      }
    } else {
      warning('投资组合重平衡失败', metadata: metadata);
    }

    logBusinessEvent('portfolio_rebalance', metadata);
  }

  /// 投资组合性能报告日志
  void logPerformanceReport({
    required String userId,
    required String portfolioId,
    required String reportType, // 'daily', 'weekly', 'monthly', 'yearly'
    required Map<String, dynamic> performanceMetrics,
    Duration? reportGenerationTime,
  }) {
    final metadata = {
      'user_id': userId,
      'portfolio_id': portfolioId,
      'report_type': reportType,
      'performance_metrics': performanceMetrics,
      if (reportGenerationTime != null) 'generation_time_ms': reportGenerationTime.inMilliseconds,
      'timestamp': DateTime.now().toIso8601String(),
    };

    info('投资组合性能报告生成: $reportType', metadata: metadata);
    
    if (reportGenerationTime != null) {
      logPerformance('performance_report', reportGenerationTime, metadata: metadata);
    }

    logBusinessEvent('performance_report', metadata);
  }

  /// 投资组合风险评估日志
  void logRiskAssessment({
    required String userId,
    required String portfolioId,
    required Map<String, dynamic> riskMetrics,
    required String riskLevel, // 'low', 'medium', 'high'
    List<String>? riskWarnings,
    Duration? assessmentTime,
  }) {
    final metadata = {
      'user_id': userId,
      'portfolio_id': portfolioId,
      'risk_metrics': riskMetrics,
      'risk_level': riskLevel,
      'risk_warnings': riskWarnings,
      'warnings_count': riskWarnings?.length ?? 0,
      if (assessmentTime != null) 'assessment_time_ms': assessmentTime.inMilliseconds,
      'timestamp': DateTime.now().toIso8601String(),
    };

    info('投资组合风险评估完成: $riskLevel', metadata: metadata);
    
    if (assessmentTime != null) {
      logPerformance('risk_assessment', assessmentTime, metadata: metadata);
    }

    logBusinessEvent('risk_assessment', metadata);
  }

  /// 投资组合导入导出日志
  void logPortfolioImportExport({
    required String userId,
    required String operation, // 'import', 'export'
    required String format, // 'csv', 'excel', 'json'
    required bool success,
    String? portfolioId,
    int? recordsCount,
    String? fileName,
    Duration? processingTime,
    String? error,
  }) {
    final metadata = {
      'user_id': userId,
      'portfolio_id': portfolioId,
      'operation': operation,
      'format': format,
      'success': success,
      'records_count': recordsCount,
      'file_name': fileName,
      if (processingTime != null) 'processing_time_ms': processingTime.inMilliseconds,
      if (error != null) 'error': error,
      'timestamp': DateTime.now().toIso8601String(),
    };

    if (success) {
      info('投资组合${operation == 'import' ? '导入' : '导出'}成功', metadata: metadata);
      if (processingTime != null) {
        logPerformance('portfolio_$operation', processingTime, metadata: metadata);
      }
    } else {
      warning('投资组合${operation == 'import' ? '导入' : '导出'}失败', metadata: metadata);
    }

    logBusinessEvent('portfolio_$operation', metadata);
  }

  /// 投资组合分享日志
  void logPortfolioSharing({
    required String userId,
    required String portfolioId,
    required String action, // 'share', 'unshare', 'view_shared'
    required bool success,
    String? shareType, // 'public', 'private', 'link'
    String? recipientId,
    String? shareId,
  }) {
    final metadata = {
      'user_id': userId,
      'portfolio_id': portfolioId,
      'action': action,
      'success': success,
      'share_type': shareType,
      'recipient_id': recipientId,
      'share_id': shareId,
      'timestamp': DateTime.now().toIso8601String(),
    };

    if (success) {
      info('投资组合分享操作成功: $action', metadata: metadata);
    } else {
      warning('投资组合分享操作失败: $action', metadata: metadata);
    }

    logBusinessEvent('portfolio_sharing', metadata);
  }

  /// 计算资产配置变化
  Map<String, double> _calculateAllocationChange(
    Map<String, double> oldAllocation,
    Map<String, double> newAllocation,
  ) {
    final changes = <String, double>{};
    final allAssets = {...oldAllocation.keys, ...newAllocation.keys};
    
    for (final asset in allAssets) {
      final oldValue = oldAllocation[asset] ?? 0.0;
      final newValue = newAllocation[asset] ?? 0.0;
      changes[asset] = newValue - oldValue;
    }
    
    return changes;
  }
}

/// 投资组合模块日志工具类
class PortfolioLoggerUtils {
  static final PortfolioLogger _logger = PortfolioLogger();

  // 基础日志方法
  static void debug(String message, {Map<String, dynamic>? metadata}) =>
      _logger.debug(message, metadata: metadata);

  static void info(String message, {Map<String, dynamic>? metadata}) =>
      _logger.info(message, metadata: metadata);

  static void warning(String message, {Map<String, dynamic>? metadata}) =>
      _logger.warning(message, metadata: metadata);

  static void error(String message, {
    dynamic error,
    StackTrace? stackTrace,
    Map<String, dynamic>? metadata,
  }) => _logger.error(message, error: error, stackTrace: stackTrace, metadata: metadata);

  // 投资组合特定方法
  static void logPortfolioCreated({
    required String userId,
    required String portfolioId,
    required String portfolioName,
    required bool success,
    String? portfolioType,
    double? initialValue,
    String? error,
  }) => _logger.logPortfolioCreated(
    userId: userId,
    portfolioId: portfolioId,
    portfolioName: portfolioName,
    success: success,
    portfolioType: portfolioType,
    initialValue: initialValue,
    error: error,
  );

  static void logPositionAdded({
    required String userId,
    required String portfolioId,
    required String symbol,
    required double quantity,
    required double price,
    required bool success,
    String? positionId,
    String? error,
  }) => _logger.logPositionAdded(
    userId: userId,
    portfolioId: portfolioId,
    symbol: symbol,
    quantity: quantity,
    price: price,
    success: success,
    positionId: positionId,
    error: error,
  );

  static void logPortfolioValuation({
    required String userId,
    required String portfolioId,
    required double totalValue,
    required double dayChange,
    required double dayChangePercent,
    required int positionsCount,
    Duration? calculationTime,
    Map<String, dynamic>? breakdown,
  }) => _logger.logPortfolioValuation(
    userId: userId,
    portfolioId: portfolioId,
    totalValue: totalValue,
    dayChange: dayChange,
    dayChangePercent: dayChangePercent,
    positionsCount: positionsCount,
    calculationTime: calculationTime,
    breakdown: breakdown,
  );
}
