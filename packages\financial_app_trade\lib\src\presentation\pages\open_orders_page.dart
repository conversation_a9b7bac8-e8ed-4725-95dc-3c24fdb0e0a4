import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:financial_app_trade/src/data/models/order_model.dart';
import 'package:financial_app_core/financial_app_core.dart'; // 导入 LoadingIndicator

class OpenOrdersPage extends StatefulWidget {
  const OpenOrdersPage({super.key});

  @override
  State<OpenOrdersPage> createState() => _OpenOrdersPageState();
}

class _OpenOrdersPageState extends State<OpenOrdersPage> {
  final TextEditingController _symbolController = TextEditingController(
    text: 'BTCUSDT',
  );

  @override
  void initState() {
    super.initState();
    // 页面加载时获取开放订单
    // WidgetsBinding.instance.addPostFrameCallback((_) {
    //   Provider.of<TradeProvider>(
    //     context,
    //     listen: false,
    //   ).fetchOpenOrders(_symbolController.text);
    // });
  }

  @override
  void dispose() {
    _symbolController.dispose();
    super.dispose();
  }

  Future<void> _refreshOrders() async {
    // await Provider.of<TradeProvider>(
    //   context,
    //   listen: false,
    // ).fetchOpenOrders(_symbolController.text);
  }

  void _showCancellationResultDialog(BuildContext context, String message) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12.0),
          ),
          title: const Text('取消订单结果'),
          content: Text(message),
          actions: <Widget>[
            TextButton(
              child: const Text('确定'),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('我的委托'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _refreshOrders,
          ),
        ],
      ),
      body: ListView.builder(
        padding: const EdgeInsets.all(8.0),
        // itemCount: tradeProvider.openOrders.length,
        itemBuilder: (context, index) {
          final order = index;
          return Card(
            margin: const EdgeInsets.symmetric(vertical: 8.0),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      // Text(
                      //   '${order.symbol} - ${order.side == OrderSide.buy ? '买入' : '卖出'}',
                      //   style: TextStyle(
                      //     fontWeight: FontWeight.bold,
                      //     fontSize: 16,
                      //     color: order.side == OrderSide.buy
                      //         ? Colors.green
                      //         : Colors.red,
                      //   ),
                      // ),
                      // Text(
                      //   '${order.type.name.toUpperCase()} (${order.status.name.toUpperCase()})',
                      //   style: const TextStyle(
                      //     fontSize: 14,
                      //     color: Colors.grey,
                      //   ),
                      // ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  // Text(
                  //   '价格: ${order.price.toStringAsFixed(order.symbol.endsWith('USDT') ? 2 : 4)}',
                  // ),
                  // Text('数量: ${order.quantity.toStringAsFixed(8)}'),
                  // Text('已成交: ${order.executedQuantity.toStringAsFixed(8)}'),
                  // Text('订单ID: ${order.id}'),
                  const SizedBox(height: 10),
                  Align(
                    alignment: Alignment.centerRight,
                    child: ElevatedButton(
                      onPressed: false
                          ? null
                          : () async {
                              final bool? confirm = await showDialog<bool>(
                                context: context,
                                builder: (BuildContext context) {
                                  return AlertDialog(
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(12.0),
                                    ),
                                    title: const Text('确认取消'),
                                    content: Text('您确定要取消订单吗？'),
                                    actions: <Widget>[
                                      TextButton(
                                        onPressed: () =>
                                            Navigator.of(context).pop(false),
                                        child: const Text('否'),
                                      ),
                                      TextButton(
                                        onPressed: () =>
                                            Navigator.of(context).pop(true),
                                        child: const Text('是'),
                                      ),
                                    ],
                                  );
                                },
                              );
                            },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.orange,
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8.0),
                        ),
                      ),
                      child: false
                          ? const SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                color: Colors.white,
                                strokeWidth: 2,
                              ),
                            )
                          : const Text('取消订单'),
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
