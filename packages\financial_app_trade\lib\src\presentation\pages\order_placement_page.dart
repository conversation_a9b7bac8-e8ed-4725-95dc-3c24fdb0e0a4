import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:financial_app_trade/src/data/models/order_model.dart';
import 'package:financial_app_core/financial_app_core.dart';

class OrderPlacementPage extends StatefulWidget {
  const OrderPlacementPage({super.key});

  @override
  State<OrderPlacementPage> createState() => _OrderPlacementPageState();
}

class _OrderPlacementPageState extends State<OrderPlacementPage> {
  final _formKey = GlobalKey<FormState>();
  final TextEditingController _symbolController = TextEditingController(
    text: 'BTCUSDT',
  );
  final TextEditingController _priceController = TextEditingController();
  final TextEditingController _quantityController = TextEditingController();

  OrderSide _selectedSide = OrderSide.buy;
  OrderType _selectedType = OrderType.limit;

  // 模拟可用的交易对
  final List<TradePairModel> _availableTradePairs = [
    TradePairModel(
      symbol: 'BTCUSDT',
      baseAsset: 'BTC',
      quoteAsset: 'USDT',
      minPrice: 0.01,
      maxPrice: 100000.0,
      tickSize: 0.01,
      minQty: 0.00001,
      maxQty: 1000.0,
      stepSize: 0.00001,
    ),
    TradePairModel(
      symbol: 'ETHUSDT',
      baseAsset: 'ETH',
      quoteAsset: 'USDT',
      minPrice: 0.01,
      maxPrice: 10000.0,
      tickSize: 0.01,
      minQty: 0.0001,
      maxQty: 5000.0,
      stepSize: 0.0001,
    ),
  ];

  @override
  void dispose() {
    _symbolController.dispose();
    _priceController.dispose();
    _quantityController.dispose();
    super.dispose();
  }

  void _showOrderResultDialog(BuildContext context, String message) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12.0),
          ),
          title: const Text('下单结果'),
          content: Text(message),
          actions: <Widget>[
            TextButton(
              child: const Text('确定'),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('下单')),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              _buildTradePairSelector(),
              const SizedBox(height: 20),
              _buildOrderSideSelector(),
              const SizedBox(height: 20),
              _buildOrderTypeSelector(),
              const SizedBox(height: 20),
              if (_selectedType == OrderType.limit ||
                  _selectedType == OrderType.stopLimit)
                AppTextField(
                  controller: _priceController,
                  labelText: '价格',
                  hintText: '请输入委托价格',
                  keyboardType: TextInputType.number,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return '价格不能为空';
                    }
                    if (double.tryParse(value) == null) {
                      return '请输入有效数字';
                    }
                    return null;
                  },
                ),
              const SizedBox(height: 20),
              AppTextField(
                controller: _quantityController,
                labelText: '数量',
                hintText: '请输入委托数量',
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return '数量不能为空';
                  }
                  if (double.tryParse(value) == null) {
                    return '请输入有效数字';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 30),
              AppButton(
                text:
                    '${_selectedSide == OrderSide.buy ? '买入' : '卖出'} ${_symbolController.text}',
                onPressed: false
                    ? () {}
                    : () async {
                        if (_formKey.currentState!.validate()) {
                          final double? price =
                              _selectedType == OrderType.market
                              ? null
                              : double.parse(_priceController.text);
                          final double quantity = double.parse(
                            _quantityController.text,
                          );
                        }
                      },
                // isLoading: tradeProvider.isLoading,
                color: _selectedSide == OrderSide.buy
                    ? Colors.green
                    : Colors.red,
              ),
              const SizedBox(height: 20),
              // if (tradeProvider.errorMessage != null)
              //   Text(
              //     tradeProvider.errorMessage!,
              //     style: TextStyle(
              //       color: Theme.of(context).colorScheme.error,
              //     ),
              //   ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTradePairSelector() {
    return DropdownButtonFormField<String>(
      decoration: InputDecoration(
        labelText: '交易对',
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(8.0)),
      ),
      value: _symbolController.text,
      items: _availableTradePairs.map((pair) {
        return DropdownMenuItem(value: pair.symbol, child: Text(pair.symbol));
      }).toList(),
      onChanged: (newValue) {
        setState(() {
          _symbolController.text = newValue!;
        });
      },
    );
  }

  Widget _buildOrderSideSelector() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      children: [
        Expanded(
          child: ChoiceChip(
            label: const Text('买入'),
            selected: _selectedSide == OrderSide.buy,
            selectedColor: Colors.green.shade100,
            onSelected: (selected) {
              if (selected) {
                setState(() {
                  _selectedSide = OrderSide.buy;
                });
              }
            },
          ),
        ),
        const SizedBox(width: 10),
        Expanded(
          child: ChoiceChip(
            label: const Text('卖出'),
            selected: _selectedSide == OrderSide.sell,
            selectedColor: Colors.red.shade100,
            onSelected: (selected) {
              if (selected) {
                setState(() {
                  _selectedSide = OrderSide.sell;
                });
              }
            },
          ),
        ),
      ],
    );
  }

  Widget _buildOrderTypeSelector() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      children: OrderType.values.map((type) {
        return Expanded(
          child: ChoiceChip(
            label: Text(
              type == OrderType.limit
                  ? '限价'
                  : type == OrderType.market
                  ? '市价'
                  : '止损限价',
            ),
            selected: _selectedType == type,
            selectedColor: Theme.of(context).primaryColor.withOpacity(0.2),
            onSelected: (selected) {
              if (selected) {
                setState(() {
                  _selectedType = type;
                  if (_selectedType == OrderType.market) {
                    _priceController.clear(); // 市价单不需要价格
                  }
                });
              }
            },
          ),
        );
      }).toList(),
    );
  }
}
