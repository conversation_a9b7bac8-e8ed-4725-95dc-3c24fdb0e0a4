import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:financial_app_trade/src/data/models/trade_history_model.dart';
import 'package:financial_app_trade/src/data/models/order_model.dart'; // For OrderSide
import 'package:intl/intl.dart'; // For date formatting

class TradeHistoryPage extends StatefulWidget {
  const TradeHistoryPage({super.key});

  @override
  State<TradeHistoryPage> createState() => _TradeHistoryPageState();
}

class _TradeHistoryPageState extends State<TradeHistoryPage> {
  final TextEditingController _symbolController = TextEditingController(
    text: 'BTCUSDT',
  );
  // 可以添加日期选择器控制器等

  @override
  void initState() {
    super.initState();
    // 页面加载时获取交易历史
    // WidgetsBinding.instance.addPostFrameCallback((_) {
    //   Provider.of<TradeProvider>(
    //     context,
    //     listen: false,
    //   ).fetchTradeHistory(_symbolController.text);
    // });
  }

  @override
  void dispose() {
    _symbolController.dispose();
    super.dispose();
  }

  Future<void> _refreshTradeHistory() async {
    // await Provider.of<TradeProvider>(
    //   context,
    //   listen: false,
    // ).fetchTradeHistory(_symbolController.text);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('交易历史'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _refreshTradeHistory,
          ),
        ],
      ),
      body: ListView.builder(
        padding: const EdgeInsets.all(8.0),
        // itemCount: tradeProvider.tradeHistory.length,
        itemBuilder: (context, index) {
          // final trade = tradeProvider.tradeHistory[index];
          final dateFormat = DateFormat('yyyy-MM-dd HH:mm:ss');
          // final tradeDateTime = DateTime.fromMillisecondsSinceEpoch(
          //   trade.timestamp,
          // );

          return Card(
            margin: const EdgeInsets.symmetric(vertical: 8.0),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      // Text(
                      //   '${trade.symbol} - ${trade.side == OrderSide.buy ? '买入' : '卖出'}',
                      //   style: TextStyle(
                      //     fontWeight: FontWeight.bold,
                      //     fontSize: 16,
                      //     color: trade.side == OrderSide.buy
                      //         ? Colors.green
                      //         : Colors.red,
                      //   ),
                      // ),
                      // Text(
                      //   dateFormat.format(tradeDateTime),
                      //   style: const TextStyle(
                      //     fontSize: 12,
                      //     color: Colors.grey,
                      //   ),
                      // ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  // Text(
                  //   '成交价格: ${trade.price.toStringAsFixed(trade.symbol.endsWith('USDT') ? 2 : 4)}',
                  // ),
                  // Text('成交数量: ${trade.quantity.toStringAsFixed(8)}'),
                  // Text('成交金额: ${trade.quoteQuantity.toStringAsFixed(2)}'),
                  // Text(
                  //   '手续费: ${trade.commission.toStringAsFixed(8)} ${trade.commissionAsset}',
                  // ),
                  // Text('交易ID: ${trade.id}'),
                  // Text(
                  //   '我是: ${trade.isBuyer ? '买方' : '卖方'} ${trade.isMaker ? '(挂单)' : '(吃单)'}',
                  // ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
