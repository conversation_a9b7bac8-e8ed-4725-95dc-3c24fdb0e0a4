import 'package:financial_app_core/financial_app_core.dart';

/// 交易模块专用日志管理器
///
/// 提供交易模块特定的日志记录功能
class TradeLogger extends ModuleLogger {
  static final TradeLogger _instance = TradeLogger._internal();
  factory TradeLogger() => _instance;
  TradeLogger._internal();

  @override
  String get moduleName => 'Trade';

  // ==================== 交易特定的日志方法 ====================

  /// 订单创建日志
  void logOrderCreated({
    required String userId,
    required String orderId,
    required String symbol,
    required String orderType, // 'market', 'limit', 'stop', 'stop_limit'
    required String side, // 'buy', 'sell'
    required double quantity,
    double? price,
    double? stopPrice,
    required bool success,
    String? error,
  }) {
    final metadata = {
      'user_id': userId,
      'order_id': orderId,
      'symbol': symbol,
      'order_type': orderType,
      'side': side,
      'quantity': quantity,
      'price': price,
      'stop_price': stopPrice,
      'estimated_value': price != null ? quantity * price : null,
      'success': success,
      if (error != null) 'error': error,
      'timestamp': DateTime.now().toIso8601String(),
    };

    if (success) {
      info('订单创建成功: $orderId ($symbol)', metadata: metadata);
    } else {
      warning('订单创建失败: $symbol', metadata: metadata);
    }

    logBusinessEvent('order_created', metadata);
  }

  /// 订单执行日志
  void logOrderExecuted({
    required String userId,
    required String orderId,
    required String symbol,
    required double executedQuantity,
    required double executionPrice,
    required double totalValue,
    required bool success,
    String? executionId,
    Duration? executionTime,
    double? commission,
    String? error,
  }) {
    final metadata = {
      'user_id': userId,
      'order_id': orderId,
      'execution_id': executionId,
      'symbol': symbol,
      'executed_quantity': executedQuantity,
      'execution_price': executionPrice,
      'total_value': totalValue,
      'commission': commission,
      'net_value': commission != null ? totalValue - commission : totalValue,
      'success': success,
      if (executionTime != null)
        'execution_time_ms': executionTime.inMilliseconds,
      if (error != null) 'error': error,
      'timestamp': DateTime.now().toIso8601String(),
    };

    if (success) {
      info('订单执行成功: $orderId', metadata: metadata);
      if (executionTime != null) {
        logPerformance('order_execution', executionTime, metadata: metadata);
      }
    } else {
      super.error('订单执行失败: $orderId', metadata: metadata);
    }

    logBusinessEvent('order_executed', metadata);
  }

  /// 订单取消日志
  void logOrderCancelled({
    required String userId,
    required String orderId,
    required String symbol,
    required String
    reason, // 'user_request', 'expired', 'insufficient_funds', 'market_closed'
    required bool success,
    String? error,
  }) {
    final metadata = {
      'user_id': userId,
      'order_id': orderId,
      'symbol': symbol,
      'cancel_reason': reason,
      'success': success,
      if (error != null) 'error': error,
      'timestamp': DateTime.now().toIso8601String(),
    };

    if (success) {
      info('订单取消成功: $orderId ($reason)', metadata: metadata);
    } else {
      warning('订单取消失败: $orderId', metadata: metadata);
    }

    logBusinessEvent('order_cancelled', metadata);
  }

  /// 订单修改日志
  void logOrderModified({
    required String userId,
    required String orderId,
    required String symbol,
    required Map<String, dynamic> oldValues,
    required Map<String, dynamic> newValues,
    required bool success,
    String? error,
  }) {
    final metadata = {
      'user_id': userId,
      'order_id': orderId,
      'symbol': symbol,
      'old_values': oldValues,
      'new_values': newValues,
      'changes': _calculateOrderChanges(oldValues, newValues),
      'success': success,
      if (error != null) 'error': error,
      'timestamp': DateTime.now().toIso8601String(),
    };

    if (success) {
      info('订单修改成功: $orderId', metadata: metadata);
    } else {
      warning('订单修改失败: $orderId', metadata: metadata);
    }

    logBusinessEvent('order_modified', metadata);
  }

  /// 交易策略执行日志
  void logStrategyExecution({
    required String userId,
    required String strategyId,
    required String strategyName,
    required String action, // 'start', 'stop', 'pause', 'resume'
    required bool success,
    Map<String, dynamic>? strategyParams,
    List<String>? affectedOrders,
    String? error,
  }) {
    final metadata = {
      'user_id': userId,
      'strategy_id': strategyId,
      'strategy_name': strategyName,
      'action': action,
      'success': success,
      'strategy_params': strategyParams,
      'affected_orders': affectedOrders,
      'affected_orders_count': affectedOrders?.length ?? 0,
      if (error != null) 'error': error,
      'timestamp': DateTime.now().toIso8601String(),
    };

    if (success) {
      info('交易策略操作成功: $action $strategyName', metadata: metadata);
    } else {
      warning('交易策略操作失败: $action $strategyName', metadata: metadata);
    }

    logBusinessEvent('strategy_execution', metadata);
  }

  /// 风险检查日志
  void logRiskCheck({
    required String userId,
    required String orderId,
    required String symbol,
    required String
    checkType, // 'position_limit', 'buying_power', 'day_trading', 'pattern_day_trader'
    required bool passed,
    String? riskLevel, // 'low', 'medium', 'high'
    Map<String, dynamic>? riskMetrics,
    String? reason,
  }) {
    final metadata = {
      'user_id': userId,
      'order_id': orderId,
      'symbol': symbol,
      'check_type': checkType,
      'passed': passed,
      'risk_level': riskLevel,
      'risk_metrics': riskMetrics,
      'reason': reason,
      'timestamp': DateTime.now().toIso8601String(),
    };

    if (passed) {
      debug('风险检查通过: $checkType', metadata: metadata);
    } else {
      warning('风险检查失败: $checkType', metadata: metadata);
    }

    logBusinessEvent('risk_check', metadata);
  }

  /// 保证金计算日志
  void logMarginCalculation({
    required String userId,
    required String symbol,
    required double requiredMargin,
    required double availableMargin,
    required bool sufficient,
    String? calculationType, // 'initial', 'maintenance', 'day_trading'
    Map<String, dynamic>? marginBreakdown,
  }) {
    final metadata = {
      'user_id': userId,
      'symbol': symbol,
      'required_margin': requiredMargin,
      'available_margin': availableMargin,
      'margin_utilization': requiredMargin / availableMargin,
      'sufficient': sufficient,
      'calculation_type': calculationType,
      'margin_breakdown': marginBreakdown,
      'timestamp': DateTime.now().toIso8601String(),
    };

    if (sufficient) {
      debug('保证金充足: $symbol', metadata: metadata);
    } else {
      warning('保证金不足: $symbol', metadata: metadata);
    }

    logBusinessEvent('margin_calculation', metadata);
  }

  /// 交易会话日志
  void logTradingSession({
    required String userId,
    required String action, // 'start', 'end', 'timeout'
    required bool success,
    Duration? sessionDuration,
    int? ordersCount,
    double? totalVolume,
    Map<String, dynamic>? sessionStats,
  }) {
    final metadata = {
      'user_id': userId,
      'action': action,
      'success': success,
      if (sessionDuration != null)
        'session_duration_ms': sessionDuration.inMilliseconds,
      'orders_count': ordersCount,
      'total_volume': totalVolume,
      'session_stats': sessionStats,
      'timestamp': DateTime.now().toIso8601String(),
    };

    if (success) {
      info('交易会话操作成功: $action', metadata: metadata);
    } else {
      warning('交易会话操作失败: $action', metadata: metadata);
    }

    logBusinessEvent('trading_session', metadata);
  }

  /// 市场数据订阅日志
  void logMarketDataSubscription({
    required String userId,
    required List<String> symbols,
    required String action, // 'subscribe', 'unsubscribe'
    required bool success,
    String? dataType, // 'level1', 'level2', 'time_sales'
    String? error,
  }) {
    final metadata = {
      'user_id': userId,
      'symbols': symbols,
      'symbols_count': symbols.length,
      'action': action,
      'data_type': dataType,
      'success': success,
      if (error != null) 'error': error,
      'timestamp': DateTime.now().toIso8601String(),
    };

    if (success) {
      info(
        '市场数据${action == 'subscribe' ? '订阅' : '取消订阅'}成功',
        metadata: metadata,
      );
    } else {
      warning(
        '市场数据${action == 'subscribe' ? '订阅' : '取消订阅'}失败',
        metadata: metadata,
      );
    }

    logBusinessEvent('market_data_subscription', metadata);
  }

  /// 交易报告生成日志
  void logTradeReport({
    required String userId,
    required String reportType, // 'daily', 'weekly', 'monthly', 'custom'
    required bool success,
    DateTime? startDate,
    DateTime? endDate,
    int? tradesCount,
    double? totalPnL,
    Duration? generationTime,
    String? error,
  }) {
    final metadata = {
      'user_id': userId,
      'report_type': reportType,
      'start_date': startDate?.toIso8601String(),
      'end_date': endDate?.toIso8601String(),
      'trades_count': tradesCount,
      'total_pnl': totalPnL,
      'success': success,
      if (generationTime != null)
        'generation_time_ms': generationTime.inMilliseconds,
      if (error != null) 'error': error,
      'timestamp': DateTime.now().toIso8601String(),
    };

    if (success) {
      info('交易报告生成成功: $reportType', metadata: metadata);
      if (generationTime != null) {
        logPerformance(
          'trade_report_generation',
          generationTime,
          metadata: metadata,
        );
      }
    } else {
      warning('交易报告生成失败: $reportType', metadata: metadata);
    }

    logBusinessEvent('trade_report', metadata);
  }

  /// 算法交易日志
  void logAlgorithmicTrade({
    required String userId,
    required String algorithmId,
    required String algorithmName,
    required String action, // 'start', 'stop', 'pause', 'error'
    required bool success,
    Map<String, dynamic>? algorithmParams,
    List<String>? generatedOrders,
    String? error,
  }) {
    final metadata = {
      'user_id': userId,
      'algorithm_id': algorithmId,
      'algorithm_name': algorithmName,
      'action': action,
      'success': success,
      'algorithm_params': algorithmParams,
      'generated_orders': generatedOrders,
      'generated_orders_count': generatedOrders?.length ?? 0,
      if (error != null) 'error': error,
      'timestamp': DateTime.now().toIso8601String(),
    };

    if (success) {
      info('算法交易操作成功: $action $algorithmName', metadata: metadata);
    } else {
      warning('算法交易操作失败: $action $algorithmName', metadata: metadata);
    }

    logBusinessEvent('algorithmic_trade', metadata);
  }

  /// 计算订单变更
  Map<String, dynamic> _calculateOrderChanges(
    Map<String, dynamic> oldValues,
    Map<String, dynamic> newValues,
  ) {
    final changes = <String, dynamic>{};
    final allKeys = {...oldValues.keys, ...newValues.keys};

    for (final key in allKeys) {
      final oldValue = oldValues[key];
      final newValue = newValues[key];
      if (oldValue != newValue) {
        changes[key] = {'from': oldValue, 'to': newValue};
      }
    }

    return changes;
  }
}

/// 交易模块日志工具类
class TradeLoggerUtils {
  static final TradeLogger _logger = TradeLogger();

  // 基础日志方法
  static void debug(String message, {Map<String, dynamic>? metadata}) =>
      _logger.debug(message, metadata: metadata);

  static void info(String message, {Map<String, dynamic>? metadata}) =>
      _logger.info(message, metadata: metadata);

  static void warning(String message, {Map<String, dynamic>? metadata}) =>
      _logger.warning(message, metadata: metadata);

  static void error(
    String message, {
    dynamic error,
    StackTrace? stackTrace,
    Map<String, dynamic>? metadata,
  }) => _logger.error(
    message,
    error: error,
    stackTrace: stackTrace,
    metadata: metadata,
  );

  // 交易特定方法
  static void logOrderCreated({
    required String userId,
    required String orderId,
    required String symbol,
    required String orderType,
    required String side,
    required double quantity,
    double? price,
    double? stopPrice,
    required bool success,
    String? error,
  }) => _logger.logOrderCreated(
    userId: userId,
    orderId: orderId,
    symbol: symbol,
    orderType: orderType,
    side: side,
    quantity: quantity,
    price: price,
    stopPrice: stopPrice,
    success: success,
    error: error,
  );

  static void logOrderExecuted({
    required String userId,
    required String orderId,
    required String symbol,
    required double executedQuantity,
    required double executionPrice,
    required double totalValue,
    required bool success,
    String? executionId,
    Duration? executionTime,
    double? commission,
    String? error,
  }) => _logger.logOrderExecuted(
    userId: userId,
    orderId: orderId,
    symbol: symbol,
    executedQuantity: executedQuantity,
    executionPrice: executionPrice,
    totalValue: totalValue,
    success: success,
    executionId: executionId,
    executionTime: executionTime,
    commission: commission,
    error: error,
  );

  static void logRiskCheck({
    required String userId,
    required String orderId,
    required String symbol,
    required String checkType,
    required bool passed,
    String? riskLevel,
    Map<String, dynamic>? riskMetrics,
    String? reason,
  }) => _logger.logRiskCheck(
    userId: userId,
    orderId: orderId,
    symbol: symbol,
    checkType: checkType,
    passed: passed,
    riskLevel: riskLevel,
    riskMetrics: riskMetrics,
    reason: reason,
  );
}
