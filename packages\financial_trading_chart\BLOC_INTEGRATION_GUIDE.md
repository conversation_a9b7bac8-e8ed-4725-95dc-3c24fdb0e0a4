# Bloc模式下的ChartAPI集成指南

## 概述

本指南详细说明如何在使用Bloc状态管理模式的页面中正确集成ChartAPI，特别是针对`kline_trading_page`这样的复杂业务页面。

## 核心原则

在Bloc模式下使用ChartAPI时，需要遵循以下原则：
1. **数据获取逻辑应该在Bloc中处理**
2. **ChartAPI作为Bloc的依赖注入**
3. **通过Bloc事件触发图表操作**
4. **状态变化通过Bloc状态流传递**

## 架构设计

```mermaid
graph TB
    A[KlineTradingPage] --> B[KlineTradingBloc]
    B --> C[ChartAPI]
    B --> D[MarketApiService]
    C --> E[ChartDataManager]
    D --> F[外部API]
    E --> G[数据缓存]
    
    H[图表可见范围变化] --> C
    C --> I[数据请求回调]
    I --> B
    B --> J[发起API请求]
    J --> D
    D --> K[返回数据]
    K --> B
    B --> L[更新图表]
    L --> C
```

## 实现方案

### 1. Bloc事件定义

```dart
// kline_trading_events.dart
abstract class KlineTradingEvent extends Equatable {
  const KlineTradingEvent();

  @override
  List<Object?> get props => [];
}

class InitializeChart extends KlineTradingEvent {
  final String symbol;
  final String timeFrame;

  const InitializeChart({
    required this.symbol,
    required this.timeFrame,
  });

  @override
  List<Object?> get props => [symbol, timeFrame];
}

class ChangeSymbol extends KlineTradingEvent {
  final String symbol;

  const ChangeSymbol(this.symbol);

  @override
  List<Object?> get props => [symbol];
}

class ChangeTimeFrame extends KlineTradingEvent {
  final String timeFrame;

  const ChangeTimeFrame(this.timeFrame);

  @override
  List<Object?> get props => [timeFrame];
}

class LoadHistoricalData extends KlineTradingEvent {
  final String symbol;
  final String timeFrame;
  final VisibleRange range;

  const LoadHistoricalData({
    required this.symbol,
    required this.timeFrame,
    required this.range,
  });

  @override
  List<Object?> get props => [symbol, timeFrame, range];
}

class RefreshChart extends KlineTradingEvent {}

class UpdateRealtimeData extends KlineTradingEvent {
  final List<ChartData> data;

  const UpdateRealtimeData(this.data);

  @override
  List<Object?> get props => [data];
}
```

### 2. Bloc状态定义

```dart
// kline_trading_state.dart
class KlineTradingState extends Equatable {
  final String? chartId;
  final String currentSymbol;
  final String currentTimeFrame;
  final bool isLoading;
  final bool isChartReady;
  final List<ChartData> chartData;
  final String? errorMessage;
  final ChartStatus? chartStatus;

  const KlineTradingState({
    this.chartId,
    required this.currentSymbol,
    required this.currentTimeFrame,
    this.isLoading = false,
    this.isChartReady = false,
    this.chartData = const [],
    this.errorMessage,
    this.chartStatus,
  });

  KlineTradingState copyWith({
    String? chartId,
    String? currentSymbol,
    String? currentTimeFrame,
    bool? isLoading,
    bool? isChartReady,
    List<ChartData>? chartData,
    String? errorMessage,
    ChartStatus? chartStatus,
  }) {
    return KlineTradingState(
      chartId: chartId ?? this.chartId,
      currentSymbol: currentSymbol ?? this.currentSymbol,
      currentTimeFrame: currentTimeFrame ?? this.currentTimeFrame,
      isLoading: isLoading ?? this.isLoading,
      isChartReady: isChartReady ?? this.isChartReady,
      chartData: chartData ?? this.chartData,
      errorMessage: errorMessage ?? this.errorMessage,
      chartStatus: chartStatus ?? this.chartStatus,
    );
  }

  @override
  List<Object?> get props => [
        chartId,
        currentSymbol,
        currentTimeFrame,
        isLoading,
        isChartReady,
        chartData,
        errorMessage,
        chartStatus,
      ];
}
```

### 3. Bloc实现

```dart
// kline_trading_bloc.dart
class KlineTradingBloc extends Bloc<KlineTradingEvent, KlineTradingState> {
  final MarketApiService _marketApiService;
  final ChartAPI _chartAPI;
  StreamSubscription<ChartDataUpdateEvent>? _chartDataSubscription;
  Timer? _chartStatusTimer;

  KlineTradingBloc({
    required MarketApiService marketApiService,
    required ChartAPI chartAPI,
  })  : _marketApiService = marketApiService,
        _chartAPI = chartAPI,
        super(const KlineTradingState(
          currentSymbol: 'BTCUSDT',
          currentTimeFrame: '15m',
        )) {
    
    // 注册事件处理器
    on<InitializeChart>(_onInitializeChart);
    on<ChangeSymbol>(_onChangeSymbol);
    on<ChangeTimeFrame>(_onChangeTimeFrame);
    on<LoadHistoricalData>(_onLoadHistoricalData);
    on<RefreshChart>(_onRefreshChart);
    on<UpdateRealtimeData>(_onUpdateRealtimeData);

    // 设置ChartAPI的数据提供者
    _setupChartDataProvider();
  }

  /// 设置ChartAPI的数据提供者
  void _setupChartDataProvider() {
    _chartAPI.setGlobalDataProvider(_handleChartDataRequest);
    
    AppLogger.logModule(
      'KlineTradingBloc',
      LogLevel.info,
      '🌐 设置图表数据提供者',
    );
  }

  /// 处理图表数据请求 - 这是关键方法
  Future<List<ChartData>> _handleChartDataRequest(
    String symbol,
    String timeFrame,
    VisibleRange range,
  ) async {
    AppLogger.logModule(
      'KlineTradingBloc',
      LogLevel.info,
      '📊 处理图表数据请求',
      metadata: {
        'symbol': symbol,
        'time_frame': timeFrame,
        'range': '${range.from}-${range.to}',
        'current_state_symbol': state.currentSymbol,
        'current_state_timeframe': state.currentTimeFrame,
      },
    );

    try {
      // 通过Bloc的API服务获取数据
      final response = await _marketApiService.getKlineData(
        symbol: symbol,
        interval: timeFrame,
        limit: range.to - range.from + 50, // 多获取一些数据作为缓冲
      );

      // 转换数据格式
      final chartData = response.map((item) => ChartData(
        timestamp: DateTime.fromMillisecondsSinceEpoch(item.openTime),
        open: item.open,
        high: item.high,
        low: item.low,
        close: item.close,
        volume: item.volume,
      )).toList();

      AppLogger.logModule(
        'KlineTradingBloc',
        LogLevel.info,
        '✅ 图表数据请求成功',
        metadata: {
          'symbol': symbol,
          'time_frame': timeFrame,
          'data_count': chartData.length,
        },
      );

      return chartData;
    } catch (e) {
      AppLogger.logModule(
        'KlineTradingBloc',
        LogLevel.error,
        '❌ 图表数据请求失败',
        error: e,
        metadata: {
          'symbol': symbol,
          'time_frame': timeFrame,
        },
      );
      
      // 在Bloc中处理错误，不直接抛出
      // 可以发送一个错误事件或更新状态
      rethrow;
    }
  }

  /// 初始化图表
  Future<void> _onInitializeChart(
    InitializeChart event,
    Emitter<KlineTradingState> emit,
  ) async {
    emit(state.copyWith(
      isLoading: true,
      currentSymbol: event.symbol,
      currentTimeFrame: event.timeFrame,
    ));

    try {
      // 创建图表实例
      final chartId = _chartAPI.createChart(
        chartId: 'kline_trading_${event.symbol}_${event.timeFrame}',
        symbol: event.symbol,
        timeFrame: event.timeFrame,
      );

      // 监听图表状态变化
      _startChartStatusMonitoring(chartId);

      emit(state.copyWith(
        chartId: chartId,
        isLoading: false,
        isChartReady: true,
      ));

      AppLogger.logModule(
        'KlineTradingBloc',
        LogLevel.info,
        '📊 图表初始化完成',
        metadata: {
          'chart_id': chartId,
          'symbol': event.symbol,
          'time_frame': event.timeFrame,
        },
      );
    } catch (e) {
      emit(state.copyWith(
        isLoading: false,
        errorMessage: '图表初始化失败: ${e.toString()}',
      ));

      AppLogger.logModule(
        'KlineTradingBloc',
        LogLevel.error,
        '❌ 图表初始化失败',
        error: e,
      );
    }
  }

  /// 切换交易对
  Future<void> _onChangeSymbol(
    ChangeSymbol event,
    Emitter<KlineTradingState> emit,
  ) async {
    if (state.chartId == null) return;

    emit(state.copyWith(
      isLoading: true,
      currentSymbol: event.symbol,
    ));

    try {
      _chartAPI.changeSymbol(state.chartId!, event.symbol);
      
      emit(state.copyWith(isLoading: false));

      AppLogger.logModule(
        'KlineTradingBloc',
        LogLevel.info,
        '🔄 切换交易对',
        metadata: {
          'chart_id': state.chartId,
          'symbol': event.symbol,
        },
      );
    } catch (e) {
      emit(state.copyWith(
        isLoading: false,
        errorMessage: '切换交易对失败: ${e.toString()}',
      ));
    }
  }

  /// 切换时间框架
  Future<void> _onChangeTimeFrame(
    ChangeTimeFrame event,
    Emitter<KlineTradingState> emit,
  ) async {
    if (state.chartId == null) return;

    emit(state.copyWith(
      isLoading: true,
      currentTimeFrame: event.timeFrame,
    ));

    try {
      _chartAPI.changeTimeFrame(state.chartId!, event.timeFrame);
      
      emit(state.copyWith(isLoading: false));

      AppLogger.logModule(
        'KlineTradingBloc',
        LogLevel.info,
        '⏰ 切换时间框架',
        metadata: {
          'chart_id': state.chartId,
          'time_frame': event.timeFrame,
        },
      );
    } catch (e) {
      emit(state.copyWith(
        isLoading: false,
        errorMessage: '切换时间框架失败: ${e.toString()}',
      ));
    }
  }

  /// 加载历史数据
  Future<void> _onLoadHistoricalData(
    LoadHistoricalData event,
    Emitter<KlineTradingState> emit,
  ) async {
    // 这个事件通常由ChartAPI内部触发，这里可以更新UI状态
    emit(state.copyWith(isLoading: true));
    
    // 实际的数据加载会通过_handleChartDataRequest方法处理
    AppLogger.logModule(
      'KlineTradingBloc',
      LogLevel.info,
      '📈 开始加载历史数据',
      metadata: {
        'symbol': event.symbol,
        'time_frame': event.timeFrame,
        'range': '${event.range.from}-${event.range.to}',
      },
    );
  }

  /// 刷新图表
  Future<void> _onRefreshChart(
    RefreshChart event,
    Emitter<KlineTradingState> emit,
  ) async {
    if (state.chartId == null) return;

    emit(state.copyWith(isLoading: true));

    try {
      _chartAPI.refreshChart(state.chartId!);
      
      emit(state.copyWith(isLoading: false));

      AppLogger.logModule(
        'KlineTradingBloc',
        LogLevel.info,
        '🔄 刷新图表',
        metadata: {'chart_id': state.chartId},
      );
    } catch (e) {
      emit(state.copyWith(
        isLoading: false,
        errorMessage: '刷新图表失败: ${e.toString()}',
      ));
    }
  }

  /// 更新实时数据
  Future<void> _onUpdateRealtimeData(
    UpdateRealtimeData event,
    Emitter<KlineTradingState> emit,
  ) async {
    if (state.chartId == null) return;

    try {
      _chartAPI.updateChart(state.chartId!, event.data);
      
      emit(state.copyWith(chartData: event.data));

      AppLogger.logModule(
        'KlineTradingBloc',
        LogLevel.info,
        '📈 更新实时数据',
        metadata: {
          'chart_id': state.chartId,
          'data_count': event.data.length,
        },
      );
    } catch (e) {
      emit(state.copyWith(
        errorMessage: '更新实时数据失败: ${e.toString()}',
      ));
    }
  }

  /// 开始监控图表状态
  void _startChartStatusMonitoring(String chartId) {
    _chartStatusTimer?.cancel();
    _chartStatusTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      final status = _chartAPI.getChartStatus(chartId);
      if (state.chartStatus?.isLoading != status.isLoading) {
        emit(state.copyWith(
          chartStatus: status,
          isLoading: status.isLoading,
        ));
      }
    });
  }

  @override
  Future<void> close() {
    _chartDataSubscription?.cancel();
    _chartStatusTimer?.cancel();
    
    // 清理图表资源
    if (state.chartId != null) {
      _chartAPI.destroyChart(state.chartId!);
    }
    
    return super.close();
  }
}
```

### 4. 页面实现

```dart
// kline_trading_page.dart
class KlineTradingPage extends StatelessWidget {
  final String symbol;
  final String timeFrame;

  const KlineTradingPage({
    Key? key,
    required this.symbol,
    this.timeFrame = '15m',
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => KlineTradingBloc(
        marketApiService: context.read<MarketApiService>(),
        chartAPI: ChartAPI.instance, // 注入ChartAPI实例
      )..add(InitializeChart(symbol: symbol, timeFrame: timeFrame)),
      child: const KlineTradingView(),
    );
  }
}

class KlineTradingView extends StatelessWidget {
  const KlineTradingView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: BlocBuilder<KlineTradingBloc, KlineTradingState>(
          builder: (context, state) {
            return Text('${state.currentSymbol} K线图');
          },
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              context.read<KlineTradingBloc>().add(const RefreshChart());
            },
          ),
        ],
      ),
      body: BlocConsumer<KlineTradingBloc, KlineTradingState>(
        listener: (context, state) {
          // 处理错误消息
          if (state.errorMessage != null) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text(state.errorMessage!)),
            );
          }
        },
        builder: (context, state) {
          return Column(
            children: [
              // 时间框架选择器
              _buildTimeFrameSelector(context, state),
              
              // 状态栏
              _buildStatusBar(state),
              
              // 图表区域
              Expanded(
                child: _buildChartArea(state),
              ),
              
              // 操作面板
              _buildActionPanel(context, state),
            ],
          );
        },
      ),
    );
  }

  Widget _buildTimeFrameSelector(BuildContext context, KlineTradingState state) {
    final timeFrames = ['1m', '5m', '15m', '1h', '4h', '1d'];
    
    return Container(
      height: 50,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        children: [
          const Text('时间框架: '),
          Expanded(
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: timeFrames.length,
              itemBuilder: (context, index) {
                final timeFrame = timeFrames[index];
                return Padding(
                  padding: const EdgeInsets.only(right: 8),
                  child: ChoiceChip(
                    label: Text(timeFrame),
                    selected: state.currentTimeFrame == timeFrame,
                    onSelected: (selected) {
                      if (selected) {
                        context.read<KlineTradingBloc>().add(
                          ChangeTimeFrame(timeFrame),
                        );
                      }
                    },
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusBar(KlineTradingState state) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: state.isLoading ? Colors.orange[100] : Colors.green[100],
      ),
      child: Row(
        children: [
          if (state.isLoading)
            const SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(strokeWidth: 2),
            ),
          if (state.isLoading) const SizedBox(width: 8),
          Icon(
            state.isLoading ? Icons.hourglass_empty : Icons.check_circle,
            size: 16,
            color: state.isLoading ? Colors.orange : Colors.green,
          ),
          const SizedBox(width: 8),
          Text(
            state.isLoading 
                ? '加载数据中...' 
                : '${state.currentSymbol} - ${state.currentTimeFrame}',
            style: TextStyle(
              color: state.isLoading ? Colors.orange[800] : Colors.green[800],
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildChartArea(KlineTradingState state) {
    if (!state.isChartReady) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(8),
      ),
      child: const Center(
        child: Text('图表区域\n(WebView图表组件)'),
      ),
    );
  }

  Widget _buildActionPanel(BuildContext context, KlineTradingState state) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          ElevatedButton(
            onPressed: state.isLoading ? null : () {
              context.read<KlineTradingBloc>().add(const RefreshChart());
            },
            child: const Text('刷新'),
          ),
          ElevatedButton(
            onPressed: () {
              // 模拟实时数据更新
              final mockData = [/* 生成模拟数据 */];
              context.read<KlineTradingBloc>().add(
                UpdateRealtimeData(mockData),
              );
            },
            child: const Text('实时更新'),
          ),
        ],
      ),
    );
  }
}
```

## 关键要点

### 1. 数据提供者设置
```dart
// ✅ 正确做法：在Bloc构造函数中设置
class KlineTradingBloc extends Bloc<KlineTradingEvent, KlineTradingState> {
  KlineTradingBloc({required ChartAPI chartAPI}) : _chartAPI = chartAPI {
    _chartAPI.setGlobalDataProvider(_handleChartDataRequest);
  }
}

// ❌ 错误做法：在页面中直接设置
class KlineTradingPage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    ChartAPI.instance.setGlobalDataProvider(_getKlineData); // 不推荐
    return ...;
  }
}
```

### 2. 依赖注入
```dart
// 在依赖注入配置中注册ChartAPI
void setupDependencies() {
  locator.registerSingleton<ChartAPI>(ChartAPI.instance);
}

// 在Bloc中注入
BlocProvider(
  create: (context) => KlineTradingBloc(
    marketApiService: context.read<MarketApiService>(),
    chartAPI: context.read<ChartAPI>(), // 或者直接使用 ChartAPI.instance
  ),
)
```

### 3. 状态管理
- 所有图表相关的状态都通过Bloc管理
- UI组件通过BlocBuilder/BlocConsumer响应状态变化
- 图表操作通过Bloc事件触发

### 4. 资源清理
```dart
@override
Future<void> close() {
  // 清理图表资源
  if (state.chartId != null) {
    _chartAPI.destroyChart(state.chartId!);
  }
  return super.close();
}
```

## 最佳实践

1. **单一职责**: Bloc负责业务逻辑，ChartAPI负责图表管理
2. **依赖注入**: 通过构造函数注入ChartAPI，便于测试
3. **错误处理**: 在Bloc中统一处理错误，更新状态
4. **资源管理**: 在Bloc的close方法中清理图表资源
5. **状态同步**: 通过定时器或监听器保持Bloc状态与图表状态同步

这种架构确保了代码的可测试性、可维护性，并且符合Bloc模式的最佳实践。

## 快速集成步骤

### 步骤1: 修改现有的KlineTradingBloc

```dart
class KlineTradingBloc extends Bloc<KlineTradingEvent, KlineTradingState> {
  final MarketApiService _marketApiService;
  final ChartAPI _chartAPI;

  KlineTradingBloc({
    required MarketApiService marketApiService,
    ChartAPI? chartAPI,
  })  : _marketApiService = marketApiService,
        _chartAPI = chartAPI ?? ChartAPI.instance,
        super(/* 初始状态 */) {

    // 🔑 关键：设置数据提供者
    _chartAPI.setGlobalDataProvider(_handleChartDataRequest);

    // 注册其他事件处理器...
  }

  // 🔑 核心方法：处理图表数据请求
  Future<List<ChartData>> _handleChartDataRequest(
    String symbol,
    String timeFrame,
    VisibleRange range,
  ) async {
    // 调用您现有的API服务
    final klineData = await _marketApiService.getKlineData(
      symbol: symbol,
      interval: timeFrame,
      limit: range.to - range.from + 50,
    );

    // 转换为ChartData格式
    return klineData.map((item) => ChartData(
      timestamp: DateTime.fromMillisecondsSinceEpoch(item.openTime),
      open: item.open,
      high: item.high,
      low: item.low,
      close: item.close,
      volume: item.volume,
    )).toList();
  }
}
```

### 步骤2: 添加图表相关事件

```dart
// 在现有事件中添加
class InitializeChart extends KlineTradingEvent {
  final String symbol;
  final String timeFrame;
  const InitializeChart({required this.symbol, required this.timeFrame});
}

class RefreshChart extends KlineTradingEvent {}

class UpdateRealtimeData extends KlineTradingEvent {
  final List<ChartData> data;
  const UpdateRealtimeData(this.data);
}
```

### 步骤3: 更新状态定义

```dart
// 在现有状态中添加
class KlineTradingState extends Equatable {
  final String? chartId;           // 新增
  final bool isChartReady;         // 新增
  // ... 其他现有字段
}
```

### 步骤4: 在页面中初始化

```dart
class KlineTradingPage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => KlineTradingBloc(
        marketApiService: context.read<MarketApiService>(),
        // chartAPI: ChartAPI.instance, // 可选
      )..add(InitializeChart(symbol: widget.symbol, timeFrame: widget.timeFrame)),
      child: const KlineTradingView(),
    );
  }
}
```

### 步骤5: 处理WebSocket实时数据

```dart
// 在您现有的WebSocket处理逻辑中
void onWebSocketMessage(dynamic message) {
  // 解析WebSocket消息
  final klineData = parseKlineMessage(message);

  // 通过Bloc更新图表
  context.read<KlineTradingBloc>().add(
    UpdateRealtimeData([klineData]),
  );
}
```

## 与现有代码的集成要点

### 1. 保持现有API服务不变
```dart
// 您现有的MarketApiService可以继续使用
class MarketApiService {
  Future<List<KlineDataModel>> getKlineData({
    required String symbol,
    required String interval,
    required int limit,
  }) async {
    // 现有实现保持不变
  }
}
```

### 2. 数据格式转换
```dart
// 只需要添加一个转换方法
List<ChartData> convertToChartData(List<KlineDataModel> klineData) {
  return klineData.map((item) => ChartData(
    timestamp: DateTime.fromMillisecondsSinceEpoch(item.openTime),
    open: item.open,
    high: item.high,
    low: item.low,
    close: item.close,
    volume: item.volume,
  )).toList();
}
```

### 3. 错误处理
```dart
// 在_handleChartDataRequest中处理错误
Future<List<ChartData>> _handleChartDataRequest(...) async {
  try {
    final data = await _marketApiService.getKlineData(...);
    return convertToChartData(data);
  } catch (e) {
    // 记录错误日志
    AppLogger.logModule('KlineTradingBloc', LogLevel.error, '数据获取失败', error: e);

    // 可以选择返回空数据或重新抛出异常
    return [];
  }
}
```

## 常见问题解答

### Q: 如何处理多个图表实例？
A: 每个页面创建独立的Bloc实例，ChartAPI会自动管理多个图表。

### Q: 如何与现有的WebSocket集成？
A: 在WebSocket回调中发送UpdateRealtimeData事件给Bloc。

### Q: 如何处理页面销毁时的资源清理？
A: 在Bloc的close方法中调用ChartAPI.destroyChart()。

### Q: 数据提供者设置在哪里最合适？
A: 在Bloc的构造函数中设置，确保每个Bloc实例都有正确的数据提供者。

### Q: 如何测试包含ChartAPI的Bloc？
A: 通过构造函数注入Mock的ChartAPI实例进行测试。

## 迁移检查清单

- [ ] 在KlineTradingBloc构造函数中设置ChartAPI数据提供者
- [ ] 实现_handleChartDataRequest方法
- [ ] 添加图表相关的事件和状态
- [ ] 在页面初始化时发送InitializeChart事件
- [ ] 在Bloc的close方法中清理图表资源
- [ ] 更新WebSocket处理逻辑以使用Bloc事件
- [ ] 测试图表可见范围变化时的数据加载
- [ ] 验证错误处理和日志记录
