# 图表数据更新机制使用指南

## 概述

本指南介绍了如何在 `financial_trading_chart` 模块中实现高性能的图表数据更新机制。当图表的可见数据范围发生变化时，系统会自动触发历史数据加载，并通过全局API更新图表，同时确保性能优化和防止重复请求。

## 核心组件

### 1. ChartDataManager (数据管理器)
- **职责**: 管理数据缓存、防止重复请求、协调数据加载
- **特性**: 
  - 智能缓存机制
  - 请求去重
  - 防抖处理
  - 性能优化

### 2. ChartDataController (数据控制器)
- **职责**: 协调图表显示和数据管理
- **特性**:
  - 处理可见范围变化事件
  - 管理数据流
  - 状态管理

### 3. JavaScript Chart Controller
- **职责**: 监听图表可见范围变化
- **特性**:
  - 防抖处理
  - 智能判断是否需要加载数据
  - 与Flutter端通信

## 工作流程

```mermaid
sequenceDiagram
    participant Chart as 图表(JS)
    participant Controller as ChartDataController
    participant Manager as ChartDataManager
    participant API as 外部API服务
    participant C<PERSON> as 数据缓存

    Chart->>Controller: 可见范围变化事件
    Controller->>Manager: 处理范围变化
    Manager->>Cache: 检查缓存数据
    
    alt 缓存不足
        Manager->>API: 请求历史数据
        API->>Manager: 返回数据
        Manager->>Cache: 更新缓存
        Manager->>Controller: 通知数据更新
        Controller->>Chart: 更新图表显示
    else 缓存充足
        Manager->>Controller: 使用缓存数据
        Controller->>Chart: 更新图表显示
    end
```

## 使用方法

### 1. 在其他模块中集成

```dart
class YourChartWidget extends StatefulWidget {
  @override
  State<YourChartWidget> createState() => _YourChartWidgetState();
}

class _YourChartWidgetState extends State<YourChartWidget> {
  late ChartDataController _chartController;

  @override
  void initState() {
    super.initState();
    
    // 初始化图表控制器
    _chartController = ChartDataController();
    _chartController.initialize(
      chartId: 'your_chart_id',
      symbol: 'BTCUSDT',
      timeFrame: '15m',
      dataProvider: _fetchHistoricalData, // 关键：提供数据获取方法
    );
  }

  /// 实现数据获取方法
  Future<List<ChartData>> _fetchHistoricalData(
    String symbol,
    String timeFrame,
    VisibleRange range,
  ) async {
    // 调用您的API服务获取历史数据
    final apiService = locator<YourApiService>();
    return await apiService.getKlineData(
      symbol: symbol,
      interval: timeFrame,
      startTime: range.from,
      endTime: range.to,
    );
  }
}
```

### 2. 设置数据提供者

```dart
// 方式1: 在初始化时设置
_chartController.initialize(
  chartId: 'chart_1',
  symbol: 'BTCUSDT',
  timeFrame: '15m',
  dataProvider: _fetchHistoricalData,
);

// 方式2: 后续设置
_chartController.dataProvider = _fetchHistoricalData;
```

### 3. 处理图表事件

```dart
// 监听控制器状态变化
_chartController.addListener(() {
  setState(() {
    _isLoading = _chartController.isLoading;
  });
});

// 切换交易对
_chartController.changeSymbol('ETHUSDT');

// 切换时间框架
_chartController.changeTimeFrame('1h');

// 刷新数据
_chartController.refreshData();
```

## 性能优化特性

### 1. 智能缓存
- **多级缓存**: 按交易对和时间框架分组缓存
- **大小限制**: 自动清理过期数据，防止内存溢出
- **命中率优化**: 智能预加载相邻数据

### 2. 请求去重
```dart
// 相同的请求会被自动去重
final requestKey = '${symbol}_${timeFrame}';
if (_pendingRequests.containsKey(requestKey)) {
  // 复用正在进行的请求，避免重复调用API
  return;
}
```

### 3. 防抖机制
```dart
// JavaScript端防抖
window.rangeChangeTimeout = setTimeout(() => {
    handleVisibleRangeChange(logicalRange);
}, 300); // 300ms防抖延迟

// Dart端防抖
_debounceTimers[requestKey] = Timer(config.debounceDelay, () {
  _checkAndRequestHistoricalData(chartId, symbol, timeFrame, range);
});
```

### 4. 智能数据加载
```dart
bool _shouldRequestMoreData(List<ChartData> cachedData, VisibleRange range) {
  // 检查可见范围是否超出缓存范围的阈值
  final bufferRatio = config.dataBufferRatio; // 默认20%缓冲区
  return rangeStart < (cacheStart + buffer) || rangeEnd > (cacheEnd - buffer);
}
```

## 配置选项

### ChartDataManagerConfig
```dart
const config = ChartDataManagerConfig(
  maxCacheSize: 5000,           // 最大缓存大小
  debounceDelay: Duration(milliseconds: 300), // 防抖延迟
  requestTimeout: Duration(seconds: 10),      // 请求超时
  dataBufferRatio: 0.2,         // 数据缓冲比例
);
```

## 日志监控

系统提供详细的日志记录，便于调试和监控：

```dart
// 可见范围变化日志
[ChartDataManager] 📊 可见范围变化
{
  "chart_id": "main_chart",
  "symbol": "BTCUSDT",
  "time_frame": "15m",
  "visible_range": {"from": 50, "to": 150}
}

// 数据请求日志
[ChartDataManager] 🚀 发起历史数据请求
{
  "symbol": "BTCUSDT",
  "time_frame": "15m",
  "request_key": "BTCUSDT_15m"
}

// 缓存更新日志
[ChartDataManager] 📦 缓存已更新
{
  "symbol": "BTCUSDT",
  "time_frame": "15m",
  "cache_size": 1250,
  "new_data_count": 100
}
```

## 错误处理

### 1. 网络错误
```dart
try {
  final data = await apiService.getKlineData(...);
  return data;
} catch (e) {
  AppLogger.logModule('YourModule', LogLevel.error, '数据获取失败', error: e);
  // 系统会自动重试或使用缓存数据
  rethrow;
}
```

### 2. 数据格式错误
```dart
// 数据验证
if (data.isEmpty || !_isValidChartData(data)) {
  throw Exception('无效的图表数据格式');
}
```

### 3. 超时处理
```dart
// 自动超时处理
Timer(config.requestTimeout, () {
  if (!completer.isCompleted) {
    completer.completeError('请求超时');
  }
});
```

## 最佳实践

### 1. 数据提供者实现
```dart
Future<List<ChartData>> _fetchHistoricalData(
  String symbol,
  String timeFrame,
  VisibleRange range,
) async {
  // ✅ 好的做法
  try {
    // 添加适当的日志
    AppLogger.logModule('YourModule', LogLevel.info, '开始获取数据');
    
    // 调用API服务
    final data = await _apiService.getKlineData(...);
    
    // 数据验证
    if (data.isEmpty) {
      throw Exception('返回数据为空');
    }
    
    return data;
  } catch (e) {
    // 记录错误并重新抛出
    AppLogger.logModule('YourModule', LogLevel.error, '数据获取失败', error: e);
    rethrow;
  }
}
```

### 2. 内存管理
```dart
@override
void dispose() {
  // ✅ 记得清理资源
  _chartController.dispose();
  super.dispose();
}
```

### 3. 状态管理
```dart
// ✅ 监听加载状态
_chartController.addListener(() {
  if (mounted) {
    setState(() {
      _isLoading = _chartController.isLoading;
    });
  }
});
```

## 故障排除

### 常见问题

1. **数据不更新**
   - 检查dataProvider是否正确设置
   - 确认API返回的数据格式正确
   - 查看日志中的错误信息

2. **性能问题**
   - 调整缓存大小配置
   - 检查防抖延迟设置
   - 监控内存使用情况

3. **重复请求**
   - 确认请求去重机制正常工作
   - 检查防抖配置是否合适

### 调试技巧

1. **启用详细日志**
```dart
await LogConfigManager.switchToDevelopmentMode();
```

2. **监控缓存状态**
```dart
final cachedData = ChartDataManager.instance.getCachedData(symbol, timeFrame);
print('缓存数据量: ${cachedData.length}');
```

3. **检查请求状态**
```dart
// 在数据提供者中添加日志
AppLogger.logModule('Debug', LogLevel.info, '数据请求参数', metadata: {
  'symbol': symbol,
  'timeFrame': timeFrame,
  'range': range.toString(),
});
```

## 总结

这个图表数据更新机制提供了：
- ✅ 自动的数据加载触发
- ✅ 高性能的缓存管理
- ✅ 智能的请求去重
- ✅ 完善的错误处理
- ✅ 详细的日志监控

通过正确实现数据提供者方法，其他模块可以轻松集成这个机制，实现高效的图表数据更新功能。
