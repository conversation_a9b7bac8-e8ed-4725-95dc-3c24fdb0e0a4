# 图表初始化问题解决方案

## 问题描述

错误信息：`❌ Chart not ready, skipping setData.`

这个错误出现的原因是在图表组件完全初始化之前就尝试设置数据。由于 WebView 和 JavaScript 的异步加载特性，图表的初始化需要一定时间。

## 问题分析

### 时序问题
1. **Flutter 端**：页面加载 → BlocConsumer 监听到 `KlineDataLoaded2` 状态 → 调用 `setData()`
2. **WebView 端**：加载 HTML → 执行 JavaScript → 初始化图表 → 发送 `onChartReady` 事件
3. **冲突**：Flutter 端可能在 WebView 端完成初始化之前就尝试设置数据

### 原始代码流程
```dart
// 1. BlocConsumer 监听状态变化
listener: (context, state) {
  if (state is KlineDataLoaded2) {
    tradingChartKey.currentState?.setData(state.klineData.cast<KLineData>());
  }
}

// 2. setData 检查图表状态
void setData(List<KLineData> data) {
  if (!_isChartReady) {
    print('❌ Chart not ready, skipping setData.');
    return; // 数据丢失！
  }
  // 设置数据...
}
```

## 解决方案

### 方案1：数据缓存机制（已实现）

在图表组件中添加数据缓存，当图表未准备好时先缓存数据，等图表准备好后自动设置。

#### 核心改进：

1. **添加数据缓存**：
```dart
class _TradingChartWidgetState extends State<TradingChartWidget> {
  bool _isChartReady = false;
  List<KLineData>? _pendingData; // 缓存待设置的数据
}
```

2. **改进 setData 方法**：
```dart
void setData(List<KLineData> data) {
  if (!_isChartReady) {
    // 图表未准备好，缓存数据
    _pendingData = List.from(data);
    debugPrint('📦 Chart not ready, caching data for later use.');
    return;
  }
  _setDataInternal(data);
}
```

3. **图表准备好时自动设置缓存数据**：
```dart
onMessageReceived: (JavaScriptMessage message) {
  final data = jsonDecode(message.message);
  if (data['event'] == 'onChartReady') {
    setState(() {
      _isChartReady = true;
    });
    // 如果有缓存的数据，立即设置
    if (_pendingData != null) {
      _setDataInternal(_pendingData!);
      _pendingData = null;
    }
  }
}
```

### 方案2：延迟设置数据

在调用端添加延迟机制：

```dart
listener: (context, state) {
  if (state is KlineDataLoaded2) {
    // 延迟设置数据，确保图表已初始化
    Future.delayed(const Duration(milliseconds: 500), () {
      tradingChartKey.currentState?.setData(state.klineData.cast<KLineData>());
    });
  }
}
```

### 方案3：状态监听机制

添加图表状态监听：

```dart
class TradingChartWidget extends StatefulWidget {
  final VoidCallback? onChartReady;
  
  const TradingChartWidget({
    Key? key,
    this.onChartReady,
  }) : super(key: key);
}

// 在图表准备好时回调
if (data['event'] == 'onChartReady') {
  setState(() {
    _isChartReady = true;
  });
  widget.onChartReady?.call();
}
```

## 推荐方案

**推荐使用方案1（数据缓存机制）**，因为：

1. **用户体验好**：无需额外延迟，数据会在图表准备好的第一时间显示
2. **可靠性高**：不依赖固定的延迟时间，适应不同设备的性能差异
3. **代码简洁**：调用端无需修改，所有逻辑封装在图表组件内部
4. **数据不丢失**：确保所有数据都能正确设置到图表中

## 使用方式

修改后的使用方式保持不变：

```dart
// 在任何时候调用都是安全的
tradingChartKey.currentState?.setData(klineData);
```

## 调试信息

新的日志输出：
- `📦 Chart not ready, caching data for later use.` - 数据被缓存
- `➡️ Sending data to JS: ...` - 数据正在发送到 JavaScript

## 注意事项

1. **内存管理**：缓存的数据在设置后会被清空，避免内存泄漏
2. **数据覆盖**：如果在图表准备好之前多次调用 `setData()`，只有最后一次的数据会被保留
3. **错误处理**：如果 JavaScript 端出现错误，缓存的数据可能不会被设置

## 扩展功能

可以进一步扩展的功能：

1. **多数据缓存**：支持缓存多个不同类型的数据
2. **超时处理**：如果图表长时间未准备好，清空缓存并报错
3. **重试机制**：在数据设置失败时自动重试

## 测试建议

1. **快速数据加载**：测试数据在图表初始化之前就准备好的情况
2. **慢速网络**：测试在慢速网络下图表初始化的表现
3. **多次调用**：测试快速多次调用 `setData()` 的情况
4. **错误恢复**：测试 WebView 加载失败后的恢复机制
