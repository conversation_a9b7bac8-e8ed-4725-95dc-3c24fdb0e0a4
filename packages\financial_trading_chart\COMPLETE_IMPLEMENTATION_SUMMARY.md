# 图表可见范围变化数据更新完整实现方案

## 方案概述

本方案实现了一个高性能的图表数据更新机制，当图表的可见数据范围发生变化时，会自动触发历史数据加载，并通过全局API更新图表。系统具备完善的性能优化和防重复请求机制。

## 核心架构

```mermaid
graph TB
    A[JavaScript图表] --> B[可见范围变化监听]
    B --> C[防抖处理]
    C --> D[ChartDataController]
    D --> E[ChartDataManager]
    E --> F{检查缓存}
    F -->|缓存不足| G[请求去重检查]
    F -->|缓存充足| H[使用缓存数据]
    G -->|无重复请求| I[调用数据提供者]
    G -->|有重复请求| J[等待现有请求]
    I --> K[外部API服务]
    K --> L[更新缓存]
    L --> M[通知图表更新]
    H --> M
    J --> M
```

## 核心组件

### 1. ChartDataManager (数据管理器)
**文件**: `packages/financial_trading_chart/lib/src/data/chart_data_manager.dart`

**核心功能**:
- 智能缓存管理（按交易对和时间框架分组）
- 请求去重机制（防止相同请求重复发送）
- 防抖处理（300ms延迟，避免频繁触发）
- 性能优化（数据截断、缓存大小限制）

**关键方法**:
```dart
// 处理可见范围变化
void handleVisibleRangeChange(String chartId, String symbol, String timeFrame, VisibleRange range)

// 处理数据响应
Future<void> handleDataResponse(String symbol, String timeFrame, List<ChartData> data)

// 获取缓存数据
List<ChartData> getCachedData(String symbol, String timeFrame)
```

### 2. ChartDataController (数据控制器)
**文件**: `packages/financial_trading_chart/lib/src/controllers/chart_data_controller.dart`

**核心功能**:
- 协调图表显示和数据管理
- 处理WebView消息通信
- 管理图表状态和生命周期
- 提供数据流订阅

**关键方法**:
```dart
// 初始化控制器
void initialize({required String chartId, required String symbol, required String timeFrame, ...})

// 处理WebView消息
void handleWebViewMessage(String message)

// 切换交易对/时间框架
void changeSymbol(String symbol)
void changeTimeFrame(String timeFrame)
```

### 3. ChartAPI (全局API)
**文件**: `packages/financial_trading_chart/lib/src/api/chart_api.dart`

**核心功能**:
- 提供简化的全局接口
- 管理多个图表实例
- 统一的状态管理和监控
- 资源清理和生命周期管理

**关键方法**:
```dart
// 设置全局数据提供者
void setGlobalDataProvider(Future<List<ChartData>> Function(...) provider)

// 创建图表实例
String createChart({required String symbol, required String timeFrame, ...})

// 更新图表数据
void updateChart(String chartId, List<ChartData> data)
```

### 4. JavaScript图表控制器
**文件**: `packages/financial_trading_chart/assets/js/chart_controller.js`

**核心功能**:
- 监听图表可见范围变化
- 防抖处理和智能判断
- 与Flutter端通信
- 数据边界检测

**关键函数**:
```javascript
// 可见范围变化处理
function handleVisibleRangeChange(logicalRange)

// 判断是否需要加载更多数据
function shouldLoadMoreData(logicalRange)

// 请求历史数据
function requestHistoricalData(logicalRange)
```

## 工作流程详解

### 1. 初始化阶段
```dart
// 1. 设置全局数据提供者（在应用启动时）
ChartAPI.instance.setGlobalDataProvider(_fetchHistoricalData);

// 2. 创建图表实例（在页面中）
final chartId = ChartAPI.instance.createChart(
  symbol: 'BTCUSDT',
  timeFrame: '15m',
);

// 3. 监听状态变化
ChartAPI.instance.addChartListener(chartId, _onStateChanged);
```

### 2. 可见范围变化触发
```javascript
// JavaScript端监听
chart.timeScale().subscribeVisibleLogicalRangeChange(logicalRange => {
    // 防抖处理
    if (window.rangeChangeTimeout) {
        clearTimeout(window.rangeChangeTimeout);
    }
    
    window.rangeChangeTimeout = setTimeout(() => {
        handleVisibleRangeChange(logicalRange);
    }, 300);
});
```

### 3. 数据请求处理
```dart
// Flutter端处理
void _handleDataRequest(ChartDataRequest request) {
  // 调用外部数据提供者
  dataProvider!(request.symbol, request.timeFrame, request.range)
    .then((data) => _dataManager.handleDataResponse(...))
    .catchError((error) => _dataManager.handleDataError(...));
}
```

### 4. 缓存和性能优化
```dart
// 智能缓存检查
bool _shouldRequestMoreData(List<ChartData> cachedData, VisibleRange range) {
  if (cachedData.isEmpty) return true;
  
  final bufferRatio = config.dataBufferRatio; // 20%缓冲区
  return rangeStart < (cacheStart + buffer) || rangeEnd > (cacheEnd - buffer);
}

// 请求去重
if (_pendingRequests.containsKey(requestKey)) {
  // 复用正在进行的请求
  return;
}
```

## 使用示例

### 基础使用（其他模块集成）
```dart
class YourTradingPage extends StatefulWidget {
  @override
  State<YourTradingPage> createState() => _YourTradingPageState();
}

class _YourTradingPageState extends State<YourTradingPage> {
  late String _chartId;

  @override
  void initState() {
    super.initState();
    
    // 1. 设置数据提供者（通常在模块初始化时）
    ChartAPI.instance.setGlobalDataProvider(_fetchYourApiData);
    
    // 2. 创建图表
    _chartId = ChartAPI.instance.createChart(
      symbol: 'BTCUSDT',
      timeFrame: '15m',
    );
    
    // 3. 监听状态
    ChartAPI.instance.addChartListener(_chartId, _onChartStateChanged);
  }

  // 4. 实现数据获取方法
  Future<List<ChartData>> _fetchYourApiData(
    String symbol,
    String timeFrame,
    VisibleRange range,
  ) async {
    final apiService = locator<YourApiService>();
    return await apiService.getKlineData(
      symbol: symbol,
      interval: timeFrame,
      startTime: range.from,
      endTime: range.to,
    );
  }

  void _onChartStateChanged() {
    final status = ChartAPI.instance.getChartStatus(_chartId);
    setState(() {
      // 更新UI状态
    });
  }

  @override
  void dispose() {
    ChartAPI.instance.destroyChart(_chartId);
    super.dispose();
  }
}
```

### 实时数据更新
```dart
// WebSocket数据推送时调用
void onWebSocketData(List<ChartData> newData) {
  ChartAPI.instance.updateChart(_chartId, newData);
}

// 手动刷新
void refreshChart() {
  ChartAPI.instance.refreshChart(_chartId);
}

// 切换交易对
void changeSymbol(String symbol) {
  ChartAPI.instance.changeSymbol(_chartId, symbol);
}
```

## 性能优化特性

### 1. 智能缓存
- **分层缓存**: 按交易对和时间框架分组
- **大小限制**: 默认最大5000条数据，自动清理
- **命中率优化**: 20%缓冲区，预加载相邻数据

### 2. 请求优化
- **去重机制**: 相同请求自动合并
- **防抖处理**: 300ms延迟，避免频繁触发
- **超时控制**: 10秒超时，防止请求堆积

### 3. 内存管理
- **数据截断**: 长数据自动截断显示
- **资源清理**: 完善的生命周期管理
- **流控制**: 自动关闭无用的数据流

## 配置选项

```dart
const config = ChartDataManagerConfig(
  maxCacheSize: 5000,                           // 最大缓存大小
  debounceDelay: Duration(milliseconds: 300),   // 防抖延迟
  requestTimeout: Duration(seconds: 10),        // 请求超时
  dataBufferRatio: 0.2,                        // 数据缓冲比例
);
```

## 日志监控

系统提供详细的日志记录：
- 📊 可见范围变化事件
- 🚀 数据请求发起
- ✅ 数据获取成功
- ❌ 错误处理
- 🔄 重试机制
- 📦 缓存操作

## 错误处理

- **网络错误**: 自动重试机制
- **数据格式错误**: 验证和容错处理
- **超时处理**: 自动取消和清理
- **内存溢出**: 缓存大小限制

## 扩展性

- **多图表支持**: 同时管理多个图表实例
- **自定义数据源**: 灵活的数据提供者接口
- **插件化架构**: 易于扩展新功能
- **跨模块通信**: 标准化的API接口

## 总结

这个完整的实现方案提供了：

✅ **自动触发**: 可见范围变化自动加载数据  
✅ **高性能**: 智能缓存和请求优化  
✅ **防重复**: 完善的去重和防抖机制  
✅ **易集成**: 简化的API接口  
✅ **可监控**: 详细的日志和状态管理  
✅ **可扩展**: 模块化和插件化设计  

其他模块只需要：
1. 实现数据获取方法
2. 调用ChartAPI创建图表
3. 监听状态变化更新UI
4. 在适当时机清理资源

系统会自动处理所有复杂的数据管理、性能优化和错误处理逻辑。
