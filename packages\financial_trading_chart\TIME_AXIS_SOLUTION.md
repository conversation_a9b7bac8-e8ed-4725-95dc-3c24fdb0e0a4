# 图表时间轴显示问题解决方案

## 问题描述

图表上没有显示时间轴，主要原因是：

1. **时间格式不匹配**：LightweightCharts 需要特定的时间格式
2. **数据转换错误**：时间戳没有正确转换为图表库要求的格式
3. **时间轴配置缺失**：图表配置中缺少时间轴的显示设置

## LightweightCharts 时间格式要求

LightweightCharts 支持以下时间格式：

### 1. 日期字符串格式（推荐）
```javascript
{ time: '2024-01-01', open: 100, high: 105, low: 98, close: 103 }
```

### 2. 时间戳格式（秒级）
```javascript
{ time: 1704067200, open: 100, high: 105, low: 98, close: 103 }
```

### 3. 日期时间字符串格式
```javascript
{ time: '2024-01-01T00:00:00', open: 100, high: 105, low: 98, close: 103 }
```

## 解决方案

### 1. 修改数据转换逻辑

在 `chart_controller.js` 中，将时间戳转换为 `YYYY-MM-DD` 格式：

```javascript
// 转换数据格式，确保时间轴正确
const formattedData = parsedData.map((item, index) => {
    try {
        let timeValue;
        
        if (item.timestamp) {
            // 优先使用 timestamp 字段（ISO8601 格式）
            const date = new Date(item.timestamp);
            if (isNaN(date.getTime())) {
                throw new Error('Invalid timestamp format');
            }
            // 转换为 YYYY-MM-DD 格式
            timeValue = date.toISOString().split('T')[0];
        } else if (item.time) {
            // 备用 time 字段
            if (typeof item.time === 'string') {
                // 如果已经是 YYYY-MM-DD 格式，直接使用
                if (/^\d{4}-\d{2}-\d{2}$/.test(item.time)) {
                    timeValue = item.time;
                } else {
                    // 否则尝试解析为日期
                    const date = new Date(item.time);
                    if (isNaN(date.getTime())) {
                        throw new Error('Invalid time format');
                    }
                    timeValue = date.toISOString().split('T')[0];
                }
            } else if (typeof item.time === 'number') {
                // 如果是数字时间戳，转换为日期
                const timestamp = item.time > 1000000000000 ? item.time : item.time * 1000;
                const date = new Date(timestamp);
                timeValue = date.toISOString().split('T')[0];
            } else {
                throw new Error('Unsupported time format');
            }
        } else {
            throw new Error('No timestamp or time field found');
        }

        return {
            time: timeValue,
            open: parseFloat(item.open),
            high: parseFloat(item.high),
            low: parseFloat(item.low),
            close: parseFloat(item.close)
        };
    } catch (error) {
        console.error(`❌ Error processing data item ${index}:`, error.message, item);
        return null;
    }
}).filter(item => item !== null);
```

### 2. 配置时间轴显示

在图表初始化时添加时间轴配置：

```javascript
chart = LightweightCharts.createChart(chartContainer, {
    layout: { 
        background: { color: '#161a25' }, 
        textColor: '#d1d4dc' 
    },
    grid: { 
        vertLines: { color: 'rgba(255, 255, 255, 0.1)' }, 
        horzLines: { color: 'rgba(255, 255, 255, 0.1)' } 
    },
    crosshair: { 
        mode: LightweightCharts.CrosshairMode.Normal 
    },
    timeScale: {
        visible: true,                    // 显示时间轴
        timeVisible: true,                // 显示时间
        secondsVisible: false,            // 不显示秒
        borderColor: 'rgba(255, 255, 255, 0.2)',
        tickMarkFormatter: (time) => {
            // time 现在是 YYYY-MM-DD 格式的字符串
            const date = new Date(time);
            return date.toLocaleDateString('zh-CN', {
                month: '2-digit',
                day: '2-digit'
            });
        }
    },
    rightPriceScale: {
        visible: true,                    // 显示价格轴
        borderColor: 'rgba(255, 255, 255, 0.2)',
        scaleMargins: {
            top: 0.1,
            bottom: 0.1,
        },
    },
    leftPriceScale: {
        visible: false,                   // 隐藏左侧价格轴
    },
});
```

### 3. 确保数据格式正确

在 Flutter 端，确保 KLineData 包含正确的时间字段：

```dart
KLineData(
  timestamp: DateTime.now(),
  time: DateTime.now().toIso8601String().split('T')[0], // YYYY-MM-DD 格式
  open: 100.0,
  high: 105.0,
  low: 98.0,
  close: 103.0,
  volume: 1000000,
)
```

## 数据流程

### 1. Flutter 端数据准备
```dart
// KLineData 对象
final klineData = KLineData(
  timestamp: DateTime(2024, 1, 1),  // DateTime 对象
  time: '2024-01-01',               // 字符串格式
  open: 100.0,
  high: 105.0,
  low: 98.0,
  close: 103.0,
);

// JSON 序列化后
{
  "timestamp": "2024-01-01T00:00:00.000",
  "time": "2024-01-01",
  "open": 100.0,
  "high": 105.0,
  "low": 98.0,
  "close": 103.0
}
```

### 2. JavaScript 端数据处理
```javascript
// 接收到的数据
const receivedData = {
  timestamp: "2024-01-01T00:00:00.000",
  time: "2024-01-01",
  open: 100.0,
  high: 105.0,
  low: 98.0,
  close: 103.0
};

// 转换后的数据
const chartData = {
  time: "2024-01-01",  // YYYY-MM-DD 格式
  open: 100.0,
  high: 105.0,
  low: 98.0,
  close: 103.0
};
```

### 3. 图表显示
- 时间轴显示：`01-01`（月-日格式）
- 蜡烛图正常显示
- 十字线显示完整日期

## 测试验证

### 1. 使用测试数据
```dart
final testData = [
  KLineData(
    timestamp: DateTime(2024, 1, 1),
    time: '2024-01-01',
    open: 100.0, high: 105.0, low: 98.0, close: 103.0,
  ),
  KLineData(
    timestamp: DateTime(2024, 1, 2),
    time: '2024-01-02',
    open: 103.0, high: 108.0, low: 101.0, close: 106.0,
  ),
  // ... 更多数据
];

chartKey.currentState?.setData(testData);
```

### 2. 检查控制台输出
```
📊 Parsed data: [...]
✅ Formatted data for chart: [...]
✅ Chart data set successfully
```

### 3. 验证时间轴显示
- 时间轴应该显示在图表底部
- 显示格式为 `MM-dd`（如：01-01, 01-02）
- 鼠标悬停时显示完整日期

## 常见问题

### 1. 时间轴仍然不显示
- 检查数据是否包含有效的时间字段
- 确认时间格式是否正确
- 查看控制台是否有错误信息

### 2. 时间显示格式不正确
- 检查 `tickMarkFormatter` 函数
- 确认时区设置
- 验证日期解析逻辑

### 3. 数据点不连续
- 确保时间数据是连续的
- 检查时间排序是否正确
- 验证数据完整性

## 最佳实践

1. **统一时间格式**：在整个应用中使用统一的时间格式
2. **数据验证**：在设置数据前验证时间字段的有效性
3. **错误处理**：添加完善的错误处理和日志记录
4. **性能优化**：对于大量数据，考虑分页加载
5. **用户体验**：提供加载状态和错误提示

通过以上解决方案，图表的时间轴应该能够正确显示，用户可以清楚地看到K线数据对应的时间信息。
