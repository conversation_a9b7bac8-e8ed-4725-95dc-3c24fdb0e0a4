# TradingView双格式支持指南

## 问题解决

✅ **K线图格式**: `time, open, high, low, close`  
✅ **折线图格式**: `time, value`  
✅ **移除volume字段**: 因为用不到  
✅ **双接口支持**: 接口1获取初始数据，接口2获取历史数据

## 核心实现

### 1. TradingView数据模型

```dart
// 数据类型枚举
enum TradingViewDataType {
  candlestick, // K线图
  line,        // 折线图
}

// K线图数据
class TradingViewCandlestickData extends TradingViewData {
  final double open;
  final double high;
  final double low;
  final double close;
  // 注意：没有volume字段

  @override
  Map<String, dynamic> toTradingViewFormat() {
    return {
      'time': time,
      'open': open,
      'high': high,
      'low': low,
      'close': close,
    };
  }
}

// 折线图数据
class TradingViewLineData extends TradingViewData {
  final double value;

  @override
  Map<String, dynamic> toTradingViewFormat() {
    return {
      'time': time,
      'value': value,
    };
  }
}
```

### 2. 双接口服务实现

```dart
class DualApiServiceImpl implements DualApiService {
  final GetLineData _getLineDataUseCase;      // 接口1
  final MarketApiService _marketApiService;   // 接口2
  final TradingViewDataType _dataType;        // 数据类型

  /// 获取初始数据（接口1）
  @override
  Future<List<TradingViewData>> getInitialData({
    required String symbol,
    required String timeFrame,
    int? limit,
  }) async {
    // 🔑 使用接口1
    final result = await _getLineDataUseCase(
      symbol: symbol,
      bar: timeFrame,
      limit: limit ?? 100,
      befor: DateTime.now().millisecondsSinceEpoch,
      t: DateTime.now().millisecondsSinceEpoch,
    );

    // 🔑 根据数据类型转换
    return _convertToTradingViewData(result, timeFrame);
  }

  /// 获取历史数据（接口2）
  @override
  Future<List<TradingViewData>> getHistoricalData({
    required String symbol,
    required String timeFrame,
    required VisibleRange range,
    int? limit,
  }) async {
    // 🔑 使用接口2
    final klineData = await _marketApiService.getKlineData(
      symbol: symbol,
      interval: timeFrame,
      limit: limit ?? (range.to - range.from + 50),
    );

    // 🔑 根据数据类型转换
    return _convertKlineToTradingViewData(klineData, timeFrame);
  }

  /// 🔑 转换GetLineData结果
  List<TradingViewData> _convertToTradingViewData(List<dynamic> result, String timeFrame) {
    switch (_dataType) {
      case TradingViewDataType.candlestick:
        return result.map((e) => TradingViewCandlestickData.fromTimestamp(
          timestampMs: e.time,
          open: e.open,
          high: e.hight, // 保持原有拼写
          low: e.low,
          close: e.close,
          timeFrame: timeFrame,
        )).toList();
      
      case TradingViewDataType.line:
        return result.map((e) => TradingViewLineData.fromTimestamp(
          timestampMs: e.time,
          value: e.close, // 使用收盘价
          timeFrame: timeFrame,
        )).toList();
    }
  }

  /// 🔑 转换KlineData结果
  List<TradingViewData> _convertKlineToTradingViewData(List<dynamic> klineData, String timeFrame) {
    switch (_dataType) {
      case TradingViewDataType.candlestick:
        return klineData.map((item) => TradingViewCandlestickData.fromTimestamp(
          timestampMs: item.openTime,
          open: item.open,
          high: item.high,
          low: item.low,
          close: item.close,
          timeFrame: timeFrame,
        )).toList();
      
      case TradingViewDataType.line:
        return klineData.map((item) => TradingViewLineData.fromTimestamp(
          timestampMs: item.openTime,
          value: item.close, // 使用收盘价
          timeFrame: timeFrame,
        )).toList();
    }
  }
}
```

### 3. Bloc中的使用

```dart
class MarketBloc extends Bloc<MarketEvent, MarketState> {
  final DualApiService _dualApiService;
  final ChartAPI _chartAPI;
  bool _isInitialDataLoaded = false;
  TradingViewDataType _currentChartType = TradingViewDataType.candlestick;

  /// 🔑 处理图表数据请求
  Future<List<TradingViewData>> _handleChartDataRequest(
    String symbol,
    String timeFrame,
    VisibleRange range,
  ) async {
    if (!_isInitialDataLoaded) {
      // 第一次：使用接口1
      final data = await _dualApiService.getInitialData(
        symbol: symbol,
        timeFrame: timeFrame,
        limit: 100,
      );
      _isInitialDataLoaded = true;
      return data;
    } else {
      // 后续：使用接口2
      return await _dualApiService.getHistoricalData(
        symbol: symbol,
        timeFrame: timeFrame,
        range: range,
      );
    }
  }

  /// 🔑 切换图表类型
  Future<void> _onSwitchChartType(
    SwitchChartTypeEvent event,
    Emitter<MarketState> emit,
  ) async {
    _currentChartType = event.chartType;
    
    // 重新创建服务实例以使用新的数据类型
    _dualApiService = DualApiServiceImpl(
      getLineDataUseCase: _getLineDataUseCase,
      marketApiService: _marketApiService,
      dataType: _currentChartType,
    );
    
    // 重置初始数据标记
    _isInitialDataLoaded = false;
    
    // 刷新图表
    if (state.chartId != null) {
      _chartAPI.refreshChart(state.chartId!);
    }
  }
}
```

### 4. JavaScript图表控制器

```javascript
// 🔑 更新图表数据 - 支持两种格式
function updateChartData(data, chartType = 'candlestick') {
    if (chartType === 'candlestick') {
        updateCandlestickChart(data);
    } else if (chartType === 'line') {
        updateLineChart(data);
    }
}

// K线图更新
function updateCandlestickChart(data) {
    const formattedData = data.map(item => ({
        time: item.time,
        open: parseFloat(item.open),
        high: parseFloat(item.high),
        low: parseFloat(item.low),
        close: parseFloat(item.close),
        // 注意：没有volume字段
    }));
    
    if (candlestickSeries) {
        candlestickSeries.setData(formattedData);
    }
}

// 折线图更新
function updateLineChart(data) {
    const formattedData = data.map(item => ({
        time: item.time,
        value: parseFloat(item.value),
    }));
    
    if (lineSeries) {
        lineSeries.setData(formattedData);
    }
}

// Flutter调用的API
window.flutterChart = {
    // 设置数据（支持两种格式）
    setData: function(data, timeFrame, chartType = 'candlestick') {
        updateChartData(data, chartType);
    },
    
    // 切换图表类型
    switchChartType: function(chartType) {
        switchChartType(chartType);
    },
    
    // 专用方法
    setCandlestickData: function(data, timeFrame) {
        this.setData(data, timeFrame, 'candlestick');
    },
    
    setLineData: function(data, timeFrame) {
        this.setData(data, timeFrame, 'line');
    },
};
```

## 使用示例

### 1. 创建K线图页面

```dart
class KlineTradingPage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => MarketBloc(
        dualApiService: DualApiServiceImpl(
          getLineDataUseCase: context.read<GetLineData>(),
          marketApiService: context.read<MarketApiService>(),
          dataType: TradingViewDataType.candlestick, // K线图
        ),
        chartAPI: ChartAPI.instance,
      )..add(InitializeChart(symbol: 'BTCUSDT', timeFrame: '15m')),
      child: const KlineTradingView(),
    );
  }
}
```

### 2. 创建折线图页面

```dart
class LineTradingPage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => MarketBloc(
        dualApiService: DualApiServiceImpl(
          getLineDataUseCase: context.read<GetLineData>(),
          marketApiService: context.read<MarketApiService>(),
          dataType: TradingViewDataType.line, // 折线图
        ),
        chartAPI: ChartAPI.instance,
      )..add(InitializeChart(symbol: 'BTCUSDT', timeFrame: '15m')),
      child: const LineTradingView(),
    );
  }
}
```

### 3. 动态切换图表类型

```dart
// 在页面中添加切换按钮
ElevatedButton(
  onPressed: () {
    context.read<MarketBloc>().add(
      SwitchChartTypeEvent(TradingViewDataType.line),
    );
  },
  child: const Text('切换到折线图'),
),

ElevatedButton(
  onPressed: () {
    context.read<MarketBloc>().add(
      SwitchChartTypeEvent(TradingViewDataType.candlestick),
    );
  },
  child: const Text('切换到K线图'),
),
```

## 数据格式对比

### K线图数据格式
```json
{
  "time": "**********",
  "open": 45000.0,
  "high": 45100.0,
  "low": 44900.0,
  "close": 45050.0
}
```

### 折线图数据格式
```json
{
  "time": "**********",
  "value": 45050.0
}
```

## 关键优势

✅ **格式兼容**: 完美支持TradingView的两种数据格式  
✅ **接口分离**: 初始数据和历史数据使用不同接口  
✅ **性能优化**: 移除不需要的volume字段  
✅ **类型安全**: 强类型的数据模型  
✅ **灵活切换**: 支持运行时切换图表类型  
✅ **错误处理**: 完善的数据验证和错误处理

这个方案完美解决了TradingView双格式支持的问题，同时保持了双接口的优势。
