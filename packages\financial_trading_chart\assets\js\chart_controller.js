// 日志 1: 脚本文件开始执行
console.log('chart_controller.js script started.');

// 确保在任何操作之前先添加监听器
// 日志 2: 确认监听器已被附加
console.log('Attaching DOMContentLoaded listener...');
document.addEventListener('DOMContentLoaded', function () {
    // 日志 3: 确认 DOMContentLoaded 事件已经触发
    console.log('✅ DOM fully loaded and parsed. Initializing chart...');

    let chart;
    let candlestickSeries;

    const chartContainer = document.getElementById('chart-container');

    // 日志 4: 确认我们是否找到了容器元素
    console.log('Attempting to find container element:', chartContainer);

    if (!chartContainer) {
        console.error('❌ FATAL: chart-container element NOT FOUND!');
        return;
    }

    // 检查容器尺寸
    console.log('📦 Container initial size:', {
        width: chartContainer.offsetWidth,
        height: chartContainer.offsetHeight,
        clientWidth: chartContainer.clientWidth,
        clientHeight: chartContainer.clientHeight
    });

    try {
        chart = LightweightCharts.createChart(chartContainer, {
            layout: {
                background: { color: '#161a25' },
                textColor: '#d1d4dc'
            },
            grid: {
                vertLines: { color: 'rgba(255, 255, 255, 0.1)' },
                horzLines: { color: 'rgba(255, 255, 255, 0.1)' }
            },
            crosshair: {
                mode: LightweightCharts.CrosshairMode.Normal
            },
            timeScale: {
                visible: true,
                timeVisible: true,
                secondsVisible: false,
                borderColor: 'rgba(255, 255, 255, 0.2)',
                // 初始化时使用默认格式，后续会通过updateTimeScaleFormat动态更新
                tickMarkFormatter: (time) => {
                    if (typeof time === 'string') {
                        // 如果是字符串格式，直接返回
                        return time;
                    }
                    // 如果是时间戳，转换为默认格式
                    const date = new Date(time * 1000);
                    return date.toLocaleDateString('zh-CN', {
                        month: '2-digit',
                        day: '2-digit'
                    });
                }
            },
            rightPriceScale: {
                visible: true,
                borderColor: 'rgba(255, 255, 255, 0.2)',
                scaleMargins: {
                    top: 0.1,
                    bottom: 0.1,
                },
            },
            leftPriceScale: {
                visible: false,
            },
        });

        // 日志 5: 确认图表对象是否被成功创建
        console.log('✅ Chart object created successfully:', chart);

        // 确保图表有正确的尺寸
        const containerWidth = chartContainer.offsetWidth || 800;
        const containerHeight = chartContainer.offsetHeight || 400;

        if (containerWidth === 0 || containerHeight === 0) {
            console.warn('⚠️ Container has zero size, using default dimensions');
            chart.resize(800, 400);
        } else {
            chart.resize(containerWidth, containerHeight);
        }

        console.log('📊 Chart resized to:', containerWidth, 'x', containerHeight);

        candlestickSeries = chart.addSeries(LightweightCharts.CandlestickSeries, {
            upColor: '#26a69a', downColor: '#ef5350',
            borderDownColor: '#ef5350', borderUpColor: '#26a69a',
            wickDownColor: '#ef5350', wickUpColor: '#26a69a',
        });
        // candlestickSeries = chart.addCandlestickSeries({
        //     upColor: '#26a69a', downColor: '#ef5350',
        //     borderDownColor: '#ef5350', borderUpColor: '#26a69a',
        //     wickDownColor: '#ef5350', wickUpColor: '#26a69a',
        // });

        // 日志 6: 确认 K 线序列是否被成功添加
        console.log('✅ Candlestick series added successfully:', candlestickSeries);

        // 可见范围变化监听器 - 用于触发历史数据加载
        chart.timeScale().subscribeVisibleLogicalRangeChange(logicalRange => {
            console.log('📊 可见范围变化:', logicalRange);

            // 防抖处理，避免频繁触发
            if (window.rangeChangeTimeout) {
                clearTimeout(window.rangeChangeTimeout);
            }

            window.rangeChangeTimeout = setTimeout(() => {
                handleVisibleRangeChange(logicalRange);
            }, 300); // 300ms 防抖延迟
        });
    } catch (error) {
        // 日志 7: 捕获并打印在 createChart 或 addCandlestickSeries 期间发生的任何错误
        console.error('❌ FATAL ERROR during chart initialization:', error);
        // 向上抛出错误，以便在 DevTools 中更显眼
        throw error;
    }


    // 处理可见范围变化
    function handleVisibleRangeChange(logicalRange) {
        if (!logicalRange) {
            console.warn('⚠️ 可见范围为空');
            return;
        }

        // 检查是否需要加载更多历史数据
        const needsMoreData = shouldLoadMoreData(logicalRange);

        if (needsMoreData) {
            requestHistoricalData(logicalRange);
        }
    }

    // 判断是否需要加载更多数据
    function shouldLoadMoreData(logicalRange) {
        // 如果可见范围接近数据边界，则需要加载更多数据
        const bufferSize = 50; // 缓冲区大小
        const totalDataPoints = candlestickSeries ? candlestickSeries.data().length : 0;

        // 如果可见范围的起始位置小于缓冲区大小，需要加载更早的数据
        if (logicalRange.from < bufferSize) {
            return true;
        }

        // 如果可见范围的结束位置接近总数据点数，需要加载更新的数据
        if (logicalRange.to > (totalDataPoints - bufferSize)) {
            return true;
        }

        return false;
    }

    // 请求历史数据
    function requestHistoricalData(logicalRange) {
        // 🔧 修复数据格式问题 - 将逻辑范围转换为时间戳
        const currentData = candlestickSeries ? candlestickSeries.data() : [];
        const currentDataCount = currentData.length;

        // 计算时间范围 - 基于现有数据推算
        let fromTimestamp, toTimestamp;

        if (currentData.length > 0) {
            // 获取第一个和最后一个数据点的时间
            const firstTime = currentData[0].time;
            const lastTime = currentData[currentData.length - 1].time;

            // 转换为时间戳（毫秒）
            const firstTimestamp = typeof firstTime === 'string' ?
                new Date(firstTime).getTime() : firstTime * 1000;
            const lastTimestamp = typeof lastTime === 'string' ?
                new Date(lastTime).getTime() : lastTime * 1000;

            // 计算时间间隔
            const timeInterval = currentData.length > 1 ?
                (lastTimestamp - firstTimestamp) / (currentData.length - 1) : 15 * 60 * 1000; // 默认15分钟

            // 根据逻辑范围计算实际时间范围
            fromTimestamp = Math.floor(firstTimestamp + logicalRange.from * timeInterval);
            toTimestamp = Math.floor(firstTimestamp + logicalRange.to * timeInterval);
        } else {
            // 如果没有数据，使用当前时间作为基准
            const now = Date.now();
            const interval = 15 * 60 * 1000; // 15分钟
            fromTimestamp = now - Math.abs(logicalRange.from) * interval;
            toTimestamp = now + Math.abs(logicalRange.to) * interval;
        }

        const requestData = {
            type: 'visibleRangeChanged',
            data: {
                from: fromTimestamp,
                to: toTimestamp,
                timestamp: Date.now(),
                currentDataCount: currentDataCount,
                logicalFrom: logicalRange.from,
                logicalTo: logicalRange.to
            }
        };

        // 通知 Flutter 端可见范围发生变化
        if (window.ChartBridge) {
            try {
                window.ChartBridge.postMessage(JSON.stringify(requestData));
            } catch (error) {
                console.error('❌ 发送可见范围变化事件失败:', error);
            }
        } else {
            console.error('❌ ChartBridge 不可用，无法发送事件');
        }
    }

    // 🔑 更新图表数据 - 支持K线图和折线图两种格式
    function updateChartData(data, chartType = 'candlestick') {
        console.log('📊 更新图表数据:', {
            dataCount: data.length,
            chartType: chartType,
            firstItem: data[0]
        });

        if (!data || data.length === 0) {
            console.warn('⚠️ 数据为空');
            return;
        }

        try {
            if (chartType === 'candlestick') {
                // 🔑 K线图格式：time, open, high, low, close
                updateCandlestickChart(data);
            } else if (chartType === 'line') {
                // 🔑 折线图格式：time, value
                updateLineChart(data);
            } else {
                console.error('❌ 不支持的图表类型:', chartType);
            }
        } catch (error) {
            console.error('❌ 更新图表数据失败:', error);
        }
    }

    // 🔑 更新K线图
    function updateCandlestickChart(data) {
        const formattedData = data.map(item => {
            let time = item.time;

            // 处理time字段格式
            if (typeof time === 'string') {
                if (time.includes('-') && !time.includes('T')) {
                    // 日期格式 "2024-01-15" - 保持原样
                    time = time;
                } else if (!isNaN(time)) {
                    // 时间戳字符串 - 转换为数字
                    time = parseInt(time);
                }
            }

            return {
                time: time,
                open: parseFloat(item.open),
                high: parseFloat(item.high),
                low: parseFloat(item.low),
                close: parseFloat(item.close),
                // 注意：不包含volume字段
            };
        });

        console.log('📊 K线图数据格式化完成:', {
            count: formattedData.length,
            sample: formattedData[0]
        });

        // 更新K线图
        if (candlestickSeries) {
            candlestickSeries.setData(formattedData);
        } else {
            console.warn('⚠️ K线图系列未初始化');
        }
    }

    // 🔑 更新折线图
    function updateLineChart(data) {
        const formattedData = data.map(item => {
            let time = item.time;

            // 处理time字段格式
            if (typeof time === 'string') {
                if (time.includes('-') && !time.includes('T')) {
                    // 日期格式 "2024-01-15" - 保持原样
                    time = time;
                } else if (!isNaN(time)) {
                    // 时间戳字符串 - 转换为数字
                    time = parseInt(time);
                }
            }

            return {
                time: time,
                value: parseFloat(item.value),
            };
        });

        console.log('📊 折线图数据格式化完成:', {
            count: formattedData.length,
            sample: formattedData[0]
        });

        // 更新折线图
        if (lineSeries) {
            lineSeries.setData(formattedData);
        } else {
            console.warn('⚠️ 折线图系列未初始化');
        }
    }

    // 🔑 切换图表类型
    function switchChartType(chartType) {
        console.log('🔄 切换图表类型:', chartType);

        if (chartType === 'candlestick') {
            // 显示K线图，隐藏折线图
            if (candlestickSeries) {
                chart.addSeries(candlestickSeries);
            }
            if (lineSeries) {
                chart.removeSeries(lineSeries);
            }
        } else if (chartType === 'line') {
            // 显示折线图，隐藏K线图
            if (lineSeries) {
                chart.addSeries(lineSeries);
            }
            if (candlestickSeries) {
                chart.removeSeries(candlestickSeries);
            }
        }

        // 通知Flutter端图表类型已切换
        if (window.ChartBridge) {
            const message = {
                type: 'chartTypeChanged',
                data: { chartType: chartType }
            };
            window.ChartBridge.postMessage(JSON.stringify(message));
        }
    }

    // 🔑 验证数据格式
    function validateDataFormat(data, expectedType) {
        if (!data || data.length === 0) {
            return { valid: false, error: '数据为空' };
        }

        const firstItem = data[0];

        if (expectedType === 'candlestick') {
            const requiredFields = ['time', 'open', 'high', 'low', 'close'];
            for (const field of requiredFields) {
                if (!(field in firstItem)) {
                    return { valid: false, error: `缺少必需字段: ${field}` };
                }
            }
        } else if (expectedType === 'line') {
            const requiredFields = ['time', 'value'];
            for (const field of requiredFields) {
                if (!(field in firstItem)) {
                    return { valid: false, error: `缺少必需字段: ${field}` };
                }
            }
        }

        return { valid: true };
    }

    // 定义供 Flutter 调用的全局 API
    window.flutterChart = {
        // 🔑 设置图表数据 - 兼容K线图和折线图
        setData: function (data, timeFrame, chartType = 'candlestick') {


            // 检查图表系列是否初始化
            if (chartType === 'candlestick' && !candlestickSeries) {
                console.error('❌ Candlestick series not initialized');
                return;
            }
            if (chartType === 'line' && !lineSeries) {
                console.error('❌ Line series not initialized');
                return;
            }

            try {
                let parsedData;

                // 解析数据
                if (typeof data === 'string') {
                    parsedData = JSON.parse(data);
                } else {
                    parsedData = data;
                }

                console.log('📊 Parsed data:', parsedData);
                console.log('📊 Data length:', parsedData.length);
                if (parsedData.length > 0) {
                    console.log('📊 First item:', parsedData[0]);
                    console.log('📊 Last item:', parsedData[parsedData.length - 1]);
                }

                // 根据时间段更新时间轴配置
                if (timeFrame) {
                    console.log('⏰ Updating time scale format for:', timeFrame);
                    updateTimeScaleFormat(timeFrame);
                    console.log('✅ Time scale format updated');
                } else {
                    console.warn('⚠️ No timeFrame provided, using default format');
                }

                // 转换数据格式，确保时间轴正确
                const formattedData = parsedData.map((item, index) => {
                    // LightweightCharts需要时间戳或YYYY-MM-DD格式
                    let timeValue;

                    try {
                        if (item.timestamp) {
                            // 优先使用timestamp，但需要根据时间框架处理
                            const date = new Date(item.timestamp);
                            if (isNaN(date.getTime())) {
                                throw new Error('Invalid timestamp format');
                            }

                            // 根据时间框架决定时间格式
                            if (timeFrame === '1day' || timeFrame === '1d') {
                                // 日线图：使用YYYY-MM-DD格式
                                timeValue = date.toISOString().split('T')[0];
                            } else {
                                // 🔧 根据时间框架生成正确的时间格式
                                timeValue = generateTimeByFrame(index, timeFrame);
                                console.log(`📅 数据项 ${index}: 生成时间 ${timeValue} (${timeFrame})`);
                            }
                        } else {
                            // 🔧 统一时间生成策略：无论原始数据格式如何，都生成连续的日期序列
                            const today = new Date();
                            today.setHours(9, 30, 0, 0);

                            // 🔧 统一使用时间生成函数
                            timeValue = generateTimeByFrame(index, timeFrame);

                            console.log(`📅 备用逻辑 - 数据项 ${index}: 生成日期 ${timeValue}`);
                        }

                        // 验证数值字段
                        const open = parseFloat(item.open);
                        const high = parseFloat(item.high);
                        const low = parseFloat(item.low);
                        const close = parseFloat(item.close);

                        if (isNaN(open) || isNaN(high) || isNaN(low) || isNaN(close)) {
                            throw new Error('Invalid OHLC values');
                        }

                        // 保存原始的格式化时间用于显示
                        const formattedTimeForDisplay = item.time || (item.timestamp ? formatTimeByFrame(new Date(item.timestamp), timeFrame) : '');

                        const result = {
                            time: timeValue, // LightweightCharts需要的时间格式
                            open: open,
                            high: high,
                            low: low,
                            close: close,
                            // customTime: formattedTimeForDisplay // 保存格式化的时间用于显示
                        };

                        // 记录第一个数据项的详细信息
                        if (index === 0) {
                            console.log('📊 第一个数据项处理结果:');
                            console.log('  - 原始数据:', item);
                            console.log('  - 处理后数据:', result);
                            console.log('  - timeValue:', timeValue);
                            console.log('  - formattedTimeForDisplay:', formattedTimeForDisplay);
                            console.log('  - 数据验证:');
                            console.log('    * timeValue类型:', typeof timeValue);
                            console.log('    * timeValue值:', timeValue);
                            console.log('    * timeValue格式有效性:', /^\d{4}-\d{2}-\d{2}$/.test(timeValue));
                            console.log('    * open有效性:', !isNaN(open) && isFinite(open));
                            console.log('    * high有效性:', !isNaN(high) && isFinite(high));
                            console.log('    * low有效性:', !isNaN(low) && isFinite(low));
                            console.log('    * close有效性:', !isNaN(close) && isFinite(close));
                        }

                        // 🔧 更新验证逻辑：支持UTCTimestamp和YYYY-MM-DD格式
                        const isValidTime = result.time && (
                            typeof result.time === 'number' || // UTCTimestamp
                            (typeof result.time === 'string' && /^\d{4}-\d{2}-\d{2}$/.test(result.time)) // YYYY-MM-DD
                        );

                        if (!isValidTime ||
                            !isFinite(result.open) ||
                            !isFinite(result.high) ||
                            !isFinite(result.low) ||
                            !isFinite(result.close) ||
                            isNaN(result.open) ||
                            isNaN(result.high) ||
                            isNaN(result.low) ||
                            isNaN(result.close)) {
                            console.warn(`⚠️ 跳过无效数据项 ${index}:`, result);
                            return null;
                        }

                        return result;
                    } catch (error) {
                        console.error(`❌ Error processing data item ${index}:`, error.message, item);
                        return null;
                    }
                }).filter(item => item !== null); // 过滤掉无效数据

                console.log('✅ Formatted data for chart:', formattedData);

                // 🔧 额外的数据安全检查
                if (formattedData.length === 0) {
                    console.error('❌ No valid data to display');
                    return;
                }

                // 检查前几个数据项的完整性
                const sampleData = formattedData.slice(0, 3);
                console.log('🔍 Sample data for verification:', sampleData);

                // 创建时间映射表，用于时间轴显示
                const timeMapping = new Map();
                formattedData.forEach((item) => {
                    if (item && item.customTime) {
                        timeMapping.set(item.time, item.customTime);
                    }
                });

                // 🔧 安全地设置数据到图表
                try {
                    console.log('设置的数据信息:', formattedData);
                    console.log('设置的数据信息22:', JSON.stringify(formattedData[0]));
                    candlestickSeries.setData(formattedData);
                    console.log('✅ Chart data set successfully');
                } catch (error) {
                    console.error('❌ Error setting chart data:', error);
                    console.log('🔍 Problematic data sample:', formattedData.slice(0, 5));
                    return;
                }

                // 更新时间轴格式化器以使用自定义时间
                if (timeMapping.size > 0) {
                    chart.timeScale().applyOptions({
                        tickMarkFormatter: (time) => {
                            try {
                                // 空值检查
                                if (!time) {
                                    console.warn('⚠️ Time is null in formatter');
                                    return '';
                                }

                                // 优先使用自定义时间映射
                                if (timeMapping.has(time)) {
                                    const customTime = timeMapping.get(time);
                                    return customTime || '';
                                }

                                // 备用格式化 - 添加安全检查
                                if (typeof time === 'string') {
                                    const date = new Date(time);
                                    if (!isNaN(date.getTime())) {
                                        return formatTimeByFrame(date, timeFrame);
                                    }
                                }

                                // 如果都失败了，返回原始时间
                                return String(time);
                            } catch (error) {
                                console.error('❌ Error in time formatter:', error, 'time:', time);
                                return String(time || '');
                            }
                        }
                    });
                    console.log('🕒 Custom time formatter applied');
                }

                // 自动调整视图以显示所有数据
                chart.timeScale().fitContent();
                console.log('📊 Chart fitted to content');

                // 检查图表可见性和强制显示
                setTimeout(() => {
                    try {
                        // 检查容器尺寸
                        const container = document.getElementById('chart-container');
                        console.log('📦 Container size:', {
                            width: container.offsetWidth,
                            height: container.offsetHeight,
                            display: window.getComputedStyle(container).display,
                            visibility: window.getComputedStyle(container).visibility
                        });

                        // 强制设置图表尺寸
                        if (container.offsetWidth === 0 || container.offsetHeight === 0) {
                            console.warn('⚠️ Container has zero size, forcing resize');
                            chart.resize(800, 400);
                        } else {
                            chart.resize(container.offsetWidth, container.offsetHeight);
                        }

                        // 再次尝试调整内容
                        chart.timeScale().fitContent();

                        const visibleRange = chart.timeScale().getVisibleRange();
                        console.log('👁️ Visible range after resize:', visibleRange);

                        // 如果仍然没有可见范围，手动设置
                        if (!visibleRange && formattedData.length > 0) {
                            const firstTime = formattedData[0].time;
                            const lastTime = formattedData[formattedData.length - 1].time;
                            console.log('🔧 Manually setting visible range:', firstTime, 'to', lastTime);

                            chart.timeScale().setVisibleRange({
                                from: firstTime,
                                to: lastTime
                            });
                        }

                        console.log('🔄 Chart setup completed');
                    } catch (e) {
                        console.warn('⚠️ Could not check chart visibility:', e);
                    }
                }, 300);

                console.log('✅ Chart data set successfully');
                console.log('📈 Chart data count:', formattedData.length);
                if (formattedData.length > 0) {
                    console.log('📈 First chart item:', formattedData[0]);
                    console.log('📈 Last chart item:', formattedData[formattedData.length - 1]);

                    // 验证数据是否有效
                    const firstItem = formattedData[0];
                    const lastItem = formattedData[formattedData.length - 1];
                    if (!firstItem.time ||
                        (typeof firstItem.time === 'string' && !/^\d{4}-\d{2}-\d{2}$/.test(firstItem.time)) ||
                        isNaN(firstItem.open) || isNaN(firstItem.close)) {
                        console.error('❌ 数据验证失败: 第一个数据项无效');
                        console.error('  - time:', firstItem.time, '(类型:', typeof firstItem.time, ')');
                        console.error('  - open:', firstItem.open);
                        console.error('  - close:', firstItem.close);
                    } else {
                        console.log('✅ 数据验证通过');
                        console.log('  - 有效数据项数量:', formattedData.length);
                        console.log('  - 时间范围:', firstItem.time, '到', lastItem.time);
                        console.log('  - 第一个数据项:', firstItem);
                        console.log('  - 最后一个数据项:', lastItem);
                    }
                } else {
                    console.error('❌ 没有有效的图表数据');
                }

                // 验证时间轴是否正确显示
                setTimeout(() => {
                    console.log('🔍 Checking time scale visibility...');
                    const timeScale = chart.timeScale();
                    console.log('🔍 Time scale options:', timeScale.options());
                }, 100);

            } catch (error) {
                console.error('❌ Error processing chart data:', error);
            }
        },

        // 🔑 更新单个数据点
        update: function (candle, chartType = 'candlestick') {
            if (chartType === 'candlestick' && candlestickSeries) {
                candlestickSeries.update(candle);
            } else if (chartType === 'line' && lineSeries) {
                lineSeries.update(candle);
            }
        },

        // 🔑 切换图表类型
        switchChartType: function(chartType) {
            try {
                console.log('🔄 切换图表类型:', chartType);
                switchChartType(chartType);
            } catch (error) {
                console.error('❌ 切换图表类型失败:', error);
            }
        },

        // 🔑 设置K线图数据
        setCandlestickData: function(data, timeFrame) {
            this.setData(data, timeFrame, 'candlestick');
        },

        // 🔑 设置折线图数据
        setLineData: function(data, timeFrame) {
            this.setData(data, timeFrame, 'line');
        },

        // 🔑 验证数据格式
        validateData: function(data, chartType) {
            try {
                const parsedData = typeof data === 'string' ? JSON.parse(data) : data;
                return validateDataFormat(parsedData, chartType);
            } catch (error) {
                return { valid: false, error: error.message };
            }
        },

        // 🔑 添加历史数据（保持当前可见范围）
        addHistoricalData: function (data, timeFrame, chartType = 'candlestick') {
            console.log('📊 添加历史数据:', {
                dataCount: Array.isArray(data) ? data.length : 'unknown',
                timeFrame: timeFrame,
                chartType: chartType
            });

            // 检查图表系列是否初始化
            if (chartType === 'candlestick' && !candlestickSeries) {
                console.error('❌ Candlestick series not initialized');
                return;
            }
            if (chartType === 'line' && !lineSeries) {
                console.error('❌ Line series not initialized');
                return;
            }

            try {
                let parsedData;

                // 解析数据
                if (typeof data === 'string') {
                    parsedData = JSON.parse(data);
                } else {
                    parsedData = data;
                }

                console.log('📊 Parsed historical data:', parsedData);
                console.log('📊 Historical data length:', parsedData.length);

                if (!parsedData || parsedData.length === 0) {
                    console.warn('⚠️ 历史数据为空');
                    return;
                }

                // 获取当前可见范围，以便保持用户的视图位置
                const currentVisibleRange = chart.timeScale().getVisibleRange();
                console.log('👁️ Current visible range before adding data:', currentVisibleRange);

                // 根据时间段更新时间轴配置
                if (timeFrame) {
                    console.log('⏰ Updating time scale format for historical data:', timeFrame);
                    updateTimeScaleFormat(timeFrame);
                }

                // 转换数据格式，确保时间轴正确
                const formattedData = parsedData.map((item, index) => {
                    try {
                        let timeValue;

                        if (item.timestamp) {
                            const date = new Date(item.timestamp);
                            if (isNaN(date.getTime())) {
                                throw new Error('Invalid timestamp format');
                            }

                            // 根据时间框架决定时间格式
                            if (timeFrame === '1day' || timeFrame === '1d') {
                                timeValue = date.toISOString().split('T')[0];
                            } else {
                                timeValue = generateTimeByFrame(index, timeFrame);
                            }
                        } else {
                            timeValue = generateTimeByFrame(index, timeFrame);
                        }

                        // 验证数值字段
                        const open = parseFloat(item.open);
                        const high = parseFloat(item.high);
                        const low = parseFloat(item.low);
                        const close = parseFloat(item.close);

                        if (isNaN(open) || isNaN(high) || isNaN(low) || isNaN(close)) {
                            throw new Error('Invalid OHLC values');
                        }

                        const result = {
                            time: timeValue,
                            open: open,
                            high: high,
                            low: low,
                            close: close,
                        };

                        // 验证时间格式
                        const isValidTime = result.time && (
                            typeof result.time === 'number' ||
                            (typeof result.time === 'string' && /^\d{4}-\d{2}-\d{2}$/.test(result.time))
                        );

                        if (!isValidTime ||
                            !isFinite(result.open) ||
                            !isFinite(result.high) ||
                            !isFinite(result.low) ||
                            !isFinite(result.close) ||
                            isNaN(result.open) ||
                            isNaN(result.high) ||
                            isNaN(result.low) ||
                            isNaN(result.close)) {
                            console.warn(`⚠️ 跳过无效历史数据项 ${index}:`, result);
                            return null;
                        }

                        return result;
                    } catch (error) {
                        console.error(`❌ Error processing historical data item ${index}:`, error.message, item);
                        return null;
                    }
                }).filter(item => item !== null);

                console.log('✅ Formatted historical data for chart:', formattedData);

                if (formattedData.length === 0) {
                    console.error('❌ No valid historical data to add');
                    return;
                }

                // 获取当前图表数据
                const currentData = candlestickSeries.data();
                console.log('📊 Current chart data count:', currentData.length);

                // 合并历史数据和当前数据（历史数据在前）
                const mergedData = [...formattedData, ...currentData];
                console.log('📊 Merged data count:', mergedData.length);

                // 按时间排序（确保数据顺序正确）
                mergedData.sort((a, b) => {
                    if (typeof a.time === 'string' && typeof b.time === 'string') {
                        return a.time.localeCompare(b.time);
                    }
                    return a.time - b.time;
                });

                // 设置合并后的数据
                candlestickSeries.setData(mergedData);
                console.log('✅ Historical data added successfully');

                // 恢复之前的可见范围（如果存在）
                if (currentVisibleRange) {
                    setTimeout(() => {
                        try {
                            chart.timeScale().setVisibleRange(currentVisibleRange);
                            console.log('👁️ Restored visible range:', currentVisibleRange);
                        } catch (e) {
                            console.warn('⚠️ Could not restore visible range:', e);
                            // 如果恢复失败，至少确保图表可见
                            chart.timeScale().fitContent();
                        }
                    }, 100);
                } else {
                    // 如果没有之前的可见范围，调整到显示所有内容
                    chart.timeScale().fitContent();
                }

            } catch (error) {
                console.error('❌ Error adding historical data:', error);
            }
        },

        // 设置主题
        setTheme: function (theme) {
            const darkTheme = { layout: { background: { color: '#161a25' }, textColor: '#d1d4dc' }, grid: { vertLines: { color: 'rgba(255, 255, 255, 0.1)' }, horzLines: { color: 'rgba(255, 255, 255, 0.1)' } }, };
            const lightTheme = { layout: { background: { color: '#ffffff' }, textColor: '#333' }, grid: { vertLines: { color: '#f0f0f0' }, horzLines: { color: '#f0f0f0' } }, };
            if (chart) { chart.applyOptions(theme === 'dark' ? darkTheme : lightTheme); }
        }
    };

    // 根据时间段获取毫秒间隔
    function getTimeIntervalMs (timeFrame) {
        switch (timeFrame) {
            case '1s': return 1000;
            case '1m': return 60 * 1000;
            case '5m': return 5 * 60 * 1000;
            case '15m': return 15 * 60 * 1000;
            case '30m': return 30 * 60 * 1000;
            case '1h': return 60 * 60 * 1000;
            case '4h': return 4 * 60 * 60 * 1000;
            case '1d': return 24 * 60 * 60 * 1000;
            case '1w': return 7 * 24 * 60 * 60 * 1000;
            case '1M': return 30 * 24 * 60 * 60 * 1000;
            default: return 60 * 1000; // 默认1分钟
        }
    }

    // 🔧 通用时间格式生成函数
    function generateTimeByFrame (index, timeFrame) {
        const baseTime = new Date();
        baseTime.setHours(9, 30, 0, 0); // 从9:30开始

        // 🔑 空值检查 - 修复"Cannot read properties of null"错误
        if (!timeFrame || typeof timeFrame !== 'string') {
            console.warn(`⚠️ timeFrame为空或无效: ${timeFrame}，使用默认15分钟`);
            const targetTime = new Date(baseTime.getTime() + index * 15 * 60 * 1000);
            return targetTime.toISOString().slice(0, 16) + ':00';
        }

        // 解析时间框架，提取数值和单位
        const match = timeFrame.match(/^(\d+)([smhdwM])$/);
        if (!match) {
            // 如果无法解析，默认按15分钟处理
            console.warn(`⚠️ 无法解析timeFrame格式: ${timeFrame}，使用默认15分钟`);
            const targetTime = new Date(baseTime.getTime() + index * 15 * 60 * 1000);
            return targetTime.toISOString().slice(0, 16) + ':00';
        }

        const [, num, unit] = match;
        const value = parseInt(num);

        let targetTime;
        let formatType;

        switch (unit) {
            case 's': // 秒
                targetTime = new Date(baseTime.getTime() + index * value * 1000);
                formatType = 'second';
                break;
            case 'm': // 分钟
                targetTime = new Date(baseTime.getTime() + index * value * 60 * 1000);
                formatType = 'minute';
                break;
            case 'h': // 小时
                targetTime = new Date(baseTime.getTime() + index * value * 60 * 60 * 1000);
                formatType = 'hour';
                break;
            case 'd': // 天
                targetTime = new Date(baseTime);
                targetTime.setDate(baseTime.getDate() + index * value);
                formatType = 'day';
                break;
            case 'w': // 周
                targetTime = new Date(baseTime);
                targetTime.setDate(baseTime.getDate() + index * value * 7);
                formatType = 'day';
                break;
            case 'M': // 月
                targetTime = new Date(baseTime);
                targetTime.setMonth(baseTime.getMonth() + index * value);
                formatType = 'day';
                break;
            default:
                targetTime = new Date(baseTime.getTime() + index * 15 * 60 * 1000);
                formatType = 'minute';
        }

        // 🔧 根据格式类型返回LightweightCharts正确的时间格式
        switch (formatType) {
            case 'second':
            case 'minute':
            case 'hour':
                // 秒/分钟/小时：使用UTCTimestamp（以秒为单位的时间戳）
                return Math.floor(targetTime.getTime() / 1000);
            case 'day':
            default:
                // 天/周/月：使用YYYY-MM-DD字符串格式
                return targetTime.toISOString().split('T')[0];
        }
    }

    // 根据时间段格式化时间
    function formatTimeByFrame (date, timeFrame) {
        switch (timeFrame) {
            case '1s':
                return date.toLocaleTimeString('zh-CN', {
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit',
                    hour12: false
                });
            case '1m':
            case '5m':
            case '15m':
            case '30m':
                return date.toLocaleTimeString('zh-CN', {
                    hour: '2-digit',
                    minute: '2-digit',
                    hour12: false
                });
            case '1h':
            case '4h':
                return date.toLocaleDateString('zh-CN', {
                    month: '2-digit',
                    day: '2-digit'
                }) + ' ' + date.toLocaleTimeString('zh-CN', {
                    hour: '2-digit',
                    minute: '2-digit',
                    hour12: false
                });
            case '1d':
            case '1w':
                return date.toLocaleDateString('zh-CN', {
                    month: '2-digit',
                    day: '2-digit'
                });
            case '1M':
                return date.toLocaleDateString('zh-CN', {
                    year: 'numeric',
                    month: '2-digit'
                });
            default:
                return date.toLocaleDateString('zh-CN', {
                    month: '2-digit',
                    day: '2-digit'
                });
        }
    }

    // 动态更新时间轴格式
    function updateTimeScaleFormat (timeFrame) {
        if (!chart) return;

        let timeScaleOptions = {
            visible: true,
            borderColor: 'rgba(255, 255, 255, 0.2)',
        };

        // 根据时间段设置不同的时间格式
        switch (timeFrame) {
            case '1s':
                // 秒级别：显示时间 HH:mm:ss
                timeScaleOptions.timeVisible = true;
                timeScaleOptions.secondsVisible = true;
                timeScaleOptions.tickMarkFormatter = (time) => {
                    if (typeof time === 'string') {
                        // 如果time是字符串格式 "HH:mm:ss"，直接返回
                        if (/^\d{2}:\d{2}:\d{2}$/.test(time)) {
                            return time;
                        }
                        // 如果是日期字符串，解析后格式化
                        const date = new Date(time);
                        return date.toLocaleTimeString('zh-CN', {
                            hour: '2-digit',
                            minute: '2-digit',
                            second: '2-digit',
                            hour12: false
                        });
                    }
                    // 如果是时间戳
                    const date = new Date(time * 1000);
                    return date.toLocaleTimeString('zh-CN', {
                        hour: '2-digit',
                        minute: '2-digit',
                        second: '2-digit',
                        hour12: false
                    });
                };
                break;

            case '1m':
            case '5m':
            case '15m':
            case '30m':
                // 分钟级别：显示时间 HH:mm
                timeScaleOptions.timeVisible = true;
                timeScaleOptions.secondsVisible = false;
                timeScaleOptions.tickMarkFormatter = (time) => {
                    if (typeof time === 'string') {
                        // 如果time是字符串格式 "HH:mm"，直接返回
                        if (/^\d{2}:\d{2}$/.test(time)) {
                            return time;
                        }
                        // 如果是日期字符串，解析后格式化
                        const date = new Date(time);
                        return date.toLocaleTimeString('zh-CN', {
                            hour: '2-digit',
                            minute: '2-digit',
                            hour12: false
                        });
                    }
                    // 如果是时间戳
                    const date = new Date(time * 1000);
                    return date.toLocaleTimeString('zh-CN', {
                        hour: '2-digit',
                        minute: '2-digit',
                        hour12: false
                    });
                };
                break;

            case '1h':
            case '4h':
                // 小时级别：显示日期和时间 MM-dd HH:mm
                timeScaleOptions.timeVisible = true;
                timeScaleOptions.secondsVisible = false;
                timeScaleOptions.tickMarkFormatter = (time) => {
                    if (typeof time === 'string') {
                        // 如果time是字符串格式 "MM-dd HH:mm"，直接返回
                        if (/^\d{2}-\d{2} \d{2}:\d{2}$/.test(time)) {
                            return time;
                        }
                        const date = new Date(time);
                        return date.toLocaleDateString('zh-CN', {
                            month: '2-digit',
                            day: '2-digit'
                        }) + ' ' + date.toLocaleTimeString('zh-CN', {
                            hour: '2-digit',
                            minute: '2-digit',
                            hour12: false
                        });
                    }
                    const date = new Date(time * 1000);
                    return date.toLocaleDateString('zh-CN', {
                        month: '2-digit',
                        day: '2-digit'
                    }) + ' ' + date.toLocaleTimeString('zh-CN', {
                        hour: '2-digit',
                        minute: '2-digit',
                        hour12: false
                    });
                };
                break;

            case '1d':
            case '1w':
                // 日/周级别：显示日期 MM-dd
                timeScaleOptions.timeVisible = false;
                timeScaleOptions.secondsVisible = false;
                timeScaleOptions.tickMarkFormatter = (time) => {
                    if (typeof time === 'string') {
                        // 如果time是字符串格式 "MM-dd"，直接返回
                        if (/^\d{2}-\d{2}$/.test(time)) {
                            return time;
                        }
                        const date = new Date(time);
                        return date.toLocaleDateString('zh-CN', {
                            month: '2-digit',
                            day: '2-digit'
                        });
                    }
                    const date = new Date(time * 1000);
                    return date.toLocaleDateString('zh-CN', {
                        month: '2-digit',
                        day: '2-digit'
                    });
                };
                break;

            case '1M':
                // 月级别：显示年月 yyyy-MM
                timeScaleOptions.timeVisible = false;
                timeScaleOptions.secondsVisible = false;
                timeScaleOptions.tickMarkFormatter = (time) => {
                    if (typeof time === 'string') {
                        // 如果time是字符串格式 "yyyy-MM"，直接返回
                        if (/^\d{4}-\d{2}$/.test(time)) {
                            return time;
                        }
                        const date = new Date(time);
                        return date.toLocaleDateString('zh-CN', {
                            year: 'numeric',
                            month: '2-digit'
                        });
                    }
                    const date = new Date(time * 1000);
                    return date.toLocaleDateString('zh-CN', {
                        year: 'numeric',
                        month: '2-digit'
                    });
                };
                break;

            default:
                // 默认格式
                timeScaleOptions.timeVisible = true;
                timeScaleOptions.secondsVisible = false;
                timeScaleOptions.tickMarkFormatter = (time) => {
                    const date = new Date(typeof time === 'string' ? time : time * 1000);
                    return date.toLocaleDateString('zh-CN', {
                        month: '2-digit',
                        day: '2-digit'
                    });
                };
        }

        // 应用时间轴配置
        chart.applyOptions({
            timeScale: timeScaleOptions
        });

        console.log('✅ Time scale format updated for:', timeFrame);
    }

    // 通知 Flutter 端图表已准备就绪
    if (window.ChartBridge) {
        console.log('Attempting to post message to ChartBridge...');
        window.ChartBridge.postMessage(JSON.stringify({ event: 'onChartReady' }));
        console.log('Message posted to ChartBridge.');
    } else {
        console.error('❌ WARNING: window.ChartBridge is not defined! Cannot communicate back to Flutter.');
    }
});