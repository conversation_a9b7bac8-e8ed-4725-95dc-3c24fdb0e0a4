import 'dart:async';
import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:financial_app_core/financial_app_core.dart';
import '../lib/src/api/chart_api.dart';
import '../lib/src/data/chart_data_manager.dart';
import '../lib/src/models/chart_data.dart';

/// 图表数据集成示例
///
/// 展示如何在其他模块中使用图表数据更新机制
class ChartDataIntegrationExample extends StatefulWidget {
  const ChartDataIntegrationExample({Key? key}) : super(key: key);

  @override
  State<ChartDataIntegrationExample> createState() =>
      _ChartDataIntegrationExampleState();
}

class _ChartDataIntegrationExampleState
    extends State<ChartDataIntegrationExample> {
  late ChartDataController _chartController;
  late MarketDataService _marketDataService;

  String _currentSymbol = 'BTCUSDT';
  String _currentTimeFrame = '15m';
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _initializeServices();
  }

  /// 初始化服务
  void _initializeServices() {
    // 初始化市场数据服务
    _marketDataService = MarketDataService();

    // 初始化图表控制器
    _chartController = ChartDataController();
    _chartController.initialize(
      chartId: 'main_chart',
      symbol: _currentSymbol,
      timeFrame: _currentTimeFrame,
      dataProvider: _fetchHistoricalData,
    );

    // 监听控制器状态变化
    _chartController.addListener(_onControllerStateChanged);

    AppLogger.logModule(
      'ChartIntegrationExample',
      LogLevel.info,
      '🚀 图表数据集成示例初始化完成',
      metadata: {'symbol': _currentSymbol, 'time_frame': _currentTimeFrame},
    );
  }

  /// 控制器状态变化回调
  void _onControllerStateChanged() {
    setState(() {
      _isLoading = _chartController.isLoading;
    });
  }

  /// 获取历史数据的实现
  /// 这是其他模块需要实现的核心方法
  Future<List<ChartData>> _fetchHistoricalData(
    String symbol,
    String timeFrame,
    VisibleRange range,
  ) async {
    AppLogger.logModule(
      'ChartIntegrationExample',
      LogLevel.info,
      '📊 开始获取历史数据',
      metadata: {
        'symbol': symbol,
        'time_frame': timeFrame,
        'range': {'from': range.from, 'to': range.to},
      },
    );

    try {
      // 模拟API调用延迟
      await Future.delayed(const Duration(milliseconds: 500));

      // 调用市场数据服务获取数据
      final data = await _marketDataService.getKlineData(
        symbol: symbol,
        interval: timeFrame,
        limit: range.to - range.from + 50, // 多获取一些数据作为缓冲
      );

      AppLogger.logModule(
        'ChartIntegrationExample',
        LogLevel.info,
        '✅ 历史数据获取成功',
        metadata: {
          'symbol': symbol,
          'time_frame': timeFrame,
          'data_count': data.length,
        },
      );

      return data;
    } catch (e) {
      AppLogger.logModule(
        'ChartIntegrationExample',
        LogLevel.error,
        '❌ 历史数据获取失败',
        error: e,
        metadata: {'symbol': symbol, 'time_frame': timeFrame},
      );
      rethrow;
    }
  }

  /// 切换交易对
  void _changeSymbol(String symbol) {
    setState(() {
      _currentSymbol = symbol;
    });
    _chartController.changeSymbol(symbol);
  }

  /// 切换时间框架
  void _changeTimeFrame(String timeFrame) {
    setState(() {
      _currentTimeFrame = timeFrame;
    });
    _chartController.changeTimeFrame(timeFrame);
  }

  /// 刷新数据
  void _refreshData() {
    _chartController.refreshData();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('图表数据集成示例'),
        actions: [
          IconButton(icon: const Icon(Icons.refresh), onPressed: _refreshData),
        ],
      ),
      body: Column(
        children: [
          // 控制面板
          _buildControlPanel(),

          // 加载指示器
          if (_isLoading) const LinearProgressIndicator(),

          // 图表区域（这里用占位符代替实际图表）
          Expanded(child: _buildChartPlaceholder()),

          // 状态信息
          _buildStatusInfo(),
        ],
      ),
    );
  }

  /// 构建控制面板
  Widget _buildControlPanel() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // 交易对选择
          Row(
            children: [
              const Text('交易对: '),
              const SizedBox(width: 8),
              DropdownButton<String>(
                value: _currentSymbol,
                items: ['BTCUSDT', 'ETHUSDT', 'ADAUSDT', 'DOTUSDT']
                    .map(
                      (symbol) =>
                          DropdownMenuItem(value: symbol, child: Text(symbol)),
                    )
                    .toList(),
                onChanged: (symbol) {
                  if (symbol != null) _changeSymbol(symbol);
                },
              ),
            ],
          ),

          const SizedBox(height: 8),

          // 时间框架选择
          Row(
            children: [
              const Text('时间框架: '),
              const SizedBox(width: 8),
              DropdownButton<String>(
                value: _currentTimeFrame,
                items: ['1m', '5m', '15m', '1h', '4h', '1d']
                    .map(
                      (timeFrame) => DropdownMenuItem(
                        value: timeFrame,
                        child: Text(timeFrame),
                      ),
                    )
                    .toList(),
                onChanged: (timeFrame) {
                  if (timeFrame != null) _changeTimeFrame(timeFrame);
                },
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建图表占位符
  Widget _buildChartPlaceholder() {
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey),
        borderRadius: BorderRadius.circular(8),
      ),
      child: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.show_chart, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text('图表区域', style: TextStyle(fontSize: 18, color: Colors.grey)),
            SizedBox(height: 8),
            Text('这里会显示实际的交易图表', style: TextStyle(color: Colors.grey)),
          ],
        ),
      ),
    );
  }

  /// 构建状态信息
  Widget _buildStatusInfo() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('当前状态: ${_isLoading ? "加载中..." : "就绪"}'),
          Text('交易对: $_currentSymbol'),
          Text('时间框架: $_currentTimeFrame'),
          const SizedBox(height: 8),
          const Text(
            '说明: 当图表可见范围变化时，会自动触发历史数据加载',
            style: TextStyle(fontSize: 12, color: Colors.grey),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _chartController.removeListener(_onControllerStateChanged);
    _chartController.dispose();
    super.dispose();
  }
}

/// 模拟的市场数据服务
/// 在实际项目中，这应该是调用真实的API服务
class MarketDataService {
  /// 获取K线数据
  Future<List<ChartData>> getKlineData({
    required String symbol,
    required String interval,
    required int limit,
  }) async {
    AppLogger.logModule(
      'MarketDataService',
      LogLevel.info,
      '📈 获取K线数据',
      metadata: {'symbol': symbol, 'interval': interval, 'limit': limit},
    );

    // 模拟API调用延迟
    await Future.delayed(const Duration(milliseconds: 300));

    // 生成模拟数据
    final data = _generateMockData(limit);

    AppLogger.logModule(
      'MarketDataService',
      LogLevel.info,
      '✅ K线数据获取完成',
      metadata: {
        'symbol': symbol,
        'interval': interval,
        'data_count': data.length,
      },
    );

    return data;
  }

  /// 生成模拟数据
  List<ChartData> _generateMockData(int count) {
    final random = math.Random();
    final data = <ChartData>[];
    final baseTime = DateTime.now().subtract(Duration(minutes: count * 15));
    double basePrice = 50000.0; // 基础价格

    for (int i = 0; i < count; i++) {
      final timestamp = baseTime.add(Duration(minutes: i * 15));

      // 生成随机价格变化
      final change = (random.nextDouble() - 0.5) * 1000; // ±500的变化
      basePrice += change;

      final open = basePrice;
      final close = basePrice + (random.nextDouble() - 0.5) * 500;
      final high = math.max(open, close) + random.nextDouble() * 200;
      final low = math.min(open, close) - random.nextDouble() * 200;
      final volume = random.nextDouble() * 1000;

      data.add(
        ChartData(
          timestamp: timestamp,
          open: open,
          high: high,
          low: low,
          close: close,
          volume: volume,
        ),
      );

      basePrice = close; // 下一个开盘价等于当前收盘价
    }

    return data;
  }
}

/// 使用示例的主函数
void main() {
  runApp(
    MaterialApp(title: '图表数据集成示例', home: const ChartDataIntegrationExample()),
  );
}
