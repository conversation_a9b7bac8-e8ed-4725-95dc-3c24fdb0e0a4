import 'dart:async';
import 'dart:convert';
import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:financial_app_core/financial_app_core.dart';
import '../lib/src/api/chart_api.dart';
import '../lib/src/data/chart_data_manager.dart';
import '../lib/src/models/chart_data.dart';

/// 完整的图表数据集成示例
/// 
/// 展示如何在其他模块中完整集成图表数据更新机制
class CompleteIntegrationExample extends StatefulWidget {
  const CompleteIntegrationExample({Key? key}) : super(key: key);

  @override
  State<CompleteIntegrationExample> createState() => _CompleteIntegrationExampleState();
}

class _CompleteIntegrationExampleState extends State<CompleteIntegrationExample> {
  late WebViewController _webViewController;
  late MarketDataService _marketDataService;
  late String _chartId;
  
  String _currentSymbol = 'BTCUSDT';
  String _currentTimeFrame = '15m';
  bool _isLoading = false;
  String _statusMessage = '初始化中...';

  @override
  void initState() {
    super.initState();
    _initializeServices();
  }

  /// 初始化服务
  void _initializeServices() async {
    // 初始化日志系统
    await AppLogger.initialize(config: LogConfig.development());
    
    // 初始化市场数据服务
    _marketDataService = MarketDataService();
    
    // 设置全局数据提供者 - 这是关键步骤
    ChartAPI.instance.setGlobalDataProvider(_fetchHistoricalData);
    
    // 创建图表实例
    _chartId = ChartAPI.instance.createChart(
      chartId: 'main_trading_chart',
      symbol: _currentSymbol,
      timeFrame: _currentTimeFrame,
    );

    // 监听图表状态变化
    ChartAPI.instance.addChartListener(_chartId, _onChartStateChanged);

    setState(() {
      _statusMessage = '图表已就绪';
    });

    AppLogger.logModule(
      'CompleteIntegrationExample',
      LogLevel.info,
      '🚀 完整集成示例初始化完成',
      metadata: {
        'chart_id': _chartId,
        'symbol': _currentSymbol,
        'time_frame': _currentTimeFrame,
      },
    );
  }

  /// 图表状态变化回调
  void _onChartStateChanged() {
    final status = ChartAPI.instance.getChartStatus(_chartId);
    setState(() {
      _isLoading = status.isLoading;
      if (status.error != null) {
        _statusMessage = '错误: ${status.error}';
      } else if (status.isLoading) {
        _statusMessage = '加载数据中...';
      } else {
        _statusMessage = '${status.symbol} - ${status.timeFrame}';
      }
    });
  }

  /// 获取历史数据的实现 - 这是其他模块需要实现的核心方法
  Future<List<ChartData>> _fetchHistoricalData(
    String symbol,
    String timeFrame,
    VisibleRange range,
  ) async {
    AppLogger.logModule(
      'CompleteIntegrationExample',
      LogLevel.info,
      '📊 开始获取历史数据',
      metadata: {
        'symbol': symbol,
        'time_frame': timeFrame,
        'range': {
          'from': range.from,
          'to': range.to,
        },
      },
    );

    try {
      // 调用市场数据服务获取数据
      final data = await _marketDataService.getKlineData(
        symbol: symbol,
        interval: timeFrame,
        limit: range.to - range.from + 50, // 多获取一些数据作为缓冲
      );

      AppLogger.logModule(
        'CompleteIntegrationExample',
        LogLevel.info,
        '✅ 历史数据获取成功',
        metadata: {
          'symbol': symbol,
          'time_frame': timeFrame,
          'data_count': data.length,
        },
      );

      return data;
    } catch (e) {
      AppLogger.logModule(
        'CompleteIntegrationExample',
        LogLevel.error,
        '❌ 历史数据获取失败',
        error: e,
        metadata: {
          'symbol': symbol,
          'time_frame': timeFrame,
        },
      );
      rethrow;
    }
  }

  /// 初始化WebView
  void _initializeWebView() {
    _webViewController = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..addJavaScriptChannel(
        'ChartBridge',
        onMessageReceived: _handleWebViewMessage,
      )
      ..loadFlutterAsset('packages/financial_trading_chart/assets/chart.html');
  }

  /// 处理来自WebView的消息
  void _handleWebViewMessage(JavaScriptMessage message) {
    try {
      final data = jsonDecode(message.message) as Map<String, dynamic>;
      final type = data['type'] as String?;
      final payload = data['data'] as Map<String, dynamic>? ?? {};

      AppLogger.logModule(
        'CompleteIntegrationExample',
        LogLevel.debug,
        '📨 收到WebView消息',
        metadata: {
          'type': type,
          'payload_keys': payload.keys.toList(),
        },
      );

      switch (type) {
        case 'visibleRangeChanged':
          _handleVisibleRangeChanged(payload);
          break;
        case 'chartReady':
          _handleChartReady();
          break;
        default:
          AppLogger.logModule(
            'CompleteIntegrationExample',
            LogLevel.warning,
            '⚠️ 未知WebView消息类型',
            metadata: {'type': type},
          );
      }
    } catch (e) {
      AppLogger.logModule(
        'CompleteIntegrationExample',
        LogLevel.error,
        '❌ 处理WebView消息失败',
        error: e,
        metadata: {'message': message.message},
      );
    }
  }

  /// 处理可见范围变化
  void _handleVisibleRangeChanged(Map<String, dynamic> payload) {
    final from = payload['from'] as int?;
    final to = payload['to'] as int?;
    
    if (from == null || to == null) {
      AppLogger.logModule(
        'CompleteIntegrationExample',
        LogLevel.warning,
        '⚠️ 可见范围参数无效',
        metadata: payload,
      );
      return;
    }

    final range = VisibleRange(from: from, to: to);
    
    // 通知数据管理器处理可见范围变化
    ChartDataManager.instance.handleVisibleRangeChange(
      _chartId,
      _currentSymbol,
      _currentTimeFrame,
      range,
    );
  }

  /// 处理图表就绪事件
  void _handleChartReady() {
    AppLogger.logModule(
      'CompleteIntegrationExample',
      LogLevel.info,
      '✅ 图表就绪',
      metadata: {'chart_id': _chartId},
    );

    setState(() {
      _statusMessage = '图表已就绪';
    });
  }

  /// 切换交易对
  void _changeSymbol(String symbol) {
    setState(() {
      _currentSymbol = symbol;
      _statusMessage = '切换到 $symbol';
    });
    
    ChartAPI.instance.changeSymbol(_chartId, symbol);
  }

  /// 切换时间框架
  void _changeTimeFrame(String timeFrame) {
    setState(() {
      _currentTimeFrame = timeFrame;
      _statusMessage = '切换到 $timeFrame';
    });
    
    ChartAPI.instance.changeTimeFrame(_chartId, timeFrame);
  }

  /// 刷新数据
  void _refreshData() {
    ChartAPI.instance.refreshChart(_chartId);
    setState(() {
      _statusMessage = '刷新数据中...';
    });
  }

  /// 手动更新图表数据（模拟实时数据推送）
  void _simulateRealtimeUpdate() async {
    final newData = await _marketDataService.getLatestKlineData(_currentSymbol, _currentTimeFrame);
    ChartAPI.instance.updateChart(_chartId, newData);
    
    setState(() {
      _statusMessage = '实时数据已更新';
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('完整图表集成示例'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _refreshData,
          ),
          IconButton(
            icon: const Icon(Icons.update),
            onPressed: _simulateRealtimeUpdate,
          ),
        ],
      ),
      body: Column(
        children: [
          // 控制面板
          _buildControlPanel(),
          
          // 状态栏
          _buildStatusBar(),
          
          // 图表区域
          Expanded(
            child: _buildChartArea(),
          ),
          
          // 操作按钮
          _buildActionButtons(),
        ],
      ),
    );
  }

  /// 构建控制面板
  Widget _buildControlPanel() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        border: Border(bottom: BorderSide(color: Colors.grey[300]!)),
      ),
      child: Column(
        children: [
          // 交易对选择
          Row(
            children: [
              const Text('交易对: ', style: TextStyle(fontWeight: FontWeight.bold)),
              const SizedBox(width: 8),
              Expanded(
                child: Wrap(
                  spacing: 8,
                  children: ['BTCUSDT', 'ETHUSDT', 'ADAUSDT', 'DOTUSDT', 'BNBUSDT']
                      .map((symbol) => ChoiceChip(
                            label: Text(symbol),
                            selected: _currentSymbol == symbol,
                            onSelected: (selected) {
                              if (selected) _changeSymbol(symbol);
                            },
                          ))
                      .toList(),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 12),
          
          // 时间框架选择
          Row(
            children: [
              const Text('时间框架: ', style: TextStyle(fontWeight: FontWeight.bold)),
              const SizedBox(width: 8),
              Expanded(
                child: Wrap(
                  spacing: 8,
                  children: ['1m', '5m', '15m', '1h', '4h', '1d']
                      .map((timeFrame) => ChoiceChip(
                            label: Text(timeFrame),
                            selected: _currentTimeFrame == timeFrame,
                            onSelected: (selected) {
                              if (selected) _changeTimeFrame(timeFrame);
                            },
                          ))
                      .toList(),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建状态栏
  Widget _buildStatusBar() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: _isLoading ? Colors.orange[100] : Colors.green[100],
        border: Border(bottom: BorderSide(color: Colors.grey[300]!)),
      ),
      child: Row(
        children: [
          if (_isLoading)
            const SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(strokeWidth: 2),
            ),
          if (_isLoading) const SizedBox(width: 8),
          Icon(
            _isLoading ? Icons.hourglass_empty : Icons.check_circle,
            size: 16,
            color: _isLoading ? Colors.orange : Colors.green,
          ),
          const SizedBox(width: 8),
          Text(
            _statusMessage,
            style: TextStyle(
              color: _isLoading ? Colors.orange[800] : Colors.green[800],
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建图表区域
  Widget _buildChartArea() {
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(8),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: WebViewWidget(controller: _webViewController),
      ),
    );
  }

  /// 构建操作按钮
  Widget _buildActionButtons() {
    final statistics = ChartAPI.instance.getStatistics();
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        border: Border(top: BorderSide(color: Colors.grey[300]!)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 统计信息
          Text(
            '统计信息: ${statistics.totalCharts} 个图表, ${statistics.loadingCharts} 个加载中, ${statistics.uniqueSymbols} 个交易对',
            style: const TextStyle(fontSize: 12, color: Colors.grey),
          ),
          const SizedBox(height: 8),
          
          // 操作按钮
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              ElevatedButton.icon(
                onPressed: _refreshData,
                icon: const Icon(Icons.refresh, size: 16),
                label: const Text('刷新'),
              ),
              ElevatedButton.icon(
                onPressed: _simulateRealtimeUpdate,
                icon: const Icon(Icons.update, size: 16),
                label: const Text('实时更新'),
              ),
              ElevatedButton.icon(
                onPressed: () {
                  ChartAPI.instance.clearSymbolCache(_currentSymbol);
                  setState(() {
                    _statusMessage = '缓存已清除';
                  });
                },
                icon: const Icon(Icons.clear, size: 16),
                label: const Text('清除缓存'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    // 移除监听器
    ChartAPI.instance.removeChartListener(_chartId, _onChartStateChanged);
    
    // 销毁图表实例
    ChartAPI.instance.destroyChart(_chartId);
    
    super.dispose();
  }
}

/// 模拟的市场数据服务
class MarketDataService {
  final _random = math.Random();

  /// 获取K线数据
  Future<List<ChartData>> getKlineData({
    required String symbol,
    required String interval,
    required int limit,
  }) async {
    AppLogger.logModule(
      'MarketDataService',
      LogLevel.info,
      '📈 模拟获取K线数据',
      metadata: {
        'symbol': symbol,
        'interval': interval,
        'limit': limit,
      },
    );

    // 模拟网络延迟
    await Future.delayed(Duration(milliseconds: 200 + _random.nextInt(300)));

    return _generateMockData(limit, symbol);
  }

  /// 获取最新K线数据（模拟实时数据）
  Future<List<ChartData>> getLatestKlineData(String symbol, String timeFrame) async {
    await Future.delayed(const Duration(milliseconds: 100));
    return _generateMockData(1, symbol);
  }

  /// 生成模拟数据
  List<ChartData> _generateMockData(int count, String symbol) {
    final data = <ChartData>[];
    final baseTime = DateTime.now().subtract(Duration(minutes: count * 15));
    
    // 根据交易对设置不同的基础价格
    double basePrice = switch (symbol) {
      'BTCUSDT' => 45000.0,
      'ETHUSDT' => 3000.0,
      'ADAUSDT' => 0.5,
      'DOTUSDT' => 8.0,
      'BNBUSDT' => 300.0,
      _ => 100.0,
    };

    for (int i = 0; i < count; i++) {
      final timestamp = baseTime.add(Duration(minutes: i * 15));
      
      // 生成随机价格变化
      final changePercent = (_random.nextDouble() - 0.5) * 0.02; // ±1%的变化
      basePrice *= (1 + changePercent);
      
      final open = basePrice;
      final closeChange = (_random.nextDouble() - 0.5) * 0.01; // ±0.5%的变化
      final close = open * (1 + closeChange);
      final high = math.max(open, close) * (1 + _random.nextDouble() * 0.005);
      final low = math.min(open, close) * (1 - _random.nextDouble() * 0.005);
      final volume = _random.nextDouble() * 1000 + 100;

      data.add(ChartData(
        timestamp: timestamp,
        open: open,
        high: high,
        low: low,
        close: close,
        volume: volume,
      ));
      
      basePrice = close;
    }

    return data;
  }
}

/// 应用入口
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // 初始化日志系统
  await AppLogger.initialize(config: LogConfig.development());
  
  runApp(const MaterialApp(
    title: '完整图表集成示例',
    home: CompleteIntegrationExample(),
    debugShowCheckedModeBanner: false,
  ));
}
