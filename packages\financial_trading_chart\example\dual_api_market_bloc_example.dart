import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:financial_app_core/financial_app_core.dart';
import '../lib/src/api/chart_api.dart';
import '../lib/src/data/chart_data_manager.dart';
import '../lib/src/models/chart_data.dart';

/// 双接口数据服务抽象类
abstract class DualApiService {
  /// 获取初始数据（接口1）
  Future<List<ChartData>> getInitialData({
    required String symbol,
    required String timeFrame,
    int? limit,
  });

  /// 获取历史数据（接口2）
  Future<List<ChartData>> getHistoricalData({
    required String symbol,
    required String timeFrame,
    required VisibleRange range,
    int? limit,
  });
}

/// 双接口数据服务实现
class DualApiServiceImpl implements DualApiService {
  final GetLineData _getLineDataUseCase;
  final MarketApiService _marketApiService;

  DualApiServiceImpl({
    required GetLineData getLineDataUseCase,
    required MarketApiService marketApiService,
  }) : _getLineDataUseCase = getLineDataUseCase,
       _marketApiService = marketApiService;

  @override
  Future<List<ChartData>> getInitialData({
    required String symbol,
    required String timeFrame,
    int? limit,
  }) async {
    AppLogger.logModule(
      'DualApiService',
      LogLevel.info,
      '📊 获取初始数据 (接口1 - GetLineData)',
      metadata: {
        'symbol': symbol,
        'time_frame': timeFrame,
        'limit': limit,
        'api_type': 'initial',
      },
    );

    try {
      // 🔑 使用接口1 (GetLineData) 获取初始数据
      final result = await _getLineDataUseCase(
        symbol: symbol,
        bar: timeFrame,
        limit: limit ?? 100,
        befor: DateTime.now().millisecondsSinceEpoch,
        t: DateTime.now().millisecondsSinceEpoch,
      );

      // 转换为ChartData格式，使用TradingView的time字段
      final chartData = result
          .map(
            (e) => ChartData(
              timestamp: DateTime.fromMillisecondsSinceEpoch(e.time),
              time: _formatTimeForTradingView(
                e.time,
                timeFrame,
              ), // 🔑 关键：格式化time字段
              open: e.open,
              high: e.hight, // 注意：保持原有的拼写
              low: e.low,
              close: e.close,
              volume: e.volume ?? 0.0,
            ),
          )
          .toList();

      AppLogger.logModule(
        'DualApiService',
        LogLevel.info,
        '✅ 初始数据获取成功',
        metadata: {
          'symbol': symbol,
          'time_frame': timeFrame,
          'data_count': chartData.length,
          'api_type': 'initial',
          'first_item_time': chartData.isNotEmpty ? chartData.first.time : null,
          'last_item_time': chartData.isNotEmpty ? chartData.last.time : null,
        },
      );

      return chartData;
    } catch (e) {
      AppLogger.logModule(
        'DualApiService',
        LogLevel.error,
        '❌ 初始数据获取失败',
        error: e,
        metadata: {
          'symbol': symbol,
          'time_frame': timeFrame,
          'api_type': 'initial',
        },
      );
      rethrow;
    }
  }

  @override
  Future<List<ChartData>> getHistoricalData({
    required String symbol,
    required String timeFrame,
    required VisibleRange range,
    int? limit,
  }) async {
    AppLogger.logModule(
      'DualApiService',
      LogLevel.info,
      '📊 获取历史数据 (接口2 - MarketApiService)',
      metadata: {
        'symbol': symbol,
        'time_frame': timeFrame,
        'range': '${range.from}-${range.to}',
        'limit': limit,
        'api_type': 'historical',
      },
    );

    try {
      // 🔑 使用接口2 (MarketApiService) 获取历史数据
      final klineData = await _marketApiService.getKlineData(
        symbol: symbol,
        interval: timeFrame,
        limit: limit ?? (range.to - range.from + 50),
        // 这里可能需要根据range计算具体的时间范围
        // startTime: _calculateStartTime(range.from, timeFrame),
        // endTime: _calculateEndTime(range.to, timeFrame),
      );

      // 转换为ChartData格式
      final chartData = klineData
          .map(
            (item) => ChartData(
              timestamp: DateTime.fromMillisecondsSinceEpoch(item.openTime),
              time: _formatTimeForTradingView(
                item.openTime,
                timeFrame,
              ), // 🔑 关键：格式化time字段
              open: item.open,
              high: item.high,
              low: item.low,
              close: item.close,
              volume: item.volume,
            ),
          )
          .toList();

      AppLogger.logModule(
        'DualApiService',
        LogLevel.info,
        '✅ 历史数据获取成功',
        metadata: {
          'symbol': symbol,
          'time_frame': timeFrame,
          'data_count': chartData.length,
          'api_type': 'historical',
          'first_item_time': chartData.isNotEmpty ? chartData.first.time : null,
          'last_item_time': chartData.isNotEmpty ? chartData.last.time : null,
        },
      );

      return chartData;
    } catch (e) {
      AppLogger.logModule(
        'DualApiService',
        LogLevel.error,
        '❌ 历史数据获取失败',
        error: e,
        metadata: {
          'symbol': symbol,
          'time_frame': timeFrame,
          'api_type': 'historical',
        },
      );
      rethrow;
    }
  }

  /// 🔑 关键方法：为TradingView格式化时间
  String _formatTimeForTradingView(int timestampMs, String timeFrame) {
    final dateTime = DateTime.fromMillisecondsSinceEpoch(timestampMs);

    switch (timeFrame) {
      case '1s':
      case '1m':
      case '5m':
      case '15m':
      case '30m':
      case '1h':
      case '4h':
        // 对于分钟/小时级别，TradingView需要时间戳（秒）
        return (timestampMs ~/ 1000).toString();
      case '1d':
      case '1w':
      case '1M':
        // 对于日/周/月级别，TradingView需要日期字符串
        return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')}';
      default:
        // 默认使用时间戳
        return (timestampMs ~/ 1000).toString();
    }
  }
}

/// 修改后的ChartData模型
class ChartData {
  final DateTime timestamp; // 保留用于内部处理
  final String time; // TradingView使用的time字段
  final double open;
  final double high;
  final double low;
  final double close;
  final double volume;

  const ChartData({
    required this.timestamp,
    required this.time,
    required this.open,
    required this.high,
    required this.low,
    required this.close,
    required this.volume,
  });

  /// 转换为TradingView图表数据格式
  Map<String, dynamic> toTradingViewFormat() {
    return {
      'time': time,
      'open': open,
      'high': high,
      'low': low,
      'close': close,
      'volume': volume,
    };
  }

  @override
  String toString() {
    return 'ChartData(time: $time, open: $open, high: $high, low: $low, close: $close, volume: $volume)';
  }
}

/// MarketBloc事件
abstract class MarketEvent extends Equatable {
  const MarketEvent();
  @override
  List<Object?> get props => [];
}

class InitializeChart extends MarketEvent {
  final String symbol;
  final String timeFrame;
  const InitializeChart({required this.symbol, required this.timeFrame});
  @override
  List<Object?> get props => [symbol, timeFrame];
}

class ChangeSymbol extends MarketEvent {
  final String symbol;
  const ChangeSymbol(this.symbol);
  @override
  List<Object?> get props => [symbol];
}

class ChangeTimeFrame extends MarketEvent {
  final String timeFrame;
  const ChangeTimeFrame(this.timeFrame);
  @override
  List<Object?> get props => [timeFrame];
}

/// MarketBloc状态
class MarketState extends Equatable {
  final String? chartId;
  final String currentSymbol;
  final String currentTimeFrame;
  final bool isLoading;
  final bool isChartReady;
  final String? errorMessage;

  const MarketState({
    this.chartId,
    required this.currentSymbol,
    required this.currentTimeFrame,
    this.isLoading = false,
    this.isChartReady = false,
    this.errorMessage,
  });

  MarketState copyWith({
    String? chartId,
    String? currentSymbol,
    String? currentTimeFrame,
    bool? isLoading,
    bool? isChartReady,
    String? errorMessage,
  }) {
    return MarketState(
      chartId: chartId ?? this.chartId,
      currentSymbol: currentSymbol ?? this.currentSymbol,
      currentTimeFrame: currentTimeFrame ?? this.currentTimeFrame,
      isLoading: isLoading ?? this.isLoading,
      isChartReady: isChartReady ?? this.isChartReady,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }

  @override
  List<Object?> get props => [
    chartId,
    currentSymbol,
    currentTimeFrame,
    isLoading,
    isChartReady,
    errorMessage,
  ];
}

/// 修改后的MarketBloc - 支持双接口
class MarketBloc extends Bloc<MarketEvent, MarketState> {
  final DualApiService _dualApiService;
  final ChartAPI _chartAPI;
  bool _isInitialDataLoaded = false; // 🔑 关键：标记是否已加载初始数据
  Timer? _chartStatusTimer;

  MarketBloc({required DualApiService dualApiService, ChartAPI? chartAPI})
    : _dualApiService = dualApiService,
      _chartAPI = chartAPI ?? ChartAPI.instance,
      super(
        const MarketState(currentSymbol: 'BTCUSDT', currentTimeFrame: '15m'),
      ) {
    // 注册事件处理器
    on<InitializeChart>(_onInitializeChart);
    on<ChangeSymbol>(_onChangeSymbol);
    on<ChangeTimeFrame>(_onChangeTimeFrame);

    // 🔑 关键：设置ChartAPI的数据提供者
    _setupChartDataProvider();
  }

  /// 🔑 关键方法：设置ChartAPI的数据提供者
  void _setupChartDataProvider() {
    _chartAPI.setGlobalDataProvider(_handleChartDataRequest);

    AppLogger.logModule(
      'MarketBloc',
      LogLevel.info,
      '🌐 设置双接口数据提供者',
      metadata: {
        'bloc_instance': hashCode,
        'chart_api_instance': _chartAPI.hashCode,
      },
    );
  }

  /// 🔑 核心方法：处理图表数据请求 - 支持双接口
  Future<List<ChartData>> _handleChartDataRequest(
    String symbol,
    String timeFrame,
    VisibleRange range,
  ) async {
    AppLogger.logModule(
      'MarketBloc',
      LogLevel.info,
      '📊 处理图表数据请求',
      metadata: {
        'symbol': symbol,
        'time_frame': timeFrame,
        'range': '${range.from}-${range.to}',
        'is_initial_loaded': _isInitialDataLoaded,
        'current_symbol': state.currentSymbol,
        'current_timeframe': state.currentTimeFrame,
      },
    );

    try {
      List<ChartData> chartData;

      if (!_isInitialDataLoaded) {
        // 🔑 第一次加载：使用接口1获取初始数据
        AppLogger.logModule(
          'MarketBloc',
          LogLevel.info,
          '🚀 使用接口1获取初始数据',
          metadata: {'symbol': symbol, 'time_frame': timeFrame},
        );

        chartData = await _dualApiService.getInitialData(
          symbol: symbol,
          timeFrame: timeFrame,
          limit: 100, // 初始加载100条数据
        );

        _isInitialDataLoaded = true;

        AppLogger.logModule(
          'MarketBloc',
          LogLevel.info,
          '✅ 初始数据加载完成',
          metadata: {
            'symbol': symbol,
            'time_frame': timeFrame,
            'data_count': chartData.length,
            'api_used': 'interface_1',
          },
        );
      } else {
        // 🔑 后续加载：使用接口2获取历史数据
        AppLogger.logModule(
          'MarketBloc',
          LogLevel.info,
          '🔄 使用接口2获取历史数据',
          metadata: {
            'symbol': symbol,
            'time_frame': timeFrame,
            'range': '${range.from}-${range.to}',
          },
        );

        chartData = await _dualApiService.getHistoricalData(
          symbol: symbol,
          timeFrame: timeFrame,
          range: range,
          limit: range.to - range.from + 50,
        );

        AppLogger.logModule(
          'MarketBloc',
          LogLevel.info,
          '✅ 历史数据加载完成',
          metadata: {
            'symbol': symbol,
            'time_frame': timeFrame,
            'data_count': chartData.length,
            'api_used': 'interface_2',
          },
        );
      }

      // 验证数据格式
      if (chartData.isNotEmpty) {
        final firstItem = chartData.first;
        AppLogger.logModule(
          'MarketBloc',
          LogLevel.debug,
          '📊 数据格式验证',
          metadata: {
            'first_item': {
              'time': firstItem.time,
              'timestamp': firstItem.timestamp.toIso8601String(),
              'ohlc':
                  '${firstItem.open}/${firstItem.high}/${firstItem.low}/${firstItem.close}',
              'volume': firstItem.volume,
            },
            'time_format_type': firstItem.time.contains('-')
                ? 'date_string'
                : 'timestamp',
          },
        );
      }

      return chartData;
    } catch (e) {
      AppLogger.logModule(
        'MarketBloc',
        LogLevel.error,
        '❌ 图表数据请求失败',
        error: e,
        metadata: {
          'symbol': symbol,
          'time_frame': timeFrame,
          'is_initial_loaded': _isInitialDataLoaded,
        },
      );
      rethrow;
    }
  }

  /// 初始化图表
  Future<void> _onInitializeChart(
    InitializeChart event,
    Emitter<MarketState> emit,
  ) async {
    emit(
      state.copyWith(
        isLoading: true,
        currentSymbol: event.symbol,
        currentTimeFrame: event.timeFrame,
        errorMessage: null,
      ),
    );

    try {
      // 重置初始数据标记
      _isInitialDataLoaded = false;

      // 创建图表实例
      final chartId = _chartAPI.createChart(
        chartId:
            'market_chart_${event.symbol}_${DateTime.now().millisecondsSinceEpoch}',
        symbol: event.symbol,
        timeFrame: event.timeFrame,
      );

      // 开始监控图表状态
      _startChartStatusMonitoring(chartId);

      emit(
        state.copyWith(chartId: chartId, isLoading: false, isChartReady: true),
      );

      AppLogger.logModule(
        'MarketBloc',
        LogLevel.info,
        '📊 图表初始化完成',
        metadata: {
          'chart_id': chartId,
          'symbol': event.symbol,
          'time_frame': event.timeFrame,
        },
      );
    } catch (e) {
      emit(
        state.copyWith(
          isLoading: false,
          errorMessage: '图表初始化失败: ${e.toString()}',
        ),
      );

      AppLogger.logModule('MarketBloc', LogLevel.error, '❌ 图表初始化失败', error: e);
    }
  }

  /// 切换交易对
  Future<void> _onChangeSymbol(
    ChangeSymbol event,
    Emitter<MarketState> emit,
  ) async {
    if (state.chartId == null) return;

    emit(
      state.copyWith(
        isLoading: true,
        currentSymbol: event.symbol,
        errorMessage: null,
      ),
    );

    try {
      // 🔑 重置初始数据标记，确保使用接口1获取新交易对的初始数据
      _isInitialDataLoaded = false;

      _chartAPI.changeSymbol(state.chartId!, event.symbol);
      emit(state.copyWith(isLoading: false));

      AppLogger.logModule(
        'MarketBloc',
        LogLevel.info,
        '🔄 切换交易对',
        metadata: {
          'chart_id': state.chartId,
          'symbol': event.symbol,
          'reset_initial_flag': true,
        },
      );
    } catch (e) {
      emit(
        state.copyWith(
          isLoading: false,
          errorMessage: '切换交易对失败: ${e.toString()}',
        ),
      );
    }
  }

  /// 切换时间框架
  Future<void> _onChangeTimeFrame(
    ChangeTimeFrame event,
    Emitter<MarketState> emit,
  ) async {
    if (state.chartId == null) return;

    emit(
      state.copyWith(
        isLoading: true,
        currentTimeFrame: event.timeFrame,
        errorMessage: null,
      ),
    );

    try {
      // 🔑 重置初始数据标记，确保使用接口1获取新时间框架的初始数据
      _isInitialDataLoaded = false;

      _chartAPI.changeTimeFrame(state.chartId!, event.timeFrame);
      emit(state.copyWith(isLoading: false));

      AppLogger.logModule(
        'MarketBloc',
        LogLevel.info,
        '⏰ 切换时间框架',
        metadata: {
          'chart_id': state.chartId,
          'time_frame': event.timeFrame,
          'reset_initial_flag': true,
        },
      );
    } catch (e) {
      emit(
        state.copyWith(
          isLoading: false,
          errorMessage: '切换时间框架失败: ${e.toString()}',
        ),
      );
    }
  }

  /// 开始监控图表状态
  void _startChartStatusMonitoring(String chartId) {
    _chartStatusTimer?.cancel();
    _chartStatusTimer = Timer.periodic(const Duration(milliseconds: 500), (
      timer,
    ) {
      final status = _chartAPI.getChartStatus(chartId);
      if (state.isLoading != status.isLoading) {
        emit(state.copyWith(isLoading: status.isLoading));
      }
    });
  }

  @override
  Future<void> close() {
    _chartStatusTimer?.cancel();

    // 🔑 重要：清理图表资源
    if (state.chartId != null) {
      _chartAPI.destroyChart(state.chartId!);

      AppLogger.logModule(
        'MarketBloc',
        LogLevel.info,
        '🗑️ 清理图表资源',
        metadata: {'chart_id': state.chartId},
      );
    }

    return super.close();
  }
}

/// 模拟的API服务类
class GetLineData {
  Future<List<LineDataItem>> call({
    required String symbol,
    required String bar,
    required int limit,
    required int befor,
    required int t,
  }) async {
    // 模拟API调用
    await Future.delayed(const Duration(milliseconds: 500));

    return List.generate(limit, (index) {
      final timestamp = DateTime.now().subtract(
        Duration(minutes: (limit - index) * 15),
      );
      return LineDataItem(
        time: timestamp.millisecondsSinceEpoch,
        open: 45000.0 + index * 10,
        hight: 45100.0 + index * 10, // 保持原有拼写
        low: 44900.0 + index * 10,
        close: 45050.0 + index * 10,
        volume: 100.0 + index,
      );
    });
  }
}

class LineDataItem {
  final int time;
  final double open;
  final double hight; // 保持原有拼写
  final double low;
  final double close;
  final double? volume;

  LineDataItem({
    required this.time,
    required this.open,
    required this.hight,
    required this.low,
    required this.close,
    this.volume,
  });
}

class MarketApiService {
  Future<List<KlineDataItem>> getKlineData({
    required String symbol,
    required String interval,
    required int limit,
    DateTime? startTime,
    DateTime? endTime,
  }) async {
    // 模拟API调用
    await Future.delayed(const Duration(milliseconds: 300));

    return List.generate(limit, (index) {
      final timestamp = DateTime.now().subtract(
        Duration(minutes: (limit - index) * 15),
      );
      return KlineDataItem(
        openTime: timestamp.millisecondsSinceEpoch,
        open: 45000.0 + index * 5,
        high: 45100.0 + index * 5,
        low: 44900.0 + index * 5,
        close: 45050.0 + index * 5,
        volume: 150.0 + index,
      );
    });
  }
}

class KlineDataItem {
  final int openTime;
  final double open;
  final double high;
  final double low;
  final double close;
  final double volume;

  KlineDataItem({
    required this.openTime,
    required this.open,
    required this.high,
    required this.low,
    required this.close,
    required this.volume,
  });
}

/// 使用示例页面
class DualApiMarketPage extends StatelessWidget {
  final String symbol;
  final String timeFrame;

  const DualApiMarketPage({
    Key? key,
    required this.symbol,
    this.timeFrame = '15m',
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => MarketBloc(
        dualApiService: DualApiServiceImpl(
          getLineDataUseCase: GetLineData(),
          marketApiService: MarketApiService(),
        ),
        // chartAPI: ChartAPI.instance, // 可选，默认使用instance
      )..add(InitializeChart(symbol: symbol, timeFrame: timeFrame)),
      child: const DualApiMarketView(),
    );
  }
}

class DualApiMarketView extends StatelessWidget {
  const DualApiMarketView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: BlocBuilder<MarketBloc, MarketState>(
          builder: (context, state) {
            return Text('${state.currentSymbol} - 双接口示例');
          },
        ),
      ),
      body: BlocConsumer<MarketBloc, MarketState>(
        listener: (context, state) {
          if (state.errorMessage != null) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.errorMessage!),
                backgroundColor: Colors.red,
              ),
            );
          }
        },
        builder: (context, state) {
          return Column(
            children: [
              // 状态栏
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: state.isLoading
                      ? Colors.orange[100]
                      : Colors.green[100],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        if (state.isLoading)
                          const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          ),
                        if (state.isLoading) const SizedBox(width: 8),
                        Icon(
                          state.isLoading
                              ? Icons.hourglass_empty
                              : Icons.check_circle,
                          size: 16,
                          color: state.isLoading ? Colors.orange : Colors.green,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          state.isLoading
                              ? '加载数据中...'
                              : '${state.currentSymbol} - ${state.currentTimeFrame}',
                          style: TextStyle(
                            color: state.isLoading
                                ? Colors.orange[800]
                                : Colors.green[800],
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      '说明：首次加载使用接口1，滚动图表时使用接口2获取历史数据',
                      style: TextStyle(fontSize: 12, color: Colors.grey),
                    ),
                  ],
                ),
              ),

              // 图表区域
              Expanded(
                child: Container(
                  margin: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey[300]!),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.show_chart, size: 64, color: Colors.grey),
                        SizedBox(height: 16),
                        Text('双接口图表区域', style: TextStyle(fontSize: 18)),
                        SizedBox(height: 8),
                        Text('初始数据：接口1 (GetLineData)'),
                        Text('历史数据：接口2 (MarketApiService)'),
                        SizedBox(height: 8),
                        Text(
                          'TradingView使用time字段而非timestamp',
                          style: TextStyle(fontSize: 12, color: Colors.grey),
                        ),
                      ],
                    ),
                  ),
                ),
              ),

              // 操作按钮
              Container(
                padding: const EdgeInsets.all(16),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    ElevatedButton(
                      onPressed: () {
                        context.read<MarketBloc>().add(
                          const ChangeSymbol('ETHUSDT'),
                        );
                      },
                      child: const Text('切换ETH'),
                    ),
                    ElevatedButton(
                      onPressed: () {
                        context.read<MarketBloc>().add(
                          const ChangeTimeFrame('1h'),
                        );
                      },
                      child: const Text('切换1h'),
                    ),
                    ElevatedButton(
                      onPressed: () {
                        context.read<MarketBloc>().add(
                          const ChangeTimeFrame('1d'),
                        );
                      },
                      child: const Text('切换1d'),
                    ),
                  ],
                ),
              ),
            ],
          );
        },
      ),
    );
  }
}

/// 应用入口
void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // 初始化日志系统
  await AppLogger.initialize(config: LogConfig.development());

  runApp(
    MaterialApp(
      title: '双接口图表集成示例',
      home: const DualApiMarketPage(symbol: 'BTCUSDT'),
      debugShowCheckedModeBanner: false,
    ),
  );
}
