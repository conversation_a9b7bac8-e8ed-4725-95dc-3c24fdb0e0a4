import 'dart:async';
import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:financial_trading_chart/financial_trading_chart.dart';

/// 高性能图表示例
/// 
/// 展示如何使用TradingView级别的高性能图表组件
class HighPerformanceChartExample extends StatefulWidget {
  const HighPerformanceChartExample({Key? key}) : super(key: key);

  @override
  State<HighPerformanceChartExample> createState() => _HighPerformanceChartExampleState();
}

class _HighPerformanceChartExampleState extends State<HighPerformanceChartExample> {
  // 图表数据
  List<CandleData> _chartData = [];
  
  // 技术指标
  List<TechnicalIndicator> _indicators = [];
  
  // 图表控制器
  late HighPerformanceChartController _chartController;
  
  // 实时数据流
  late StreamController<CandleData> _realTimeController;
  Timer? _dataTimer;
  
  // 性能监控
  bool _showPerformanceMonitor = false;
  
  @override
  void initState() {
    super.initState();
    _initializeChart();
    _generateSampleData();
    _setupIndicators();
    _startRealTimeData();
  }
  
  /// 初始化图表
  void _initializeChart() {
    _chartController = HighPerformanceChartController();
    _realTimeController = StreamController<CandleData>.broadcast();
  }
  
  /// 生成示例数据
  void _generateSampleData() {
    final random = math.Random();
    double price = 100.0;
    final now = DateTime.now();
    
    for (int i = 0; i < 5000; i++) {
      final change = (random.nextDouble() - 0.5) * 4;
      price += change;
      
      final open = price;
      final close = price + (random.nextDouble() - 0.5) * 2;
      final high = math.max(open, close) + random.nextDouble();
      final low = math.min(open, close) - random.nextDouble();
      
      _chartData.add(CandleData(
        timestamp: now.subtract(Duration(minutes: 5000 - i)),
        open: open,
        high: high,
        low: low,
        close: close,
        volume: random.nextDouble() * 1000000,
      ));
    }
    
    if (mounted) {
      setState(() {});
    }
  }
  
  /// 设置技术指标
  void _setupIndicators() {
    // 计算移动平均线
    final ma20Data = _calculateMA(_chartData, 20);
    final ma50Data = _calculateMA(_chartData, 50);
    
    _indicators = [
      TechnicalIndicator.movingAverage(
        key: 'ma20',
        name: 'MA(20)',
        period: 20,
        data: ma20Data,
        config: TechnicalIndicatorConfig(
          colors: [Colors.blue],
          lineWidth: 1.5,
        ),
      ),
      TechnicalIndicator.movingAverage(
        key: 'ma50',
        name: 'MA(50)',
        period: 50,
        data: ma50Data,
        config: TechnicalIndicatorConfig(
          colors: [Colors.orange],
          lineWidth: 1.5,
        ),
      ),
    ];
  }
  
  /// 计算移动平均线
  List<double> _calculateMA(List<CandleData> data, int period) {
    final result = <double>[];
    
    for (int i = 0; i < data.length; i++) {
      if (i < period - 1) {
        result.add(double.nan);
        continue;
      }
      
      double sum = 0;
      for (int j = i - period + 1; j <= i; j++) {
        sum += data[j].close;
      }
      
      result.add(sum / period);
    }
    
    return result;
  }
  
  /// 启动实时数据模拟
  void _startRealTimeData() {
    _dataTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_chartData.isNotEmpty) {
        final lastCandle = _chartData.last;
        final random = math.Random();
        
        // 模拟价格变化
        final change = (random.nextDouble() - 0.5) * 2;
        final newPrice = lastCandle.close + change;
        
        final newCandle = CandleData(
          timestamp: DateTime.now(),
          open: newPrice,
          high: newPrice + random.nextDouble(),
          low: newPrice - random.nextDouble(),
          close: newPrice + (random.nextDouble() - 0.5),
          volume: random.nextDouble() * 1000000,
        );
        
        _realTimeController.add(newCandle);
      }
    });
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('高性能交易图表'),
        actions: [
          IconButton(
            icon: Icon(_showPerformanceMonitor ? Icons.visibility_off : Icons.visibility),
            onPressed: () {
              setState(() {
                _showPerformanceMonitor = !_showPerformanceMonitor;
              });
            },
            tooltip: '切换性能监控',
          ),
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: _showSettings,
            tooltip: '设置',
          ),
        ],
      ),
      body: Column(
        children: [
          // 工具栏
          _buildToolbar(),
          
          // 图表区域
          Expanded(
            child: _chartData.isEmpty
                ? const Center(child: CircularProgressIndicator())
                : HighPerformanceChart(
                    data: _chartData,
                    indicators: _indicators,
                    config: _buildChartConfig(),
                    theme: _buildChartTheme(),
                    controller: _chartController,
                    realTimeDataStream: _realTimeController.stream,
                    enablePerformanceMonitor: _showPerformanceMonitor,
                    enableDebugInfo: _showPerformanceMonitor,
                    onCandleTap: _onCandleTap,
                    onCrosshairMove: _onCrosshairMove,
                    onZoomChanged: _onZoomChanged,
                    onRangeChanged: _onRangeChanged,
                  ),
          ),
        ],
      ),
    );
  }
  
  /// 构建工具栏
  Widget _buildToolbar() {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        border: Border(bottom: BorderSide(color: Colors.grey.shade300)),
      ),
      child: Row(
        children: [
          _buildToolbarButton('1分钟', () => _changeTimeframe('1m')),
          _buildToolbarButton('5分钟', () => _changeTimeframe('5m')),
          _buildToolbarButton('15分钟', () => _changeTimeframe('15m')),
          _buildToolbarButton('1小时', () => _changeTimeframe('1h')),
          _buildToolbarButton('1天', () => _changeTimeframe('1d')),
          const Spacer(),
          _buildToolbarButton('重置', () => _chartController.resetViewport()),
          _buildToolbarButton('指标', _showIndicatorSettings),
        ],
      ),
    );
  }
  
  Widget _buildToolbarButton(String text, VoidCallback onPressed) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 4),
      child: ElevatedButton(
        onPressed: onPressed,
        style: ElevatedButton.styleFrom(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          minimumSize: Size.zero,
        ),
        child: Text(text, style: const TextStyle(fontSize: 12)),
      ),
    );
  }
  
  /// 构建图表配置
  ChartConfig _buildChartConfig() {
    return ChartConfig(
      theme: ChartTheme.light(),
      showGrid: true,
      showCrosshair: true,
      showVolume: true,
      enableZoom: true,
      enablePan: true,
      enableRealTime: true,
      performanceConfig: PerformanceConfig.highPerformance(),
      gestureConfig: GestureConfig.smooth(),
    );
  }
  
  /// 构建图表主题
  ChartTheme _buildChartTheme() {
    return ChartTheme(
      backgroundColor: Colors.white,
      gridColor: Colors.grey.shade300,
      bullishColor: Colors.green,
      bearishColor: Colors.red,
      textColor: Colors.black,
    );
  }
  
  /// K线点击事件
  void _onCandleTap(CandleData candle, int index) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('K线详情'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('时间: ${candle.timestamp}'),
            Text('开盘: ${candle.open.toStringAsFixed(2)}'),
            Text('最高: ${candle.high.toStringAsFixed(2)}'),
            Text('最低: ${candle.low.toStringAsFixed(2)}'),
            Text('收盘: ${candle.close.toStringAsFixed(2)}'),
            Text('成交量: ${candle.volume.toStringAsFixed(0)}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }
  
  /// 十字线移动事件
  void _onCrosshairMove(Offset position, CandleData? candle) {
    // 可以在这里显示十字线信息
    if (candle != null) {
      print('十字线位置: ${candle.close.toStringAsFixed(2)}');
    }
  }
  
  /// 缩放变化事件
  void _onZoomChanged(double zoom, double startX, double endX) {
    print('缩放变化: ${zoom.toStringAsFixed(2)}x, 范围: $startX - $endX');
  }
  
  /// 数据范围变化事件
  void _onRangeChanged(int startIndex, int endIndex) {
    print('数据范围: $startIndex - $endIndex');
    // 可以在这里加载更多历史数据
  }
  
  /// 切换时间周期
  void _changeTimeframe(String timeframe) {
    print('切换时间周期: $timeframe');
    // TODO: 实现时间周期切换逻辑
  }
  
  /// 显示设置对话框
  void _showSettings() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('图表设置'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            SwitchListTile(
              title: const Text('显示网格'),
              value: true,
              onChanged: (value) {
                // TODO: 更新配置
              },
            ),
            SwitchListTile(
              title: const Text('显示成交量'),
              value: true,
              onChanged: (value) {
                // TODO: 更新配置
              },
            ),
            SwitchListTile(
              title: const Text('性能监控'),
              value: _showPerformanceMonitor,
              onChanged: (value) {
                setState(() {
                  _showPerformanceMonitor = value;
                });
                Navigator.of(context).pop();
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }
  
  /// 显示指标设置
  void _showIndicatorSettings() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('技术指标'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            CheckboxListTile(
              title: const Text('MA(20)'),
              value: true,
              onChanged: (value) {
                // TODO: 切换指标显示
              },
            ),
            CheckboxListTile(
              title: const Text('MA(50)'),
              value: true,
              onChanged: (value) {
                // TODO: 切换指标显示
              },
            ),
            ListTile(
              title: const Text('添加RSI'),
              trailing: const Icon(Icons.add),
              onTap: () {
                // TODO: 添加RSI指标
              },
            ),
            ListTile(
              title: const Text('添加MACD'),
              trailing: const Icon(Icons.add),
              onTap: () {
                // TODO: 添加MACD指标
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }
  
  @override
  void dispose() {
    _dataTimer?.cancel();
    _realTimeController.close();
    _chartController.dispose();
    super.dispose();
  }
}

/// 示例应用入口
void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: '高性能交易图表示例',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        visualDensity: VisualDensity.adaptivePlatformDensity,
      ),
      home: const HighPerformanceChartExample(),
    );
  }
}
