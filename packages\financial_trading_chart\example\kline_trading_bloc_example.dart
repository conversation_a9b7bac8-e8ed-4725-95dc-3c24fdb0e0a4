import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:financial_app_core/financial_app_core.dart';
import '../lib/src/api/chart_api.dart';
import '../lib/src/data/chart_data_manager.dart';
import '../lib/src/models/chart_data.dart';

/// KlineTradingBloc事件定义
abstract class KlineTradingEvent extends Equatable {
  const KlineTradingEvent();
  @override
  List<Object?> get props => [];
}

class InitializeChart extends KlineTradingEvent {
  final String symbol;
  final String timeFrame;

  const InitializeChart({required this.symbol, required this.timeFrame});
  @override
  List<Object?> get props => [symbol, timeFrame];
}

class ChangeSymbol extends KlineTradingEvent {
  final String symbol;
  const ChangeSymbol(this.symbol);
  @override
  List<Object?> get props => [symbol];
}

class ChangeTimeFrame extends KlineTradingEvent {
  final String timeFrame;
  const ChangeTimeFrame(this.timeFrame);
  @override
  List<Object?> get props => [timeFrame];
}

class RefreshChart extends KlineTradingEvent {}

class UpdateRealtimeData extends KlineTradingEvent {
  final List<ChartData> data;
  const UpdateRealtimeData(this.data);
  @override
  List<Object?> get props => [data];
}

/// KlineTradingBloc状态定义
class KlineTradingState extends Equatable {
  final String? chartId;
  final String currentSymbol;
  final String currentTimeFrame;
  final bool isLoading;
  final bool isChartReady;
  final String? errorMessage;

  const KlineTradingState({
    this.chartId,
    required this.currentSymbol,
    required this.currentTimeFrame,
    this.isLoading = false,
    this.isChartReady = false,
    this.errorMessage,
  });

  KlineTradingState copyWith({
    String? chartId,
    String? currentSymbol,
    String? currentTimeFrame,
    bool? isLoading,
    bool? isChartReady,
    String? errorMessage,
  }) {
    return KlineTradingState(
      chartId: chartId ?? this.chartId,
      currentSymbol: currentSymbol ?? this.currentSymbol,
      currentTimeFrame: currentTimeFrame ?? this.currentTimeFrame,
      isLoading: isLoading ?? this.isLoading,
      isChartReady: isChartReady ?? this.isChartReady,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }

  @override
  List<Object?> get props => [
        chartId,
        currentSymbol,
        currentTimeFrame,
        isLoading,
        isChartReady,
        errorMessage,
      ];
}

/// KlineTradingBloc实现
class KlineTradingBloc extends Bloc<KlineTradingEvent, KlineTradingState> {
  final MarketApiService _marketApiService;
  final ChartAPI _chartAPI;
  Timer? _chartStatusTimer;

  KlineTradingBloc({
    required MarketApiService marketApiService,
    ChartAPI? chartAPI,
  })  : _marketApiService = marketApiService,
        _chartAPI = chartAPI ?? ChartAPI.instance,
        super(const KlineTradingState(
          currentSymbol: 'BTCUSDT',
          currentTimeFrame: '15m',
        )) {
    
    // 注册事件处理器
    on<InitializeChart>(_onInitializeChart);
    on<ChangeSymbol>(_onChangeSymbol);
    on<ChangeTimeFrame>(_onChangeTimeFrame);
    on<RefreshChart>(_onRefreshChart);
    on<UpdateRealtimeData>(_onUpdateRealtimeData);

    // 🔑 关键步骤：设置ChartAPI的数据提供者
    _setupChartDataProvider();
  }

  /// 🔑 关键方法：设置ChartAPI的数据提供者
  /// 这是在Bloc模式下使用ChartAPI的正确方式
  void _setupChartDataProvider() {
    _chartAPI.setGlobalDataProvider(_handleChartDataRequest);
    
    AppLogger.logModule(
      'KlineTradingBloc',
      LogLevel.info,
      '🌐 设置图表数据提供者',
      metadata: {
        'bloc_instance': hashCode,
        'chart_api_instance': _chartAPI.hashCode,
      },
    );
  }

  /// 🔑 核心方法：处理图表数据请求
  /// 当图表可见范围变化时，ChartAPI会调用这个方法
  Future<List<ChartData>> _handleChartDataRequest(
    String symbol,
    String timeFrame,
    VisibleRange range,
  ) async {
    AppLogger.logModule(
      'KlineTradingBloc',
      LogLevel.info,
      '📊 处理图表数据请求',
      metadata: {
        'symbol': symbol,
        'time_frame': timeFrame,
        'range': '${range.from}-${range.to}',
        'current_symbol': state.currentSymbol,
        'current_timeframe': state.currentTimeFrame,
      },
    );

    try {
      // 🔑 关键：通过Bloc的API服务获取数据
      // 这里应该调用您在financial_app_market模块中的API服务
      final klineData = await _marketApiService.getKlineData(
        symbol: symbol,
        interval: timeFrame,
        limit: range.to - range.from + 50, // 多获取一些数据作为缓冲
        // startTime: _calculateStartTime(range.from, timeFrame),
        // endTime: _calculateEndTime(range.to, timeFrame),
      );

      // 转换为ChartData格式
      final chartData = klineData.map((item) => ChartData(
        timestamp: DateTime.fromMillisecondsSinceEpoch(item.openTime),
        open: item.open,
        high: item.high,
        low: item.low,
        close: item.close,
        volume: item.volume,
      )).toList();

      AppLogger.logModule(
        'KlineTradingBloc',
        LogLevel.info,
        '✅ 图表数据请求成功',
        metadata: {
          'symbol': symbol,
          'time_frame': timeFrame,
          'data_count': chartData.length,
        },
      );

      return chartData;
    } catch (e) {
      AppLogger.logModule(
        'KlineTradingBloc',
        LogLevel.error,
        '❌ 图表数据请求失败',
        error: e,
        metadata: {
          'symbol': symbol,
          'time_frame': timeFrame,
        },
      );
      
      // 在Bloc中处理错误，可以选择返回空数据或重新抛出异常
      rethrow;
    }
  }

  /// 初始化图表
  Future<void> _onInitializeChart(
    InitializeChart event,
    Emitter<KlineTradingState> emit,
  ) async {
    emit(state.copyWith(
      isLoading: true,
      currentSymbol: event.symbol,
      currentTimeFrame: event.timeFrame,
      errorMessage: null,
    ));

    try {
      // 创建图表实例
      final chartId = _chartAPI.createChart(
        chartId: 'kline_trading_${event.symbol}_${DateTime.now().millisecondsSinceEpoch}',
        symbol: event.symbol,
        timeFrame: event.timeFrame,
      );

      // 开始监控图表状态
      _startChartStatusMonitoring(chartId);

      emit(state.copyWith(
        chartId: chartId,
        isLoading: false,
        isChartReady: true,
      ));

      AppLogger.logModule(
        'KlineTradingBloc',
        LogLevel.info,
        '📊 图表初始化完成',
        metadata: {
          'chart_id': chartId,
          'symbol': event.symbol,
          'time_frame': event.timeFrame,
        },
      );
    } catch (e) {
      emit(state.copyWith(
        isLoading: false,
        errorMessage: '图表初始化失败: ${e.toString()}',
      ));

      AppLogger.logModule(
        'KlineTradingBloc',
        LogLevel.error,
        '❌ 图表初始化失败',
        error: e,
      );
    }
  }

  /// 切换交易对
  Future<void> _onChangeSymbol(
    ChangeSymbol event,
    Emitter<KlineTradingState> emit,
  ) async {
    if (state.chartId == null) return;

    emit(state.copyWith(
      isLoading: true,
      currentSymbol: event.symbol,
      errorMessage: null,
    ));

    try {
      _chartAPI.changeSymbol(state.chartId!, event.symbol);
      emit(state.copyWith(isLoading: false));

      AppLogger.logModule(
        'KlineTradingBloc',
        LogLevel.info,
        '🔄 切换交易对',
        metadata: {
          'chart_id': state.chartId,
          'symbol': event.symbol,
        },
      );
    } catch (e) {
      emit(state.copyWith(
        isLoading: false,
        errorMessage: '切换交易对失败: ${e.toString()}',
      ));
    }
  }

  /// 切换时间框架
  Future<void> _onChangeTimeFrame(
    ChangeTimeFrame event,
    Emitter<KlineTradingState> emit,
  ) async {
    if (state.chartId == null) return;

    emit(state.copyWith(
      isLoading: true,
      currentTimeFrame: event.timeFrame,
      errorMessage: null,
    ));

    try {
      _chartAPI.changeTimeFrame(state.chartId!, event.timeFrame);
      emit(state.copyWith(isLoading: false));

      AppLogger.logModule(
        'KlineTradingBloc',
        LogLevel.info,
        '⏰ 切换时间框架',
        metadata: {
          'chart_id': state.chartId,
          'time_frame': event.timeFrame,
        },
      );
    } catch (e) {
      emit(state.copyWith(
        isLoading: false,
        errorMessage: '切换时间框架失败: ${e.toString()}',
      ));
    }
  }

  /// 刷新图表
  Future<void> _onRefreshChart(
    RefreshChart event,
    Emitter<KlineTradingState> emit,
  ) async {
    if (state.chartId == null) return;

    emit(state.copyWith(isLoading: true, errorMessage: null));

    try {
      _chartAPI.refreshChart(state.chartId!);
      emit(state.copyWith(isLoading: false));

      AppLogger.logModule(
        'KlineTradingBloc',
        LogLevel.info,
        '🔄 刷新图表',
        metadata: {'chart_id': state.chartId},
      );
    } catch (e) {
      emit(state.copyWith(
        isLoading: false,
        errorMessage: '刷新图表失败: ${e.toString()}',
      ));
    }
  }

  /// 更新实时数据
  Future<void> _onUpdateRealtimeData(
    UpdateRealtimeData event,
    Emitter<KlineTradingState> emit,
  ) async {
    if (state.chartId == null) return;

    try {
      _chartAPI.updateChart(state.chartId!, event.data);

      AppLogger.logModule(
        'KlineTradingBloc',
        LogLevel.info,
        '📈 更新实时数据',
        metadata: {
          'chart_id': state.chartId,
          'data_count': event.data.length,
        },
      );
    } catch (e) {
      emit(state.copyWith(
        errorMessage: '更新实时数据失败: ${e.toString()}',
      ));
    }
  }

  /// 开始监控图表状态
  void _startChartStatusMonitoring(String chartId) {
    _chartStatusTimer?.cancel();
    _chartStatusTimer = Timer.periodic(const Duration(milliseconds: 500), (timer) {
      final status = _chartAPI.getChartStatus(chartId);
      if (state.isLoading != status.isLoading) {
        emit(state.copyWith(isLoading: status.isLoading));
      }
    });
  }

  @override
  Future<void> close() {
    _chartStatusTimer?.cancel();
    
    // 🔑 重要：清理图表资源
    if (state.chartId != null) {
      _chartAPI.destroyChart(state.chartId!);
      
      AppLogger.logModule(
        'KlineTradingBloc',
        LogLevel.info,
        '🗑️ 清理图表资源',
        metadata: {'chart_id': state.chartId},
      );
    }
    
    return super.close();
  }
}

/// 模拟的MarketApiService
class MarketApiService {
  Future<List<KlineDataItem>> getKlineData({
    required String symbol,
    required String interval,
    required int limit,
  }) async {
    // 模拟API调用
    await Future.delayed(const Duration(milliseconds: 500));
    
    // 返回模拟数据
    return List.generate(limit, (index) {
      final timestamp = DateTime.now().subtract(Duration(minutes: (limit - index) * 15));
      return KlineDataItem(
        openTime: timestamp.millisecondsSinceEpoch,
        open: 45000.0 + index * 10,
        high: 45100.0 + index * 10,
        low: 44900.0 + index * 10,
        close: 45050.0 + index * 10,
        volume: 100.0 + index,
      );
    });
  }
}

/// K线数据项
class KlineDataItem {
  final int openTime;
  final double open;
  final double high;
  final double low;
  final double close;
  final double volume;

  KlineDataItem({
    required this.openTime,
    required this.open,
    required this.high,
    required this.low,
    required this.close,
    required this.volume,
  });
}

/// 使用示例页面
class KlineTradingPageExample extends StatelessWidget {
  final String symbol;
  final String timeFrame;

  const KlineTradingPageExample({
    Key? key,
    required this.symbol,
    this.timeFrame = '15m',
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => KlineTradingBloc(
        marketApiService: MarketApiService(), // 在实际项目中从依赖注入获取
        // chartAPI: ChartAPI.instance, // 可选，默认使用instance
      )..add(InitializeChart(symbol: symbol, timeFrame: timeFrame)),
      child: const KlineTradingView(),
    );
  }
}

class KlineTradingView extends StatelessWidget {
  const KlineTradingView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: BlocBuilder<KlineTradingBloc, KlineTradingState>(
          builder: (context, state) {
            return Text('${state.currentSymbol} K线图');
          },
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              context.read<KlineTradingBloc>().add(RefreshChart());
            },
          ),
        ],
      ),
      body: BlocConsumer<KlineTradingBloc, KlineTradingState>(
        listener: (context, state) {
          if (state.errorMessage != null) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.errorMessage!),
                backgroundColor: Colors.red,
              ),
            );
          }
        },
        builder: (context, state) {
          return Column(
            children: [
              // 时间框架选择器
              _buildTimeFrameSelector(context, state),
              
              // 状态栏
              _buildStatusBar(state),
              
              // 图表区域
              Expanded(
                child: _buildChartArea(state),
              ),
              
              // 操作面板
              _buildActionPanel(context, state),
            ],
          );
        },
      ),
    );
  }

  Widget _buildTimeFrameSelector(BuildContext context, KlineTradingState state) {
    final timeFrames = ['1m', '5m', '15m', '1h', '4h', '1d'];
    
    return Container(
      height: 50,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        border: Border(bottom: BorderSide(color: Colors.grey[300]!)),
      ),
      child: Row(
        children: [
          const Text('时间框架: ', style: TextStyle(fontWeight: FontWeight.bold)),
          Expanded(
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: timeFrames.length,
              itemBuilder: (context, index) {
                final timeFrame = timeFrames[index];
                return Padding(
                  padding: const EdgeInsets.only(right: 8),
                  child: ChoiceChip(
                    label: Text(timeFrame),
                    selected: state.currentTimeFrame == timeFrame,
                    onSelected: (selected) {
                      if (selected) {
                        context.read<KlineTradingBloc>().add(
                          ChangeTimeFrame(timeFrame),
                        );
                      }
                    },
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusBar(KlineTradingState state) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: state.isLoading ? Colors.orange[100] : Colors.green[100],
      ),
      child: Row(
        children: [
          if (state.isLoading)
            const SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(strokeWidth: 2),
            ),
          if (state.isLoading) const SizedBox(width: 8),
          Icon(
            state.isLoading ? Icons.hourglass_empty : Icons.check_circle,
            size: 16,
            color: state.isLoading ? Colors.orange : Colors.green,
          ),
          const SizedBox(width: 8),
          Text(
            state.isLoading 
                ? '加载数据中...' 
                : '${state.currentSymbol} - ${state.currentTimeFrame}',
            style: TextStyle(
              color: state.isLoading ? Colors.orange[800] : Colors.green[800],
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildChartArea(KlineTradingState state) {
    if (!state.isChartReady) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('初始化图表中...'),
          ],
        ),
      );
    }

    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(8),
      ),
      child: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.show_chart, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text('K线图表区域', style: TextStyle(fontSize: 18)),
            SizedBox(height: 8),
            Text('实际使用时这里是WebView图表组件'),
            SizedBox(height: 8),
            Text(
              '可见范围变化时会自动触发数据加载',
              style: TextStyle(fontSize: 12, color: Colors.grey),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionPanel(BuildContext context, KlineTradingState state) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        border: Border(top: BorderSide(color: Colors.grey[300]!)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          ElevatedButton(
            onPressed: state.isLoading ? null : () {
              context.read<KlineTradingBloc>().add(RefreshChart());
            },
            child: const Text('刷新'),
          ),
          ElevatedButton(
            onPressed: () {
              // 模拟实时数据更新
              final mockData = [
                ChartData(
                  timestamp: DateTime.now(),
                  open: 45000,
                  high: 45100,
                  low: 44900,
                  close: 45050,
                  volume: 100,
                ),
              ];
              context.read<KlineTradingBloc>().add(
                UpdateRealtimeData(mockData),
              );
            },
            child: const Text('实时更新'),
          ),
          ElevatedButton(
            onPressed: () {
              context.read<KlineTradingBloc>().add(
                const ChangeSymbol('ETHUSDT'),
              );
            },
            child: const Text('切换ETH'),
          ),
        ],
      ),
    );
  }
}

/// 应用入口
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // 初始化日志系统
  await AppLogger.initialize(config: LogConfig.development());
  
  runApp(MaterialApp(
    title: 'Bloc模式图表集成示例',
    home: const KlineTradingPageExample(symbol: 'BTCUSDT'),
    debugShowCheckedModeBanner: false,
  ));
}
