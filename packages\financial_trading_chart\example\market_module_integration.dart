import 'dart:async';
import 'package:flutter/material.dart';
import 'package:financial_app_core/financial_app_core.dart';
// 假设这些是financial_app_market模块的导入
// import 'package:financial_app_market/financial_app_market.dart';
import '../lib/src/api/chart_api.dart';
import '../lib/src/data/chart_data_manager.dart';
import '../lib/src/models/chart_data.dart';

/// financial_app_market 模块集成示例
/// 
/// 展示如何在实际的业务模块中集成图表数据更新功能
class MarketModuleIntegration {
  
  /// 模块初始化 - 在financial_app_market模块的初始化方法中调用
  static Future<void> initializeChartIntegration() async {
    AppLogger.logModule(
      'MarketModule',
      LogLevel.info,
      '🚀 初始化图表集成功能',
    );

    // 设置全局数据提供者
    ChartAPI.instance.setGlobalDataProvider(_fetchMarketData);
    
    AppLogger.logModule(
      'MarketModule',
      LogLevel.info,
      '✅ 图表集成功能初始化完成',
    );
  }

  /// 核心数据获取方法 - 连接到market模块的API服务
  static Future<List<ChartData>> _fetchMarketData(
    String symbol,
    String timeFrame,
    VisibleRange range,
  ) async {
    AppLogger.logModule(
      'MarketModule',
      LogLevel.info,
      '📊 获取市场数据',
      metadata: {
        'symbol': symbol,
        'time_frame': timeFrame,
        'range': {
          'from': range.from,
          'to': range.to,
        },
      },
    );

    try {
      // 实际使用时，这里应该调用market模块的API服务
      // 例如：
      // final marketApiService = locator<MarketApiService>();
      // final response = await marketApiService.getKlineData(
      //   symbol: symbol,
      //   interval: timeFrame,
      //   startTime: _calculateStartTime(range.from, timeFrame),
      //   endTime: _calculateEndTime(range.to, timeFrame),
      //   limit: range.to - range.from + 100, // 多获取一些数据作为缓冲
      // );
      // 
      // return response.data.map((item) => ChartData(
      //   timestamp: DateTime.fromMillisecondsSinceEpoch(item.openTime),
      //   open: item.open,
      //   high: item.high,
      //   low: item.low,
      //   close: item.close,
      //   volume: item.volume,
      // )).toList();

      // 模拟API调用
      await Future.delayed(const Duration(milliseconds: 500));
      return _generateMockMarketData(symbol, timeFrame, range.to - range.from + 50);
      
    } catch (e) {
      AppLogger.logModule(
        'MarketModule',
        LogLevel.error,
        '❌ 获取市场数据失败',
        error: e,
        metadata: {
          'symbol': symbol,
          'time_frame': timeFrame,
        },
      );
      rethrow;
    }
  }

  /// 计算开始时间（根据时间框架和范围）
  static DateTime _calculateStartTime(int rangeFrom, String timeFrame) {
    final intervalMs = _getTimeIntervalMs(timeFrame);
    return DateTime.now().subtract(Duration(milliseconds: rangeFrom * intervalMs));
  }

  /// 计算结束时间
  static DateTime _calculateEndTime(int rangeTo, String timeFrame) {
    final intervalMs = _getTimeIntervalMs(timeFrame);
    return DateTime.now().subtract(Duration(milliseconds: rangeTo * intervalMs));
  }

  /// 获取时间间隔（毫秒）
  static int _getTimeIntervalMs(String timeFrame) {
    switch (timeFrame) {
      case '1s': return 1000;
      case '1m': return 60 * 1000;
      case '5m': return 5 * 60 * 1000;
      case '15m': return 15 * 60 * 1000;
      case '1h': return 60 * 60 * 1000;
      case '4h': return 4 * 60 * 60 * 1000;
      case '1d': return 24 * 60 * 60 * 1000;
      default: return 15 * 60 * 1000; // 默认15分钟
    }
  }

  /// 生成模拟市场数据
  static List<ChartData> _generateMockMarketData(String symbol, String timeFrame, int count) {
    final data = <ChartData>[];
    final intervalMs = _getTimeIntervalMs(timeFrame);
    final baseTime = DateTime.now().subtract(Duration(milliseconds: count * intervalMs));
    
    // 根据交易对设置基础价格
    double basePrice = switch (symbol.toUpperCase()) {
      'BTCUSDT' => 45000.0,
      'ETHUSDT' => 3000.0,
      'ADAUSDT' => 0.5,
      'DOTUSDT' => 8.0,
      'BNBUSDT' => 300.0,
      _ => 100.0,
    };

    for (int i = 0; i < count; i++) {
      final timestamp = baseTime.add(Duration(milliseconds: i * intervalMs));
      
      // 模拟价格波动
      final volatility = 0.02; // 2%的波动率
      final change = (0.5 - (i % 100) / 100) * volatility;
      basePrice *= (1 + change);
      
      final open = basePrice;
      final close = basePrice * (1 + (0.5 - (i % 50) / 50) * volatility * 0.5);
      final high = [open, close].reduce((a, b) => a > b ? a : b) * (1 + volatility * 0.3);
      final low = [open, close].reduce((a, b) => a < b ? a : b) * (1 - volatility * 0.3);
      final volume = 100.0 + (i % 100) * 10;

      data.add(ChartData(
        timestamp: timestamp,
        open: open,
        high: high,
        low: low,
        close: close,
        volume: volume,
      ));
      
      basePrice = close;
    }

    return data;
  }
}

/// K线交易页面 - 对应financial_app_market模块中的kline_trading_page
class KlineTradingPage extends StatefulWidget {
  final String symbol;
  final String initialTimeFrame;

  const KlineTradingPage({
    Key? key,
    required this.symbol,
    this.initialTimeFrame = '15m',
  }) : super(key: key);

  @override
  State<KlineTradingPage> createState() => _KlineTradingPageState();
}

class _KlineTradingPageState extends State<KlineTradingPage> {
  late String _chartId;
  late String _currentSymbol;
  late String _currentTimeFrame;
  bool _isLoading = false;
  String _statusMessage = '初始化中...';

  // 支持的时间框架
  final List<String> _timeFrames = ['1m', '5m', '15m', '1h', '4h', '1d'];

  @override
  void initState() {
    super.initState();
    _currentSymbol = widget.symbol;
    _currentTimeFrame = widget.initialTimeFrame;
    _initializeChart();
  }

  /// 初始化图表
  void _initializeChart() {
    // 创建图表实例
    _chartId = ChartAPI.instance.createChart(
      chartId: 'kline_trading_${widget.symbol}',
      symbol: _currentSymbol,
      timeFrame: _currentTimeFrame,
    );

    // 监听图表状态变化
    ChartAPI.instance.addChartListener(_chartId, _onChartStateChanged);

    AppLogger.logModule(
      'KlineTradingPage',
      LogLevel.info,
      '📊 K线交易页面图表初始化',
      metadata: {
        'chart_id': _chartId,
        'symbol': _currentSymbol,
        'time_frame': _currentTimeFrame,
      },
    );
  }

  /// 图表状态变化回调
  void _onChartStateChanged() {
    final status = ChartAPI.instance.getChartStatus(_chartId);
    setState(() {
      _isLoading = status.isLoading;
      if (status.error != null) {
        _statusMessage = '错误: ${status.error}';
      } else if (status.isLoading) {
        _statusMessage = '加载数据中...';
      } else {
        _statusMessage = '${status.symbol} - ${status.timeFrame}';
      }
    });
  }

  /// 切换时间框架
  void _changeTimeFrame(String timeFrame) {
    setState(() {
      _currentTimeFrame = timeFrame;
    });
    
    ChartAPI.instance.changeTimeFrame(_chartId, timeFrame);
    
    AppLogger.logModule(
      'KlineTradingPage',
      LogLevel.info,
      '⏰ 切换时间框架',
      metadata: {
        'chart_id': _chartId,
        'time_frame': timeFrame,
      },
    );
  }

  /// 刷新图表数据
  void _refreshChart() {
    ChartAPI.instance.refreshChart(_chartId);
    setState(() {
      _statusMessage = '刷新数据中...';
    });
  }

  /// 模拟WebSocket实时数据更新
  void _simulateRealtimeUpdate() {
    // 在实际应用中，这里应该是WebSocket推送的数据
    final newData = MarketModuleIntegration._generateMockMarketData(
      _currentSymbol,
      _currentTimeFrame,
      1,
    );
    
    ChartAPI.instance.updateChart(_chartId, newData);
    
    AppLogger.logModule(
      'KlineTradingPage',
      LogLevel.info,
      '📈 实时数据更新',
      metadata: {
        'chart_id': _chartId,
        'data_count': newData.length,
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('$_currentSymbol K线图'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _refreshChart,
          ),
          IconButton(
            icon: const Icon(Icons.update),
            onPressed: _simulateRealtimeUpdate,
          ),
        ],
      ),
      body: Column(
        children: [
          // 时间框架选择器
          _buildTimeFrameSelector(),
          
          // 状态栏
          _buildStatusBar(),
          
          // 图表区域
          Expanded(
            child: _buildChartArea(),
          ),
          
          // 交易操作面板（可选）
          _buildTradingPanel(),
        ],
      ),
    );
  }

  /// 构建时间框架选择器
  Widget _buildTimeFrameSelector() {
    return Container(
      height: 50,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        border: Border(bottom: BorderSide(color: Colors.grey[300]!)),
      ),
      child: Row(
        children: [
          const Text('时间框架: ', style: TextStyle(fontWeight: FontWeight.bold)),
          const SizedBox(width: 8),
          Expanded(
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: _timeFrames.length,
              itemBuilder: (context, index) {
                final timeFrame = _timeFrames[index];
                final isSelected = timeFrame == _currentTimeFrame;
                
                return Padding(
                  padding: const EdgeInsets.only(right: 8),
                  child: ChoiceChip(
                    label: Text(timeFrame),
                    selected: isSelected,
                    onSelected: (selected) {
                      if (selected) _changeTimeFrame(timeFrame);
                    },
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  /// 构建状态栏
  Widget _buildStatusBar() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: _isLoading ? Colors.orange[100] : Colors.green[100],
        border: Border(bottom: BorderSide(color: Colors.grey[300]!)),
      ),
      child: Row(
        children: [
          if (_isLoading)
            const SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(strokeWidth: 2),
            ),
          if (_isLoading) const SizedBox(width: 8),
          Icon(
            _isLoading ? Icons.hourglass_empty : Icons.check_circle,
            size: 16,
            color: _isLoading ? Colors.orange : Colors.green,
          ),
          const SizedBox(width: 8),
          Text(
            _statusMessage,
            style: TextStyle(
              color: _isLoading ? Colors.orange[800] : Colors.green[800],
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建图表区域
  Widget _buildChartArea() {
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(8),
      ),
      child: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.show_chart, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'K线图表区域',
              style: TextStyle(fontSize: 18, color: Colors.grey),
            ),
            SizedBox(height: 8),
            Text(
              '实际使用时这里是WebView图表',
              style: TextStyle(color: Colors.grey),
            ),
            SizedBox(height: 8),
            Text(
              '可见范围变化时会自动加载历史数据',
              style: TextStyle(fontSize: 12, color: Colors.grey),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建交易操作面板
  Widget _buildTradingPanel() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        border: Border(top: BorderSide(color: Colors.grey[300]!)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          ElevatedButton(
            onPressed: () {
              // 买入操作
              AppLogger.logModule('KlineTradingPage', LogLevel.info, '买入操作');
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.green),
            child: const Text('买入', style: TextStyle(color: Colors.white)),
          ),
          ElevatedButton(
            onPressed: () {
              // 卖出操作
              AppLogger.logModule('KlineTradingPage', LogLevel.info, '卖出操作');
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('卖出', style: TextStyle(color: Colors.white)),
          ),
          OutlinedButton(
            onPressed: _refreshChart,
            child: const Text('刷新'),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    // 移除监听器
    ChartAPI.instance.removeChartListener(_chartId, _onChartStateChanged);
    
    // 销毁图表实例
    ChartAPI.instance.destroyChart(_chartId);
    
    AppLogger.logModule(
      'KlineTradingPage',
      LogLevel.info,
      '🗑️ K线交易页面资源清理',
      metadata: {'chart_id': _chartId},
    );
    
    super.dispose();
  }
}

/// 使用示例的主函数
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // 初始化日志系统
  await AppLogger.initialize(config: LogConfig.development());
  
  // 初始化market模块的图表集成
  await MarketModuleIntegration.initializeChartIntegration();
  
  runApp(MaterialApp(
    title: 'Market模块图表集成示例',
    home: const KlineTradingPage(symbol: 'BTCUSDT'),
    debugShowCheckedModeBanner: false,
  ));
}
