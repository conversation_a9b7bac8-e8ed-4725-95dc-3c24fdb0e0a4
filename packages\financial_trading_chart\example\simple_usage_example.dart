import 'dart:async';
import 'package:flutter/material.dart';
import 'package:financial_app_core/financial_app_core.dart';
import '../lib/src/api/chart_api.dart';
import '../lib/src/data/chart_data_manager.dart';
import '../lib/src/models/chart_data.dart';

/// 简化的使用示例
/// 
/// 展示其他模块如何快速集成图表数据更新功能
class SimpleUsageExample {
  
  /// 步骤1: 设置全局数据提供者
  /// 这是其他模块需要做的第一步，通常在应用启动时调用
  static void setupGlobalDataProvider() {
    ChartAPI.instance.setGlobalDataProvider(_fetchHistoricalData);
    
    AppLogger.logModule(
      'SimpleUsageExample',
      LogLevel.info,
      '🌐 全局数据提供者已设置',
    );
  }

  /// 步骤2: 实现数据获取方法
  /// 这是核心方法，需要根据实际的API服务来实现
  static Future<List<ChartData>> _fetchHistoricalData(
    String symbol,
    String timeFrame,
    VisibleRange range,
  ) async {
    AppLogger.logModule(
      'SimpleUsageExample',
      LogLevel.info,
      '📊 获取历史数据',
      metadata: {
        'symbol': symbol,
        'time_frame': timeFrame,
        'range': '${range.from}-${range.to}',
      },
    );

    // 这里应该调用您的实际API服务
    // 例如：
    // final apiService = locator<MarketApiService>();
    // return await apiService.getKlineData(
    //   symbol: symbol,
    //   interval: timeFrame,
    //   startTime: range.from,
    //   endTime: range.to,
    // );

    // 模拟数据（实际使用时请替换为真实API调用）
    return _generateMockData(symbol, timeFrame, range.to - range.from);
  }

  /// 步骤3: 在页面中使用图表
  /// 这是在具体页面中如何使用图表的示例
  static String createChartInPage({
    required String symbol,
    required String timeFrame,
    String? chartId,
  }) {
    // 创建图表实例
    final id = ChartAPI.instance.createChart(
      chartId: chartId,
      symbol: symbol,
      timeFrame: timeFrame,
    );

    AppLogger.logModule(
      'SimpleUsageExample',
      LogLevel.info,
      '📊 创建图表实例',
      metadata: {
        'chart_id': id,
        'symbol': symbol,
        'time_frame': timeFrame,
      },
    );

    return id;
  }

  /// 步骤4: 监听图表状态变化
  static void listenToChartChanges(String chartId, VoidCallback onStateChanged) {
    ChartAPI.instance.addChartListener(chartId, onStateChanged);
    
    AppLogger.logModule(
      'SimpleUsageExample',
      LogLevel.debug,
      '👂 添加图表状态监听器',
      metadata: {'chart_id': chartId},
    );
  }

  /// 步骤5: 操作图表
  static void operateChart(String chartId) {
    // 切换交易对
    ChartAPI.instance.changeSymbol(chartId, 'ETHUSDT');
    
    // 切换时间框架
    ChartAPI.instance.changeTimeFrame(chartId, '1h');
    
    // 刷新数据
    ChartAPI.instance.refreshChart(chartId);
    
    // 获取图表状态
    final status = ChartAPI.instance.getChartStatus(chartId);
    AppLogger.logModule(
      'SimpleUsageExample',
      LogLevel.info,
      '📊 图表状态',
      metadata: {
        'chart_id': chartId,
        'is_loading': status.isLoading,
        'symbol': status.symbol,
        'time_frame': status.timeFrame,
      },
    );
  }

  /// 步骤6: 手动更新图表数据（用于实时数据推送）
  static void updateChartWithRealtimeData(String chartId, List<ChartData> newData) {
    ChartAPI.instance.updateChart(chartId, newData);
    
    AppLogger.logModule(
      'SimpleUsageExample',
      LogLevel.info,
      '📈 手动更新图表数据',
      metadata: {
        'chart_id': chartId,
        'data_count': newData.length,
      },
    );
  }

  /// 步骤7: 清理资源
  static void cleanupChart(String chartId) {
    // 移除监听器（如果有的话）
    // ChartAPI.instance.removeChartListener(chartId, onStateChanged);
    
    // 销毁图表实例
    ChartAPI.instance.destroyChart(chartId);
    
    AppLogger.logModule(
      'SimpleUsageExample',
      LogLevel.info,
      '🗑️ 清理图表资源',
      metadata: {'chart_id': chartId},
    );
  }

  /// 生成模拟数据（实际使用时请删除）
  static List<ChartData> _generateMockData(String symbol, String timeFrame, int count) {
    final data = <ChartData>[];
    final baseTime = DateTime.now().subtract(Duration(minutes: count * 15));
    double basePrice = 50000.0;

    for (int i = 0; i < count; i++) {
      final timestamp = baseTime.add(Duration(minutes: i * 15));
      final open = basePrice;
      final close = basePrice * (1 + (0.5 - (i % 100) / 100) * 0.02);
      final high = [open, close].reduce((a, b) => a > b ? a : b) * 1.01;
      final low = [open, close].reduce((a, b) => a < b ? a : b) * 0.99;
      final volume = 100.0 + (i % 50) * 10;

      data.add(ChartData(
        timestamp: timestamp,
        open: open,
        high: high,
        low: low,
        close: close,
        volume: volume,
      ));
      
      basePrice = close;
    }

    return data;
  }
}

/// Widget使用示例
class ChartWidget extends StatefulWidget {
  final String symbol;
  final String timeFrame;

  const ChartWidget({
    Key? key,
    required this.symbol,
    required this.timeFrame,
  }) : super(key: key);

  @override
  State<ChartWidget> createState() => _ChartWidgetState();
}

class _ChartWidgetState extends State<ChartWidget> {
  late String _chartId;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    
    // 创建图表实例
    _chartId = SimpleUsageExample.createChartInPage(
      symbol: widget.symbol,
      timeFrame: widget.timeFrame,
    );
    
    // 监听状态变化
    SimpleUsageExample.listenToChartChanges(_chartId, _onChartStateChanged);
  }

  void _onChartStateChanged() {
    final status = ChartAPI.instance.getChartStatus(_chartId);
    setState(() {
      _isLoading = status.isLoading;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // 状态指示器
        if (_isLoading)
          const LinearProgressIndicator(),
        
        // 图表区域（这里用占位符代替实际的WebView）
        Expanded(
          child: Container(
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey),
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Center(
              child: Text('图表区域\n(实际使用时这里是WebView)'),
            ),
          ),
        ),
        
        // 操作按钮
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            ElevatedButton(
              onPressed: () => SimpleUsageExample.operateChart(_chartId),
              child: const Text('操作图表'),
            ),
            ElevatedButton(
              onPressed: () {
                final mockData = SimpleUsageExample._generateMockData(
                  widget.symbol,
                  widget.timeFrame,
                  10,
                );
                SimpleUsageExample.updateChartWithRealtimeData(_chartId, mockData);
              },
              child: const Text('更新数据'),
            ),
          ],
        ),
      ],
    );
  }

  @override
  void dispose() {
    // 清理资源
    SimpleUsageExample.cleanupChart(_chartId);
    super.dispose();
  }
}

/// 在其他模块中的使用示例
class MarketPage extends StatefulWidget {
  @override
  State<MarketPage> createState() => _MarketPageState();
}

class _MarketPageState extends State<MarketPage> {
  @override
  void initState() {
    super.initState();
    
    // 在应用启动时设置全局数据提供者（通常在main.dart或模块初始化时调用）
    SimpleUsageExample.setupGlobalDataProvider();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('市场页面')),
      body: const ChartWidget(
        symbol: 'BTCUSDT',
        timeFrame: '15m',
      ),
    );
  }
}

/// 完整的集成步骤总结
/// 
/// 1. 在应用启动时调用 SimpleUsageExample.setupGlobalDataProvider()
/// 2. 实现 _fetchHistoricalData 方法，连接到您的API服务
/// 3. 在需要图表的页面中使用 ChartWidget 或直接调用 ChartAPI
/// 4. 监听图表状态变化以更新UI
/// 5. 在页面销毁时清理资源
/// 
/// 关键点：
/// - 可见范围变化会自动触发数据加载
/// - 系统会自动处理缓存和去重
/// - 支持多个图表实例同时运行
/// - 提供完整的状态管理和错误处理
