import 'package:flutter/material.dart';
import '../lib/src/widgets/trading_chart_widget.dart';
import '../lib/src/models/chart_data.dart';

/// 测试时间轴显示的示例
class TimeAxisTestPage extends StatefulWidget {
  const TimeAxisTestPage({Key? key}) : super(key: key);

  @override
  State<TimeAxisTestPage> createState() => _TimeAxisTestPageState();
}

class _TimeAxisTestPageState extends State<TimeAxisTestPage> {
  final GlobalKey<TradingChartWidgetState> _chartKey = GlobalKey();

  // 生成测试数据，包含不同的时间格式
  List<KLineData> _generateTestData() {
    final now = DateTime.now();
    final data = <KLineData>[];
    
    // 生成最近30天的数据
    for (int i = 29; i >= 0; i--) {
      final date = now.subtract(Duration(days: i));
      final basePrice = 100.0 + (i * 0.5); // 基础价格
      final volatility = 5.0; // 波动率
      
      // 模拟价格波动
      final random = (date.millisecondsSinceEpoch % 1000) / 1000.0;
      final priceChange = (random - 0.5) * volatility;
      
      final open = basePrice + priceChange;
      final close = open + ((random - 0.5) * 2);
      final high = [open, close].reduce((a, b) => a > b ? a : b) + (random * 2);
      final low = [open, close].reduce((a, b) => a < b ? a : b) - (random * 2);
      
      data.add(KLineData(
        timestamp: date,
        time: date.toIso8601String().split('T')[0], // YYYY-MM-DD 格式
        open: open,
        high: high,
        low: low,
        close: close,
        volume: 1000000 + (random * 500000),
      ));
    }
    
    return data;
  }

  // 生成不同时间范围的测试数据
  List<KLineData> _generateDataForRange(int days) {
    final now = DateTime.now();
    final data = <KLineData>[];
    
    for (int i = days - 1; i >= 0; i--) {
      final date = now.subtract(Duration(days: i));
      final basePrice = 100.0;
      final random = (date.millisecondsSinceEpoch % 1000) / 1000.0;
      
      final open = basePrice + (random * 10);
      final close = open + ((random - 0.5) * 5);
      final high = [open, close].reduce((a, b) => a > b ? a : b) + (random * 3);
      final low = [open, close].reduce((a, b) => a < b ? a : b) - (random * 3);
      
      data.add(KLineData(
        timestamp: date,
        time: date.toIso8601String().split('T')[0],
        open: open,
        high: high,
        low: low,
        close: close,
        volume: 1000000,
      ));
    }
    
    return data;
  }

  void _loadTestData() {
    final data = _generateTestData();
    debugPrint('📊 Loading ${data.length} data points');
    debugPrint('📅 Date range: ${data.first.time} to ${data.last.time}');
    
    _chartKey.currentState?.setData(data);
  }

  void _load7DaysData() {
    final data = _generateDataForRange(7);
    debugPrint('📊 Loading 7 days data: ${data.length} points');
    _chartKey.currentState?.setData(data);
  }

  void _load30DaysData() {
    final data = _generateDataForRange(30);
    debugPrint('📊 Loading 30 days data: ${data.length} points');
    _chartKey.currentState?.setData(data);
  }

  void _load90DaysData() {
    final data = _generateDataForRange(90);
    debugPrint('📊 Loading 90 days data: ${data.length} points');
    _chartKey.currentState?.setData(data);
  }

  void _loadSampleData() {
    // 使用固定的示例数据，确保时间轴显示
    final sampleData = [
      KLineData(
        timestamp: DateTime(2024, 1, 1),
        time: '2024-01-01',
        open: 100.0,
        high: 105.0,
        low: 98.0,
        close: 103.0,
        volume: 1000000,
      ),
      KLineData(
        timestamp: DateTime(2024, 1, 2),
        time: '2024-01-02',
        open: 103.0,
        high: 108.0,
        low: 101.0,
        close: 106.0,
        volume: 1200000,
      ),
      KLineData(
        timestamp: DateTime(2024, 1, 3),
        time: '2024-01-03',
        open: 106.0,
        high: 110.0,
        low: 104.0,
        close: 108.0,
        volume: 1100000,
      ),
      KLineData(
        timestamp: DateTime(2024, 1, 4),
        time: '2024-01-04',
        open: 108.0,
        high: 112.0,
        low: 106.0,
        close: 110.0,
        volume: 1300000,
      ),
      KLineData(
        timestamp: DateTime(2024, 1, 5),
        time: '2024-01-05',
        open: 110.0,
        high: 115.0,
        low: 108.0,
        close: 113.0,
        volume: 1400000,
      ),
    ];
    
    debugPrint('📊 Loading sample data: ${sampleData.length} points');
    for (final item in sampleData) {
      debugPrint('📅 ${item.time}: O=${item.open}, H=${item.high}, L=${item.low}, C=${item.close}');
    }
    
    _chartKey.currentState?.setData(sampleData);
  }

  @override
  void initState() {
    super.initState();
    // 延迟加载测试数据
    Future.delayed(const Duration(milliseconds: 500), () {
      _loadSampleData();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Time Axis Test'),
        backgroundColor: Colors.blue,
      ),
      body: Column(
        children: [
          // 信息面板
          Container(
            padding: const EdgeInsets.all(16),
            color: Colors.grey[100],
            child: const Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '时间轴测试',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                SizedBox(height: 8),
                Text('测试不同时间范围的K线数据显示'),
                Text('检查时间轴是否正确显示日期'),
              ],
            ),
          ),
          
          // 图表区域
          Expanded(
            child: Container(
              margin: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey),
                borderRadius: BorderRadius.circular(8),
              ),
              child: TradingChartWidget(
                key: _chartKey,
              ),
            ),
          ),
          
          // 控制按钮
          Container(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton(
                        onPressed: _loadSampleData,
                        child: const Text('Sample Data'),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: _load7DaysData,
                        child: const Text('7 Days'),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton(
                        onPressed: _load30DaysData,
                        child: const Text('30 Days'),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: _load90DaysData,
                        child: const Text('90 Days'),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: _loadTestData,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                    ),
                    child: const Text('Load Test Data'),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

/// 测试应用
class TimeAxisTestApp extends StatelessWidget {
  const TimeAxisTestApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Time Axis Test',
      theme: ThemeData(
        primarySwatch: Colors.blue,
      ),
      home: const TimeAxisTestPage(),
      debugShowCheckedModeBanner: false,
    );
  }
}

void main() {
  runApp(const TimeAxisTestApp());
}
