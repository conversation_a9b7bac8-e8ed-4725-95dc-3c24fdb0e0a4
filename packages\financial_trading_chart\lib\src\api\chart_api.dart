import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:financial_app_core/financial_app_core.dart';
import '../controllers/chart_data_controller.dart';
import '../data/chart_data_manager.dart';
import '../models/chart_data.dart';

/// 图表API - 提供简化的接口供其他模块使用
///
/// 这是一个全局单例，其他模块通过这个API来集成图表数据更新功能
class ChartAPI {
  static ChartAPI? _instance;
  static ChartAPI get instance => _instance ??= ChartAPI._();

  ChartAPI._();

  /// 已注册的图表控制器
  final Map<String, ChartDataController> _controllers = {};

  /// 全局数据提供者
  Future<List<ChartData>> Function(
    String symbol,
    String timeFrame,
    VisibleRange range,
  )?
  _globalDataProvider;

  /// 设置全局数据提供者
  ///
  /// 其他模块调用此方法来设置数据获取逻辑
  void setGlobalDataProvider(
    Future<List<ChartData>> Function(
      String symbol,
      String timeFrame,
      VisibleRange range,
    )
    provider,
  ) {
    _globalDataProvider = provider;

    AppLogger.logModule(
      'ChartAPI',
      LogLevel.info,
      '🌐 设置全局数据提供者',
      metadata: {
        'provider_set': true,
        'active_controllers': _controllers.length,
      },
    );

    // 为所有已存在的控制器设置数据提供者
    for (final controller in _controllers.values) {
      controller.dataProvider = provider;
    }
  }

  /// 创建图表实例
  ///
  /// 其他模块调用此方法来创建和管理图表
  String createChart({
    required String symbol,
    required String timeFrame,
    String? chartId,
    Future<List<ChartData>> Function(
      String symbol,
      String timeFrame,
      VisibleRange range,
    )?
    dataProvider,
  }) {
    final id = chartId ?? 'chart_${DateTime.now().millisecondsSinceEpoch}';

    if (_controllers.containsKey(id)) {
      AppLogger.logModule(
        'ChartAPI',
        LogLevel.warning,
        '⚠️ 图表ID已存在，将覆盖现有图表',
        metadata: {'chart_id': id, 'symbol': symbol, 'time_frame': timeFrame},
      );

      _controllers[id]?.dispose();
    }

    final controller = ChartDataController();
    controller.initialize(
      chartId: id,
      symbol: symbol,
      timeFrame: timeFrame,
      dataProvider: dataProvider ?? _globalDataProvider,
    );

    _controllers[id] = controller;

    AppLogger.logModule(
      'ChartAPI',
      LogLevel.info,
      '📊 创建图表实例',
      metadata: {
        'chart_id': id,
        'symbol': symbol,
        'time_frame': timeFrame,
        'has_custom_provider': dataProvider != null,
        'has_global_provider': _globalDataProvider != null,
        'total_charts': _controllers.length,
      },
    );

    return id;
  }

  /// 获取图表控制器
  ChartDataController? getController(String chartId) {
    return _controllers[chartId];
  }

  /// 添加历史数据（保持当前可见范围）
  ///
  /// 专门用于添加历史数据，不会重置图表位置
  void addHistoricalData(String chartId, List<ChartData> data) {
    final controller = _controllers[chartId];
    if (controller == null) {
      AppLogger.logModule(
        'ChartAPI',
        LogLevel.warning,
        '⚠️ 图表控制器不存在',
        metadata: {'chart_id': chartId},
      );
      return;
    }

    AppLogger.logModule(
      'ChartAPI',
      LogLevel.info,
      '📈 添加历史数据',
      metadata: {
        'chart_id': chartId,
        'data_count': data.length,
        'symbol': controller.currentSymbol,
        'time_frame': controller.currentTimeFrame,
      },
    );

    // 🔑 直接添加历史数据到WebView图表
    _addHistoricalDataToWebView(chartId, data);

    // 同时通过数据管理器更新缓存
    if (controller.currentSymbol != null &&
        controller.currentTimeFrame != null) {
      ChartDataManager.instance.handleDataResponse(
        controller.currentSymbol!,
        controller.currentTimeFrame!,
        data,
      );
    }
  }

  /// 更新图表数据
  ///
  /// 其他模块可以调用此方法来主动更新图表数据
  void updateChart(String chartId, List<ChartData> data) {
    final controller = _controllers[chartId];
    if (controller == null) {
      AppLogger.logModule(
        'ChartAPI',
        LogLevel.warning,
        '⚠️ 图表控制器不存在',
        metadata: {'chart_id': chartId},
      );
      return;
    }

    AppLogger.logModule(
      'ChartAPI',
      LogLevel.info,
      '📈 更新图表数据',
      metadata: {
        'chart_id': chartId,
        'data_count': data.length,
        'symbol': controller.currentSymbol,
        'time_frame': controller.currentTimeFrame,
      },
    );

    // 🔑 直接更新WebView图表 - 修复数据不显示问题
    _updateWebViewChart(chartId, data);

    // 同时通过数据管理器更新缓存
    if (controller.currentSymbol != null &&
        controller.currentTimeFrame != null) {
      ChartDataManager.instance.handleDataResponse(
        controller.currentSymbol!,
        controller.currentTimeFrame!,
        data,
      );
    }
  }

  /// 🔑 直接添加历史数据到WebView图表
  void _addHistoricalDataToWebView(String chartId, List<ChartData> data) {
    try {
      // 获取图表控制器以获取timeFrame信息
      final controller = _controllers[chartId];
      final timeFrame = controller?.currentTimeFrame ?? '15min'; // 默认15分钟

      // 导入TradingChartWidget的全局key
      final tradingChartKey = _getTradingChartKey();
      if (tradingChartKey?.currentState != null) {
        // 转换数据格式
        final klineData = data.map((item) {
          if (item is KLineData) {
            return item;
          } else {
            // 转换其他类型的ChartData为KLineData
            return KLineData(
              time: item.timestamp.toIso8601String(),
              open: item.value,
              high: item.value,
              low: item.value,
              close: item.value,
              timestamp: item.timestamp,
              volume: 0.0,
            );
          }
        }).toList();

        AppLogger.logModule(
          'ChartAPI',
          LogLevel.info,
          '🔄 直接添加历史数据到WebView图表',
          metadata: {
            'chart_id': chartId,
            'kline_data_count': klineData.length,
            'time_frame': timeFrame,
          },
        );

        // 🔑 直接调用TradingChartWidget的addHistoricalData方法
        tradingChartKey!.currentState!.addHistoricalData(
          klineData,
          timeFrame: timeFrame,
        );

        AppLogger.logModule('ChartAPI', LogLevel.info, '✅ WebView历史数据添加完成');
      } else {
        AppLogger.logModule(
          'ChartAPI',
          LogLevel.warning,
          '⚠️ TradingChartWidget未准备好，无法添加历史数据',
        );
      }
    } catch (e) {
      AppLogger.logModule(
        'ChartAPI',
        LogLevel.error,
        '❌ 直接添加历史数据到WebView图表失败',
        error: e,
      );
    }
  }

  /// 🔑 直接更新WebView图表
  void _updateWebViewChart(String chartId, List<ChartData> data) {
    try {
      // 获取图表控制器以获取timeFrame信息
      final controller = _controllers[chartId];
      final timeFrame = controller?.currentTimeFrame ?? '15min'; // 默认15分钟

      // 导入TradingChartWidget的全局key
      final tradingChartKey = _getTradingChartKey();
      if (tradingChartKey?.currentState != null) {
        // 转换数据格式
        final klineData = data.map((item) {
          if (item is KLineData) {
            return item;
          } else {
            // 转换其他类型的ChartData为KLineData
            return KLineData(
              time: item.timestamp.toIso8601String(),
              open: item.value,
              high: item.value,
              low: item.value,
              close: item.value,
              timestamp: item.timestamp,
              volume: 0.0,
            );
          }
        }).toList();

        AppLogger.logModule(
          'ChartAPI',
          LogLevel.info,
          '🔄 直接更新WebView图表',
          metadata: {
            'chart_id': chartId,
            'kline_data_count': klineData.length,
            'time_frame': timeFrame,
          },
        );

        // 🔑 直接调用TradingChartWidget的setData方法，传递timeFrame参数
        tradingChartKey!.currentState!.setData(klineData, timeFrame: timeFrame);

        AppLogger.logModule('ChartAPI', LogLevel.info, '✅ WebView图表更新完成');
      } else {
        AppLogger.logModule(
          'ChartAPI',
          LogLevel.warning,
          '⚠️ TradingChartWidget未准备好，无法直接更新',
        );
      }
    } catch (e) {
      AppLogger.logModule(
        'ChartAPI',
        LogLevel.error,
        '❌ 直接更新WebView图表失败',
        error: e,
      );
    }
  }

  /// 获取TradingChartWidget的全局key
  GlobalKey<dynamic>? _getTradingChartKey() {
    // 这里需要导入并返回tradingChartKey
    // 由于模块依赖关系，我们需要通过回调或注册机制来获取
    return _tradingChartKey;
  }

  /// 全局TradingChartWidget key引用
  GlobalKey<dynamic>? _tradingChartKey;

  /// 注册TradingChartWidget的key
  void registerTradingChartKey(GlobalKey<dynamic> key) {
    _tradingChartKey = key;
    AppLogger.logModule(
      'ChartAPI',
      LogLevel.info,
      '📝 注册TradingChartWidget key',
    );
  }

  /// 🔑 处理图表可见范围变化 - 双接口模式专用
  void handleVisibleRangeChanged(Map<String, dynamic> data) async {
    try {
      AppLogger.logModule(
        'ChartAPI',
        LogLevel.info,
        '📊 ChartAPI处理可见范围变化: $data',
      );

      if (_globalDataProvider == null) {
        AppLogger.logModule(
          'ChartAPI',
          LogLevel.warning,
          '⚠️ 全局数据提供者未设置，无法处理范围变化',
        );
        return;
      }

      // 解析数据
      final from = data['from'] as int?;
      final to = data['to'] as int?;
      final currentDataCount = data['currentDataCount'] as int? ?? 0;

      if (from == null || to == null) {
        AppLogger.logModule(
          'ChartAPI',
          LogLevel.warning,
          '⚠️ 可见范围参数无效: from=$from, to=$to',
        );
        return;
      }

      // 获取当前活跃的图表控制器和chartId
      final activeEntry = _controllers.entries.isNotEmpty
          ? _controllers.entries.first
          : null;

      if (activeEntry == null) {
        AppLogger.logModule('ChartAPI', LogLevel.warning, '⚠️ 没有活跃的图表控制器');
        return;
      }

      final activeController = activeEntry.value;
      final chartId = activeEntry.key;

      // 🔧 修复VisibleRange构造函数 - 使用正确的参数名
      final range = VisibleRange(from: from, to: to);

      AppLogger.logModule(
        'ChartAPI',
        LogLevel.info,
        '📡 调用全局数据提供者获取历史数据',
        metadata: {
          'symbol': activeController.currentSymbol,
          'timeFrame': activeController.currentTimeFrame,
          'startTime': DateTime.fromMillisecondsSinceEpoch(
            from,
          ).toIso8601String(),
          'endTime': DateTime.fromMillisecondsSinceEpoch(to).toIso8601String(),
          'currentDataCount': currentDataCount,
        },
      );

      // 调用全局数据提供者获取数据
      final newData = await _globalDataProvider!(
        activeController.currentSymbol ?? '',
        activeController.currentTimeFrame ?? '15min',
        range,
      );

      AppLogger.logModule(
        'ChartAPI',
        LogLevel.info,
        '✅ 获取到历史数据，数量: ${newData.length}',
      );

      // 更新图表数据
      if (newData.isNotEmpty) {
        _updateWebViewChart(chartId, newData);
      }
    } catch (e, stackTrace) {
      AppLogger.logModule(
        'ChartAPI',
        LogLevel.error,
        '❌ 处理可见范围变化失败: $e',
        error: e,
        metadata: {'stackTrace': stackTrace.toString()},
      );
    }
  }

  /// 切换图表交易对
  void changeSymbol(String chartId, String symbol) {
    final controller = _controllers[chartId];
    if (controller == null) {
      AppLogger.logModule(
        'ChartAPI',
        LogLevel.warning,
        '⚠️ 图表控制器不存在',
        metadata: {'chart_id': chartId, 'symbol': symbol},
      );
      return;
    }

    AppLogger.logModule(
      'ChartAPI',
      LogLevel.info,
      '🔄 切换图表交易对',
      metadata: {
        'chart_id': chartId,
        'from': controller.currentSymbol,
        'to': symbol,
      },
    );

    controller.changeSymbol(symbol);
  }

  /// 切换图表时间框架
  void changeTimeFrame(String chartId, String timeFrame) {
    final controller = _controllers[chartId];
    if (controller == null) {
      AppLogger.logModule(
        'ChartAPI',
        LogLevel.warning,
        '⚠️ 图表控制器不存在',
        metadata: {'chart_id': chartId, 'time_frame': timeFrame},
      );
      return;
    }

    AppLogger.logModule(
      'ChartAPI',
      LogLevel.info,
      '⏰ 切换图表时间框架',
      metadata: {
        'chart_id': chartId,
        'from': controller.currentTimeFrame,
        'to': timeFrame,
      },
    );

    controller.changeTimeFrame(timeFrame);
  }

  /// 刷新图表数据
  void refreshChart(String chartId) {
    final controller = _controllers[chartId];
    if (controller == null) {
      AppLogger.logModule(
        'ChartAPI',
        LogLevel.warning,
        '⚠️ 图表控制器不存在',
        metadata: {'chart_id': chartId},
      );
      return;
    }

    AppLogger.logModule(
      'ChartAPI',
      LogLevel.info,
      '🔄 刷新图表数据',
      metadata: {
        'chart_id': chartId,
        'symbol': controller.currentSymbol,
        'time_frame': controller.currentTimeFrame,
      },
    );

    controller.refreshData();
  }

  /// 获取图表状态
  ChartStatus getChartStatus(String chartId) {
    final controller = _controllers[chartId];
    if (controller == null) {
      return ChartStatus(
        exists: false,
        isLoading: false,
        symbol: null,
        timeFrame: null,
        error: '图表不存在',
      );
    }

    return ChartStatus(
      exists: true,
      isLoading: controller.isLoading,
      symbol: controller.currentSymbol,
      timeFrame: controller.currentTimeFrame,
      error: null,
    );
  }

  /// 监听图表状态变化
  void addChartListener(String chartId, VoidCallback listener) {
    final controller = _controllers[chartId];
    if (controller == null) {
      AppLogger.logModule(
        'ChartAPI',
        LogLevel.warning,
        '⚠️ 无法添加监听器，图表控制器不存在',
        metadata: {'chart_id': chartId},
      );
      return;
    }

    controller.addListener(listener);

    AppLogger.logModule(
      'ChartAPI',
      LogLevel.debug,
      '👂 添加图表状态监听器',
      metadata: {'chart_id': chartId},
    );
  }

  /// 移除图表状态监听器
  void removeChartListener(String chartId, VoidCallback listener) {
    final controller = _controllers[chartId];
    if (controller == null) {
      return;
    }

    controller.removeListener(listener);

    AppLogger.logModule(
      'ChartAPI',
      LogLevel.debug,
      '🔇 移除图表状态监听器',
      metadata: {'chart_id': chartId},
    );
  }

  /// 销毁图表实例
  void destroyChart(String chartId) {
    final controller = _controllers.remove(chartId);
    if (controller == null) {
      AppLogger.logModule(
        'ChartAPI',
        LogLevel.warning,
        '⚠️ 图表控制器不存在',
        metadata: {'chart_id': chartId},
      );
      return;
    }

    controller.dispose();

    AppLogger.logModule(
      'ChartAPI',
      LogLevel.info,
      '🗑️ 销毁图表实例',
      metadata: {'chart_id': chartId, 'remaining_charts': _controllers.length},
    );
  }

  /// 清除所有缓存
  void clearAllCache() {
    ChartDataManager.instance.clearCache();

    AppLogger.logModule(
      'ChartAPI',
      LogLevel.info,
      '🧹 清除所有图表缓存',
      metadata: {'active_charts': _controllers.length},
    );
  }

  /// 清除指定交易对的缓存
  void clearSymbolCache(String symbol, [String? timeFrame]) {
    ChartDataManager.instance.clearCache(symbol, timeFrame);

    AppLogger.logModule(
      'ChartAPI',
      LogLevel.info,
      '🧹 清除交易对缓存',
      metadata: {'symbol': symbol, 'time_frame': timeFrame},
    );
  }

  /// 获取所有活跃的图表ID
  List<String> getActiveChartIds() {
    return _controllers.keys.toList();
  }

  /// 获取图表统计信息
  ChartStatistics getStatistics() {
    final totalCharts = _controllers.length;
    final loadingCharts = _controllers.values.where((c) => c.isLoading).length;
    final symbols = _controllers.values
        .map((c) => c.currentSymbol)
        .where((s) => s != null)
        .toSet()
        .length;

    return ChartStatistics(
      totalCharts: totalCharts,
      loadingCharts: loadingCharts,
      uniqueSymbols: symbols,
      hasGlobalProvider: _globalDataProvider != null,
    );
  }

  /// 销毁所有图表
  void dispose() {
    for (final controller in _controllers.values) {
      controller.dispose();
    }
    _controllers.clear();

    AppLogger.logModule('ChartAPI', LogLevel.info, '🔚 销毁所有图表实例');
  }
}

/// 图表状态
class ChartStatus {
  final bool exists;
  final bool isLoading;
  final String? symbol;
  final String? timeFrame;
  final String? error;

  const ChartStatus({
    required this.exists,
    required this.isLoading,
    required this.symbol,
    required this.timeFrame,
    required this.error,
  });

  @override
  String toString() {
    return 'ChartStatus(exists: $exists, isLoading: $isLoading, symbol: $symbol, timeFrame: $timeFrame, error: $error)';
  }
}

/// 图表统计信息
class ChartStatistics {
  final int totalCharts;
  final int loadingCharts;
  final int uniqueSymbols;
  final bool hasGlobalProvider;

  const ChartStatistics({
    required this.totalCharts,
    required this.loadingCharts,
    required this.uniqueSymbols,
    required this.hasGlobalProvider,
  });

  @override
  String toString() {
    return 'ChartStatistics(totalCharts: $totalCharts, loadingCharts: $loadingCharts, uniqueSymbols: $uniqueSymbols, hasGlobalProvider: $hasGlobalProvider)';
  }
}
