import 'dart:async';
import 'dart:math' as math;
import 'dart:ui' as ui;
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../models/chart_data.dart';
import '../models/performance_data.dart'
    hide ChartDataVirtualizer, IncrementalUpdater, PerformanceMonitor;
import '../config/chart_config.dart';
import '../config/chart_theme.dart';
import '../rendering/high_performance_renderer.dart';
import '../interaction/high_performance_gesture.dart';
import '../performance/data_virtualizer.dart';
import '../performance/memory_manager.dart';
import '../performance/performance_monitor.dart';
import '../data/incremental_updater.dart';
import '../indicators/indicator_calculator.dart';
import '../utils/performance_utils.dart';

/// TradingView级别的高性能图表组件
///
/// 特性：
/// - 支持100万+数据点流畅渲染
/// - 60FPS交互体验
/// - 内存使用优化 (<150MB)
/// - 多种图表类型支持
/// - 丰富的技术指标
/// - 实时数据更新
/// - WebGL硬件加速 (Web平台)
class HighPerformanceChart extends StatefulWidget {
  /// 图表数据
  final List<CandleData> data;

  /// 技术指标列表
  final List<TechnicalIndicator> indicators;

  /// 图表配置
  final ChartConfig config;

  /// 图表主题
  final ChartTheme? theme;

  /// K线点击回调
  final Function(CandleData candle, int index)? onCandleTap;

  /// 十字线移动回调
  final Function(Offset position, CandleData? candle)? onCrosshairMove;

  /// 缩放变化回调
  final Function(double zoom, double startX, double endX)? onZoomChanged;

  /// 数据范围变化回调
  final Function(int startIndex, int endIndex)? onRangeChanged;

  /// 实时数据更新回调
  final Stream<CandleData>? realTimeDataStream;

  /// 图表控制器
  final HighPerformanceChartController? controller;

  /// 是否启用性能监控
  final bool enablePerformanceMonitor;

  /// 是否启用调试信息
  final bool enableDebugInfo;

  const HighPerformanceChart({
    Key? key,
    required this.data,
    this.indicators = const [],
    required this.config,
    this.theme,
    this.onCandleTap,
    this.onCrosshairMove,
    this.onZoomChanged,
    this.onRangeChanged,
    this.realTimeDataStream,
    this.controller,
    this.enablePerformanceMonitor = false,
    this.enableDebugInfo = false,
  }) : super(key: key);

  @override
  State<HighPerformanceChart> createState() => _HighPerformanceChartState();
}

class _HighPerformanceChartState extends State<HighPerformanceChart>
    with TickerProviderStateMixin {
  // 核心组件
  late HighPerformanceChartController _controller;
  late ChartDataVirtualizer _virtualizer;
  late IncrementalUpdater _updater;
  late PerformanceMonitor _performanceMonitor;
  late MemoryManager _memoryManager;
  late IndicatorCalculator _indicatorCalculator;

  // 实时数据订阅
  StreamSubscription<CandleData>? _realTimeSubscription;

  // 性能监控
  final Stopwatch _renderStopwatch = Stopwatch();
  int _frameCount = 0;

  @override
  void initState() {
    super.initState();
    _initializeComponents();
    _setupRealTimeData();
  }

  /// 初始化核心组件
  void _initializeComponents() {
    // 初始化性能监控
    _performanceMonitor = PerformanceMonitor();
    if (widget.enablePerformanceMonitor) {
      _performanceMonitor.startMonitoring();
    }

    // 初始化内存管理器
    _memoryManager = MemoryManager();

    // 初始化数据虚拟化器
    _virtualizer = ChartDataVirtualizer(
      maxVisiblePoints: widget.config.performanceConfig.samplingThreshold,
      bufferSize: 500,
    );

    // 初始化增量更新器
    _updater = IncrementalUpdater(_virtualizer);

    // 初始化指标计算器
    _indicatorCalculator = IndicatorCalculator();

    // 初始化控制器
    _controller = widget.controller ?? HighPerformanceChartController();
    _controller.initialize(
      virtualizer: _virtualizer,
      updater: _updater,
      performanceMonitor: _performanceMonitor,
      config: widget.config,
    );

    // 设置初始数据
    _virtualizer.setFullDataset(widget.data);

    // 计算技术指标
    _calculateIndicators();

    // 监听控制器变化
    _controller.addListener(_onControllerChanged);
  }

  /// 设置实时数据流
  void _setupRealTimeData() {
    if (widget.realTimeDataStream != null) {
      _realTimeSubscription = widget.realTimeDataStream!.listen(
        (newCandle) {
          _updater.addCandle(newCandle);
          _calculateIndicators();
        },
        onError: (error) {
          debugPrint('Real-time data error: $error');
        },
      );
    }
  }

  /// 计算技术指标
  void _calculateIndicators() {
    if (widget.indicators.isEmpty) return;

    final data = _virtualizer.getVirtualizedData();
    for (final indicator in widget.indicators) {
      final calculatedData = _indicatorCalculator.calculate(indicator, data);
      _virtualizer.setIndicatorData(indicator.key, calculatedData);
    }
  }

  /// 控制器变化回调
  void _onControllerChanged() {
    if (mounted) {
      setState(() {});

      // 触发回调
      final viewport = _controller.viewport;
      widget.onZoomChanged?.call(viewport.zoom, viewport.startX, viewport.endX);

      widget.onRangeChanged?.call(viewport.startIndex, viewport.endIndex);
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = widget.theme ?? ChartTheme.fromContext(context);

    return LayoutBuilder(
      builder: (context, constraints) {
        return Container(
          width: constraints.maxWidth,
          height: constraints.maxHeight,
          child: Stack(
            children: [
              // 主图表区域
              Positioned.fill(
                child: HighPerformanceGestureHandler(
                  controller: _controller,
                  config: _convertGestureConfig(widget.config.gestureConfig),
                  onTap: _onChartTap,
                  onLongPress: _onChartLongPress,
                  child: RepaintBoundary(
                    child: CustomPaint(
                      painter: HighPerformanceRenderer(
                        data: _virtualizer.getVirtualizedData(),
                        indicators: _getIndicatorData(),
                        viewport: _controller.viewport,
                        config: widget.config,
                        theme: theme,
                        memoryManager: _memoryManager,
                        onRenderComplete: _onRenderComplete,
                      ),
                      size: Size(constraints.maxWidth, constraints.maxHeight),
                    ),
                  ),
                ),
              ),

              // 价格标尺
              if (widget.config.showPriceScale)
                Positioned(
                  right: 0,
                  top: 0,
                  bottom: widget.config.showTimeScale ? 30 : 0,
                  width: 60,
                  child: _buildPriceScale(theme),
                ),

              // 时间标尺
              if (widget.config.showTimeScale)
                Positioned(
                  left: 0,
                  right: widget.config.showPriceScale ? 60 : 0,
                  bottom: 0,
                  height: 30,
                  child: _buildTimeScale(theme),
                ),

              // 工具栏
              if (widget.config.showToolbar)
                Positioned(top: 10, left: 10, child: _buildToolbar(theme)),

              // 性能监控面板
              if (widget.enablePerformanceMonitor && kDebugMode)
                Positioned(top: 10, right: 10, child: _buildPerformancePanel()),

              // 调试信息面板
              if (widget.enableDebugInfo && kDebugMode)
                Positioned(bottom: 40, left: 10, child: _buildDebugPanel()),
            ],
          ),
        );
      },
    );
  }

  /// 构建价格标尺
  Widget _buildPriceScale(ChartTheme theme) {
    return RepaintBoundary(
      child: CustomPaint(
        painter: PriceScalePainter(
          viewport: _controller.viewport,
          theme: theme,
          config: widget.config,
        ),
      ),
    );
  }

  /// 构建时间标尺
  Widget _buildTimeScale(ChartTheme theme) {
    return RepaintBoundary(
      child: CustomPaint(
        painter: TimeScalePainter(
          data: _virtualizer.getVirtualizedData(),
          viewport: _controller.viewport,
          theme: theme,
          config: widget.config,
        ),
      ),
    );
  }

  /// 构建工具栏
  Widget _buildToolbar(ChartTheme theme) {
    return Container(
      padding: const EdgeInsets.all(4),
      decoration: BoxDecoration(
        color: theme.backgroundColor.withOpacity(0.9),
        borderRadius: BorderRadius.circular(4),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildToolbarButton(
            Icons.zoom_in,
            () => _controller.zoom(1.2, Offset.zero),
            theme,
          ),
          _buildToolbarButton(
            Icons.zoom_out,
            () => _controller.zoom(0.8, Offset.zero),
            theme,
          ),
          _buildToolbarButton(
            Icons.refresh,
            () => _controller.resetViewport(),
            theme,
          ),
          _buildToolbarButton(Icons.settings, () => _showSettings(), theme),
        ],
      ),
    );
  }

  Widget _buildToolbarButton(
    IconData icon,
    VoidCallback onPressed,
    ChartTheme theme,
  ) {
    return InkWell(
      onTap: onPressed,
      borderRadius: BorderRadius.circular(4),
      child: Container(
        padding: const EdgeInsets.all(8),
        child: Icon(icon, size: 20, color: theme.textColor),
      ),
    );
  }

  /// 构建性能监控面板
  Widget _buildPerformancePanel() {
    return StreamBuilder<PerformanceStats>(
      stream: _performanceMonitor.statsStream,
      builder: (context, snapshot) {
        if (!snapshot.hasData) return const SizedBox.shrink();

        final stats = snapshot.data!;
        return Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.black.withOpacity(0.8),
            borderRadius: BorderRadius.circular(4),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'FPS: ${stats.fps.toStringAsFixed(1)}',
                style: TextStyle(
                  color: stats.fps >= 55 ? Colors.green : Colors.red,
                  fontSize: 12,
                  fontFamily: 'monospace',
                ),
              ),
              Text(
                'Render: ${stats.avgRenderTime.toStringAsFixed(1)}ms',
                style: TextStyle(
                  color: stats.avgRenderTime <= 16 ? Colors.green : Colors.red,
                  fontSize: 12,
                  fontFamily: 'monospace',
                ),
              ),
              Text(
                'Memory: ${stats.memoryUsage ~/ 1024}KB',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontFamily: 'monospace',
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  /// 构建调试信息面板
  Widget _buildDebugPanel() {
    final viewport = _controller.viewport;
    final dataCount = _virtualizer.getVirtualizedData().length;

    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.blue.withOpacity(0.8),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            'Data: $dataCount points',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 12,
              fontFamily: 'monospace',
            ),
          ),
          Text(
            'Zoom: ${viewport.zoom.toStringAsFixed(2)}x',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 12,
              fontFamily: 'monospace',
            ),
          ),
          Text(
            'Range: ${viewport.startIndex}-${viewport.endIndex}',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 12,
              fontFamily: 'monospace',
            ),
          ),
        ],
      ),
    );
  }

  /// 获取指标数据
  Map<String, List<double>> _getIndicatorData() {
    final indicatorData = <String, List<double>>{};
    for (final indicator in widget.indicators) {
      final data = _virtualizer.getIndicatorData(indicator.key);
      if (data != null) {
        indicatorData[indicator.key] = data;
      }
    }
    return indicatorData;
  }

  /// 转换手势配置
  HighPerformanceGestureConfig _convertGestureConfig(GestureConfig config) {
    return HighPerformanceGestureConfig(
      debounceDelay: const Duration(milliseconds: 1),
      inertiaThreshold: 50.0,
      inertiaFactor: 0.95,
      inertiaCurve: Curves.decelerate,
      performanceThreshold: 16,
      scaleSensitivity: 1.0,
      panSensitivity: 1.0,
      enableHapticFeedback: true,
    );
  }

  /// 图表点击事件
  void _onChartTap(Offset position) {
    final candle = _controller.getCandleAtPosition(position);
    if (candle != null) {
      final index = _controller.getIndexAtPosition(position);
      widget.onCandleTap?.call(candle, index);
    }
  }

  /// 图表长按事件
  void _onChartLongPress(Offset position) {
    final candle = _controller.getCandleAtPosition(position);
    widget.onCrosshairMove?.call(position, candle);
  }

  /// 渲染完成回调
  void _onRenderComplete(int renderTimeMs) {
    _frameCount++;
    _performanceMonitor.recordRenderTime(renderTimeMs);
    _performanceMonitor.recordFrame();
  }

  /// 显示设置对话框
  void _showSettings() {
    // TODO: 实现设置对话框
  }

  @override
  void dispose() {
    _realTimeSubscription?.cancel();
    _controller.removeListener(_onControllerChanged);
    _controller.dispose();
    _performanceMonitor.stopMonitoring();
    _memoryManager.cleanup();
    super.dispose();
  }
}
