import 'package:flutter/material.dart';
import 'chart_theme.dart';

/// 图表样式配置
/// 
/// 提供更细粒度的样式控制，包括各种图表元素的详细样式设置
class ChartStyle {
  /// 蜡烛图样式
  final CandlestickStyle candlestickStyle;

  /// 折线图样式
  final LineStyle lineStyle;

  /// 成交量样式
  final VolumeStyle volumeStyle;

  /// 网格样式
  final GridStyle gridStyle;

  /// 坐标轴样式
  final AxisStyle axisStyle;

  /// 十字线样式
  final CrosshairStyle crosshairStyle;

  /// 图例样式
  final LegendStyle legendStyle;

  /// 工具提示样式
  final TooltipStyle tooltipStyle;

  /// 技术指标样式
  final IndicatorStyle indicatorStyle;

  /// 深度图样式
  final DepthStyle depthStyle;

  const ChartStyle({
    this.candlestickStyle = const CandlestickStyle(),
    this.lineStyle = const LineStyle(),
    this.volumeStyle = const VolumeStyle(),
    this.gridStyle = const GridStyle(),
    this.axisStyle = const AxisStyle(),
    this.crosshairStyle = const CrosshairStyle(),
    this.legendStyle = const LegendStyle(),
    this.tooltipStyle = const TooltipStyle(),
    this.indicatorStyle = const IndicatorStyle(),
    this.depthStyle = const DepthStyle(),
  });

  /// 从主题创建样式
  factory ChartStyle.fromTheme(ChartTheme theme) {
    return ChartStyle(
      candlestickStyle: CandlestickStyle.fromTheme(theme),
      lineStyle: LineStyle.fromTheme(theme),
      volumeStyle: VolumeStyle.fromTheme(theme),
      gridStyle: GridStyle.fromTheme(theme),
      axisStyle: AxisStyle.fromTheme(theme),
      crosshairStyle: CrosshairStyle.fromTheme(theme),
      legendStyle: LegendStyle.fromTheme(theme),
      tooltipStyle: TooltipStyle.fromTheme(theme),
      indicatorStyle: IndicatorStyle.fromTheme(theme),
      depthStyle: DepthStyle.fromTheme(theme),
    );
  }

  /// 创建副本
  ChartStyle copyWith({
    CandlestickStyle? candlestickStyle,
    LineStyle? lineStyle,
    VolumeStyle? volumeStyle,
    GridStyle? gridStyle,
    AxisStyle? axisStyle,
    CrosshairStyle? crosshairStyle,
    LegendStyle? legendStyle,
    TooltipStyle? tooltipStyle,
    IndicatorStyle? indicatorStyle,
    DepthStyle? depthStyle,
  }) {
    return ChartStyle(
      candlestickStyle: candlestickStyle ?? this.candlestickStyle,
      lineStyle: lineStyle ?? this.lineStyle,
      volumeStyle: volumeStyle ?? this.volumeStyle,
      gridStyle: gridStyle ?? this.gridStyle,
      axisStyle: axisStyle ?? this.axisStyle,
      crosshairStyle: crosshairStyle ?? this.crosshairStyle,
      legendStyle: legendStyle ?? this.legendStyle,
      tooltipStyle: tooltipStyle ?? this.tooltipStyle,
      indicatorStyle: indicatorStyle ?? this.indicatorStyle,
      depthStyle: depthStyle ?? this.depthStyle,
    );
  }
}

/// 蜡烛图样式
class CandlestickStyle {
  /// 蜡烛体宽度比例 (0.0 - 1.0)
  final double bodyWidthRatio;

  /// 影线宽度
  final double wickWidth;

  /// 是否显示影线
  final bool showWicks;

  /// 是否显示边框
  final bool showBorder;

  /// 边框宽度
  final double borderWidth;

  /// 上涨蜡烛颜色
  final Color upColor;

  /// 下跌蜡烛颜色
  final Color downColor;

  /// 十字星颜色
  final Color dojiColor;

  /// 边框颜色
  final Color? borderColor;

  /// 是否使用渐变填充
  final bool useGradient;

  /// 上涨渐变
  final Gradient? upGradient;

  /// 下跌渐变
  final Gradient? downGradient;

  const CandlestickStyle({
    this.bodyWidthRatio = 0.8,
    this.wickWidth = 1.0,
    this.showWicks = true,
    this.showBorder = false,
    this.borderWidth = 1.0,
    this.upColor = const Color(0xFF4CAF50),
    this.downColor = const Color(0xFFF44336),
    this.dojiColor = const Color(0xFF9E9E9E),
    this.borderColor,
    this.useGradient = false,
    this.upGradient,
    this.downGradient,
  });

  factory CandlestickStyle.fromTheme(ChartTheme theme) {
    return CandlestickStyle(
      upColor: theme.upColor,
      downColor: theme.downColor,
      borderColor: theme.borderColor,
      borderWidth: theme.borderWidth,
    );
  }
}

/// 折线图样式
class LineStyle {
  /// 线条宽度
  final double width;

  /// 线条颜色
  final Color color;

  /// 线条样式
  final StrokeStyle strokeStyle;

  /// 是否显示数据点
  final bool showPoints;

  /// 数据点大小
  final double pointSize;

  /// 数据点颜色
  final Color? pointColor;

  /// 是否填充区域
  final bool fillArea;

  /// 填充颜色
  final Color? fillColor;

  /// 填充渐变
  final Gradient? fillGradient;

  const LineStyle({
    this.width = 2.0,
    this.color = const Color(0xFF2196F3),
    this.strokeStyle = StrokeStyle.solid,
    this.showPoints = false,
    this.pointSize = 4.0,
    this.pointColor,
    this.fillArea = false,
    this.fillColor,
    this.fillGradient,
  });

  factory LineStyle.fromTheme(ChartTheme theme) {
    return LineStyle(
      color: theme.primaryColor,
      width: theme.lineWidth,
    );
  }
}

/// 成交量样式
class VolumeStyle {
  /// 柱状图宽度比例
  final double barWidthRatio;

  /// 上涨成交量颜色
  final Color upColor;

  /// 下跌成交量颜色
  final Color downColor;

  /// 透明度
  final double opacity;

  /// 是否显示边框
  final bool showBorder;

  /// 边框颜色
  final Color? borderColor;

  const VolumeStyle({
    this.barWidthRatio = 0.8,
    this.upColor = const Color(0xFF4CAF50),
    this.downColor = const Color(0xFFF44336),
    this.opacity = 0.6,
    this.showBorder = false,
    this.borderColor,
  });

  factory VolumeStyle.fromTheme(ChartTheme theme) {
    return VolumeStyle(
      upColor: theme.upColor,
      downColor: theme.downColor,
      borderColor: theme.borderColor,
    );
  }
}

/// 网格样式
class GridStyle {
  /// 是否显示水平网格线
  final bool showHorizontalLines;

  /// 是否显示垂直网格线
  final bool showVerticalLines;

  /// 网格线颜色
  final Color color;

  /// 网格线宽度
  final double width;

  /// 网格线样式
  final StrokeStyle strokeStyle;

  /// 网格线透明度
  final double opacity;

  const GridStyle({
    this.showHorizontalLines = true,
    this.showVerticalLines = true,
    this.color = const Color(0xFFE0E0E0),
    this.width = 0.5,
    this.strokeStyle = StrokeStyle.solid,
    this.opacity = 1.0,
  });

  factory GridStyle.fromTheme(ChartTheme theme) {
    return GridStyle(
      color: theme.gridColor,
      width: theme.gridLineWidth,
    );
  }
}

/// 坐标轴样式
class AxisStyle {
  /// 轴线颜色
  final Color axisColor;

  /// 轴线宽度
  final double axisWidth;

  /// 刻度线颜色
  final Color tickColor;

  /// 刻度线宽度
  final double tickWidth;

  /// 刻度线长度
  final double tickLength;

  /// 标签文字样式
  final TextStyle labelStyle;

  /// 标题文字样式
  final TextStyle titleStyle;

  /// 是否显示轴线
  final bool showAxisLine;

  /// 是否显示刻度线
  final bool showTicks;

  /// 是否显示标签
  final bool showLabels;

  const AxisStyle({
    this.axisColor = const Color(0xFF9E9E9E),
    this.axisWidth = 1.0,
    this.tickColor = const Color(0xFF9E9E9E),
    this.tickWidth = 1.0,
    this.tickLength = 4.0,
    this.labelStyle = const TextStyle(
      color: Color(0xFF757575),
      fontSize: 10.0,
    ),
    this.titleStyle = const TextStyle(
      color: Color(0xFF424242),
      fontSize: 12.0,
      fontWeight: FontWeight.bold,
    ),
    this.showAxisLine = true,
    this.showTicks = true,
    this.showLabels = true,
  });

  factory AxisStyle.fromTheme(ChartTheme theme) {
    return AxisStyle(
      axisColor: theme.borderColor,
      tickColor: theme.borderColor,
      labelStyle: theme.smallTextStyle,
      titleStyle: theme.textStyle,
    );
  }
}

/// 十字线样式
class CrosshairStyle {
  /// 线条颜色
  final Color color;

  /// 线条宽度
  final double width;

  /// 线条样式
  final StrokeStyle strokeStyle;

  /// 透明度
  final double opacity;

  /// 是否显示水平线
  final bool showHorizontalLine;

  /// 是否显示垂直线
  final bool showVerticalLine;

  const CrosshairStyle({
    this.color = const Color(0xFF666666),
    this.width = 1.0,
    this.strokeStyle = StrokeStyle.dashed,
    this.opacity = 0.8,
    this.showHorizontalLine = true,
    this.showVerticalLine = true,
  });

  factory CrosshairStyle.fromTheme(ChartTheme theme) {
    return CrosshairStyle(
      color: theme.crosshairColor,
      width: theme.lineWidth,
    );
  }
}

/// 图例样式
class LegendStyle {
  /// 背景颜色
  final Color backgroundColor;

  /// 文字样式
  final TextStyle textStyle;

  /// 边框颜色
  final Color? borderColor;

  /// 边框宽度
  final double borderWidth;

  /// 圆角半径
  final double borderRadius;

  /// 内边距
  final EdgeInsets padding;

  /// 图例项间距
  final double itemSpacing;

  const LegendStyle({
    this.backgroundColor = const Color(0xFFFAFAFA),
    this.textStyle = const TextStyle(
      color: Color(0xFF424242),
      fontSize: 11.0,
    ),
    this.borderColor,
    this.borderWidth = 1.0,
    this.borderRadius = 4.0,
    this.padding = const EdgeInsets.all(8.0),
    this.itemSpacing = 8.0,
  });

  factory LegendStyle.fromTheme(ChartTheme theme) {
    return LegendStyle(
      backgroundColor: theme.backgroundColor,
      textStyle: theme.smallTextStyle,
      borderColor: theme.borderColor,
    );
  }
}

/// 工具提示样式
class TooltipStyle {
  /// 背景颜色
  final Color backgroundColor;

  /// 文字样式
  final TextStyle textStyle;

  /// 边框颜色
  final Color? borderColor;

  /// 边框宽度
  final double borderWidth;

  /// 圆角半径
  final double borderRadius;

  /// 内边距
  final EdgeInsets padding;

  /// 阴影
  final List<BoxShadow>? shadows;

  const TooltipStyle({
    this.backgroundColor = const Color(0xFF424242),
    this.textStyle = const TextStyle(
      color: Color(0xFFFFFFFF),
      fontSize: 11.0,
    ),
    this.borderColor,
    this.borderWidth = 0.0,
    this.borderRadius = 4.0,
    this.padding = const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
    this.shadows,
  });

  factory TooltipStyle.fromTheme(ChartTheme theme) {
    return TooltipStyle(
      textStyle: theme.smallTextStyle.copyWith(color: Colors.white),
      shadows: theme.shadows,
    );
  }
}

/// 技术指标样式
class IndicatorStyle {
  /// 指标线条宽度
  final double lineWidth;

  /// 指标颜色列表
  final List<Color> colors;

  /// 线条样式
  final StrokeStyle strokeStyle;

  /// 透明度
  final double opacity;

  const IndicatorStyle({
    this.lineWidth = 1.5,
    this.colors = const [
      Color(0xFF2196F3),
      Color(0xFFFF9800),
      Color(0xFF9C27B0),
      Color(0xFF00BCD4),
      Color(0xFF4CAF50),
    ],
    this.strokeStyle = StrokeStyle.solid,
    this.opacity = 1.0,
  });

  factory IndicatorStyle.fromTheme(ChartTheme theme) {
    return IndicatorStyle(
      colors: theme.indicatorColors,
      lineWidth: theme.lineWidth,
    );
  }
}

/// 深度图样式
class DepthStyle {
  /// 买盘颜色
  final Color bidColor;

  /// 卖盘颜色
  final Color askColor;

  /// 填充透明度
  final double fillOpacity;

  /// 线条宽度
  final double lineWidth;

  /// 是否填充区域
  final bool fillArea;

  const DepthStyle({
    this.bidColor = const Color(0xFF4CAF50),
    this.askColor = const Color(0xFFF44336),
    this.fillOpacity = 0.3,
    this.lineWidth = 2.0,
    this.fillArea = true,
  });

  factory DepthStyle.fromTheme(ChartTheme theme) {
    return DepthStyle(
      bidColor: theme.upColor,
      askColor: theme.downColor,
      lineWidth: theme.lineWidth,
    );
  }
}

/// 线条样式枚举
enum StrokeStyle {
  /// 实线
  solid,
  /// 虚线
  dashed,
  /// 点线
  dotted,
  /// 点划线
  dashDot,
}
