import 'package:flutter/foundation.dart';

/// 性能配置
///
/// 定义图表的性能相关配置选项
@immutable
class PerformanceConfig {
  /// 数据采样阈值
  /// 当数据点超过此数量时启用采样
  final int samplingThreshold;

  /// 是否启用数据虚拟化
  final bool enableDataVirtualization;

  /// 是否启用内存优化
  final bool enableMemoryOptimization;

  /// 是否启用渲染缓存
  final bool enableRenderCaching;

  /// 是否启用批量渲染
  final bool enableBatchRendering;

  /// 最大渲染时间 (毫秒)
  final int maxRenderTime;

  /// 最大缓存大小 (字节)
  final int maxCacheSize;

  /// 对象池大小
  final int objectPoolSize;

  /// 是否启用WebGL加速 (Web平台)
  final bool enableWebGLAcceleration;

  /// 是否启用多线程计算
  final bool enableMultiThreading;

  /// 帧率目标
  final int targetFPS;

  /// 内存使用限制 (MB)
  final int memoryLimit;

  /// 是否启用性能监控
  final bool enablePerformanceMonitoring;

  /// 是否启用调试模式
  final bool enableDebugMode;

  const PerformanceConfig({
    this.samplingThreshold = 2000,
    this.enableDataVirtualization = true,
    this.enableMemoryOptimization = true,
    this.enableRenderCaching = true,
    this.enableBatchRendering = true,
    this.maxRenderTime = 16,
    this.maxCacheSize = 50 * 1024 * 1024, // 50MB
    this.objectPoolSize = 1000,
    this.enableWebGLAcceleration = true,
    this.enableMultiThreading = false,
    this.targetFPS = 60,
    this.memoryLimit = 150, // 150MB
    this.enablePerformanceMonitoring = false,
    this.enableDebugMode = false,
  });

  /// 默认配置
  factory PerformanceConfig.defaultConfig() {
    return const PerformanceConfig();
  }

  /// 高性能配置
  factory PerformanceConfig.highPerformance() {
    return const PerformanceConfig(
      samplingThreshold: 1000,
      enableDataVirtualization: true,
      enableMemoryOptimization: true,
      enableRenderCaching: true,
      enableBatchRendering: true,
      maxRenderTime: 8,
      maxCacheSize: 100 * 1024 * 1024, // 100MB
      objectPoolSize: 2000,
      enableWebGLAcceleration: true,
      enableMultiThreading: true,
      targetFPS: 60,
      memoryLimit: 200, // 200MB
    );
  }

  /// 低内存配置
  factory PerformanceConfig.lowMemory() {
    return const PerformanceConfig(
      samplingThreshold: 500,
      enableDataVirtualization: true,
      enableMemoryOptimization: true,
      enableRenderCaching: false,
      enableBatchRendering: true,
      maxRenderTime: 33,
      maxCacheSize: 20 * 1024 * 1024, // 20MB
      objectPoolSize: 500,
      enableWebGLAcceleration: false,
      enableMultiThreading: false,
      targetFPS: 30,
      memoryLimit: 80, // 80MB
    );
  }

  /// 平衡配置
  factory PerformanceConfig.balanced() {
    return const PerformanceConfig(
      samplingThreshold: 1500,
      enableDataVirtualization: true,
      enableMemoryOptimization: true,
      enableRenderCaching: true,
      enableBatchRendering: true,
      maxRenderTime: 16,
      maxCacheSize: 30 * 1024 * 1024, // 30MB
      objectPoolSize: 800,
      enableWebGLAcceleration: true,
      enableMultiThreading: false,
      targetFPS: 55,
      memoryLimit: 120, // 120MB
    );
  }

  /// Web优化配置
  factory PerformanceConfig.webOptimized() {
    return const PerformanceConfig(
      samplingThreshold: 1200,
      enableDataVirtualization: true,
      enableMemoryOptimization: true,
      enableRenderCaching: true,
      enableBatchRendering: true,
      maxRenderTime: 16,
      maxCacheSize: 40 * 1024 * 1024, // 40MB
      objectPoolSize: 1200,
      enableWebGLAcceleration: true,
      enableMultiThreading: true,
      targetFPS: 60,
      memoryLimit: 100, // 100MB
    );
  }

  /// 移动端优化配置
  factory PerformanceConfig.mobileOptimized() {
    return const PerformanceConfig(
      samplingThreshold: 1000,
      enableDataVirtualization: true,
      enableMemoryOptimization: true,
      enableRenderCaching: true,
      enableBatchRendering: true,
      maxRenderTime: 16,
      maxCacheSize: 25 * 1024 * 1024, // 25MB
      objectPoolSize: 600,
      enableWebGLAcceleration: false,
      enableMultiThreading: false,
      targetFPS: 55,
      memoryLimit: 100, // 100MB
    );
  }

  /// 复制并修改配置
  PerformanceConfig copyWith({
    int? samplingThreshold,
    bool? enableDataVirtualization,
    bool? enableMemoryOptimization,
    bool? enableRenderCaching,
    bool? enableBatchRendering,
    int? maxRenderTime,
    int? maxCacheSize,
    int? objectPoolSize,
    bool? enableWebGLAcceleration,
    bool? enableMultiThreading,
    int? targetFPS,
    int? memoryLimit,
    bool? enablePerformanceMonitoring,
    bool? enableDebugMode,
  }) {
    return PerformanceConfig(
      samplingThreshold: samplingThreshold ?? this.samplingThreshold,
      enableDataVirtualization:
          enableDataVirtualization ?? this.enableDataVirtualization,
      enableMemoryOptimization:
          enableMemoryOptimization ?? this.enableMemoryOptimization,
      enableRenderCaching: enableRenderCaching ?? this.enableRenderCaching,
      enableBatchRendering: enableBatchRendering ?? this.enableBatchRendering,
      maxRenderTime: maxRenderTime ?? this.maxRenderTime,
      maxCacheSize: maxCacheSize ?? this.maxCacheSize,
      objectPoolSize: objectPoolSize ?? this.objectPoolSize,
      enableWebGLAcceleration:
          enableWebGLAcceleration ?? this.enableWebGLAcceleration,
      enableMultiThreading: enableMultiThreading ?? this.enableMultiThreading,
      targetFPS: targetFPS ?? this.targetFPS,
      memoryLimit: memoryLimit ?? this.memoryLimit,
      enablePerformanceMonitoring:
          enablePerformanceMonitoring ?? this.enablePerformanceMonitoring,
      enableDebugMode: enableDebugMode ?? this.enableDebugMode,
    );
  }

  /// 性能等级评估
  PerformanceLevel get performanceLevel {
    int score = 0;

    // 采样阈值评分
    if (samplingThreshold <= 1000)
      score += 20;
    else if (samplingThreshold <= 1500)
      score += 15;
    else if (samplingThreshold <= 2000)
      score += 10;
    else
      score += 5;

    // 优化选项评分
    if (enableDataVirtualization) score += 15;
    if (enableMemoryOptimization) score += 15;
    if (enableRenderCaching) score += 10;
    if (enableBatchRendering) score += 10;
    if (enableWebGLAcceleration) score += 10;

    // 性能目标评分
    if (targetFPS >= 60)
      score += 10;
    else if (targetFPS >= 55)
      score += 8;
    else if (targetFPS >= 45)
      score += 5;

    // 内存限制评分
    if (memoryLimit <= 100)
      score += 10;
    else if (memoryLimit <= 150)
      score += 8;
    else if (memoryLimit <= 200)
      score += 5;

    if (score >= 85) return PerformanceLevel.excellent;
    if (score >= 70) return PerformanceLevel.good;
    if (score >= 55) return PerformanceLevel.fair;
    return PerformanceLevel.poor;
  }

  /// 获取优化建议
  List<String> getOptimizationSuggestions() {
    final suggestions = <String>[];

    if (samplingThreshold > 2000) {
      suggestions.add('降低采样阈值以提升性能');
    }

    if (!enableDataVirtualization) {
      suggestions.add('启用数据虚拟化以处理大数据集');
    }

    if (!enableMemoryOptimization) {
      suggestions.add('启用内存优化以减少内存使用');
    }

    if (!enableRenderCaching) {
      suggestions.add('启用渲染缓存以提升渲染性能');
    }

    if (!enableBatchRendering) {
      suggestions.add('启用批量渲染以减少绘制调用');
    }

    if (maxRenderTime > 16) {
      suggestions.add('降低最大渲染时间以保证60FPS');
    }

    if (targetFPS < 55) {
      suggestions.add('提高目标帧率以获得更流畅的体验');
    }

    if (memoryLimit > 150) {
      suggestions.add('降低内存限制以适应更多设备');
    }

    if (kIsWeb && !enableWebGLAcceleration) {
      suggestions.add('在Web平台启用WebGL加速');
    }

    return suggestions;
  }

  /// 验证配置有效性
  bool isValid() {
    return samplingThreshold > 0 &&
        maxRenderTime > 0 &&
        maxCacheSize > 0 &&
        objectPoolSize > 0 &&
        targetFPS > 0 &&
        memoryLimit > 0;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PerformanceConfig &&
        other.samplingThreshold == samplingThreshold &&
        other.enableDataVirtualization == enableDataVirtualization &&
        other.enableMemoryOptimization == enableMemoryOptimization &&
        other.enableRenderCaching == enableRenderCaching &&
        other.enableBatchRendering == enableBatchRendering &&
        other.maxRenderTime == maxRenderTime &&
        other.maxCacheSize == maxCacheSize &&
        other.objectPoolSize == objectPoolSize &&
        other.enableWebGLAcceleration == enableWebGLAcceleration &&
        other.enableMultiThreading == enableMultiThreading &&
        other.targetFPS == targetFPS &&
        other.memoryLimit == memoryLimit &&
        other.enablePerformanceMonitoring == enablePerformanceMonitoring &&
        other.enableDebugMode == enableDebugMode;
  }

  @override
  int get hashCode {
    return Object.hash(
      samplingThreshold,
      enableDataVirtualization,
      enableMemoryOptimization,
      enableRenderCaching,
      enableBatchRendering,
      maxRenderTime,
      maxCacheSize,
      objectPoolSize,
      enableWebGLAcceleration,
      enableMultiThreading,
      targetFPS,
      memoryLimit,
      enablePerformanceMonitoring,
      enableDebugMode,
    );
  }

  @override
  String toString() {
    return 'PerformanceConfig('
        'samplingThreshold: $samplingThreshold, '
        'dataVirtualization: $enableDataVirtualization, '
        'memoryOptimization: $enableMemoryOptimization, '
        'renderCaching: $enableRenderCaching, '
        'batchRendering: $enableBatchRendering, '
        'maxRenderTime: ${maxRenderTime}ms, '
        'targetFPS: $targetFPS, '
        'memoryLimit: ${memoryLimit}MB, '
        'level: $performanceLevel)';
  }
}

/// 性能等级
enum PerformanceLevel {
  /// 优秀
  excellent,

  /// 良好
  good,

  /// 一般
  fair,

  /// 较差
  poor,
}
