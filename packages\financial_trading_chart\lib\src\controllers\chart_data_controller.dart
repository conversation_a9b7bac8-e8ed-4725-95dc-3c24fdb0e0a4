import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:financial_app_core/financial_app_core.dart';
import '../data/chart_data_manager.dart';
import '../models/chart_data.dart';
import '../webview/chart_bridge.dart';

/// 图表数据控制器
///
/// 负责协调图表显示和数据管理，处理可见范围变化事件
class ChartDataController extends ChangeNotifier {
  /// 图表数据管理器
  final ChartDataManager _dataManager = ChartDataManager.instance;

  /// 图表桥接器
  ChartBridge? _chartBridge;

  /// 当前图表ID
  String? _chartId;

  /// 当前交易对
  String? _currentSymbol;

  /// 当前时间框架
  String? _currentTimeFrame;

  /// 数据流订阅
  StreamSubscription<ChartDataUpdateEvent>? _dataStreamSubscription;

  /// 是否正在加载数据
  bool _isLoading = false;

  /// 外部数据提供者
  Future<List<ChartData>> Function(
    String symbol,
    String timeFrame,
    VisibleRange range,
  )?
  dataProvider;

  /// 获取当前状态
  bool get isLoading => _isLoading;
  String? get currentSymbol => _currentSymbol;
  String? get currentTimeFrame => _currentTimeFrame;

  /// 初始化控制器
  void initialize({
    required String chartId,
    required String symbol,
    required String timeFrame,
    Future<List<ChartData>> Function(
      String symbol,
      String timeFrame,
      VisibleRange range,
    )?
    dataProvider,
  }) {
    _chartId = chartId;
    _currentSymbol = symbol;
    _currentTimeFrame = timeFrame;
    this.dataProvider = dataProvider;

    // 设置数据管理器的请求回调
    _dataManager.setDataRequestCallback(_handleDataRequest);

    // 订阅数据更新流
    _subscribeToDataUpdates();

    AppLogger.logModule(
      'ChartDataController',
      LogLevel.info,
      '🎯 图表数据控制器初始化',
      metadata: {
        'chart_id': chartId,
        'symbol': symbol,
        'time_frame': timeFrame,
        'has_data_provider': dataProvider != null,
      },
    );
  }

  /// 设置图表桥接器
  void setChartBridge(ChartBridge bridge) {
    _chartBridge = bridge;

    AppLogger.logModule(
      'ChartDataController',
      LogLevel.info,
      '🌉 设置图表桥接器',
      metadata: {'chart_id': _chartId},
    );
  }

  /// 处理来自WebView的消息
  void handleWebViewMessage(String message) {
    try {
      final data = jsonDecode(message) as Map<String, dynamic>;
      final type = data['type'] as String?;
      final payload = data['data'] as Map<String, dynamic>? ?? {};

      AppLogger.logModule(
        'ChartDataController',
        LogLevel.debug,
        '📨 收到WebView消息',
        metadata: {'type': type, 'payload_keys': payload.keys.toList()},
      );

      switch (type) {
        case 'visibleRangeChanged':
          _handleVisibleRangeChanged(payload);
          break;
        case 'chartReady':
          _handleChartReady(payload);
          break;
        default:
          AppLogger.logModule(
            'ChartDataController',
            LogLevel.warning,
            '⚠️ 未知消息类型',
            metadata: {'type': type},
          );
      }
    } catch (e) {
      AppLogger.logModule(
        'ChartDataController',
        LogLevel.error,
        '❌ 处理WebView消息失败',
        error: e,
        metadata: {'message': message},
      );
    }
  }

  /// 处理可见范围变化
  void _handleVisibleRangeChanged(Map<String, dynamic> payload) {
    if (_currentSymbol == null ||
        _currentTimeFrame == null ||
        _chartId == null) {
      AppLogger.logModule(
        'ChartDataController',
        LogLevel.warning,
        '⚠️ 图表未完全初始化，忽略可见范围变化',
        metadata: {
          'symbol': _currentSymbol,
          'time_frame': _currentTimeFrame,
          'chart_id': _chartId,
        },
      );
      return;
    }

    final from = payload['from'] as int?;
    final to = payload['to'] as int?;

    if (from == null || to == null) {
      AppLogger.logModule(
        'ChartDataController',
        LogLevel.warning,
        '⚠️ 可见范围参数无效',
        metadata: payload,
      );
      return;
    }

    final range = VisibleRange(from: from, to: to);

    // 通知数据管理器处理可见范围变化
    _dataManager.handleVisibleRangeChange(
      _chartId!,
      _currentSymbol!,
      _currentTimeFrame!,
      range,
    );
  }

  /// 处理图表就绪事件
  void _handleChartReady(Map<String, dynamic> payload) {
    AppLogger.logModule(
      'ChartDataController',
      LogLevel.info,
      '✅ 图表就绪',
      metadata: {'chart_id': _chartId, 'payload': payload},
    );

    // 图表就绪后，可以进行初始数据加载
    _loadInitialData();
  }

  /// 加载初始数据
  void _loadInitialData() {
    if (_currentSymbol == null || _currentTimeFrame == null) return;

    AppLogger.logModule(
      'ChartDataController',
      LogLevel.info,
      '📊 开始加载初始数据',
      metadata: {'symbol': _currentSymbol, 'time_frame': _currentTimeFrame},
    );

    // 检查缓存中是否有数据
    final cachedData = _dataManager.getCachedData(
      _currentSymbol!,
      _currentTimeFrame!,
    );

    if (cachedData.isNotEmpty) {
      AppLogger.logModule(
        'ChartDataController',
        LogLevel.info,
        '📦 使用缓存数据',
        metadata: {'cached_count': cachedData.length},
      );

      _updateChartData(cachedData);
    } else {
      // 请求初始数据
      final initialRange = VisibleRange(from: 0, to: 100); // 默认加载100个数据点
      _dataManager.handleVisibleRangeChange(
        _chartId!,
        _currentSymbol!,
        _currentTimeFrame!,
        initialRange,
      );
    }
  }

  /// 处理数据请求
  void _handleDataRequest(ChartDataRequest request) {
    if (dataProvider == null) {
      AppLogger.logModule(
        'ChartDataController',
        LogLevel.warning,
        '⚠️ 未设置数据提供者',
        metadata: {'request': request.toString()},
      );

      _dataManager.handleDataError(
        request.symbol,
        request.timeFrame,
        '数据提供者未设置',
      );
      return;
    }

    _setLoading(true);

    AppLogger.logModule(
      'ChartDataController',
      LogLevel.info,
      '🚀 开始处理数据请求',
      metadata: {
        'symbol': request.symbol,
        'time_frame': request.timeFrame,
        'range': {'from': request.range.from, 'to': request.range.to},
        'request_type': request.requestType.toString(),
      },
    );

    // 调用外部数据提供者
    dataProvider!(request.symbol, request.timeFrame, request.range)
        .then((data) {
          AppLogger.logModule(
            'ChartDataController',
            LogLevel.info,
            '✅ 数据请求成功',
            metadata: {
              'symbol': request.symbol,
              'time_frame': request.timeFrame,
              'data_count': data.length,
            },
          );

          _dataManager.handleDataResponse(
            request.symbol,
            request.timeFrame,
            data,
          );
          _setLoading(false);
        })
        .catchError((error) {
          AppLogger.logModule(
            'ChartDataController',
            LogLevel.error,
            '❌ 数据请求失败',
            error: error,
            metadata: {
              'symbol': request.symbol,
              'time_frame': request.timeFrame,
            },
          );

          _dataManager.handleDataError(
            request.symbol,
            request.timeFrame,
            error,
          );
          _setLoading(false);
        });
  }

  /// 订阅数据更新
  void _subscribeToDataUpdates() {
    if (_chartId == null) return;

    _dataStreamSubscription?.cancel();
    _dataStreamSubscription = _dataManager.getDataStream(_chartId!).listen((
      event,
    ) {
      AppLogger.logModule(
        'ChartDataController',
        LogLevel.info,
        '📈 收到数据更新事件',
        metadata: {
          'symbol': event.symbol,
          'time_frame': event.timeFrame,
          'data_count': event.data.length,
          'update_type': event.updateType.toString(),
        },
      );

      // 如果是当前图表的数据，则更新图表
      if (event.symbol == _currentSymbol &&
          event.timeFrame == _currentTimeFrame) {
        _updateChartData(event.data);
      }
    });
  }

  /// 更新图表数据
  void _updateChartData(List<ChartData> data) {
    if (_chartBridge == null) {
      AppLogger.logModule(
        'ChartDataController',
        LogLevel.warning,
        '⚠️ 图表桥接器未设置，无法更新图表',
      );
      return;
    }

    AppLogger.logModule(
      'ChartDataController',
      LogLevel.info,
      '📊 更新图表数据',
      metadata: {
        'data_count': data.length,
        'symbol': _currentSymbol,
        'time_frame': _currentTimeFrame,
      },
    );

    // 转换数据格式并发送到图表
    final chartData = data.map((item) {
      if (item is KLineData) {
        // K线数据
        return {
          'time': item.timestamp.toIso8601String(),
          'open': item.open,
          'high': item.high,
          'low': item.low,
          'close': item.close,
          'volume': item.volume ?? 0.0,
        };
      } else if (item is CandleData) {
        // 蜡烛图数据
        return {
          'time': item.timestamp.toIso8601String(),
          'open': item.open,
          'high': item.high,
          'low': item.low,
          'close': item.close,
          'volume': item.volume,
        };
      } else {
        // 简单数据，转换为线图格式
        return {'time': item.timestamp.toIso8601String(), 'value': item.value};
      }
    }).toList();

    final message = _chartBridge!.createDataUpdateMessage(chartData);

    // 这里需要通过WebView发送消息到JavaScript
    // 具体实现取决于WebView的集成方式
    _sendMessageToWebView(message);
  }

  /// 发送消息到WebView
  void _sendMessageToWebView(String message) {
    // 这个方法需要根据具体的WebView实现来完成
    // 例如使用webview_flutter的evaluateJavascript方法
    AppLogger.logModule(
      'ChartDataController',
      LogLevel.debug,
      '📤 发送消息到WebView',
      metadata: {'message_length': message.length},
    );
  }

  /// 设置加载状态
  void _setLoading(bool loading) {
    if (_isLoading != loading) {
      _isLoading = loading;
      notifyListeners();
    }
  }

  /// 切换交易对
  void changeSymbol(String symbol) {
    if (_currentSymbol == symbol) return;

    AppLogger.logModule(
      'ChartDataController',
      LogLevel.info,
      '🔄 切换交易对',
      metadata: {'from': _currentSymbol, 'to': symbol},
    );

    _currentSymbol = symbol;
    _loadInitialData();
    notifyListeners();
  }

  /// 切换时间框架
  void changeTimeFrame(String timeFrame) {
    if (_currentTimeFrame == timeFrame) return;

    AppLogger.logModule(
      'ChartDataController',
      LogLevel.info,
      '⏰ 切换时间框架',
      metadata: {'from': _currentTimeFrame, 'to': timeFrame},
    );

    _currentTimeFrame = timeFrame;
    _loadInitialData();
    notifyListeners();
  }

  /// 刷新数据
  void refreshData() {
    AppLogger.logModule(
      'ChartDataController',
      LogLevel.info,
      '🔄 刷新数据',
      metadata: {'symbol': _currentSymbol, 'time_frame': _currentTimeFrame},
    );

    // 清除缓存并重新加载
    if (_currentSymbol != null && _currentTimeFrame != null) {
      _dataManager.clearCache(_currentSymbol, _currentTimeFrame);
      _loadInitialData();
    }
  }

  @override
  void dispose() {
    _dataStreamSubscription?.cancel();
    super.dispose();
  }
}
