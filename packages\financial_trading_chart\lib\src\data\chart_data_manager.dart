import 'dart:async';
import 'dart:math' as math;
import 'package:flutter/foundation.dart';
import 'package:financial_app_core/financial_app_core.dart';
import '../models/chart_data.dart';

/// 图表数据管理器
///
/// 负责管理图表数据的加载、缓存和更新，防止重复请求，优化性能
class ChartDataManager extends ChangeNotifier {
  static ChartDataManager? _instance;
  static ChartDataManager get instance => _instance ??= ChartDataManager._();

  ChartDataManager._() : config = const ChartDataManagerConfig();

  /// 数据缓存 - 按symbol和timeFrame分组
  final Map<String, Map<String, List<ChartData>>> _dataCache = {};

  /// 正在进行的请求 - 防止重复请求
  final Map<String, Completer<List<ChartData>>> _pendingRequests = {};

  /// 数据流控制器
  final Map<String, StreamController<ChartDataUpdateEvent>> _streamControllers =
      {};

  /// 可见范围变化监听器
  final Map<String, VoidCallback> _rangeChangeListeners = {};

  /// 防抖定时器
  final Map<String, Timer?> _debounceTimers = {};

  /// 数据请求回调
  Function(ChartDataRequest)? onDataRequest;

  /// 配置参数
  final ChartDataManagerConfig config;

  ChartDataManager({this.config = const ChartDataManagerConfig()});

  /// 注册数据请求回调
  void setDataRequestCallback(Function(ChartDataRequest) callback) {
    onDataRequest = callback;
  }

  /// 获取数据流
  Stream<ChartDataUpdateEvent> getDataStream(String chartId) {
    if (!_streamControllers.containsKey(chartId)) {
      _streamControllers[chartId] =
          StreamController<ChartDataUpdateEvent>.broadcast();
    }
    return _streamControllers[chartId]!.stream;
  }

  /// 注册可见范围变化监听器
  void registerRangeChangeListener(String chartId, VoidCallback listener) {
    _rangeChangeListeners[chartId] = listener;

    AppLogger.logModule(
      'ChartDataManager',
      LogLevel.info,
      '注册可见范围变化监听器',
      metadata: {
        'chart_id': chartId,
        'listener_count': _rangeChangeListeners.length,
      },
    );
  }

  /// 处理可见范围变化
  void handleVisibleRangeChange(
    String chartId,
    String symbol,
    String timeFrame,
    VisibleRange range,
  ) {
    final requestKey = _generateRequestKey(symbol, timeFrame);

    AppLogger.logModule(
      'ChartDataManager',
      LogLevel.info,
      '📊 可见范围变化',
      metadata: {
        'chart_id': chartId,
        'symbol': symbol,
        'time_frame': timeFrame,
        'visible_range': {'from': range.from, 'to': range.to},
        'request_key': requestKey,
      },
    );

    // 使用防抖机制，避免频繁请求
    _debounceTimers[requestKey]?.cancel();
    _debounceTimers[requestKey] = Timer(config.debounceDelay, () {
      _checkAndRequestHistoricalData(chartId, symbol, timeFrame, range);
    });
  }

  /// 检查并请求历史数据
  void _checkAndRequestHistoricalData(
    String chartId,
    String symbol,
    String timeFrame,
    VisibleRange range,
  ) {
    final requestKey = _generateRequestKey(symbol, timeFrame);

    // 检查是否已有足够的数据
    final cachedData = getCachedData(symbol, timeFrame);
    final needsMoreData = _shouldRequestMoreData(cachedData, range);

    if (!needsMoreData) {
      AppLogger.logModule(
        'ChartDataManager',
        LogLevel.debug,
        '缓存数据充足，无需请求',
        metadata: {
          'symbol': symbol,
          'time_frame': timeFrame,
          'cached_count': cachedData.length,
        },
      );
      return;
    }

    // 检查是否已有相同的请求在进行
    if (_pendingRequests.containsKey(requestKey)) {
      AppLogger.logModule(
        'ChartDataManager',
        LogLevel.warning,
        '⚠️ 请求去重 - 相同请求正在进行',
        metadata: {
          'symbol': symbol,
          'time_frame': timeFrame,
          'request_key': requestKey,
        },
      );
      return;
    }

    // 发起新的数据请求
    _requestHistoricalData(chartId, symbol, timeFrame, range);
  }

  /// 请求历史数据
  void _requestHistoricalData(
    String chartId,
    String symbol,
    String timeFrame,
    VisibleRange range,
  ) {
    final requestKey = _generateRequestKey(symbol, timeFrame);
    final completer = Completer<List<ChartData>>();
    _pendingRequests[requestKey] = completer;

    final request = ChartDataRequest(
      chartId: chartId,
      symbol: symbol,
      timeFrame: timeFrame,
      range: range,
      requestType: ChartDataRequestType.historical,
      timestamp: DateTime.now(),
    );

    AppLogger.logModule(
      'ChartDataManager',
      LogLevel.info,
      '🚀 发起历史数据请求',
      metadata: {
        'chart_id': chartId,
        'symbol': symbol,
        'time_frame': timeFrame,
        'request_key': requestKey,
        'range': {'from': range.from, 'to': range.to},
      },
    );

    // 调用外部数据请求回调
    if (onDataRequest != null) {
      try {
        onDataRequest!(request);
      } catch (e) {
        AppLogger.logModule(
          'ChartDataManager',
          LogLevel.error,
          '❌ 数据请求回调执行失败',
          error: e,
          metadata: {
            'symbol': symbol,
            'time_frame': timeFrame,
            'request_key': requestKey,
          },
        );

        completer.completeError(e);
        _pendingRequests.remove(requestKey);
      }
    } else {
      AppLogger.logModule(
        'ChartDataManager',
        LogLevel.warning,
        '⚠️ 未设置数据请求回调',
        metadata: {'symbol': symbol, 'time_frame': timeFrame},
      );

      completer.completeError('数据请求回调未设置');
      _pendingRequests.remove(requestKey);
    }

    // 设置请求超时
    Timer(config.requestTimeout, () {
      if (!completer.isCompleted) {
        AppLogger.logModule(
          'ChartDataManager',
          LogLevel.error,
          '❌ 数据请求超时',
          metadata: {
            'symbol': symbol,
            'time_frame': timeFrame,
            'timeout_ms': config.requestTimeout.inMilliseconds,
          },
        );

        completer.completeError('请求超时');
        _pendingRequests.remove(requestKey);
      }
    });
  }

  /// 处理数据响应
  Future<void> handleDataResponse(
    String symbol,
    String timeFrame,
    List<ChartData> data,
  ) async {
    final requestKey = _generateRequestKey(symbol, timeFrame);

    AppLogger.logModule(
      'ChartDataManager',
      LogLevel.info,
      '✅ 接收到数据响应',
      metadata: {
        'symbol': symbol,
        'time_frame': timeFrame,
        'data_count': data.length,
        'request_key': requestKey,
      },
    );

    // 更新缓存
    _updateCache(symbol, timeFrame, data);

    // 完成待处理的请求
    final completer = _pendingRequests.remove(requestKey);
    if (completer != null && !completer.isCompleted) {
      completer.complete(data);
    }

    // 通知所有相关的图表
    _notifyDataUpdate(symbol, timeFrame, data);
  }

  /// 处理数据请求错误
  void handleDataError(String symbol, String timeFrame, dynamic error) {
    final requestKey = _generateRequestKey(symbol, timeFrame);

    AppLogger.logModule(
      'ChartDataManager',
      LogLevel.error,
      '❌ 数据请求失败',
      error: error,
      metadata: {
        'symbol': symbol,
        'time_frame': timeFrame,
        'request_key': requestKey,
      },
    );

    // 完成待处理的请求
    final completer = _pendingRequests.remove(requestKey);
    if (completer != null && !completer.isCompleted) {
      completer.completeError(error);
    }
  }

  /// 获取缓存数据
  List<ChartData> getCachedData(String symbol, String timeFrame) {
    return List.from(_dataCache[symbol]?[timeFrame] ?? []);
  }

  /// 更新缓存
  void _updateCache(String symbol, String timeFrame, List<ChartData> data) {
    if (!_dataCache.containsKey(symbol)) {
      _dataCache[symbol] = {};
    }

    if (!_dataCache[symbol]!.containsKey(timeFrame)) {
      _dataCache[symbol]![timeFrame] = [];
    }

    final cache = _dataCache[symbol]![timeFrame]!;

    // 合并新数据到缓存
    for (final newData in data) {
      final existingIndex = cache.indexWhere(
        (item) =>
            item.timestamp.millisecondsSinceEpoch ==
            newData.timestamp.millisecondsSinceEpoch,
      );

      if (existingIndex != -1) {
        cache[existingIndex] = newData;
      } else {
        cache.add(newData);
      }
    }

    // 按时间排序
    cache.sort((a, b) => a.timestamp.compareTo(b.timestamp));

    // 保持缓存大小限制
    while (cache.length > config.maxCacheSize) {
      cache.removeAt(0);
    }

    AppLogger.logModule(
      'ChartDataManager',
      LogLevel.debug,
      '📦 缓存已更新',
      metadata: {
        'symbol': symbol,
        'time_frame': timeFrame,
        'cache_size': cache.length,
        'new_data_count': data.length,
      },
    );
  }

  /// 通知数据更新
  void _notifyDataUpdate(
    String symbol,
    String timeFrame,
    List<ChartData> data,
  ) {
    final event = ChartDataUpdateEvent(
      symbol: symbol,
      timeFrame: timeFrame,
      data: data,
      updateType: ChartDataUpdateType.historical,
      timestamp: DateTime.now(),
    );

    // 通知所有相关的流
    for (final controller in _streamControllers.values) {
      if (!controller.isClosed) {
        controller.add(event);
      }
    }

    notifyListeners();
  }

  /// 判断是否需要请求更多数据
  bool _shouldRequestMoreData(List<ChartData> cachedData, VisibleRange range) {
    if (cachedData.isEmpty) return true;

    // 检查可见范围是否超出缓存范围
    final cacheStart = cachedData.first.timestamp.millisecondsSinceEpoch;
    final cacheEnd = cachedData.last.timestamp.millisecondsSinceEpoch;

    final rangeStart = range.from;
    final rangeEnd = range.to;

    // 如果可见范围超出缓存范围的阈值，则需要请求更多数据
    final bufferRatio = config.dataBufferRatio;
    final cacheRange = cacheEnd - cacheStart;
    final buffer = cacheRange * bufferRatio;

    return rangeStart < (cacheStart + buffer) || rangeEnd > (cacheEnd - buffer);
  }

  /// 生成请求键
  String _generateRequestKey(String symbol, String timeFrame) {
    return '${symbol}_$timeFrame';
  }

  /// 清除缓存
  void clearCache([String? symbol, String? timeFrame]) {
    if (symbol != null && timeFrame != null) {
      _dataCache[symbol]?.remove(timeFrame);
    } else if (symbol != null) {
      _dataCache.remove(symbol);
    } else {
      _dataCache.clear();
    }

    AppLogger.logModule(
      'ChartDataManager',
      LogLevel.info,
      '🗑️ 缓存已清除',
      metadata: {
        'symbol': symbol,
        'time_frame': timeFrame,
        'scope': symbol != null
            ? (timeFrame != null ? 'specific' : 'symbol')
            : 'all',
      },
    );
  }

  @override
  void dispose() {
    // 取消所有防抖定时器
    for (final timer in _debounceTimers.values) {
      timer?.cancel();
    }
    _debounceTimers.clear();

    // 关闭所有流控制器
    for (final controller in _streamControllers.values) {
      if (!controller.isClosed) {
        controller.close();
      }
    }
    _streamControllers.clear();

    // 清除所有数据
    _dataCache.clear();
    _pendingRequests.clear();
    _rangeChangeListeners.clear();

    super.dispose();
  }
}

/// 图表数据管理器配置
class ChartDataManagerConfig {
  /// 最大缓存大小
  final int maxCacheSize;

  /// 防抖延迟
  final Duration debounceDelay;

  /// 请求超时
  final Duration requestTimeout;

  /// 数据缓冲比例（用于判断是否需要加载更多数据）
  final double dataBufferRatio;

  const ChartDataManagerConfig({
    this.maxCacheSize = 5000,
    this.debounceDelay = const Duration(milliseconds: 300),
    this.requestTimeout = const Duration(seconds: 10),
    this.dataBufferRatio = 0.2, // 20%的缓冲区
  });
}

/// 可见范围
class VisibleRange {
  final int from;
  final int to;

  const VisibleRange({required this.from, required this.to});

  @override
  String toString() => 'VisibleRange(from: $from, to: $to)';
}

/// 图表数据请求
class ChartDataRequest {
  final String chartId;
  final String symbol;
  final String timeFrame;
  final VisibleRange range;
  final ChartDataRequestType requestType;
  final DateTime timestamp;

  const ChartDataRequest({
    required this.chartId,
    required this.symbol,
    required this.timeFrame,
    required this.range,
    required this.requestType,
    required this.timestamp,
  });

  @override
  String toString() {
    return 'ChartDataRequest(chartId: $chartId, symbol: $symbol, timeFrame: $timeFrame, range: $range, type: $requestType)';
  }
}

/// 图表数据请求类型
enum ChartDataRequestType { historical, realtime, refresh }

/// 图表数据更新事件
class ChartDataUpdateEvent {
  final String symbol;
  final String timeFrame;
  final List<ChartData> data;
  final ChartDataUpdateType updateType;
  final DateTime timestamp;

  const ChartDataUpdateEvent({
    required this.symbol,
    required this.timeFrame,
    required this.data,
    required this.updateType,
    required this.timestamp,
  });

  @override
  String toString() {
    return 'ChartDataUpdateEvent(symbol: $symbol, timeFrame: $timeFrame, dataCount: ${data.length}, type: $updateType)';
  }
}

/// 图表数据更新类型
enum ChartDataUpdateType { historical, realtime, refresh }
