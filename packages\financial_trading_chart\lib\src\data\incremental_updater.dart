import 'dart:async';
import 'dart:collection';
import 'package:flutter/foundation.dart';

import '../models/chart_data.dart';
import '../performance/data_virtualizer.dart';

/// 增量数据更新器
/// 
/// 负责管理实时数据的增量更新
/// 特性：
/// - 批量更新：减少更新频率
/// - 智能合并：合并相同时间戳的数据
/// - 缓冲机制：平滑数据流
/// - 性能优化：最小化重绘
class IncrementalUpdater {
  final ChartDataVirtualizer virtualizer;
  
  // 更新事件流
  final StreamController<ChartUpdateEvent> _updateController = 
      StreamController<ChartUpdateEvent>.broadcast();
  
  // 待处理的更新队列
  final Queue<CandleData> _pendingUpdates = Queue<CandleData>();
  final Queue<CandleData> _pendingLastCandleUpdates = Queue<CandleData>();
  
  // 批量更新定时器
  Timer? _batchUpdateTimer;
  final Duration _batchInterval = const Duration(milliseconds: 16); // 60FPS
  
  // 性能监控
  int _updateCount = 0;
  int _batchCount = 0;
  final Stopwatch _updateStopwatch = Stopwatch();
  
  IncrementalUpdater(this.virtualizer);
  
  /// 更新事件流
  Stream<ChartUpdateEvent> get updateStream => _updateController.stream;
  
  /// 添加新的K线数据
  void addCandle(CandleData candle) {
    _pendingUpdates.add(candle);
    _scheduleUpdate();
    
    if (kDebugMode) {
      debugPrint('IncrementalUpdater: Added new candle at ${candle.timestamp}');
    }
  }
  
  /// 更新最后一根K线
  void updateLastCandle(CandleData candle) {
    _pendingLastCandleUpdates.add(candle);
    _scheduleUpdate();
    
    if (kDebugMode) {
      debugPrint('IncrementalUpdater: Updated last candle at ${candle.timestamp}');
    }
  }
  
  /// 批量添加数据
  void addBatch(List<CandleData> candles) {
    if (candles.isEmpty) return;
    
    _pendingUpdates.addAll(candles);
    _scheduleUpdate();
    
    if (kDebugMode) {
      debugPrint('IncrementalUpdater: Added batch of ${candles.length} candles');
    }
  }
  
  /// 调度更新
  void _scheduleUpdate() {
    if (_batchUpdateTimer?.isActive == true) return;
    
    _batchUpdateTimer = Timer(_batchInterval, _processPendingUpdates);
  }
  
  /// 处理待更新的数据
  void _processPendingUpdates() {
    if (_pendingUpdates.isEmpty && _pendingLastCandleUpdates.isEmpty) {
      return;
    }
    
    _updateStopwatch.start();
    
    try {
      // 处理新增K线
      if (_pendingUpdates.isNotEmpty) {
        final newCandles = List<CandleData>.from(_pendingUpdates);
        _pendingUpdates.clear();
        
        // 去重和排序
        final uniqueCandles = _deduplicateAndSort(newCandles);
        
        // 更新虚拟化器
        virtualizer.addData(uniqueCandles);
        
        // 发送更新事件
        _updateController.add(ChartUpdateEvent(
          type: UpdateType.newCandles,
          data: uniqueCandles,
          timestamp: DateTime.now(),
        ));
        
        _updateCount += uniqueCandles.length;
      }
      
      // 处理最后一根K线更新
      if (_pendingLastCandleUpdates.isNotEmpty) {
        final lastUpdate = _pendingLastCandleUpdates.last;
        _pendingLastCandleUpdates.clear();
        
        // 更新虚拟化器
        virtualizer.updateLastData(lastUpdate);
        
        // 发送更新事件
        _updateController.add(ChartUpdateEvent(
          type: UpdateType.lastCandleUpdate,
          data: [lastUpdate],
          timestamp: DateTime.now(),
        ));
        
        _updateCount++;
      }
      
      _batchCount++;
      
    } finally {
      _updateStopwatch.stop();
      
      if (kDebugMode && _updateStopwatch.elapsedMilliseconds > 5) {
        debugPrint('IncrementalUpdater: Batch update took ${_updateStopwatch.elapsedMilliseconds}ms');
      }
      
      _updateStopwatch.reset();
    }
  }
  
  /// 去重和排序
  List<CandleData> _deduplicateAndSort(List<CandleData> candles) {
    if (candles.isEmpty) return candles;
    
    // 按时间戳分组
    final Map<DateTime, CandleData> candleMap = {};
    
    for (final candle in candles) {
      final timestamp = candle.timestamp;
      
      // 如果已存在相同时间戳的数据，使用最新的
      if (!candleMap.containsKey(timestamp) || 
          candle.timestamp.isAfter(candleMap[timestamp]!.timestamp)) {
        candleMap[timestamp] = candle;
      }
    }
    
    // 按时间戳排序
    final sortedCandles = candleMap.values.toList();
    sortedCandles.sort((a, b) => a.timestamp.compareTo(b.timestamp));
    
    return sortedCandles;
  }
  
  /// 清空待更新队列
  void clearPendingUpdates() {
    _pendingUpdates.clear();
    _pendingLastCandleUpdates.clear();
    
    if (kDebugMode) {
      debugPrint('IncrementalUpdater: Cleared pending updates');
    }
  }
  
  /// 获取更新统计
  UpdaterStats getStats() {
    return UpdaterStats(
      totalUpdates: _updateCount,
      batchCount: _batchCount,
      pendingUpdates: _pendingUpdates.length,
      pendingLastCandleUpdates: _pendingLastCandleUpdates.length,
      averageUpdatesPerBatch: _batchCount > 0 ? _updateCount / _batchCount : 0.0,
    );
  }
  
  /// 重置统计
  void resetStats() {
    _updateCount = 0;
    _batchCount = 0;
    
    if (kDebugMode) {
      debugPrint('IncrementalUpdater: Stats reset');
    }
  }
  
  /// 释放资源
  void dispose() {
    _batchUpdateTimer?.cancel();
    _updateController.close();
    _pendingUpdates.clear();
    _pendingLastCandleUpdates.clear();
    
    if (kDebugMode) {
      debugPrint('IncrementalUpdater: Disposed');
    }
  }
}

/// 图表更新事件
@immutable
class ChartUpdateEvent {
  final UpdateType type;
  final List<CandleData> data;
  final DateTime timestamp;
  final Map<String, dynamic>? metadata;
  
  const ChartUpdateEvent({
    required this.type,
    required this.data,
    required this.timestamp,
    this.metadata,
  });
  
  @override
  String toString() {
    return 'ChartUpdateEvent('
           'type: $type, '
           'dataCount: ${data.length}, '
           'timestamp: $timestamp)';
  }
}

/// 更新类型
enum UpdateType {
  /// 新增K线数据
  newCandles,
  
  /// 最后一根K线更新
  lastCandleUpdate,
  
  /// 批量数据更新
  batchUpdate,
  
  /// 指标数据更新
  indicatorUpdate,
  
  /// 配置更新
  configUpdate,
  
  /// 主题更新
  themeUpdate,
}

/// 更新器统计信息
@immutable
class UpdaterStats {
  final int totalUpdates;
  final int batchCount;
  final int pendingUpdates;
  final int pendingLastCandleUpdates;
  final double averageUpdatesPerBatch;
  
  const UpdaterStats({
    required this.totalUpdates,
    required this.batchCount,
    required this.pendingUpdates,
    required this.pendingLastCandleUpdates,
    required this.averageUpdatesPerBatch,
  });
  
  /// 更新效率
  double get updateEfficiency {
    if (totalUpdates == 0) return 0.0;
    return (totalUpdates - pendingUpdates - pendingLastCandleUpdates) / totalUpdates;
  }
  
  @override
  String toString() {
    return 'UpdaterStats('
           'total: $totalUpdates, '
           'batches: $batchCount, '
           'pending: $pendingUpdates, '
           'pendingLast: $pendingLastCandleUpdates, '
           'avgPerBatch: ${averageUpdatesPerBatch.toStringAsFixed(1)}, '
           'efficiency: ${(updateEfficiency * 100).toStringAsFixed(1)}%)';
  }
}

/// 实时数据管理器
class RealTimeDataManager {
  final IncrementalUpdater updater;
  
  // WebSocket连接
  StreamSubscription? _webSocketSubscription;
  
  // 数据缓冲
  final Queue<CandleData> _dataBuffer = Queue<CandleData>();
  Timer? _bufferFlushTimer;
  
  // 连接状态
  bool _isConnected = false;
  DateTime? _lastDataTime;
  
  RealTimeDataManager(this.updater);
  
  /// 是否已连接
  bool get isConnected => _isConnected;
  
  /// 最后数据时间
  DateTime? get lastDataTime => _lastDataTime;
  
  /// 连接实时数据流
  void connectStream(Stream<CandleData> dataStream) {
    _webSocketSubscription?.cancel();
    
    _webSocketSubscription = dataStream.listen(
      _onDataReceived,
      onError: _onError,
      onDone: _onDisconnected,
    );
    
    _isConnected = true;
    
    if (kDebugMode) {
      debugPrint('RealTimeDataManager: Connected to data stream');
    }
  }
  
  /// 断开连接
  void disconnect() {
    _webSocketSubscription?.cancel();
    _bufferFlushTimer?.cancel();
    _dataBuffer.clear();
    _isConnected = false;
    
    if (kDebugMode) {
      debugPrint('RealTimeDataManager: Disconnected from data stream');
    }
  }
  
  /// 数据接收处理
  void _onDataReceived(CandleData data) {
    _lastDataTime = DateTime.now();
    
    // 数据验证
    if (_isValidData(data)) {
      _dataBuffer.add(data);
      _scheduleBufferFlush();
    } else {
      if (kDebugMode) {
        debugPrint('RealTimeDataManager: Invalid data received: $data');
      }
    }
  }
  
  /// 错误处理
  void _onError(dynamic error) {
    if (kDebugMode) {
      debugPrint('RealTimeDataManager: Stream error: $error');
    }
    
    // 可以实现重连逻辑
    _scheduleReconnect();
  }
  
  /// 连接断开处理
  void _onDisconnected() {
    _isConnected = false;
    
    if (kDebugMode) {
      debugPrint('RealTimeDataManager: Stream disconnected');
    }
    
    // 可以实现重连逻辑
    _scheduleReconnect();
  }
  
  /// 数据验证
  bool _isValidData(CandleData data) {
    return data.open > 0 &&
           data.high > 0 &&
           data.low > 0 &&
           data.close > 0 &&
           data.high >= data.low &&
           data.high >= data.open &&
           data.high >= data.close &&
           data.low <= data.open &&
           data.low <= data.close &&
           data.volume >= 0;
  }
  
  /// 调度缓冲区刷新
  void _scheduleBufferFlush() {
    if (_bufferFlushTimer?.isActive == true) return;
    
    _bufferFlushTimer = Timer(const Duration(milliseconds: 100), _flushBuffer);
  }
  
  /// 刷新缓冲区
  void _flushBuffer() {
    if (_dataBuffer.isEmpty) return;
    
    final bufferedData = List<CandleData>.from(_dataBuffer);
    _dataBuffer.clear();
    
    // 发送到更新器
    if (bufferedData.length == 1) {
      updater.updateLastCandle(bufferedData.first);
    } else {
      updater.addBatch(bufferedData);
    }
    
    if (kDebugMode) {
      debugPrint('RealTimeDataManager: Flushed ${bufferedData.length} data points');
    }
  }
  
  /// 调度重连
  void _scheduleReconnect() {
    // TODO: 实现重连逻辑
    Timer(const Duration(seconds: 5), () {
      if (!_isConnected) {
        if (kDebugMode) {
          debugPrint('RealTimeDataManager: Attempting to reconnect...');
        }
        // 重连逻辑
      }
    });
  }
  
  /// 释放资源
  void dispose() {
    disconnect();
  }
}
