import 'dart:math' as math;
import 'package:flutter/foundation.dart';

import '../models/chart_data.dart';
import '../models/performance_data.dart';

/// 技术指标计算器
/// 
/// 负责计算各种技术指标
/// 特性：
/// - 高性能计算：优化的算法实现
/// - 缓存机制：避免重复计算
/// - 增量计算：支持实时数据更新
/// - 多种指标：支持20+种常用指标
class IndicatorCalculator {
  // 计算缓存
  final Map<String, List<double>> _cache = {};
  
  // 性能监控
  final Map<String, int> _calculationCount = {};
  final Map<String, int> _calculationTime = {};
  
  /// 计算技术指标
  List<double> calculate(TechnicalIndicator indicator, List<CandleData> data) {
    if (data.isEmpty) return [];
    
    final cacheKey = _generateCacheKey(indicator, data);
    
    // 检查缓存
    if (_cache.containsKey(cacheKey)) {
      return _cache[cacheKey]!;
    }
    
    final stopwatch = Stopwatch()..start();
    
    List<double> result;
    
    try {
      switch (indicator.type) {
        case IndicatorType.movingAverage:
          result = _calculateSMA(data, indicator.config.periods['period'] ?? 20);
          break;
        case IndicatorType.exponentialMovingAverage:
          result = _calculateEMA(data, indicator.config.periods['period'] ?? 20);
          break;
        case IndicatorType.bollingerBands:
          result = _calculateBollingerBands(data, indicator.config.periods['period'] ?? 20);
          break;
        case IndicatorType.rsi:
          result = _calculateRSI(data, indicator.config.periods['period'] ?? 14);
          break;
        case IndicatorType.macd:
          result = _calculateMACD(data, 
            indicator.config.periods['fast'] ?? 12,
            indicator.config.periods['slow'] ?? 26,
            indicator.config.periods['signal'] ?? 9);
          break;
        case IndicatorType.kdj:
          result = _calculateKDJ(data, indicator.config.periods['period'] ?? 9);
          break;
        case IndicatorType.volume:
          result = _calculateVolume(data);
          break;
        case IndicatorType.williams:
          result = _calculateWilliams(data, indicator.config.periods['period'] ?? 14);
          break;
        case IndicatorType.stochastic:
          result = _calculateStochastic(data, 
            indicator.config.periods['k'] ?? 14,
            indicator.config.periods['d'] ?? 3);
          break;
        default:
          result = [];
      }
      
      // 缓存结果
      _cache[cacheKey] = result;
      
    } finally {
      stopwatch.stop();
      
      // 记录性能统计
      final indicatorName = indicator.type.toString();
      _calculationCount[indicatorName] = (_calculationCount[indicatorName] ?? 0) + 1;
      _calculationTime[indicatorName] = (_calculationTime[indicatorName] ?? 0) + stopwatch.elapsedMilliseconds;
      
      if (kDebugMode && stopwatch.elapsedMilliseconds > 10) {
        debugPrint('IndicatorCalculator: ${indicator.type} calculation took ${stopwatch.elapsedMilliseconds}ms');
      }
    }
    
    return result;
  }
  
  /// 简单移动平均线 (SMA)
  List<double> _calculateSMA(List<CandleData> data, int period) {
    final result = <double>[];
    
    for (int i = 0; i < data.length; i++) {
      if (i < period - 1) {
        result.add(double.nan);
        continue;
      }
      
      double sum = 0;
      for (int j = i - period + 1; j <= i; j++) {
        sum += data[j].close;
      }
      
      result.add(sum / period);
    }
    
    return result;
  }
  
  /// 指数移动平均线 (EMA)
  List<double> _calculateEMA(List<CandleData> data, int period) {
    final result = <double>[];
    final multiplier = 2.0 / (period + 1);
    
    for (int i = 0; i < data.length; i++) {
      if (i == 0) {
        result.add(data[i].close);
      } else {
        final ema = (data[i].close * multiplier) + (result[i - 1] * (1 - multiplier));
        result.add(ema);
      }
    }
    
    return result;
  }
  
  /// 布林带 (Bollinger Bands)
  List<double> _calculateBollingerBands(List<CandleData> data, int period) {
    final sma = _calculateSMA(data, period);
    final result = <double>[];
    
    for (int i = 0; i < data.length; i++) {
      if (i < period - 1) {
        result.add(double.nan);
        continue;
      }
      
      // 计算标准差
      double sum = 0;
      for (int j = i - period + 1; j <= i; j++) {
        final diff = data[j].close - sma[i];
        sum += diff * diff;
      }
      
      final stdDev = math.sqrt(sum / period);
      
      // 返回中轨（SMA）
      result.add(sma[i]);
      
      // 上轨和下轨可以通过额外的方法返回
      // upperBand = sma[i] + (2 * stdDev)
      // lowerBand = sma[i] - (2 * stdDev)
    }
    
    return result;
  }
  
  /// 相对强弱指数 (RSI)
  List<double> _calculateRSI(List<CandleData> data, int period) {
    final result = <double>[];
    final gains = <double>[];
    final losses = <double>[];
    
    // 计算价格变化
    for (int i = 1; i < data.length; i++) {
      final change = data[i].close - data[i - 1].close;
      gains.add(change > 0 ? change : 0);
      losses.add(change < 0 ? -change : 0);
    }
    
    // 计算RSI
    result.add(double.nan); // 第一个值无法计算
    
    for (int i = 0; i < gains.length; i++) {
      if (i < period - 1) {
        result.add(double.nan);
        continue;
      }
      
      double avgGain = 0;
      double avgLoss = 0;
      
      for (int j = i - period + 1; j <= i; j++) {
        avgGain += gains[j];
        avgLoss += losses[j];
      }
      
      avgGain /= period;
      avgLoss /= period;
      
      if (avgLoss == 0) {
        result.add(100);
      } else {
        final rs = avgGain / avgLoss;
        final rsi = 100 - (100 / (1 + rs));
        result.add(rsi);
      }
    }
    
    return result;
  }
  
  /// MACD指标
  List<double> _calculateMACD(List<CandleData> data, int fastPeriod, int slowPeriod, int signalPeriod) {
    final ema12 = _calculateEMA(data, fastPeriod);
    final ema26 = _calculateEMA(data, slowPeriod);
    final macdLine = <double>[];
    
    // 计算MACD线
    for (int i = 0; i < data.length; i++) {
      if (ema12[i].isNaN || ema26[i].isNaN) {
        macdLine.add(double.nan);
      } else {
        macdLine.add(ema12[i] - ema26[i]);
      }
    }
    
    // 计算信号线（MACD的EMA）
    final signalLine = _calculateEMAFromValues(macdLine, signalPeriod);
    
    // 返回MACD线（可以扩展返回信号线和柱状图）
    return macdLine;
  }
  
  /// KDJ指标
  List<double> _calculateKDJ(List<CandleData> data, int period) {
    final result = <double>[];
    
    for (int i = 0; i < data.length; i++) {
      if (i < period - 1) {
        result.add(double.nan);
        continue;
      }
      
      // 计算最高价和最低价
      double highest = data[i - period + 1].high;
      double lowest = data[i - period + 1].low;
      
      for (int j = i - period + 2; j <= i; j++) {
        highest = math.max(highest, data[j].high);
        lowest = math.min(lowest, data[j].low);
      }
      
      // 计算K值
      final k = ((data[i].close - lowest) / (highest - lowest)) * 100;
      result.add(k);
    }
    
    return result;
  }
  
  /// 成交量
  List<double> _calculateVolume(List<CandleData> data) {
    return data.map((candle) => candle.volume).toList();
  }
  
  /// 威廉指标 (Williams %R)
  List<double> _calculateWilliams(List<CandleData> data, int period) {
    final result = <double>[];
    
    for (int i = 0; i < data.length; i++) {
      if (i < period - 1) {
        result.add(double.nan);
        continue;
      }
      
      // 计算最高价和最低价
      double highest = data[i - period + 1].high;
      double lowest = data[i - period + 1].low;
      
      for (int j = i - period + 2; j <= i; j++) {
        highest = math.max(highest, data[j].high);
        lowest = math.min(lowest, data[j].low);
      }
      
      // 计算Williams %R
      final wr = ((highest - data[i].close) / (highest - lowest)) * -100;
      result.add(wr);
    }
    
    return result;
  }
  
  /// 随机指标 (Stochastic)
  List<double> _calculateStochastic(List<CandleData> data, int kPeriod, int dPeriod) {
    final kValues = <double>[];
    
    // 计算%K值
    for (int i = 0; i < data.length; i++) {
      if (i < kPeriod - 1) {
        kValues.add(double.nan);
        continue;
      }
      
      double highest = data[i - kPeriod + 1].high;
      double lowest = data[i - kPeriod + 1].low;
      
      for (int j = i - kPeriod + 2; j <= i; j++) {
        highest = math.max(highest, data[j].high);
        lowest = math.min(lowest, data[j].low);
      }
      
      final k = ((data[i].close - lowest) / (highest - lowest)) * 100;
      kValues.add(k);
    }
    
    // 计算%D值（%K的移动平均）
    return _calculateSMAFromValues(kValues, dPeriod);
  }
  
  /// 从数值列表计算EMA
  List<double> _calculateEMAFromValues(List<double> values, int period) {
    final result = <double>[];
    final multiplier = 2.0 / (period + 1);
    
    for (int i = 0; i < values.length; i++) {
      if (values[i].isNaN) {
        result.add(double.nan);
        continue;
      }
      
      if (i == 0 || result.isEmpty || result.last.isNaN) {
        result.add(values[i]);
      } else {
        final ema = (values[i] * multiplier) + (result[i - 1] * (1 - multiplier));
        result.add(ema);
      }
    }
    
    return result;
  }
  
  /// 从数值列表计算SMA
  List<double> _calculateSMAFromValues(List<double> values, int period) {
    final result = <double>[];
    
    for (int i = 0; i < values.length; i++) {
      if (i < period - 1) {
        result.add(double.nan);
        continue;
      }
      
      double sum = 0;
      int count = 0;
      
      for (int j = i - period + 1; j <= i; j++) {
        if (!values[j].isNaN) {
          sum += values[j];
          count++;
        }
      }
      
      if (count > 0) {
        result.add(sum / count);
      } else {
        result.add(double.nan);
      }
    }
    
    return result;
  }
  
  /// 生成缓存键
  String _generateCacheKey(TechnicalIndicator indicator, List<CandleData> data) {
    final dataHash = data.length > 0 
        ? '${data.first.timestamp}_${data.last.timestamp}_${data.length}'
        : 'empty';
    
    return '${indicator.key}_${indicator.type}_${indicator.config.periods}_$dataHash';
  }
  
  /// 清除缓存
  void clearCache() {
    _cache.clear();
    
    if (kDebugMode) {
      debugPrint('IndicatorCalculator: Cache cleared');
    }
  }
  
  /// 获取性能统计
  CalculatorStats getPerformanceStats() {
    final totalCalculations = _calculationCount.values.fold(0, (sum, count) => sum + count);
    final totalTime = _calculationTime.values.fold(0, (sum, time) => sum + time);
    
    return CalculatorStats(
      totalCalculations: totalCalculations,
      totalCalculationTime: totalTime,
      averageCalculationTime: totalCalculations > 0 ? totalTime / totalCalculations : 0.0,
      cacheSize: _cache.length,
      calculationsByType: Map.from(_calculationCount),
    );
  }
  
  /// 重置统计
  void resetStats() {
    _calculationCount.clear();
    _calculationTime.clear();
    
    if (kDebugMode) {
      debugPrint('IndicatorCalculator: Stats reset');
    }
  }
}

/// 计算器性能统计
@immutable
class CalculatorStats {
  final int totalCalculations;
  final int totalCalculationTime;
  final double averageCalculationTime;
  final int cacheSize;
  final Map<String, int> calculationsByType;
  
  const CalculatorStats({
    required this.totalCalculations,
    required this.totalCalculationTime,
    required this.averageCalculationTime,
    required this.cacheSize,
    required this.calculationsByType,
  });
  
  @override
  String toString() {
    return 'CalculatorStats('
           'total: $totalCalculations, '
           'totalTime: ${totalCalculationTime}ms, '
           'avgTime: ${averageCalculationTime.toStringAsFixed(1)}ms, '
           'cacheSize: $cacheSize)';
  }
}
