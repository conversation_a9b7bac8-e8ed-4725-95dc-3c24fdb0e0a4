import 'dart:async';
import 'dart:math' as math;
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../models/performance_data.dart';
import '../config/chart_config.dart';

/// 高性能手势处理器
///
/// 特性：
/// - 防抖优化：减少无效更新
/// - 惯性滚动：流畅的用户体验
/// - 多点触控：精确的缩放控制
/// - 性能监控：手势响应时间跟踪
class HighPerformanceGestureHandler extends StatefulWidget {
  final Widget child;
  final HighPerformanceChartController controller;
  final HighPerformanceGestureConfig config;
  final Function(Offset)? onTap;
  final Function(Offset)? onLongPress;
  final Function(Offset)? onDoubleTap;

  const HighPerformanceGestureHandler({
    Key? key,
    required this.child,
    required this.controller,
    required this.config,
    this.onTap,
    this.onLongPress,
    this.onDoubleTap,
  }) : super(key: key);

  @override
  State<HighPerformanceGestureHandler> createState() =>
      _HighPerformanceGestureHandlerState();
}

class _HighPerformanceGestureHandlerState
    extends State<HighPerformanceGestureHandler>
    with TickerProviderStateMixin {
  // 手势状态
  bool _isPanning = false;
  bool _isScaling = false;
  Offset? _lastPanPosition;
  double _lastScale = 1.0;

  // 动画控制器
  late AnimationController _panAnimationController;
  late AnimationController _scaleAnimationController;
  late AnimationController _inertiaAnimationController;

  // 防抖定时器
  Timer? _debounceTimer;
  Timer? _longPressTimer;

  // 性能监控
  final Stopwatch _gestureStopwatch = Stopwatch();
  int _gestureCount = 0;

  // 惯性滚动
  Offset _velocity = Offset.zero;
  bool _isInertiaScrolling = false;

  @override
  void initState() {
    super.initState();
    _initializeAnimationControllers();
  }

  void _initializeAnimationControllers() {
    _panAnimationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _scaleAnimationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );

    _inertiaAnimationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      // 平移手势
      onPanStart: _onPanStart,
      onPanUpdate: _onPanUpdate,
      onPanEnd: _onPanEnd,

      // 缩放手势
      onScaleStart: _onScaleStart,
      onScaleUpdate: _onScaleUpdate,
      onScaleEnd: _onScaleEnd,

      // 点击手势
      onTap: _onTap,
      onDoubleTap: _onDoubleTap,
      onLongPressStart: _onLongPressStart,
      onLongPressEnd: _onLongPressEnd,

      child: widget.child,
    );
  }

  /// 平移开始
  void _onPanStart(DragStartDetails details) {
    _startGesturePerformanceMonitoring();

    _isPanning = true;
    _lastPanPosition = details.localPosition;

    // 停止惯性滚动
    _stopInertiaScrolling();

    // 通知控制器
    widget.controller.onInteractionStart();

    if (kDebugMode) {
      debugPrint('Gesture: Pan started at ${details.localPosition}');
    }
  }

  /// 平移更新
  void _onPanUpdate(DragUpdateDetails details) {
    if (!_isPanning || _lastPanPosition == null) return;

    final delta = details.localPosition - _lastPanPosition!;
    _lastPanPosition = details.localPosition;

    // 防抖处理
    _debounceGestureUpdate(() {
      widget.controller.pan(delta.dx, delta.dy);
    });

    // 记录速度用于惯性滚动
    _velocity = details.delta;
  }

  /// 平移结束
  void _onPanEnd(DragEndDetails details) {
    _isPanning = false;
    _lastPanPosition = null;

    // 惯性滚动
    final velocity = details.velocity.pixelsPerSecond;
    if (velocity.distance > widget.config.inertiaThreshold) {
      _startInertiaScrolling(velocity);
    }

    widget.controller.onInteractionEnd();
    _endGesturePerformanceMonitoring('pan');

    if (kDebugMode) {
      debugPrint('Gesture: Pan ended with velocity ${velocity.distance}');
    }
  }

  /// 缩放开始
  void _onScaleStart(ScaleStartDetails details) {
    _startGesturePerformanceMonitoring();

    _isScaling = true;
    _lastScale = 1.0;

    _scaleAnimationController.stop();
    widget.controller.onInteractionStart();

    if (kDebugMode) {
      debugPrint('Gesture: Scale started at ${details.localFocalPoint}');
    }
  }

  /// 缩放更新
  void _onScaleUpdate(ScaleUpdateDetails details) {
    if (!_isScaling) return;

    final scaleDelta = details.scale / _lastScale;
    _lastScale = details.scale;

    // 防抖处理
    _debounceGestureUpdate(() {
      widget.controller.zoom(scaleDelta, details.localFocalPoint);
    });
  }

  /// 缩放结束
  void _onScaleEnd(ScaleEndDetails details) {
    _isScaling = false;
    _lastScale = 1.0;

    widget.controller.onInteractionEnd();
    _endGesturePerformanceMonitoring('scale');

    if (kDebugMode) {
      debugPrint('Gesture: Scale ended');
    }
  }

  /// 点击事件
  void _onTap() {
    _startGesturePerformanceMonitoring();

    widget.onTap?.call(Offset.zero);

    _endGesturePerformanceMonitoring('tap');

    if (kDebugMode) {
      debugPrint('Gesture: Tap detected');
    }
  }

  /// 双击事件
  void _onDoubleTap() {
    _startGesturePerformanceMonitoring();

    // 双击缩放到合适大小
    widget.controller.autoZoom();
    widget.onDoubleTap?.call(Offset.zero);

    _endGesturePerformanceMonitoring('doubleTap');

    if (kDebugMode) {
      debugPrint('Gesture: Double tap detected');
    }
  }

  /// 长按开始
  void _onLongPressStart(LongPressStartDetails details) {
    _startGesturePerformanceMonitoring();

    widget.onLongPress?.call(details.localPosition);

    // 触觉反馈
    HapticFeedback.lightImpact();

    if (kDebugMode) {
      debugPrint('Gesture: Long press started at ${details.localPosition}');
    }
  }

  /// 长按结束
  void _onLongPressEnd(LongPressEndDetails details) {
    _endGesturePerformanceMonitoring('longPress');

    if (kDebugMode) {
      debugPrint('Gesture: Long press ended');
    }
  }

  /// 防抖手势更新
  void _debounceGestureUpdate(VoidCallback callback) {
    _debounceTimer?.cancel();
    _debounceTimer = Timer(widget.config.debounceDelay, callback);
  }

  /// 开始惯性滚动
  void _startInertiaScrolling(Offset velocity) {
    if (_isInertiaScrolling) return;

    _isInertiaScrolling = true;

    final animation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _inertiaAnimationController,
        curve: widget.config.inertiaCurve,
      ),
    );

    animation.addListener(() {
      if (!_isInertiaScrolling) return;

      final progress = animation.value;
      final currentVelocity =
          velocity * (1.0 - progress) * widget.config.inertiaFactor;

      if (currentVelocity.distance > 1.0) {
        widget.controller.pan(
          currentVelocity.dx * 0.016, // 60FPS
          currentVelocity.dy * 0.016,
        );
      } else {
        _stopInertiaScrolling();
      }
    });

    _inertiaAnimationController.forward(from: 0.0);

    if (kDebugMode) {
      debugPrint(
        'Gesture: Inertia scrolling started with velocity ${velocity.distance}',
      );
    }
  }

  /// 停止惯性滚动
  void _stopInertiaScrolling() {
    if (!_isInertiaScrolling) return;

    _isInertiaScrolling = false;
    _inertiaAnimationController.stop();

    if (kDebugMode) {
      debugPrint('Gesture: Inertia scrolling stopped');
    }
  }

  /// 开始手势性能监控
  void _startGesturePerformanceMonitoring() {
    _gestureStopwatch.start();
  }

  /// 结束手势性能监控
  void _endGesturePerformanceMonitoring(String gestureType) {
    _gestureStopwatch.stop();
    _gestureCount++;

    final responseTime = _gestureStopwatch.elapsedMilliseconds;

    if (kDebugMode && responseTime > widget.config.performanceThreshold) {
      debugPrint(
        'Gesture: $gestureType response time: ${responseTime}ms (threshold: ${widget.config.performanceThreshold}ms)',
      );
    }

    _gestureStopwatch.reset();
  }

  /// 获取手势性能统计
  GesturePerformanceStats getPerformanceStats() {
    return GesturePerformanceStats(
      totalGestures: _gestureCount,
      averageResponseTime: _gestureCount > 0
          ? _gestureStopwatch.elapsedMilliseconds / _gestureCount
          : 0.0,
      isInertiaScrolling: _isInertiaScrolling,
    );
  }

  @override
  void dispose() {
    _debounceTimer?.cancel();
    _longPressTimer?.cancel();
    _panAnimationController.dispose();
    _scaleAnimationController.dispose();
    _inertiaAnimationController.dispose();
    super.dispose();
  }
}

/// 高性能手势配置
@immutable
class HighPerformanceGestureConfig {
  /// 防抖延迟
  final Duration debounceDelay;

  /// 惯性滚动阈值
  final double inertiaThreshold;

  /// 惯性滚动因子
  final double inertiaFactor;

  /// 惯性滚动曲线
  final Curve inertiaCurve;

  /// 性能阈值 (毫秒)
  final int performanceThreshold;

  /// 缩放敏感度
  final double scaleSensitivity;

  /// 平移敏感度
  final double panSensitivity;

  /// 是否启用触觉反馈
  final bool enableHapticFeedback;

  const HighPerformanceGestureConfig({
    this.debounceDelay = const Duration(milliseconds: 1),
    this.inertiaThreshold = 50.0,
    this.inertiaFactor = 0.95,
    this.inertiaCurve = Curves.decelerate,
    this.performanceThreshold = 16,
    this.scaleSensitivity = 1.0,
    this.panSensitivity = 1.0,
    this.enableHapticFeedback = true,
  });

  /// 默认配置
  factory HighPerformanceGestureConfig.defaultConfig() {
    return const HighPerformanceGestureConfig();
  }

  /// 高性能配置
  factory HighPerformanceGestureConfig.highPerformance() {
    return const HighPerformanceGestureConfig(
      debounceDelay: Duration(milliseconds: 2),
      inertiaThreshold: 100.0,
      inertiaFactor: 0.9,
      performanceThreshold: 8,
    );
  }

  /// 流畅配置
  factory HighPerformanceGestureConfig.smooth() {
    return const HighPerformanceGestureConfig(
      debounceDelay: Duration.zero,
      inertiaThreshold: 30.0,
      inertiaFactor: 0.98,
      inertiaCurve: Curves.easeOut,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is HighPerformanceGestureConfig &&
        other.debounceDelay == debounceDelay &&
        other.inertiaThreshold == inertiaThreshold &&
        other.inertiaFactor == inertiaFactor &&
        other.inertiaCurve == inertiaCurve &&
        other.performanceThreshold == performanceThreshold &&
        other.scaleSensitivity == scaleSensitivity &&
        other.panSensitivity == panSensitivity &&
        other.enableHapticFeedback == enableHapticFeedback;
  }

  @override
  int get hashCode {
    return Object.hash(
      debounceDelay,
      inertiaThreshold,
      inertiaFactor,
      inertiaCurve,
      performanceThreshold,
      scaleSensitivity,
      panSensitivity,
      enableHapticFeedback,
    );
  }
}

/// 手势性能统计
@immutable
class GesturePerformanceStats {
  final int totalGestures;
  final double averageResponseTime;
  final bool isInertiaScrolling;

  const GesturePerformanceStats({
    required this.totalGestures,
    required this.averageResponseTime,
    required this.isInertiaScrolling,
  });

  /// 性能等级
  PerformanceLevel get performanceLevel {
    if (averageResponseTime <= 8.0) return PerformanceLevel.excellent;
    if (averageResponseTime <= 16.0) return PerformanceLevel.good;
    if (averageResponseTime <= 33.0) return PerformanceLevel.fair;
    return PerformanceLevel.poor;
  }

  @override
  String toString() {
    return 'GesturePerformanceStats('
        'gestures: $totalGestures, '
        'avgResponse: ${averageResponseTime.toStringAsFixed(1)}ms, '
        'inertia: $isInertiaScrolling, '
        'level: $performanceLevel)';
  }
}

/// 扩展控制器方法
extension HighPerformanceChartControllerGesture
    on HighPerformanceChartController {
  /// 交互开始
  void onInteractionStart() {
    // TODO: 实现交互开始逻辑
  }

  /// 交互结束
  void onInteractionEnd() {
    // TODO: 实现交互结束逻辑
  }

  /// 自动缩放
  void autoZoom() {
    // TODO: 实现自动缩放逻辑
    resetViewport();
  }
}
