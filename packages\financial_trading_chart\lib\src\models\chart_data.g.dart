// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'chart_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ChartDataPoint _$ChartDataPointFromJson(Map<String, dynamic> json) =>
    ChartDataPoint(
      timestamp: DateTime.parse(json['timestamp'] as String),
      value: (json['value'] as num).toDouble(),
      extra: json['extra'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$ChartDataPointToJson(ChartDataPoint instance) =>
    <String, dynamic>{
      'timestamp': instance.timestamp.toIso8601String(),
      'value': instance.value,
      'extra': instance.extra,
    };

KLineData _$KLineDataFromJson(Map<String, dynamic> json) => KLineData(
  timestamp: DateTime.parse(json['timestamp'] as String),
  open: (json['open'] as num).toDouble(),
  high: (json['high'] as num).toDouble(),
  low: (json['low'] as num).toDouble(),
  close: (json['close'] as num).toDouble(),
  time: json['time'] as String,
  volume: (json['volume'] as num?)?.toDouble(),
  amount: (json['amount'] as num?)?.toDouble(),
  extra: json['extra'] as Map<String, dynamic>?,
);

Map<String, dynamic> _$KLineDataToJson(KLineData instance) => <String, dynamic>{
  'timestamp': instance.timestamp.toIso8601String(),
  'open': instance.open,
  'high': instance.high,
  'low': instance.low,
  'close': instance.close,
  'time': instance.time,
  'volume': instance.volume,
  'amount': instance.amount,
  'extra': instance.extra,
};
