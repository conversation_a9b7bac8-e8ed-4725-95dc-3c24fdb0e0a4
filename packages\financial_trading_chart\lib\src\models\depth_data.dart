import 'package:json_annotation/json_annotation.dart';

part 'depth_data.g.dart';

/// 深度数据模型
/// 
/// 用于表示市场深度信息，包括买盘和卖盘的价格和数量
@JsonSerializable()
class DepthData {
  /// 买盘数据
  final List<DepthLevel> bids;

  /// 卖盘数据
  final List<DepthLevel> asks;

  /// 时间戳
  final DateTime timestamp;

  /// 交易对符号
  final String? symbol;

  /// 市场标识
  final String? market;

  /// 序列号（用于增量更新）
  final int? sequence;

  /// 是否为快照数据
  final bool isSnapshot;

  const DepthData({
    required this.bids,
    required this.asks,
    required this.timestamp,
    this.symbol,
    this.market,
    this.sequence,
    this.isSnapshot = true,
  });

  factory DepthData.fromJson(Map<String, dynamic> json) =>
      _$DepthDataFromJson(json);

  Map<String, dynamic> toJson() => _$DepthDataToJson(this);

  /// 最佳买价
  double? get bestBid => bids.isNotEmpty ? bids.first.price : null;

  /// 最佳卖价
  double? get bestAsk => asks.isNotEmpty ? asks.first.price : null;

  /// 买卖价差
  double? get spread {
    final bid = bestBid;
    final ask = bestAsk;
    return (bid != null && ask != null) ? ask - bid : null;
  }

  /// 中间价
  double? get midPrice {
    final bid = bestBid;
    final ask = bestAsk;
    return (bid != null && ask != null) ? (bid + ask) / 2 : null;
  }

  /// 买盘总量
  double get totalBidVolume => bids.fold<double>(0, (sum, level) => sum + level.quantity);

  /// 卖盘总量
  double get totalAskVolume => asks.fold<double>(0, (sum, level) => sum + level.quantity);

  /// 买盘总额
  double get totalBidAmount => bids.fold<double>(0, (sum, level) => sum + level.amount);

  /// 卖盘总额
  double get totalAskAmount => asks.fold<double>(0, (sum, level) => sum + level.amount);

  /// 获取指定深度的买盘数据
  List<DepthLevel> getBids(int depth) {
    return bids.take(depth).toList();
  }

  /// 获取指定深度的卖盘数据
  List<DepthLevel> getAsks(int depth) {
    return asks.take(depth).toList();
  }

  /// 创建副本
  DepthData copyWith({
    List<DepthLevel>? bids,
    List<DepthLevel>? asks,
    DateTime? timestamp,
    String? symbol,
    String? market,
    int? sequence,
    bool? isSnapshot,
  }) {
    return DepthData(
      bids: bids ?? this.bids,
      asks: asks ?? this.asks,
      timestamp: timestamp ?? this.timestamp,
      symbol: symbol ?? this.symbol,
      market: market ?? this.market,
      sequence: sequence ?? this.sequence,
      isSnapshot: isSnapshot ?? this.isSnapshot,
    );
  }

  /// 合并深度数据（用于增量更新）
  DepthData merge(DepthData other) {
    if (!other.isSnapshot) {
      // 增量更新逻辑
      final newBids = _mergeDepthLevels(bids, other.bids);
      final newAsks = _mergeDepthLevels(asks, other.asks);
      
      return copyWith(
        bids: newBids,
        asks: newAsks,
        timestamp: other.timestamp,
        sequence: other.sequence,
        isSnapshot: false,
      );
    } else {
      // 快照替换
      return other;
    }
  }

  /// 合并深度档位
  List<DepthLevel> _mergeDepthLevels(List<DepthLevel> current, List<DepthLevel> updates) {
    final Map<double, DepthLevel> levelMap = {
      for (final level in current) level.price: level
    };

    for (final update in updates) {
      if (update.quantity == 0) {
        levelMap.remove(update.price);
      } else {
        levelMap[update.price] = update;
      }
    }

    final result = levelMap.values.toList();
    result.sort((a, b) => b.price.compareTo(a.price)); // 降序排列
    return result;
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is DepthData &&
          runtimeType == other.runtimeType &&
          timestamp == other.timestamp &&
          symbol == other.symbol &&
          sequence == other.sequence;

  @override
  int get hashCode => timestamp.hashCode ^ symbol.hashCode ^ sequence.hashCode;

  @override
  String toString() =>
      'DepthData(timestamp: $timestamp, symbol: $symbol, bids: ${bids.length}, asks: ${asks.length})';
}

/// 深度档位
@JsonSerializable()
class DepthLevel {
  /// 价格
  final double price;

  /// 数量
  final double quantity;

  /// 累计数量
  final double? cumulative;

  /// 订单数量
  final int? orderCount;

  const DepthLevel({
    required this.price,
    required this.quantity,
    this.cumulative,
    this.orderCount,
  });

  factory DepthLevel.fromJson(Map<String, dynamic> json) =>
      _$DepthLevelFromJson(json);

  Map<String, dynamic> toJson() => _$DepthLevelToJson(this);

  /// 成交额
  double get amount => price * quantity;

  /// 创建副本
  DepthLevel copyWith({
    double? price,
    double? quantity,
    double? cumulative,
    int? orderCount,
  }) {
    return DepthLevel(
      price: price ?? this.price,
      quantity: quantity ?? this.quantity,
      cumulative: cumulative ?? this.cumulative,
      orderCount: orderCount ?? this.orderCount,
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is DepthLevel &&
          runtimeType == other.runtimeType &&
          price == other.price &&
          quantity == other.quantity;

  @override
  int get hashCode => price.hashCode ^ quantity.hashCode;

  @override
  String toString() => 'DepthLevel(price: $price, quantity: $quantity)';
}

/// 深度数据聚合器
class DepthDataAggregator {
  /// 聚合深度数据
  static DepthData aggregate(List<DepthData> dataList) {
    if (dataList.isEmpty) {
      return DepthData(
        bids: [],
        asks: [],
        timestamp: DateTime.now(),
      );
    }

    if (dataList.length == 1) {
      return dataList.first;
    }

    // 按时间戳排序
    final sortedData = List<DepthData>.from(dataList)
      ..sort((a, b) => a.timestamp.compareTo(b.timestamp));

    // 从第一个快照开始，逐步应用增量更新
    DepthData result = sortedData.first;
    for (int i = 1; i < sortedData.length; i++) {
      result = result.merge(sortedData[i]);
    }

    return result;
  }

  /// 计算累计数量
  static List<DepthLevel> calculateCumulative(List<DepthLevel> levels) {
    double cumulative = 0;
    return levels.map((level) {
      cumulative += level.quantity;
      return level.copyWith(cumulative: cumulative);
    }).toList();
  }
}
