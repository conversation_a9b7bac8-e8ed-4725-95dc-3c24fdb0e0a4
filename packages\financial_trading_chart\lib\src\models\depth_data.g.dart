// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'depth_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

DepthData _$DepthDataFromJson(Map<String, dynamic> json) => DepthData(
  bids: (json['bids'] as List<dynamic>)
      .map((e) => DepthLevel.fromJson(e as Map<String, dynamic>))
      .toList(),
  asks: (json['asks'] as List<dynamic>)
      .map((e) => DepthLevel.fromJson(e as Map<String, dynamic>))
      .toList(),
  timestamp: DateTime.parse(json['timestamp'] as String),
  symbol: json['symbol'] as String?,
  market: json['market'] as String?,
  sequence: (json['sequence'] as num?)?.toInt(),
  isSnapshot: json['isSnapshot'] as bool? ?? true,
);

Map<String, dynamic> _$DepthDataToJson(DepthData instance) => <String, dynamic>{
  'bids': instance.bids,
  'asks': instance.asks,
  'timestamp': instance.timestamp.toIso8601String(),
  'symbol': instance.symbol,
  'market': instance.market,
  'sequence': instance.sequence,
  'isSnapshot': instance.isSnapshot,
};

DepthLevel _$DepthLevelFromJson(Map<String, dynamic> json) => DepthLevel(
  price: (json['price'] as num).toDouble(),
  quantity: (json['quantity'] as num).toDouble(),
  cumulative: (json['cumulative'] as num?)?.toDouble(),
  orderCount: (json['orderCount'] as num?)?.toInt(),
);

Map<String, dynamic> _$DepthLevelToJson(DepthLevel instance) =>
    <String, dynamic>{
      'price': instance.price,
      'quantity': instance.quantity,
      'cumulative': instance.cumulative,
      'orderCount': instance.orderCount,
    };
