import 'dart:math' as math;
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

import 'chart_data.dart';

/// 视口信息
///
/// 定义图表的可见区域和缩放状态
@immutable
class ViewportInfo {
  /// 起始X位置 (0.0 - 1.0)
  final double startX;

  /// 结束X位置 (0.0 - 1.0)
  final double endX;

  /// 最小价格
  final double minPrice;

  /// 最大价格
  final double maxPrice;

  /// 缩放级别
  final double zoom;

  /// 起始数据索引
  final int startIndex;

  /// 结束数据索引
  final int endIndex;

  const ViewportInfo({
    required this.startX,
    required this.endX,
    required this.minPrice,
    required this.maxPrice,
    required this.zoom,
    this.startIndex = 0,
    this.endIndex = 0,
  });

  /// 创建默认视口
  factory ViewportInfo.defaultViewport() {
    return const ViewportInfo(
      startX: 0.0,
      endX: 1.0,
      minPrice: 0.0,
      maxPrice: 100.0,
      zoom: 1.0,
    );
  }

  /// 从数据创建视口
  factory ViewportInfo.fromData(List<CandleData> data) {
    if (data.isEmpty) {
      return ViewportInfo.defaultViewport();
    }

    double minPrice = data.first.low;
    double maxPrice = data.first.high;

    for (final candle in data) {
      minPrice = math.min(minPrice, candle.low);
      maxPrice = math.max(maxPrice, candle.high);
    }

    // 添加5%的边距
    final priceRange = maxPrice - minPrice;
    final margin = priceRange * 0.05;

    return ViewportInfo(
      startX: 0.0,
      endX: 1.0,
      minPrice: minPrice - margin,
      maxPrice: maxPrice + margin,
      zoom: 1.0,
      startIndex: 0,
      endIndex: data.length - 1,
    );
  }

  /// 复制并修改视口
  ViewportInfo copyWith({
    double? startX,
    double? endX,
    double? minPrice,
    double? maxPrice,
    double? zoom,
    int? startIndex,
    int? endIndex,
  }) {
    return ViewportInfo(
      startX: startX ?? this.startX,
      endX: endX ?? this.endX,
      minPrice: minPrice ?? this.minPrice,
      maxPrice: maxPrice ?? this.maxPrice,
      zoom: zoom ?? this.zoom,
      startIndex: startIndex ?? this.startIndex,
      endIndex: endIndex ?? this.endIndex,
    );
  }

  /// 价格范围
  double get priceRange => maxPrice - minPrice;

  /// X轴范围
  double get xRange => endX - startX;

  /// 数据索引范围
  int get indexRange => endIndex - startIndex;

  /// 是否包含指定价格
  bool containsPrice(double price) {
    return price >= minPrice && price <= maxPrice;
  }

  /// 是否包含指定X位置
  bool containsX(double x) {
    return x >= startX && x <= endX;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ViewportInfo &&
        other.startX == startX &&
        other.endX == endX &&
        other.minPrice == minPrice &&
        other.maxPrice == maxPrice &&
        other.zoom == zoom &&
        other.startIndex == startIndex &&
        other.endIndex == endIndex;
  }

  @override
  int get hashCode {
    return Object.hash(
      startX,
      endX,
      minPrice,
      maxPrice,
      zoom,
      startIndex,
      endIndex,
    );
  }

  @override
  String toString() {
    return 'ViewportInfo('
        'startX: $startX, '
        'endX: $endX, '
        'minPrice: $minPrice, '
        'maxPrice: $maxPrice, '
        'zoom: $zoom, '
        'startIndex: $startIndex, '
        'endIndex: $endIndex)';
  }
}

/// 技术指标数据
@immutable
class TechnicalIndicator {
  /// 指标唯一标识
  final String key;

  /// 指标类型
  final IndicatorType type;

  /// 指标名称
  final String name;

  /// 指标配置
  final TechnicalIndicatorConfig config;

  /// 指标数据
  final List<double> data;

  /// 是否可见
  final bool visible;

  const TechnicalIndicator({
    required this.key,
    required this.type,
    required this.name,
    required this.config,
    required this.data,
    this.visible = true,
  });

  /// 创建移动平均线指标
  factory TechnicalIndicator.movingAverage({
    required String key,
    required String name,
    required int period,
    required List<double> data,
    TechnicalIndicatorConfig? config,
  }) {
    return TechnicalIndicator(
      key: key,
      type: IndicatorType.movingAverage,
      name: name,
      config: config ?? TechnicalIndicatorConfig.defaultMA(period),
      data: data,
    );
  }

  /// 创建RSI指标
  factory TechnicalIndicator.rsi({
    required String key,
    required int period,
    required List<double> data,
    TechnicalIndicatorConfig? config,
  }) {
    return TechnicalIndicator(
      key: key,
      type: IndicatorType.rsi,
      name: 'RSI($period)',
      config: config ?? TechnicalIndicatorConfig.defaultRSI(period),
      data: data,
    );
  }

  /// 创建MACD指标
  factory TechnicalIndicator.macd({
    required String key,
    required List<double> macdLine,
    required List<double> signalLine,
    required List<double> histogram,
    TechnicalIndicatorConfig? config,
  }) {
    return TechnicalIndicator(
      key: key,
      type: IndicatorType.macd,
      name: 'MACD',
      config: config ?? TechnicalIndicatorConfig.defaultMACD(),
      data: macdLine, // 主要使用MACD线数据
    );
  }

  /// 复制并修改指标
  TechnicalIndicator copyWith({
    String? key,
    IndicatorType? type,
    String? name,
    TechnicalIndicatorConfig? config,
    List<double>? data,
    bool? visible,
  }) {
    return TechnicalIndicator(
      key: key ?? this.key,
      type: type ?? this.type,
      name: name ?? this.name,
      config: config ?? this.config,
      data: data ?? this.data,
      visible: visible ?? this.visible,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TechnicalIndicator &&
        other.key == key &&
        other.type == type &&
        other.name == name &&
        other.config == config &&
        listEquals(other.data, data) &&
        other.visible == visible;
  }

  @override
  int get hashCode {
    return Object.hash(key, type, name, config, Object.hashAll(data), visible);
  }

  @override
  String toString() {
    return 'TechnicalIndicator('
        'key: $key, '
        'type: $type, '
        'name: $name, '
        'dataLength: ${data.length}, '
        'visible: $visible)';
  }
}

/// 指标类型枚举
enum IndicatorType {
  /// 移动平均线
  movingAverage,

  /// 指数移动平均线
  exponentialMovingAverage,

  /// 布林带
  bollingerBands,

  /// 相对强弱指数
  rsi,

  /// MACD
  macd,

  /// KDJ
  kdj,

  /// 成交量
  volume,

  /// 威廉指标
  williams,

  /// 随机指标
  stochastic,
}

/// 技术指标配置
@immutable
class TechnicalIndicatorConfig {
  /// 指标颜色
  final List<Color> colors;

  /// 线宽
  final double lineWidth;

  /// 周期参数
  final Map<String, int> periods;

  /// 其他参数
  final Map<String, dynamic> parameters;

  /// 是否显示在主图
  final bool showOnMainChart;

  /// 显示区域高度比例 (0.0 - 1.0)
  final double heightRatio;

  const TechnicalIndicatorConfig({
    required this.colors,
    this.lineWidth = 1.5,
    this.periods = const {},
    this.parameters = const {},
    this.showOnMainChart = true,
    this.heightRatio = 1.0,
  });

  /// 默认移动平均线配置
  factory TechnicalIndicatorConfig.defaultMA(int period) {
    return TechnicalIndicatorConfig(
      colors: [_getMAColor(period)],
      periods: {'period': period},
    );
  }

  /// 默认RSI配置
  factory TechnicalIndicatorConfig.defaultRSI(int period) {
    return const TechnicalIndicatorConfig(
      colors: [Color(0xFF9C27B0)],
      showOnMainChart: false,
      heightRatio: 0.3,
      parameters: {'overbought': 70.0, 'oversold': 30.0},
    );
  }

  /// 默认MACD配置
  factory TechnicalIndicatorConfig.defaultMACD() {
    return const TechnicalIndicatorConfig(
      colors: [
        Color(0xFF2196F3), // MACD线
        Color(0xFFFF9800), // 信号线
        Color(0xFF4CAF50), // 柱状图
      ],
      showOnMainChart: false,
      heightRatio: 0.3,
      periods: {'fast': 12, 'slow': 26, 'signal': 9},
    );
  }

  /// 获取移动平均线颜色
  static Color _getMAColor(int period) {
    switch (period) {
      case 5:
        return const Color(0xFFFFEB3B);
      case 10:
        return const Color(0xFF9C27B0);
      case 20:
        return const Color(0xFF2196F3);
      case 30:
        return const Color(0xFF4CAF50);
      case 50:
        return const Color(0xFFFF9800);
      case 100:
        return const Color(0xFFF44336);
      case 200:
        return const Color(0xFF795548);
      default:
        return const Color(0xFF607D8B);
    }
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TechnicalIndicatorConfig &&
        listEquals(other.colors, colors) &&
        other.lineWidth == lineWidth &&
        mapEquals(other.periods, periods) &&
        mapEquals(other.parameters, parameters) &&
        other.showOnMainChart == showOnMainChart &&
        other.heightRatio == heightRatio;
  }

  @override
  int get hashCode {
    return Object.hash(
      Object.hashAll(colors),
      lineWidth,
      Object.hashAllUnordered(periods.entries),
      Object.hashAllUnordered(parameters.entries),
      showOnMainChart,
      heightRatio,
    );
  }
}

/// 性能统计数据
@immutable
class PerformanceStats {
  /// 平均渲染时间 (毫秒)
  final double avgRenderTime;

  /// 最大渲染时间 (毫秒)
  final double maxRenderTime;

  /// 平均帧时间 (毫秒)
  final double avgFrameTime;

  /// 帧率 (FPS)
  final double fps;

  /// 内存使用量 (字节)
  final int memoryUsage;

  /// 掉帧次数
  final int frameDrops;

  /// 数据点数量
  final int dataPoints;

  /// 可见数据点数量
  final int visibleDataPoints;

  const PerformanceStats({
    required this.avgRenderTime,
    required this.maxRenderTime,
    required this.avgFrameTime,
    required this.fps,
    required this.memoryUsage,
    required this.frameDrops,
    required this.dataPoints,
    required this.visibleDataPoints,
  });

  /// 创建空的性能统计
  factory PerformanceStats.empty() {
    return const PerformanceStats(
      avgRenderTime: 0.0,
      maxRenderTime: 0.0,
      avgFrameTime: 0.0,
      fps: 0.0,
      memoryUsage: 0,
      frameDrops: 0,
      dataPoints: 0,
      visibleDataPoints: 0,
    );
  }

  /// 性能等级
  PerformanceLevel get performanceLevel {
    if (fps >= 55 && avgRenderTime <= 16 && frameDrops <= 5) {
      return PerformanceLevel.excellent;
    } else if (fps >= 45 && avgRenderTime <= 22 && frameDrops <= 10) {
      return PerformanceLevel.good;
    } else if (fps >= 30 && avgRenderTime <= 33 && frameDrops <= 20) {
      return PerformanceLevel.fair;
    } else {
      return PerformanceLevel.poor;
    }
  }

  /// 是否有性能问题
  bool get hasPerformanceIssues {
    return fps < 45 || avgRenderTime > 22 || frameDrops > 10;
  }

  @override
  String toString() {
    return 'PerformanceStats('
        'fps: ${fps.toStringAsFixed(1)}, '
        'avgRender: ${avgRenderTime.toStringAsFixed(1)}ms, '
        'memory: ${memoryUsage ~/ 1024}KB, '
        'drops: $frameDrops, '
        'level: $performanceLevel)';
  }
}

/// 性能等级
enum PerformanceLevel {
  /// 优秀 (>55FPS, <16ms渲染)
  excellent,

  /// 良好 (>45FPS, <22ms渲染)
  good,

  /// 一般 (>30FPS, <33ms渲染)
  fair,

  /// 较差 (<30FPS, >33ms渲染)
  poor,
}

/// 图表控制器
class HighPerformanceChartController extends ChangeNotifier {
  ViewportInfo _viewport = ViewportInfo.defaultViewport();

  // 组件引用 - 使用dynamic类型避免循环依赖
  dynamic _virtualizer;
  dynamic _updater;
  dynamic _performanceMonitor;
  dynamic _config;

  /// 当前视口信息
  ViewportInfo get viewport => _viewport;

  /// 初始化控制器
  void initialize({
    required dynamic virtualizer,
    required dynamic updater,
    required dynamic performanceMonitor,
    required dynamic config,
  }) {
    _virtualizer = virtualizer;
    _updater = updater;
    _performanceMonitor = performanceMonitor;
    _config = config;
  }

  /// 平移操作
  void pan(double deltaX, double deltaY) {
    if (_config == null) return;

    final sensitivity = 1.0 / _viewport.zoom;
    final newStartX = (_viewport.startX - deltaX * sensitivity).clamp(0.0, 1.0);
    final newEndX = (_viewport.endX - deltaX * sensitivity).clamp(0.0, 1.0);

    if (newStartX >= 0 && newEndX <= 1.0) {
      _updateViewport(_viewport.copyWith(startX: newStartX, endX: newEndX));
    }
  }

  /// 缩放操作
  void zoom(double scaleFactor, Offset focalPoint) {
    if (_config == null) return;

    final newZoom = (_viewport.zoom * scaleFactor).clamp(0.1, 10.0);
    final zoomRatio = newZoom / _viewport.zoom;

    final currentRange = _viewport.endX - _viewport.startX;
    final newRange = (currentRange / zoomRatio).clamp(0.01, 1.0);

    final focalRatio = focalPoint.dx / 1.0; // 假设宽度为1.0
    final newStartX =
        (_viewport.startX + (currentRange - newRange) * focalRatio).clamp(
          0.0,
          1.0 - newRange,
        );
    final newEndX = (newStartX + newRange).clamp(newRange, 1.0);

    _updateViewport(
      _viewport.copyWith(startX: newStartX, endX: newEndX, zoom: newZoom),
    );
  }

  /// 重置视口
  void resetViewport() {
    _updateViewport(ViewportInfo.defaultViewport());
  }

  /// 更新视口
  void _updateViewport(ViewportInfo newViewport) {
    if (_viewport != newViewport) {
      _viewport = newViewport;
      _updateVirtualizer();
      notifyListeners();
    }
  }

  /// 更新虚拟化器
  void _updateVirtualizer() {
    if (_virtualizer == null) return;

    try {
      final totalDataLength = _virtualizer.fullDatasetLength ?? 0;
      final startIndex = (_viewport.startX * totalDataLength).round();
      final endIndex = (_viewport.endX * totalDataLength).round();

      _virtualizer.updateViewport?.call(startIndex, endIndex, _viewport.zoom);
    } catch (e) {
      // 忽略动态调用错误
    }
  }

  /// 获取指定位置的K线数据
  CandleData? getCandleAtPosition(Offset position) {
    // TODO: 实现位置到K线数据的转换
    return null;
  }

  /// 获取指定位置的数据索引
  int getIndexAtPosition(Offset position) {
    // TODO: 实现位置到数据索引的转换
    return 0;
  }

  @override
  void dispose() {
    super.dispose();
  }
}

// 注意：实际的类定义在对应的文件中，这里只是为了避免循环依赖的占位符
