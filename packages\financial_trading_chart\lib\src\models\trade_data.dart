import 'package:json_annotation/json_annotation.dart';

part 'trade_data.g.dart';

/// 交易数据模型
/// 
/// 用于表示单笔交易的详细信息，包括价格、数量、方向等
@JsonSerializable()
class TradeData {
  /// 时间戳
  final DateTime timestamp;

  /// 价格
  final double price;

  /// 数量
  final double quantity;

  /// 交易方向 (buy/sell)
  final String side;

  /// 交易ID
  final String? tradeId;

  /// 交易所或市场标识
  final String? market;

  /// 交易对符号
  final String? symbol;

  /// 交易手续费
  final double? fee;

  /// 手续费币种
  final String? feeCurrency;

  /// 是否为主动交易
  final bool? isTaker;

  /// 额外数据
  final Map<String, dynamic>? extra;

  const TradeData({
    required this.timestamp,
    required this.price,
    required this.quantity,
    required this.side,
    this.tradeId,
    this.market,
    this.symbol,
    this.fee,
    this.feeCurrency,
    this.isTaker,
    this.extra,
  });

  factory TradeData.fromJson(Map<String, dynamic> json) =>
      _$TradeDataFromJson(json);

  Map<String, dynamic> toJson() => _$TradeDataToJson(this);

  /// 是否为买单
  bool get isBuy => side.toLowerCase() == 'buy';

  /// 是否为卖单
  bool get isSell => side.toLowerCase() == 'sell';

  /// 成交额
  double get amount => price * quantity;

  /// 获取交易方向的显示文本
  String get sideDisplay => isBuy ? '买入' : '卖出';

  /// 获取交易类型（主动/被动）
  String get tradeType => isTaker == true ? '主动' : '被动';

  /// 创建副本
  TradeData copyWith({
    DateTime? timestamp,
    double? price,
    double? quantity,
    String? side,
    String? tradeId,
    String? market,
    String? symbol,
    double? fee,
    String? feeCurrency,
    bool? isTaker,
    Map<String, dynamic>? extra,
  }) {
    return TradeData(
      timestamp: timestamp ?? this.timestamp,
      price: price ?? this.price,
      quantity: quantity ?? this.quantity,
      side: side ?? this.side,
      tradeId: tradeId ?? this.tradeId,
      market: market ?? this.market,
      symbol: symbol ?? this.symbol,
      fee: fee ?? this.fee,
      feeCurrency: feeCurrency ?? this.feeCurrency,
      isTaker: isTaker ?? this.isTaker,
      extra: extra ?? this.extra,
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is TradeData &&
          runtimeType == other.runtimeType &&
          timestamp == other.timestamp &&
          price == other.price &&
          quantity == other.quantity &&
          side == other.side &&
          tradeId == other.tradeId;

  @override
  int get hashCode =>
      timestamp.hashCode ^
      price.hashCode ^
      quantity.hashCode ^
      side.hashCode ^
      tradeId.hashCode;

  @override
  String toString() =>
      'TradeData(timestamp: $timestamp, price: $price, quantity: $quantity, side: $side, tradeId: $tradeId)';
}

/// 交易数据列表
class TradeDataList {
  /// 交易数据列表
  final List<TradeData> trades;

  /// 总成交量
  final double totalVolume;

  /// 总成交额
  final double totalAmount;

  /// 时间范围开始
  final DateTime? startTime;

  /// 时间范围结束
  final DateTime? endTime;

  const TradeDataList({
    required this.trades,
    required this.totalVolume,
    required this.totalAmount,
    this.startTime,
    this.endTime,
  });

  /// 从交易列表创建
  factory TradeDataList.fromTrades(List<TradeData> trades) {
    if (trades.isEmpty) {
      return const TradeDataList(
        trades: [],
        totalVolume: 0,
        totalAmount: 0,
      );
    }

    final totalVolume = trades.fold<double>(0, (sum, trade) => sum + trade.quantity);
    final totalAmount = trades.fold<double>(0, (sum, trade) => sum + trade.amount);
    
    final sortedTrades = List<TradeData>.from(trades)
      ..sort((a, b) => a.timestamp.compareTo(b.timestamp));

    return TradeDataList(
      trades: trades,
      totalVolume: totalVolume,
      totalAmount: totalAmount,
      startTime: sortedTrades.first.timestamp,
      endTime: sortedTrades.last.timestamp,
    );
  }

  /// 获取买单列表
  List<TradeData> get buyTrades => trades.where((trade) => trade.isBuy).toList();

  /// 获取卖单列表
  List<TradeData> get sellTrades => trades.where((trade) => trade.isSell).toList();

  /// 买单总量
  double get buyVolume => buyTrades.fold<double>(0, (sum, trade) => sum + trade.quantity);

  /// 卖单总量
  double get sellVolume => sellTrades.fold<double>(0, (sum, trade) => sum + trade.quantity);

  /// 买单总额
  double get buyAmount => buyTrades.fold<double>(0, (sum, trade) => sum + trade.amount);

  /// 卖单总额
  double get sellAmount => sellTrades.fold<double>(0, (sum, trade) => sum + trade.amount);

  /// 平均价格
  double get averagePrice => totalVolume > 0 ? totalAmount / totalVolume : 0;

  /// 最高价
  double get highPrice => trades.isEmpty ? 0 : trades.map((t) => t.price).reduce((a, b) => a > b ? a : b);

  /// 最低价
  double get lowPrice => trades.isEmpty ? 0 : trades.map((t) => t.price).reduce((a, b) => a < b ? a : b);

  /// 是否为空
  bool get isEmpty => trades.isEmpty;

  /// 是否不为空
  bool get isNotEmpty => trades.isNotEmpty;

  /// 交易数量
  int get length => trades.length;
}
