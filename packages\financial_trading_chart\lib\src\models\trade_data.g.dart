// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'trade_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TradeData _$TradeDataFromJson(Map<String, dynamic> json) => TradeData(
  timestamp: DateTime.parse(json['timestamp'] as String),
  price: (json['price'] as num).toDouble(),
  quantity: (json['quantity'] as num).toDouble(),
  side: json['side'] as String,
  tradeId: json['tradeId'] as String?,
  market: json['market'] as String?,
  symbol: json['symbol'] as String?,
  fee: (json['fee'] as num?)?.toDouble(),
  feeCurrency: json['feeCurrency'] as String?,
  isTaker: json['isTaker'] as bool?,
  extra: json['extra'] as Map<String, dynamic>?,
);

Map<String, dynamic> _$TradeDataToJson(TradeData instance) => <String, dynamic>{
  'timestamp': instance.timestamp.toIso8601String(),
  'price': instance.price,
  'quantity': instance.quantity,
  'side': instance.side,
  'tradeId': instance.tradeId,
  'market': instance.market,
  'symbol': instance.symbol,
  'fee': instance.fee,
  'feeCurrency': instance.feeCurrency,
  'isTaker': instance.isTaker,
  'extra': instance.extra,
};
