/// TradingView图表数据适配器
/// 
/// 支持K线图和折线图两种数据格式
library trading_view_data;

/// TradingView图表数据类型
enum TradingViewDataType {
  candlestick, // K线图：time, open, high, low, close
  line,        // 折线图：time, value
}

/// TradingView数据基类
abstract class TradingViewData {
  /// TradingView使用的time字段
  final String time;
  
  /// 数据类型
  final TradingViewDataType type;
  
  /// 内部时间戳（用于排序和处理）
  final DateTime timestamp;

  const TradingViewData({
    required this.time,
    required this.type,
    required this.timestamp,
  });

  /// 转换为TradingView格式的Map
  Map<String, dynamic> toTradingViewFormat();
  
  /// 获取主要数值（K线图用close，折线图用value）
  double get primaryValue;
}

/// TradingView K线图数据
class TradingViewCandlestickData extends TradingViewData {
  final double open;
  final double high;
  final double low;
  final double close;

  const TradingViewCandlestickData({
    required super.time,
    required super.timestamp,
    required this.open,
    required this.high,
    required this.low,
    required this.close,
  }) : super(type: TradingViewDataType.candlestick);

  /// 工厂方法：从时间戳创建
  factory TradingViewCandlestickData.fromTimestamp({
    required int timestampMs,
    required double open,
    required double high,
    required double low,
    required double close,
    String? timeFrame,
  }) {
    final timestamp = DateTime.fromMillisecondsSinceEpoch(timestampMs);
    final time = TradingViewDataUtils.formatTimeForTradingView(timestampMs, timeFrame);
    
    return TradingViewCandlestickData(
      time: time,
      timestamp: timestamp,
      open: open,
      high: high,
      low: low,
      close: close,
    );
  }

  /// 从现有的KLineData创建
  factory TradingViewCandlestickData.fromKLineData(dynamic klineData, {String? timeFrame}) {
    // 支持多种KLineData格式
    if (klineData is Map<String, dynamic>) {
      return TradingViewCandlestickData.fromTimestamp(
        timestampMs: klineData['timestamp'] ?? klineData['time'] ?? klineData['openTime'],
        open: (klineData['open'] as num).toDouble(),
        high: (klineData['high'] ?? klineData['hight'] as num).toDouble(), // 兼容拼写错误
        low: (klineData['low'] as num).toDouble(),
        close: (klineData['close'] as num).toDouble(),
        timeFrame: timeFrame,
      );
    }
    
    // 如果是自定义对象，使用反射或者具体的属性访问
    final timestampMs = _getTimestamp(klineData);
    final open = _getDouble(klineData, ['open']);
    final high = _getDouble(klineData, ['high', 'hight']); // 兼容拼写错误
    final low = _getDouble(klineData, ['low']);
    final close = _getDouble(klineData, ['close']);
    
    return TradingViewCandlestickData.fromTimestamp(
      timestampMs: timestampMs,
      open: open,
      high: high,
      low: low,
      close: close,
      timeFrame: timeFrame,
    );
  }

  @override
  Map<String, dynamic> toTradingViewFormat() {
    return {
      'time': time,
      'open': open,
      'high': high,
      'low': low,
      'close': close,
    };
  }

  @override
  double get primaryValue => close;

  /// 是否为阳线
  bool get isBullish => close >= open;

  /// 是否为阴线
  bool get isBearish => close < open;

  @override
  String toString() {
    return 'TradingViewCandlestickData(time: $time, open: $open, high: $high, low: $low, close: $close)';
  }
}

/// TradingView 折线图数据
class TradingViewLineData extends TradingViewData {
  final double value;

  const TradingViewLineData({
    required super.time,
    required super.timestamp,
    required this.value,
  }) : super(type: TradingViewDataType.line);

  /// 工厂方法：从时间戳创建
  factory TradingViewLineData.fromTimestamp({
    required int timestampMs,
    required double value,
    String? timeFrame,
  }) {
    final timestamp = DateTime.fromMillisecondsSinceEpoch(timestampMs);
    final time = TradingViewDataUtils.formatTimeForTradingView(timestampMs, timeFrame);
    
    return TradingViewLineData(
      time: time,
      timestamp: timestamp,
      value: value,
    );
  }

  /// 从K线数据创建折线数据（使用收盘价）
  factory TradingViewLineData.fromCandlestickData(TradingViewCandlestickData candlestickData) {
    return TradingViewLineData(
      time: candlestickData.time,
      timestamp: candlestickData.timestamp,
      value: candlestickData.close,
    );
  }

  @override
  Map<String, dynamic> toTradingViewFormat() {
    return {
      'time': time,
      'value': value,
    };
  }

  @override
  double get primaryValue => value;

  @override
  String toString() {
    return 'TradingViewLineData(time: $time, value: $value)';
  }
}

/// TradingView数据工具类
class TradingViewDataUtils {
  /// 为TradingView格式化时间
  static String formatTimeForTradingView(int timestampMs, String? timeFrame) {
    final dateTime = DateTime.fromMillisecondsSinceEpoch(timestampMs);
    
    switch (timeFrame) {
      case '1s':
      case '1m':
      case '5m':
      case '15m':
      case '30m':
      case '1h':
      case '4h':
        // 对于分钟/小时级别，使用时间戳（秒）
        return (timestampMs ~/ 1000).toString();
      case '1d':
      case '1w':
      case '1M':
        // 对于日/周/月级别，使用日期字符串
        return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')}';
      default:
        // 默认使用时间戳
        return (timestampMs ~/ 1000).toString();
    }
  }

  /// 批量转换K线数据为TradingView格式
  static List<TradingViewCandlestickData> convertToCandlestickData(
    List<dynamic> klineDataList, {
    String? timeFrame,
  }) {
    return klineDataList.map((data) => 
      TradingViewCandlestickData.fromKLineData(data, timeFrame: timeFrame)
    ).toList();
  }

  /// 批量转换为折线数据
  static List<TradingViewLineData> convertToLineData(
    List<dynamic> dataList, {
    String? timeFrame,
    String valueField = 'close', // 默认使用收盘价
  }) {
    return dataList.map((data) {
      final timestampMs = _getTimestamp(data);
      final value = _getDouble(data, [valueField, 'value']);
      
      return TradingViewLineData.fromTimestamp(
        timestampMs: timestampMs,
        value: value,
        timeFrame: timeFrame,
      );
    }).toList();
  }

  /// K线数据转折线数据
  static List<TradingViewLineData> candlestickToLineData(
    List<TradingViewCandlestickData> candlestickData
  ) {
    return candlestickData.map((data) => 
      TradingViewLineData.fromCandlestickData(data)
    ).toList();
  }

  /// 验证TradingView数据格式
  static bool validateTradingViewFormat(Map<String, dynamic> data, TradingViewDataType type) {
    switch (type) {
      case TradingViewDataType.candlestick:
        return data.containsKey('time') &&
               data.containsKey('open') &&
               data.containsKey('high') &&
               data.containsKey('low') &&
               data.containsKey('close');
      case TradingViewDataType.line:
        return data.containsKey('time') &&
               data.containsKey('value');
    }
  }

  /// 获取数据的主要价格值
  static double getPrimaryValue(TradingViewData data) {
    return data.primaryValue;
  }

  /// 按时间排序数据
  static List<T> sortByTime<T extends TradingViewData>(List<T> data) {
    final sortedData = List<T>.from(data);
    sortedData.sort((a, b) => a.timestamp.compareTo(b.timestamp));
    return sortedData;
  }
}

/// 辅助函数：获取时间戳
int _getTimestamp(dynamic data) {
  if (data is Map<String, dynamic>) {
    return data['timestamp'] ?? data['time'] ?? data['openTime'] ?? 0;
  }
  
  // 尝试通过反射获取时间戳
  try {
    if (data.timestamp != null) {
      if (data.timestamp is DateTime) {
        return (data.timestamp as DateTime).millisecondsSinceEpoch;
      }
      return data.timestamp as int;
    }
    if (data.time != null) {
      return data.time as int;
    }
    if (data.openTime != null) {
      return data.openTime as int;
    }
  } catch (e) {
    // 忽略反射错误
  }
  
  return DateTime.now().millisecondsSinceEpoch;
}

/// 辅助函数：获取double值
double _getDouble(dynamic data, List<String> fieldNames) {
  if (data is Map<String, dynamic>) {
    for (final fieldName in fieldNames) {
      if (data.containsKey(fieldName)) {
        return (data[fieldName] as num).toDouble();
      }
    }
    return 0.0;
  }
  
  // 尝试通过反射获取值
  try {
    for (final fieldName in fieldNames) {
      final value = _getFieldValue(data, fieldName);
      if (value != null) {
        return (value as num).toDouble();
      }
    }
  } catch (e) {
    // 忽略反射错误
  }
  
  return 0.0;
}

/// 辅助函数：获取字段值
dynamic _getFieldValue(dynamic object, String fieldName) {
  try {
    switch (fieldName) {
      case 'open': return object.open;
      case 'high': return object.high;
      case 'hight': return object.hight; // 兼容拼写错误
      case 'low': return object.low;
      case 'close': return object.close;
      case 'value': return object.value;
      case 'volume': return object.volume;
      case 'timestamp': return object.timestamp;
      case 'time': return object.time;
      case 'openTime': return object.openTime;
      default: return null;
    }
  } catch (e) {
    return null;
  }
}
