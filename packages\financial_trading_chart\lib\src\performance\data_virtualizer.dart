import 'dart:math' as math;
import 'package:flutter/foundation.dart';

import '../models/chart_data.dart';
import '../utils/performance_utils.dart';

/// 数据虚拟化管理器
/// 
/// 负责管理大数据集的虚拟化显示，只处理可见区域的数据
/// 特性：
/// - 视口裁剪：只处理可见数据
/// - 智能抽样：大数据集自动降采样
/// - 缓冲区管理：预加载周边数据
/// - 指标缓存：技术指标结果缓存
class ChartDataVirtualizer {
  /// 最大可见数据点数量
  final int maxVisiblePoints;
  
  /// 缓冲区大小
  final int bufferSize;
  
  // 数据存储
  List<CandleData> _fullDataset = [];
  List<CandleData> _virtualizedData = [];
  final Map<String, List<double>> _indicatorCache = {};
  
  // 视口状态
  int _currentStartIndex = 0;
  int _currentEndIndex = 0;
  double _currentZoomLevel = 1.0;
  
  // 性能监控
  final Stopwatch _updateStopwatch = Stopwatch();
  int _updateCount = 0;
  
  ChartDataVirtualizer({
    this.maxVisiblePoints = 2000,
    this.bufferSize = 500,
  });
  
  /// 完整数据集长度
  int get fullDatasetLength => _fullDataset.length;
  
  /// 虚拟化数据长度
  int get virtualizedDataLength => _virtualizedData.length;
  
  /// 当前视口范围
  ViewportRange get currentRange => ViewportRange(
    startIndex: _currentStartIndex,
    endIndex: _currentEndIndex,
    zoomLevel: _currentZoomLevel,
  );
  
  /// 设置完整数据集
  void setFullDataset(List<CandleData> dataset) {
    if (listEquals(_fullDataset, dataset)) return;
    
    _fullDataset = List.from(dataset);
    _updateVirtualizedData();
    
    if (kDebugMode) {
      debugPrint('DataVirtualizer: Set dataset with ${dataset.length} points');
    }
  }
  
  /// 添加新数据
  void addData(List<CandleData> newData) {
    if (newData.isEmpty) return;
    
    _fullDataset.addAll(newData);
    _updateVirtualizedData();
    
    if (kDebugMode) {
      debugPrint('DataVirtualizer: Added ${newData.length} new points');
    }
  }
  
  /// 更新最后一个数据点
  void updateLastData(CandleData updatedData) {
    if (_fullDataset.isEmpty) return;
    
    _fullDataset[_fullDataset.length - 1] = updatedData;
    
    // 如果最后一个数据在虚拟化范围内，更新虚拟化数据
    if (_virtualizedData.isNotEmpty && 
        _currentEndIndex >= _fullDataset.length - 1) {
      final virtualizedIndex = _virtualizedData.length - 1;
      if (virtualizedIndex >= 0) {
        _virtualizedData[virtualizedIndex] = updatedData;
      }
    }
  }
  
  /// 更新视口
  void updateViewport(int startIndex, int endIndex, double zoomLevel) {
    if (!_shouldUpdateVirtualizedData(startIndex, endIndex, zoomLevel)) {
      return;
    }
    
    _updateStopwatch.start();
    
    _currentStartIndex = startIndex;
    _currentEndIndex = endIndex;
    _currentZoomLevel = zoomLevel;
    
    _updateVirtualizedData();
    
    _updateStopwatch.stop();
    _updateCount++;
    
    if (kDebugMode && _updateStopwatch.elapsedMilliseconds > 10) {
      debugPrint('DataVirtualizer: Viewport update took ${_updateStopwatch.elapsedMilliseconds}ms');
    }
    
    _updateStopwatch.reset();
  }
  
  /// 获取虚拟化数据
  List<CandleData> getVirtualizedData() {
    return _virtualizedData;
  }
  
  /// 获取指标数据
  List<double>? getIndicatorData(String indicatorKey) {
    return _indicatorCache[indicatorKey];
  }
  
  /// 设置指标数据
  void setIndicatorData(String indicatorKey, List<double> data) {
    _indicatorCache[indicatorKey] = List.from(data);
  }
  
  /// 清除指标缓存
  void clearIndicatorCache() {
    _indicatorCache.clear();
  }
  
  /// 获取指定范围的数据
  List<CandleData> getDataInRange(int startIndex, int endIndex) {
    if (_fullDataset.isEmpty) return [];
    
    final safeStartIndex = math.max(0, startIndex);
    final safeEndIndex = math.min(_fullDataset.length, endIndex + 1);
    
    if (safeStartIndex >= safeEndIndex) return [];
    
    return _fullDataset.sublist(safeStartIndex, safeEndIndex);
  }
  
  /// 判断是否需要更新虚拟化数据
  bool _shouldUpdateVirtualizedData(int startIndex, int endIndex, double zoomLevel) {
    final currentRange = _currentEndIndex - _currentStartIndex;
    final newRange = endIndex - startIndex;
    
    // 检查各种更新条件
    return (startIndex < _currentStartIndex - bufferSize) ||
           (endIndex > _currentEndIndex + bufferSize) ||
           (newRange - currentRange).abs() > maxVisiblePoints * 0.1 ||
           (zoomLevel - _currentZoomLevel).abs() > 0.1;
  }
  
  /// 更新虚拟化数据
  void _updateVirtualizedData() {
    if (_fullDataset.isEmpty) {
      _virtualizedData = [];
      return;
    }
    
    // 计算虚拟化范围（包含缓冲区）
    final bufferStart = math.max(0, _currentStartIndex - bufferSize);
    final bufferEnd = math.min(_fullDataset.length, _currentEndIndex + bufferSize);
    
    // 数据抽样策略
    if (bufferEnd - bufferStart > maxVisiblePoints) {
      _virtualizedData = _sampleData(bufferStart, bufferEnd);
    } else {
      _virtualizedData = _fullDataset.sublist(bufferStart, bufferEnd);
    }
    
    // 更新指标缓存
    _updateIndicatorCache();
    
    if (kDebugMode) {
      debugPrint('DataVirtualizer: Updated virtualized data '
                 '(${_virtualizedData.length} points from ${_fullDataset.length})');
    }
  }
  
  /// 数据抽样
  List<CandleData> _sampleData(int start, int end) {
    final totalPoints = end - start;
    final sampleRatio = totalPoints / maxVisiblePoints;
    final sampledData = <CandleData>[];
    
    // 使用最大值-最小值抽样算法，保持重要的价格信息
    for (int i = 0; i < maxVisiblePoints; i++) {
      final sourceIndex = start + (i * sampleRatio).round();
      if (sourceIndex < end && sourceIndex < _fullDataset.length) {
        sampledData.add(_fullDataset[sourceIndex]);
      }
    }
    
    return sampledData;
  }
  
  /// 高级抽样算法（保持价格极值）
  List<CandleData> _advancedSampleData(int start, int end) {
    final totalPoints = end - start;
    if (totalPoints <= maxVisiblePoints) {
      return _fullDataset.sublist(start, end);
    }
    
    final sampledData = <CandleData>[];
    final bucketSize = totalPoints / maxVisiblePoints;
    
    for (int i = 0; i < maxVisiblePoints; i++) {
      final bucketStart = start + (i * bucketSize).floor();
      final bucketEnd = math.min(start + ((i + 1) * bucketSize).floor(), end);
      
      if (bucketStart >= bucketEnd) continue;
      
      // 在每个桶中选择最具代表性的数据点
      final bucketData = _fullDataset.sublist(bucketStart, bucketEnd);
      final representativeData = _selectRepresentativeData(bucketData);
      
      if (representativeData != null) {
        sampledData.add(representativeData);
      }
    }
    
    return sampledData;
  }
  
  /// 选择最具代表性的数据点
  CandleData? _selectRepresentativeData(List<CandleData> bucketData) {
    if (bucketData.isEmpty) return null;
    if (bucketData.length == 1) return bucketData.first;
    
    // 选择价格波动最大的数据点
    CandleData? maxVolatilityData;
    double maxVolatility = 0;
    
    for (final data in bucketData) {
      final volatility = (data.high - data.low) / data.close;
      if (volatility > maxVolatility) {
        maxVolatility = volatility;
        maxVolatilityData = data;
      }
    }
    
    return maxVolatilityData ?? bucketData.first;
  }
  
  /// 更新指标缓存
  void _updateIndicatorCache() {
    if (_indicatorCache.isEmpty) return;
    
    // 重新计算所有指标的虚拟化数据
    final newIndicatorCache = <String, List<double>>{};
    
    for (final entry in _indicatorCache.entries) {
      final indicatorKey = entry.key;
      final fullIndicatorData = entry.value;
      
      // 根据虚拟化数据范围提取对应的指标数据
      final virtualizedIndicatorData = _extractIndicatorDataForVirtualizedRange(
        fullIndicatorData,
        _currentStartIndex - bufferSize,
        _currentEndIndex + bufferSize,
      );
      
      newIndicatorCache[indicatorKey] = virtualizedIndicatorData;
    }
    
    _indicatorCache.clear();
    _indicatorCache.addAll(newIndicatorCache);
  }
  
  /// 为虚拟化范围提取指标数据
  List<double> _extractIndicatorDataForVirtualizedRange(
    List<double> fullData,
    int startIndex,
    int endIndex,
  ) {
    if (fullData.isEmpty) return [];
    
    final safeStartIndex = math.max(0, startIndex);
    final safeEndIndex = math.min(fullData.length, endIndex);
    
    if (safeStartIndex >= safeEndIndex) return [];
    
    return fullData.sublist(safeStartIndex, safeEndIndex);
  }
  
  /// 获取性能统计
  VirtualizerStats getPerformanceStats() {
    return VirtualizerStats(
      fullDatasetSize: _fullDataset.length,
      virtualizedDataSize: _virtualizedData.length,
      indicatorCacheSize: _indicatorCache.length,
      updateCount: _updateCount,
      compressionRatio: _fullDataset.isEmpty 
          ? 0.0 
          : _virtualizedData.length / _fullDataset.length,
    );
  }
  
  /// 清理资源
  void dispose() {
    _fullDataset.clear();
    _virtualizedData.clear();
    _indicatorCache.clear();
  }
}

/// 视口范围
@immutable
class ViewportRange {
  final int startIndex;
  final int endIndex;
  final double zoomLevel;
  
  const ViewportRange({
    required this.startIndex,
    required this.endIndex,
    required this.zoomLevel,
  });
  
  int get length => endIndex - startIndex;
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ViewportRange &&
           other.startIndex == startIndex &&
           other.endIndex == endIndex &&
           other.zoomLevel == zoomLevel;
  }
  
  @override
  int get hashCode => Object.hash(startIndex, endIndex, zoomLevel);
  
  @override
  String toString() {
    return 'ViewportRange(start: $startIndex, end: $endIndex, zoom: $zoomLevel)';
  }
}

/// 虚拟化器性能统计
@immutable
class VirtualizerStats {
  final int fullDatasetSize;
  final int virtualizedDataSize;
  final int indicatorCacheSize;
  final int updateCount;
  final double compressionRatio;
  
  const VirtualizerStats({
    required this.fullDatasetSize,
    required this.virtualizedDataSize,
    required this.indicatorCacheSize,
    required this.updateCount,
    required this.compressionRatio,
  });
  
  /// 内存节省百分比
  double get memorySavingPercentage => (1.0 - compressionRatio) * 100;
  
  @override
  String toString() {
    return 'VirtualizerStats('
           'full: $fullDatasetSize, '
           'virtualized: $virtualizedDataSize, '
           'indicators: $indicatorCacheSize, '
           'updates: $updateCount, '
           'compression: ${(compressionRatio * 100).toStringAsFixed(1)}%, '
           'memorySaving: ${memorySavingPercentage.toStringAsFixed(1)}%)';
  }
}
