import 'dart:collection';
import 'dart:ui' as ui;
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

/// 内存管理器
///
/// 负责管理图表渲染过程中的内存使用
/// 特性：
/// - 对象池：复用Paint、Path等对象
/// - 缓存管理：LRU缓存策略
/// - 内存监控：实时内存使用跟踪
/// - 垃圾回收：智能清理过期资源
class MemoryManager {
  /// 最大缓存大小 (50MB)
  static const int maxCacheSize = 50 * 1024 * 1024;

  /// 最大对象池大小
  static const int maxObjectPoolSize = 1000;

  // 对象池
  final Queue<Paint> _paintPool = Queue<Paint>();
  final Queue<Path> _pathPool = Queue<Path>();
  final Queue<Rect> _rectPool = Queue<Rect>();
  final Queue<TextPainter> _textPainterPool = Queue<TextPainter>();

  // 渲染缓存
  final Map<String, CacheEntry> _renderCache = {};
  int _currentCacheSize = 0;

  // 性能监控
  int _paintPoolHits = 0;
  int _paintPoolMisses = 0;
  int _pathPoolHits = 0;
  int _pathPoolMisses = 0;
  int _cacheHits = 0;
  int _cacheMisses = 0;

  // 单例模式
  static final MemoryManager _instance = MemoryManager._internal();
  factory MemoryManager() => _instance;
  MemoryManager._internal();

  /// 获取Paint对象
  Paint getPaint() {
    if (_paintPool.isNotEmpty) {
      _paintPoolHits++;
      final paint = _paintPool.removeFirst();
      _resetPaint(paint);
      return paint;
    }

    _paintPoolMisses++;
    return Paint();
  }

  /// 归还Paint对象
  void returnPaint(Paint paint) {
    if (_paintPool.length < maxObjectPoolSize) {
      _paintPool.add(paint);
    }
  }

  /// 获取Path对象
  Path getPath() {
    if (_pathPool.isNotEmpty) {
      _pathPoolHits++;
      final path = _pathPool.removeFirst();
      path.reset();
      return path;
    }

    _pathPoolMisses++;
    return Path();
  }

  /// 归还Path对象
  void returnPath(Path path) {
    if (_pathPool.length < maxObjectPoolSize) {
      _pathPool.add(path);
    }
  }

  /// 获取Rect对象
  Rect getRect(double left, double top, double right, double bottom) {
    // Rect是不可变对象，直接创建
    return Rect.fromLTRB(left, top, right, bottom);
  }

  /// 获取TextPainter对象
  TextPainter getTextPainter() {
    if (_textPainterPool.isNotEmpty) {
      final textPainter = _textPainterPool.removeFirst();
      _resetTextPainter(textPainter);
      return textPainter;
    }

    return TextPainter(textDirection: TextDirection.ltr);
  }

  /// 归还TextPainter对象
  void returnTextPainter(TextPainter textPainter) {
    if (_textPainterPool.length < maxObjectPoolSize) {
      _textPainterPool.add(textPainter);
    }
  }

  /// 缓存渲染结果
  void cacheRender(String key, ui.Picture picture, int sizeBytes) {
    // 检查缓存大小
    if (_currentCacheSize + sizeBytes > maxCacheSize) {
      _evictCache(sizeBytes);
    }

    _renderCache[key] = CacheEntry(
      picture: picture,
      sizeBytes: sizeBytes,
      lastAccessed: DateTime.now(),
    );

    _currentCacheSize += sizeBytes;
  }

  /// 获取缓存的渲染结果
  ui.Picture? getCachedRender(String key) {
    final entry = _renderCache[key];
    if (entry != null) {
      _cacheHits++;
      entry.lastAccessed = DateTime.now();
      return entry.picture;
    }

    _cacheMisses++;
    return null;
  }

  /// 移除缓存
  void removeCachedRender(String key) {
    final entry = _renderCache.remove(key);
    if (entry != null) {
      _currentCacheSize -= entry.sizeBytes;
    }
  }

  /// 缓存淘汰 (LRU策略)
  void _evictCache(int requiredSpace) {
    final entries = _renderCache.entries.toList();

    // 按最后访问时间排序
    entries.sort(
      (a, b) => a.value.lastAccessed.compareTo(b.value.lastAccessed),
    );

    int freedSpace = 0;
    for (final entry in entries) {
      _renderCache.remove(entry.key);
      _currentCacheSize -= entry.value.sizeBytes;
      freedSpace += entry.value.sizeBytes;

      if (freedSpace >= requiredSpace) {
        break;
      }
    }

    if (kDebugMode) {
      debugPrint('MemoryManager: Evicted ${freedSpace ~/ 1024}KB from cache');
    }
  }

  /// 重置Paint对象
  void _resetPaint(Paint paint) {
    // 重置Paint属性到默认值
    paint.color = const Color(0xFF000000);
    paint.strokeWidth = 0.0;
    paint.style = PaintingStyle.fill;
    paint.strokeCap = StrokeCap.butt;
    paint.strokeJoin = StrokeJoin.miter;
    paint.isAntiAlias = true;
    paint.filterQuality = FilterQuality.none;
  }

  /// 重置TextPainter对象
  void _resetTextPainter(TextPainter textPainter) {
    textPainter.text = null;
  }

  /// 清理所有缓存和对象池
  void cleanup() {
    // 清理对象池
    _paintPool.clear();
    _pathPool.clear();
    _rectPool.clear();
    _textPainterPool.clear();

    // 清理渲染缓存
    _renderCache.clear();
    _currentCacheSize = 0;

    // 重置统计
    _paintPoolHits = 0;
    _paintPoolMisses = 0;
    _pathPoolHits = 0;
    _pathPoolMisses = 0;
    _cacheHits = 0;
    _cacheMisses = 0;

    if (kDebugMode) {
      debugPrint('MemoryManager: Cleanup completed');
    }
  }

  /// 强制垃圾回收
  void forceGarbageCollection() {
    // 清理过期缓存
    final now = DateTime.now();
    final expiredKeys = <String>[];

    for (final entry in _renderCache.entries) {
      final age = now.difference(entry.value.lastAccessed);
      if (age.inMinutes > 5) {
        // 5分钟未访问的缓存
        expiredKeys.add(entry.key);
      }
    }

    for (final key in expiredKeys) {
      removeCachedRender(key);
    }

    if (kDebugMode && expiredKeys.isNotEmpty) {
      debugPrint(
        'MemoryManager: Removed ${expiredKeys.length} expired cache entries',
      );
    }
  }

  /// 获取内存使用情况
  MemoryUsage getMemoryUsage() {
    return MemoryUsage(
      cacheSize: _currentCacheSize,
      paintPoolSize: _paintPool.length,
      pathPoolSize: _pathPool.length,
      rectPoolSize: _rectPool.length,
      textPainterPoolSize: _textPainterPool.length,
      renderCacheEntries: _renderCache.length,
    );
  }

  /// 获取性能统计
  MemoryPerformanceStats getPerformanceStats() {
    final totalPaintRequests = _paintPoolHits + _paintPoolMisses;
    final totalPathRequests = _pathPoolHits + _pathPoolMisses;
    final totalCacheRequests = _cacheHits + _cacheMisses;

    return MemoryPerformanceStats(
      paintPoolHitRate: totalPaintRequests > 0
          ? _paintPoolHits / totalPaintRequests
          : 0.0,
      pathPoolHitRate: totalPathRequests > 0
          ? _pathPoolHits / totalPathRequests
          : 0.0,
      cacheHitRate: totalCacheRequests > 0
          ? _cacheHits / totalCacheRequests
          : 0.0,
      totalPaintRequests: totalPaintRequests,
      totalPathRequests: totalPathRequests,
      totalCacheRequests: totalCacheRequests,
    );
  }

  /// 优化建议
  List<String> getOptimizationSuggestions() {
    final suggestions = <String>[];
    final stats = getPerformanceStats();
    final usage = getMemoryUsage();

    // 对象池命中率建议
    if (stats.paintPoolHitRate < 0.8) {
      suggestions.add(
        'Paint对象池命中率较低 (${(stats.paintPoolHitRate * 100).toStringAsFixed(1)}%)，考虑增加池大小',
      );
    }

    if (stats.pathPoolHitRate < 0.8) {
      suggestions.add(
        'Path对象池命中率较低 (${(stats.pathPoolHitRate * 100).toStringAsFixed(1)}%)，考虑增加池大小',
      );
    }

    // 缓存命中率建议
    if (stats.cacheHitRate < 0.5) {
      suggestions.add(
        '渲染缓存命中率较低 (${(stats.cacheHitRate * 100).toStringAsFixed(1)}%)，考虑优化缓存策略',
      );
    }

    // 内存使用建议
    if (usage.cacheSize > maxCacheSize * 0.8) {
      suggestions.add('缓存使用量较高 (${usage.cacheSize ~/ 1024}KB)，考虑清理过期缓存');
    }

    if (usage.paintPoolSize > maxObjectPoolSize * 0.8) {
      suggestions.add('Paint对象池接近满载，考虑增加池大小或优化对象归还');
    }

    return suggestions;
  }
}

/// 缓存条目
class CacheEntry {
  final ui.Picture picture;
  final int sizeBytes;
  DateTime lastAccessed;

  CacheEntry({
    required this.picture,
    required this.sizeBytes,
    required this.lastAccessed,
  });
}

/// 内存使用情况
@immutable
class MemoryUsage {
  final int cacheSize;
  final int paintPoolSize;
  final int pathPoolSize;
  final int rectPoolSize;
  final int textPainterPoolSize;
  final int renderCacheEntries;

  const MemoryUsage({
    required this.cacheSize,
    required this.paintPoolSize,
    required this.pathPoolSize,
    required this.rectPoolSize,
    required this.textPainterPoolSize,
    required this.renderCacheEntries,
  });

  /// 总内存使用量估算 (字节)
  int get totalMemoryUsage {
    // 估算各种对象的内存使用
    const paintObjectSize = 100; // 估算Paint对象大小
    const pathObjectSize = 200; // 估算Path对象大小
    const textPainterObjectSize = 300; // 估算TextPainter对象大小

    return cacheSize +
        (paintPoolSize * paintObjectSize) +
        (pathPoolSize * pathObjectSize) +
        (textPainterPoolSize * textPainterObjectSize);
  }

  @override
  String toString() {
    return 'MemoryUsage('
        'cache: ${cacheSize ~/ 1024}KB, '
        'paintPool: $paintPoolSize, '
        'pathPool: $pathPoolSize, '
        'textPainterPool: $textPainterPoolSize, '
        'cacheEntries: $renderCacheEntries, '
        'total: ${totalMemoryUsage ~/ 1024}KB)';
  }
}

/// 内存性能统计
@immutable
class MemoryPerformanceStats {
  final double paintPoolHitRate;
  final double pathPoolHitRate;
  final double cacheHitRate;
  final int totalPaintRequests;
  final int totalPathRequests;
  final int totalCacheRequests;

  const MemoryPerformanceStats({
    required this.paintPoolHitRate,
    required this.pathPoolHitRate,
    required this.cacheHitRate,
    required this.totalPaintRequests,
    required this.totalPathRequests,
    required this.totalCacheRequests,
  });

  /// 整体性能评分 (0-100)
  double get overallScore {
    final paintScore = paintPoolHitRate * 100;
    final pathScore = pathPoolHitRate * 100;
    final cacheScore = cacheHitRate * 100;

    return (paintScore + pathScore + cacheScore) / 3;
  }

  /// 性能等级
  PerformanceLevel get performanceLevel {
    final score = overallScore;
    if (score >= 90) return PerformanceLevel.excellent;
    if (score >= 75) return PerformanceLevel.good;
    if (score >= 60) return PerformanceLevel.fair;
    return PerformanceLevel.poor;
  }

  @override
  String toString() {
    return 'MemoryPerformanceStats('
        'paintHitRate: ${(paintPoolHitRate * 100).toStringAsFixed(1)}%, '
        'pathHitRate: ${(pathPoolHitRate * 100).toStringAsFixed(1)}%, '
        'cacheHitRate: ${(cacheHitRate * 100).toStringAsFixed(1)}%, '
        'score: ${overallScore.toStringAsFixed(1)}, '
        'level: $performanceLevel)';
  }
}

/// 性能等级
enum PerformanceLevel { excellent, good, fair, poor }

/// 内存管理器扩展 - 智能预加载
extension MemoryManagerPreloading on MemoryManager {
  /// 预热对象池
  void warmupObjectPools() {
    // 预创建一些常用对象
    final paints = <Paint>[];
    final paths = <Path>[];
    final textPainters = <TextPainter>[];

    // 创建初始对象
    for (int i = 0; i < 50; i++) {
      paints.add(Paint());
      paths.add(Path());
      textPainters.add(TextPainter(textDirection: TextDirection.ltr));
    }

    // 添加到对象池
    for (final paint in paints) {
      returnPaint(paint);
    }
    for (final path in paths) {
      returnPath(path);
    }
    for (final textPainter in textPainters) {
      returnTextPainter(textPainter);
    }

    if (kDebugMode) {
      debugPrint('MemoryManager: Object pools warmed up');
    }
  }

  /// 智能缓存预加载
  void preloadCache(List<String> cacheKeys) {
    // TODO: 实现智能缓存预加载逻辑
    // 根据使用模式预加载可能需要的缓存项
  }
}
