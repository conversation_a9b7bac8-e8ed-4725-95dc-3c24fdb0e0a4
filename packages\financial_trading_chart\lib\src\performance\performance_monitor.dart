import 'dart:async';
import 'dart:math' as math;
import 'package:flutter/foundation.dart';

import '../models/performance_data.dart';
import 'memory_manager.dart';

/// 性能监控器
/// 
/// 负责监控图表的实时性能指标
/// 特性：
/// - FPS监控：实时帧率统计
/// - 渲染时间：绘制性能监控
/// - 内存使用：内存占用跟踪
/// - 性能告警：异常性能检测
class PerformanceMonitor {
  /// 最大样本数量
  static const int maxSamples = 100;
  
  /// 监控间隔
  static const Duration monitorInterval = Duration(seconds: 1);
  
  // 性能数据
  final List<double> _renderTimes = [];
  final List<double> _frameTimes = [];
  final List<int> _memoryUsage = [];
  final List<double> _fpsHistory = [];
  
  // 监控状态
  Timer? _monitorTimer;
  Stopwatch? _frameStopwatch;
  int _frameCount = 0;
  int _totalFrames = 0;
  
  // 性能统计流
  final StreamController<PerformanceStats> _statsController = 
      StreamController<PerformanceStats>.broadcast();
  
  // 告警回调
  final List<Function(PerformanceAlert)> _alertCallbacks = [];
  
  /// 性能统计流
  Stream<PerformanceStats> get statsStream => _statsController.stream;
  
  /// 是否正在监控
  bool get isMonitoring => _monitorTimer?.isActive ?? false;
  
  /// 开始监控
  void startMonitoring() {
    if (isMonitoring) return;
    
    _frameStopwatch = Stopwatch()..start();
    _monitorTimer = Timer.periodic(monitorInterval, _collectMetrics);
    
    if (kDebugMode) {
      debugPrint('PerformanceMonitor: Started monitoring');
    }
  }
  
  /// 停止监控
  void stopMonitoring() {
    _monitorTimer?.cancel();
    _frameStopwatch?.stop();
    
    if (kDebugMode) {
      debugPrint('PerformanceMonitor: Stopped monitoring');
    }
  }
  
  /// 记录渲染时间
  void recordRenderTime(int microseconds) {
    final renderTimeMs = microseconds / 1000.0;
    _renderTimes.add(renderTimeMs);
    
    if (_renderTimes.length > maxSamples) {
      _renderTimes.removeAt(0);
    }
    
    // 检查渲染性能告警
    if (renderTimeMs > 16.0) {
      _triggerAlert(PerformanceAlert(
        type: AlertType.slowRender,
        message: 'Render time exceeded 16ms: ${renderTimeMs.toStringAsFixed(1)}ms',
        severity: renderTimeMs > 33.0 ? AlertSeverity.critical : AlertSeverity.warning,
        timestamp: DateTime.now(),
        value: renderTimeMs,
      ));
    }
  }
  
  /// 记录帧时间
  void recordFrame() {
    if (_frameStopwatch != null) {
      final frameTimeMs = _frameStopwatch!.elapsedMicroseconds / 1000.0;
      _frameTimes.add(frameTimeMs);
      
      if (_frameTimes.length > maxSamples) {
        _frameTimes.removeAt(0);
      }
      
      _frameStopwatch!.reset();
      _frameCount++;
      _totalFrames++;
      
      // 检查帧率告警
      final currentFPS = _calculateCurrentFPS();
      if (currentFPS < 45.0) {
        _triggerAlert(PerformanceAlert(
          type: AlertType.lowFPS,
          message: 'FPS dropped below 45: ${currentFPS.toStringAsFixed(1)}',
          severity: currentFPS < 30.0 ? AlertSeverity.critical : AlertSeverity.warning,
          timestamp: DateTime.now(),
          value: currentFPS,
        ));
      }
    }
  }
  
  /// 添加告警回调
  void addAlertCallback(Function(PerformanceAlert) callback) {
    _alertCallbacks.add(callback);
  }
  
  /// 移除告警回调
  void removeAlertCallback(Function(PerformanceAlert) callback) {
    _alertCallbacks.remove(callback);
  }
  
  /// 获取当前性能统计
  PerformanceStats getCurrentStats() {
    return PerformanceStats(
      avgRenderTime: _calculateAverage(_renderTimes),
      maxRenderTime: _renderTimes.isNotEmpty ? _renderTimes.reduce(math.max) : 0.0,
      avgFrameTime: _calculateAverage(_frameTimes),
      fps: _calculateCurrentFPS(),
      memoryUsage: _memoryUsage.isNotEmpty ? _memoryUsage.last : 0,
      frameDrops: _countFrameDrops(),
      dataPoints: 0, // 由外部设置
      visibleDataPoints: 0, // 由外部设置
    );
  }
  
  /// 获取性能历史
  PerformanceHistory getPerformanceHistory() {
    return PerformanceHistory(
      renderTimes: List.from(_renderTimes),
      frameTimes: List.from(_frameTimes),
      memoryUsage: List.from(_memoryUsage),
      fpsHistory: List.from(_fpsHistory),
      totalFrames: _totalFrames,
    );
  }
  
  /// 重置统计数据
  void resetStats() {
    _renderTimes.clear();
    _frameTimes.clear();
    _memoryUsage.clear();
    _fpsHistory.clear();
    _frameCount = 0;
    _totalFrames = 0;
    
    if (kDebugMode) {
      debugPrint('PerformanceMonitor: Stats reset');
    }
  }
  
  /// 收集性能指标
  void _collectMetrics(Timer timer) {
    // 收集内存使用情况
    final memoryManager = MemoryManager();
    final memoryUsage = memoryManager.getMemoryUsage();
    _memoryUsage.add(memoryUsage.totalMemoryUsage);
    
    if (_memoryUsage.length > maxSamples) {
      _memoryUsage.removeAt(0);
    }
    
    // 计算当前FPS
    final currentFPS = _calculateCurrentFPS();
    _fpsHistory.add(currentFPS);
    
    if (_fpsHistory.length > maxSamples) {
      _fpsHistory.removeAt(0);
    }
    
    // 重置帧计数
    _frameCount = 0;
    
    // 发送性能统计
    final stats = getCurrentStats();
    _statsController.add(stats);
    
    // 检查内存告警
    if (memoryUsage.totalMemoryUsage > 100 * 1024 * 1024) { // 100MB
      _triggerAlert(PerformanceAlert(
        type: AlertType.highMemory,
        message: 'Memory usage exceeded 100MB: ${memoryUsage.totalMemoryUsage ~/ 1024 ~/ 1024}MB',
        severity: memoryUsage.totalMemoryUsage > 200 * 1024 * 1024 
            ? AlertSeverity.critical 
            : AlertSeverity.warning,
        timestamp: DateTime.now(),
        value: memoryUsage.totalMemoryUsage.toDouble(),
      ));
    }
    
    // 性能优化建议
    _checkOptimizationOpportunities(stats);
  }
  
  /// 计算平均值
  double _calculateAverage(List<double> values) {
    if (values.isEmpty) return 0.0;
    return values.reduce((a, b) => a + b) / values.length;
  }
  
  /// 计算当前FPS
  double _calculateCurrentFPS() {
    if (_frameCount == 0) return 0.0;
    
    // 基于最近1秒的帧数计算FPS
    return _frameCount.toDouble();
  }
  
  /// 统计掉帧次数
  int _countFrameDrops() {
    return _frameTimes.where((time) => time > 16.67).length; // 超过60FPS阈值
  }
  
  /// 触发性能告警
  void _triggerAlert(PerformanceAlert alert) {
    for (final callback in _alertCallbacks) {
      try {
        callback(alert);
      } catch (e) {
        if (kDebugMode) {
          debugPrint('PerformanceMonitor: Alert callback error: $e');
        }
      }
    }
    
    if (kDebugMode) {
      debugPrint('PerformanceMonitor: ${alert.severity.name.toUpperCase()} - ${alert.message}');
    }
  }
  
  /// 检查优化机会
  void _checkOptimizationOpportunities(PerformanceStats stats) {
    final suggestions = <String>[];
    
    // 渲染性能建议
    if (stats.avgRenderTime > 16.0) {
      suggestions.add('考虑减少绘制复杂度或启用渲染缓存');
    }
    
    // FPS建议
    if (stats.fps < 50.0) {
      suggestions.add('考虑减少数据点数量或启用数据虚拟化');
    }
    
    // 内存建议
    if (stats.memoryUsage > 80 * 1024 * 1024) { // 80MB
      suggestions.add('考虑清理缓存或减少对象池大小');
    }
    
    // 掉帧建议
    if (stats.frameDrops > 10) {
      suggestions.add('考虑优化交互响应或减少动画复杂度');
    }
    
    if (suggestions.isNotEmpty && kDebugMode) {
      debugPrint('PerformanceMonitor: Optimization suggestions:');
      for (final suggestion in suggestions) {
        debugPrint('  - $suggestion');
      }
    }
  }
  
  /// 生成性能报告
  String generatePerformanceReport() {
    final stats = getCurrentStats();
    final history = getPerformanceHistory();
    
    final buffer = StringBuffer();
    buffer.writeln('=== Performance Report ===');
    buffer.writeln('Total Frames: ${history.totalFrames}');
    buffer.writeln('Average FPS: ${stats.fps.toStringAsFixed(1)}');
    buffer.writeln('Average Render Time: ${stats.avgRenderTime.toStringAsFixed(1)}ms');
    buffer.writeln('Max Render Time: ${stats.maxRenderTime.toStringAsFixed(1)}ms');
    buffer.writeln('Memory Usage: ${stats.memoryUsage ~/ 1024}KB');
    buffer.writeln('Frame Drops: ${stats.frameDrops}');
    buffer.writeln('Performance Level: ${stats.performanceLevel.name}');
    buffer.writeln('========================');
    
    return buffer.toString();
  }
  
  /// 释放资源
  void dispose() {
    stopMonitoring();
    _statsController.close();
    _alertCallbacks.clear();
    resetStats();
  }
}

/// 性能告警
@immutable
class PerformanceAlert {
  final AlertType type;
  final String message;
  final AlertSeverity severity;
  final DateTime timestamp;
  final double value;
  
  const PerformanceAlert({
    required this.type,
    required this.message,
    required this.severity,
    required this.timestamp,
    required this.value,
  });
  
  @override
  String toString() {
    return 'PerformanceAlert('
           'type: $type, '
           'severity: $severity, '
           'message: $message, '
           'value: $value, '
           'time: ${timestamp.toIso8601String()})';
  }
}

/// 告警类型
enum AlertType {
  slowRender,
  lowFPS,
  highMemory,
  frameDrops,
  dataOverload,
}

/// 告警严重程度
enum AlertSeverity {
  info,
  warning,
  critical,
}

/// 性能历史数据
@immutable
class PerformanceHistory {
  final List<double> renderTimes;
  final List<double> frameTimes;
  final List<int> memoryUsage;
  final List<double> fpsHistory;
  final int totalFrames;
  
  const PerformanceHistory({
    required this.renderTimes,
    required this.frameTimes,
    required this.memoryUsage,
    required this.fpsHistory,
    required this.totalFrames,
  });
  
  /// 性能趋势分析
  PerformanceTrend analyzeTrend() {
    if (fpsHistory.length < 10) {
      return PerformanceTrend.stable;
    }
    
    final recentFPS = fpsHistory.sublist(fpsHistory.length - 10);
    final earlierFPS = fpsHistory.sublist(0, 10);
    
    final recentAvg = recentFPS.reduce((a, b) => a + b) / recentFPS.length;
    final earlierAvg = earlierFPS.reduce((a, b) => a + b) / earlierFPS.length;
    
    final difference = recentAvg - earlierAvg;
    
    if (difference > 5.0) return PerformanceTrend.improving;
    if (difference < -5.0) return PerformanceTrend.degrading;
    return PerformanceTrend.stable;
  }
  
  @override
  String toString() {
    return 'PerformanceHistory('
           'renderSamples: ${renderTimes.length}, '
           'frameSamples: ${frameTimes.length}, '
           'memorySamples: ${memoryUsage.length}, '
           'fpsSamples: ${fpsHistory.length}, '
           'totalFrames: $totalFrames, '
           'trend: ${analyzeTrend()})';
  }
}

/// 性能趋势
enum PerformanceTrend {
  improving,
  stable,
  degrading,
}
