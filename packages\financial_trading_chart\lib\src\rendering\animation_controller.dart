import 'package:flutter/material.dart';

/// 图表动画控制器
///
/// 管理图表的各种动画效果，包括数据更新动画、缩放动画、平移动画等
class ChartAnimationController extends ChangeNotifier {
  /// 动画控制器
  late final AnimationController _animationController;

  /// 数据更新动画
  late final Animation<double> _dataUpdateAnimation;

  /// 缩放动画
  late final Animation<double> _zoomAnimation;

  /// 平移动画
  late final Animation<Offset> _panAnimation;

  /// 淡入淡出动画
  late final Animation<double> _fadeAnimation;

  /// 动画配置
  final ChartAnimationConfig config;

  /// 当前动画状态
  ChartAnimationState _state = ChartAnimationState.idle;

  /// 动画开始时间
  DateTime? _animationStartTime;

  /// 动画持续时间
  Duration _currentDuration = const Duration(milliseconds: 300);

  /// 是否正在执行动画
  bool get isAnimating => _state != ChartAnimationState.idle;

  /// 当前动画状态
  ChartAnimationState get state => _state;

  /// 数据更新动画值
  double get dataUpdateValue => _dataUpdateAnimation.value;

  /// 缩放动画值
  double get zoomValue => _zoomAnimation.value;

  /// 平移动画值
  Offset get panValue => _panAnimation.value;

  /// 淡入淡出动画值
  double get fadeValue => _fadeAnimation.value;

  ChartAnimationController({
    required TickerProvider vsync,
    this.config = const ChartAnimationConfig(),
  }) {
    _initializeAnimations(vsync);
  }

  /// 初始化动画
  void _initializeAnimations(TickerProvider vsync) {
    _animationController = AnimationController(
      duration: config.defaultDuration,
      vsync: vsync,
    );

    // 数据更新动画
    _dataUpdateAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: config.dataUpdateCurve,
      ),
    );

    // 缩放动画
    _zoomAnimation = Tween<double>(begin: 1.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: config.zoomCurve),
    );

    // 平移动画
    _panAnimation = Tween<Offset>(begin: Offset.zero, end: Offset.zero).animate(
      CurvedAnimation(parent: _animationController, curve: config.panCurve),
    );

    // 淡入淡出动画
    _fadeAnimation = Tween<double>(begin: 1.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: config.fadeCurve),
    );

    // 监听动画状态变化
    _animationController.addStatusListener(_onAnimationStatusChanged);
    _animationController.addListener(_onAnimationValueChanged);
  }

  /// 动画状态变化回调
  void _onAnimationStatusChanged(AnimationStatus status) {
    switch (status) {
      case AnimationStatus.forward:
        _state = ChartAnimationState.running;
        _animationStartTime = DateTime.now();
        break;
      case AnimationStatus.completed:
        _state = ChartAnimationState.completed;
        break;
      case AnimationStatus.dismissed:
        _state = ChartAnimationState.idle;
        break;
      case AnimationStatus.reverse:
        _state = ChartAnimationState.reversing;
        break;
    }
    notifyListeners();
  }

  /// 动画值变化回调
  void _onAnimationValueChanged() {
    notifyListeners();
  }

  /// 开始数据更新动画
  Future<void> animateDataUpdate({Duration? duration, Curve? curve}) async {
    if (!config.enableDataUpdateAnimation) return;

    _currentDuration = duration ?? config.dataUpdateDuration;
    _animationController.duration = _currentDuration;

    if (curve != null) {
      _dataUpdateAnimation = Tween<double>(
        begin: 0.0,
        end: 1.0,
      ).animate(CurvedAnimation(parent: _animationController, curve: curve));
    }

    await _animationController.forward(from: 0.0);
  }

  /// 开始缩放动画
  Future<void> animateZoom({
    required double fromScale,
    required double toScale,
    Duration? duration,
    Curve? curve,
  }) async {
    if (!config.enableZoomAnimation) return;

    _currentDuration = duration ?? config.zoomDuration;
    _animationController.duration = _currentDuration;

    _zoomAnimation = Tween<double>(begin: fromScale, end: toScale).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: curve ?? config.zoomCurve,
      ),
    );

    await _animationController.forward(from: 0.0);
  }

  /// 开始平移动画
  Future<void> animatePan({
    required Offset fromOffset,
    required Offset toOffset,
    Duration? duration,
    Curve? curve,
  }) async {
    if (!config.enablePanAnimation) return;

    _currentDuration = duration ?? config.panDuration;
    _animationController.duration = _currentDuration;

    _panAnimation = Tween<Offset>(begin: fromOffset, end: toOffset).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: curve ?? config.panCurve,
      ),
    );

    await _animationController.forward(from: 0.0);
  }

  /// 开始淡入淡出动画
  Future<void> animateFade({
    required double fromOpacity,
    required double toOpacity,
    Duration? duration,
    Curve? curve,
  }) async {
    if (!config.enableFadeAnimation) return;

    _currentDuration = duration ?? config.fadeDuration;
    _animationController.duration = _currentDuration;

    _fadeAnimation = Tween<double>(begin: fromOpacity, end: toOpacity).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: curve ?? config.fadeCurve,
      ),
    );

    await _animationController.forward(from: 0.0);
  }

  /// 停止动画
  void stopAnimation() {
    _animationController.stop();
    _state = ChartAnimationState.idle;
    notifyListeners();
  }

  /// 重置动画
  void resetAnimation() {
    _animationController.reset();
    _state = ChartAnimationState.idle;
    _animationStartTime = null;
    notifyListeners();
  }

  /// 获取动画进度
  double getAnimationProgress() {
    if (_animationStartTime == null) return 0.0;

    final elapsed = DateTime.now().difference(_animationStartTime!);
    final progress = elapsed.inMilliseconds / _currentDuration.inMilliseconds;
    return progress.clamp(0.0, 1.0);
  }

  /// 获取剩余动画时间
  Duration getRemainingDuration() {
    if (_animationStartTime == null) return Duration.zero;

    final elapsed = DateTime.now().difference(_animationStartTime!);
    final remaining = _currentDuration - elapsed;
    return remaining.isNegative ? Duration.zero : remaining;
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }
}

/// 图表动画配置
class ChartAnimationConfig {
  /// 是否启用动画
  final bool enableAnimations;

  /// 是否启用数据更新动画
  final bool enableDataUpdateAnimation;

  /// 是否启用缩放动画
  final bool enableZoomAnimation;

  /// 是否启用平移动画
  final bool enablePanAnimation;

  /// 是否启用淡入淡出动画
  final bool enableFadeAnimation;

  /// 默认动画持续时间
  final Duration defaultDuration;

  /// 数据更新动画持续时间
  final Duration dataUpdateDuration;

  /// 缩放动画持续时间
  final Duration zoomDuration;

  /// 平移动画持续时间
  final Duration panDuration;

  /// 淡入淡出动画持续时间
  final Duration fadeDuration;

  /// 数据更新动画曲线
  final Curve dataUpdateCurve;

  /// 缩放动画曲线
  final Curve zoomCurve;

  /// 平移动画曲线
  final Curve panCurve;

  /// 淡入淡出动画曲线
  final Curve fadeCurve;

  const ChartAnimationConfig({
    this.enableAnimations = true,
    this.enableDataUpdateAnimation = true,
    this.enableZoomAnimation = true,
    this.enablePanAnimation = true,
    this.enableFadeAnimation = true,
    this.defaultDuration = const Duration(milliseconds: 300),
    this.dataUpdateDuration = const Duration(milliseconds: 500),
    this.zoomDuration = const Duration(milliseconds: 200),
    this.panDuration = const Duration(milliseconds: 150),
    this.fadeDuration = const Duration(milliseconds: 250),
    this.dataUpdateCurve = Curves.easeInOut,
    this.zoomCurve = Curves.easeOut,
    this.panCurve = Curves.easeInOut,
    this.fadeCurve = Curves.easeInOut,
  });

  /// 创建副本
  ChartAnimationConfig copyWith({
    bool? enableAnimations,
    bool? enableDataUpdateAnimation,
    bool? enableZoomAnimation,
    bool? enablePanAnimation,
    bool? enableFadeAnimation,
    Duration? defaultDuration,
    Duration? dataUpdateDuration,
    Duration? zoomDuration,
    Duration? panDuration,
    Duration? fadeDuration,
    Curve? dataUpdateCurve,
    Curve? zoomCurve,
    Curve? panCurve,
    Curve? fadeCurve,
  }) {
    return ChartAnimationConfig(
      enableAnimations: enableAnimations ?? this.enableAnimations,
      enableDataUpdateAnimation:
          enableDataUpdateAnimation ?? this.enableDataUpdateAnimation,
      enableZoomAnimation: enableZoomAnimation ?? this.enableZoomAnimation,
      enablePanAnimation: enablePanAnimation ?? this.enablePanAnimation,
      enableFadeAnimation: enableFadeAnimation ?? this.enableFadeAnimation,
      defaultDuration: defaultDuration ?? this.defaultDuration,
      dataUpdateDuration: dataUpdateDuration ?? this.dataUpdateDuration,
      zoomDuration: zoomDuration ?? this.zoomDuration,
      panDuration: panDuration ?? this.panDuration,
      fadeDuration: fadeDuration ?? this.fadeDuration,
      dataUpdateCurve: dataUpdateCurve ?? this.dataUpdateCurve,
      zoomCurve: zoomCurve ?? this.zoomCurve,
      panCurve: panCurve ?? this.panCurve,
      fadeCurve: fadeCurve ?? this.fadeCurve,
    );
  }
}

/// 图表动画状态
enum ChartAnimationState {
  /// 空闲状态
  idle,

  /// 运行中
  running,

  /// 已完成
  completed,

  /// 反向运行
  reversing,
}
