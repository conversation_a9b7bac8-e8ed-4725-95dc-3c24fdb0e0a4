import 'dart:math' as math;
import 'dart:ui' as ui;
import 'package:flutter/material.dart';

import '../models/chart_data.dart';
import '../models/performance_data.dart';
import '../config/chart_config.dart';
import '../config/chart_theme.dart';
import '../performance/memory_manager.dart';
import '../utils/performance_utils.dart';

/// TradingView级别的高性能渲染器
/// 
/// 特性：
/// - 分层渲染优化
/// - 脏区域重绘
/// - 批量绘制
/// - 对象池复用
/// - Canvas缓存
class HighPerformanceRenderer extends CustomPainter {
  final List<CandleData> data;
  final Map<String, List<double>> indicators;
  final ViewportInfo viewport;
  final ChartConfig config;
  final ChartTheme theme;
  final MemoryManager memoryManager;
  final Function(int)? onRenderComplete;
  
  // 渲染缓存
  ui.Picture? _backgroundPicture;
  ui.Picture? _gridPicture;
  ui.Picture? _candlestickPicture;
  ui.Picture? _indicatorPicture;
  
  // 脏区域标记
  bool _backgroundDirty = true;
  bool _gridDirty = true;
  bool _candlestickDirty = true;
  bool _indicatorDirty = true;
  
  // 性能监控
  final Stopwatch _renderStopwatch = Stopwatch();
  
  HighPerformanceRenderer({
    required this.data,
    required this.indicators,
    required this.viewport,
    required this.config,
    required this.theme,
    required this.memoryManager,
    this.onRenderComplete,
  });
  
  @override
  void paint(Canvas canvas, Size size) {
    _renderStopwatch.start();
    
    try {
      // 设置渲染上下文
      final renderContext = RenderContext(
        canvas: canvas,
        size: size,
        viewport: viewport,
        config: config,
        theme: theme,
      );
      
      // 分层渲染
      _renderBackground(renderContext);
      _renderGrid(renderContext);
      _renderCandlesticks(renderContext);
      _renderIndicators(renderContext);
      _renderCrosshair(renderContext);
      _renderOverlays(renderContext);
      
    } finally {
      _renderStopwatch.stop();
      final renderTime = _renderStopwatch.elapsedMilliseconds;
      onRenderComplete?.call(renderTime);
      _renderStopwatch.reset();
    }
  }
  
  /// 渲染背景层 (静态)
  void _renderBackground(RenderContext context) {
    if (!_backgroundDirty && _backgroundPicture != null) {
      context.canvas.drawPicture(_backgroundPicture!);
      return;
    }
    
    final recorder = ui.PictureRecorder();
    final canvas = Canvas(recorder);
    
    // 绘制背景
    final backgroundPaint = memoryManager.getPaint()
      ..color = theme.backgroundColor
      ..style = PaintingStyle.fill;
    
    canvas.drawRect(
      Rect.fromLTWH(0, 0, context.size.width, context.size.height),
      backgroundPaint,
    );
    
    memoryManager.returnPaint(backgroundPaint);
    
    _backgroundPicture = recorder.endRecording();
    _backgroundDirty = false;
    
    context.canvas.drawPicture(_backgroundPicture!);
  }
  
  /// 渲染网格层 (半静态)
  void _renderGrid(RenderContext context) {
    if (!config.showGrid) return;
    
    if (!_gridDirty && _gridPicture != null) {
      context.canvas.drawPicture(_gridPicture!);
      return;
    }
    
    final recorder = ui.PictureRecorder();
    final canvas = Canvas(recorder);
    
    final gridPaint = memoryManager.getPaint()
      ..color = theme.gridColor
      ..strokeWidth = 0.5
      ..style = PaintingStyle.stroke;
    
    // 垂直网格线
    final xStep = context.size.width / config.verticalGridCount;
    for (int i = 0; i <= config.verticalGridCount; i++) {
      final x = i * xStep;
      canvas.drawLine(
        Offset(x, 0),
        Offset(x, context.size.height),
        gridPaint,
      );
    }
    
    // 水平网格线
    final yStep = context.size.height / config.horizontalGridCount;
    for (int i = 0; i <= config.horizontalGridCount; i++) {
      final y = i * yStep;
      canvas.drawLine(
        Offset(0, y),
        Offset(context.size.width, y),
        gridPaint,
      );
    }
    
    memoryManager.returnPaint(gridPaint);
    
    _gridPicture = recorder.endRecording();
    _gridDirty = false;
    
    context.canvas.drawPicture(_gridPicture!);
  }
  
  /// 渲染K线层 (动态)
  void _renderCandlesticks(RenderContext context) {
    if (data.isEmpty) return;
    
    // 视口裁剪优化
    final visibleRange = _calculateVisibleRange(context);
    final visibleData = _getVisibleData(visibleRange);
    
    if (visibleData.isEmpty) return;
    
    // 批量渲染优化
    final candlestickPaint = memoryManager.getPaint()..style = PaintingStyle.fill;
    final wickPaint = memoryManager.getPaint()
      ..strokeWidth = 1.0
      ..style = PaintingStyle.stroke;
    
    // 使用Path批量绘制
    final bullishPath = memoryManager.getPath();
    final bearishPath = memoryManager.getPath();
    final wickPath = memoryManager.getPath();
    
    try {
      for (int i = 0; i < visibleData.length; i++) {
        final candle = visibleData[i];
        final x = _getXPosition(visibleRange.startIndex + i, context);
        final candleWidth = _getCandleWidth(context);
        
        // 计算价格位置
        final openY = _getPriceY(candle.open, context);
        final closeY = _getPriceY(candle.close, context);
        final highY = _getPriceY(candle.high, context);
        final lowY = _getPriceY(candle.low, context);
        
        // 绘制影线
        wickPath.moveTo(x, highY);
        wickPath.lineTo(x, lowY);
        
        // 绘制实体
        final bodyTop = math.min(openY, closeY);
        final bodyBottom = math.max(openY, closeY);
        final bodyHeight = math.max(1.0, bodyBottom - bodyTop); // 最小高度1像素
        
        final rect = Rect.fromLTWH(
          x - candleWidth / 2,
          bodyTop,
          candleWidth,
          bodyHeight,
        );
        
        if (candle.close >= candle.open) {
          bullishPath.addRect(rect);
        } else {
          bearishPath.addRect(rect);
        }
      }
      
      // 批量绘制
      wickPaint.color = theme.wickColor;
      context.canvas.drawPath(wickPath, wickPaint);
      
      candlestickPaint.color = theme.bullishColor;
      context.canvas.drawPath(bullishPath, candlestickPaint);
      
      candlestickPaint.color = theme.bearishColor;
      context.canvas.drawPath(bearishPath, candlestickPaint);
      
    } finally {
      // 归还对象到对象池
      memoryManager.returnPaint(candlestickPaint);
      memoryManager.returnPaint(wickPaint);
      memoryManager.returnPath(bullishPath);
      memoryManager.returnPath(bearishPath);
      memoryManager.returnPath(wickPath);
    }
  }
  
  /// 渲染技术指标层 (动态)
  void _renderIndicators(RenderContext context) {
    if (indicators.isEmpty) return;
    
    final visibleRange = _calculateVisibleRange(context);
    
    for (final entry in indicators.entries) {
      final indicatorKey = entry.key;
      final indicatorData = entry.value;
      
      _renderIndicatorLine(
        context,
        indicatorData,
        visibleRange,
        _getIndicatorColor(indicatorKey),
        _getIndicatorLineWidth(indicatorKey),
      );
    }
  }
  
  /// 渲染指标线
  void _renderIndicatorLine(
    RenderContext context,
    List<double> data,
    VisibleRange range,
    Color color,
    double lineWidth,
  ) {
    if (data.isEmpty) return;
    
    final paint = memoryManager.getPaint()
      ..color = color
      ..strokeWidth = lineWidth
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round
      ..strokeJoin = StrokeJoin.round;
    
    final path = memoryManager.getPath();
    
    try {
      bool firstPoint = true;
      for (int i = range.startIndex; i <= range.endIndex && i < data.length; i++) {
        final value = data[i];
        if (value.isNaN || value.isInfinite) continue;
        
        final x = _getXPosition(i, context);
        final y = _getPriceY(value, context);
        
        if (firstPoint) {
          path.moveTo(x, y);
          firstPoint = false;
        } else {
          path.lineTo(x, y);
        }
      }
      
      context.canvas.drawPath(path, paint);
      
    } finally {
      memoryManager.returnPaint(paint);
      memoryManager.returnPath(path);
    }
  }
  
  /// 渲染十字线层 (交互)
  void _renderCrosshair(RenderContext context) {
    // TODO: 实现十字线渲染
  }
  
  /// 渲染覆盖层 (UI元素)
  void _renderOverlays(RenderContext context) {
    // TODO: 实现覆盖层渲染 (价格标签、时间标签等)
  }
  
  /// 计算可见范围
  VisibleRange _calculateVisibleRange(RenderContext context) {
    final totalDataLength = data.length;
    if (totalDataLength == 0) {
      return VisibleRange(startIndex: 0, endIndex: 0);
    }
    
    final startIndex = (viewport.startX * totalDataLength).floor().clamp(0, totalDataLength - 1);
    final endIndex = (viewport.endX * totalDataLength).ceil().clamp(0, totalDataLength - 1);
    
    return VisibleRange(
      startIndex: startIndex,
      endIndex: endIndex,
    );
  }
  
  /// 获取可见数据
  List<CandleData> _getVisibleData(VisibleRange range) {
    if (data.isEmpty || range.startIndex >= data.length) {
      return [];
    }
    
    final endIndex = math.min(range.endIndex + 1, data.length);
    return data.sublist(range.startIndex, endIndex);
  }
  
  /// 获取X坐标位置
  double _getXPosition(int index, RenderContext context) {
    final visibleRange = _calculateVisibleRange(context);
    final visibleWidth = visibleRange.endIndex - visibleRange.startIndex;
    
    if (visibleWidth <= 0) return 0;
    
    return (index - visibleRange.startIndex) * (context.size.width / visibleWidth);
  }
  
  /// 获取价格Y坐标
  double _getPriceY(double price, RenderContext context) {
    final priceRange = viewport.maxPrice - viewport.minPrice;
    if (priceRange <= 0) return context.size.height / 2;
    
    final normalizedPrice = (price - viewport.minPrice) / priceRange;
    return context.size.height * (1 - normalizedPrice);
  }
  
  /// 获取K线宽度
  double _getCandleWidth(RenderContext context) {
    final visibleRange = _calculateVisibleRange(context);
    final visibleCount = math.max(1, visibleRange.endIndex - visibleRange.startIndex);
    final availableWidth = context.size.width * 0.8; // 80%用于K线
    
    return math.max(1.0, availableWidth / visibleCount);
  }
  
  /// 获取指标颜色
  Color _getIndicatorColor(String indicatorKey) {
    // TODO: 根据指标类型返回对应颜色
    switch (indicatorKey) {
      case 'sma_20':
        return Colors.blue;
      case 'sma_50':
        return Colors.orange;
      case 'ema_12':
        return Colors.green;
      case 'ema_26':
        return Colors.red;
      case 'rsi_14':
        return Colors.purple;
      case 'macd':
        return Colors.cyan;
      default:
        return Colors.grey;
    }
  }
  
  /// 获取指标线宽
  double _getIndicatorLineWidth(String indicatorKey) {
    // TODO: 根据指标类型返回对应线宽
    return 1.5;
  }
  
  @override
  bool shouldRepaint(covariant HighPerformanceRenderer oldDelegate) {
    return data != oldDelegate.data ||
           indicators != oldDelegate.indicators ||
           viewport != oldDelegate.viewport ||
           config != oldDelegate.config ||
           theme != oldDelegate.theme;
  }
  
  /// 标记需要重绘
  void markNeedsRepaint({
    bool background = false,
    bool grid = false,
    bool candlestick = true,
    bool indicator = true,
  }) {
    if (background) _backgroundDirty = true;
    if (grid) _gridDirty = true;
    if (candlestick) _candlestickDirty = true;
    if (indicator) _indicatorDirty = true;
  }
}

/// 渲染上下文
class RenderContext {
  final Canvas canvas;
  final Size size;
  final ViewportInfo viewport;
  final ChartConfig config;
  final ChartTheme theme;
  
  RenderContext({
    required this.canvas,
    required this.size,
    required this.viewport,
    required this.config,
    required this.theme,
  });
}

/// 可见范围
class VisibleRange {
  final int startIndex;
  final int endIndex;
  
  VisibleRange({
    required this.startIndex,
    required this.endIndex,
  });
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is VisibleRange &&
           other.startIndex == startIndex &&
           other.endIndex == endIndex;
  }
  
  @override
  int get hashCode => startIndex.hashCode ^ endIndex.hashCode;
}

/// 价格标尺绘制器
class PriceScalePainter extends CustomPainter {
  final ViewportInfo viewport;
  final ChartTheme theme;
  final ChartConfig config;
  
  PriceScalePainter({
    required this.viewport,
    required this.theme,
    required this.config,
  });
  
  @override
  void paint(Canvas canvas, Size size) {
    // TODO: 实现价格标尺绘制
  }
  
  @override
  bool shouldRepaint(covariant PriceScalePainter oldDelegate) {
    return viewport != oldDelegate.viewport ||
           theme != oldDelegate.theme ||
           config != oldDelegate.config;
  }
}

/// 时间标尺绘制器
class TimeScalePainter extends CustomPainter {
  final List<CandleData> data;
  final ViewportInfo viewport;
  final ChartTheme theme;
  final ChartConfig config;
  
  TimeScalePainter({
    required this.data,
    required this.viewport,
    required this.theme,
    required this.config,
  });
  
  @override
  void paint(Canvas canvas, Size size) {
    // TODO: 实现时间标尺绘制
  }
  
  @override
  bool shouldRepaint(covariant TimeScalePainter oldDelegate) {
    return data != oldDelegate.data ||
           viewport != oldDelegate.viewport ||
           theme != oldDelegate.theme ||
           config != oldDelegate.config;
  }
}
