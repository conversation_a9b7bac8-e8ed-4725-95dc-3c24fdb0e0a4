import 'package:financial_app_core/financial_app_core.dart';

/// 交易图表模块专用日志管理器
///
/// 提供交易图表模块特定的日志记录功能
class ChartLogger extends ModuleLogger {
  static final ChartLogger _instance = ChartLogger._internal();
  factory ChartLogger() => _instance;
  ChartLogger._internal();

  @override
  String get moduleName => 'Chart';

  // ==================== 图表特定的日志方法 ====================

  /// 图表初始化日志
  void logChartInitialization({
    required String chartId,
    required String chartType, // 'candlestick', 'line', 'area', 'bar'
    required bool success,
    String? symbol,
    String? timeframe,
    Duration? initTime,
    Map<String, dynamic>? chartConfig,
    String? error,
  }) {
    final metadata = {
      'chart_id': chartId,
      'chart_type': chartType,
      'symbol': symbol,
      'timeframe': timeframe,
      'success': success,
      'chart_config': chartConfig,
      if (initTime != null) 'init_time_ms': initTime.inMilliseconds,
      if (error != null) 'error': error,
      'timestamp': DateTime.now().toIso8601String(),
    };

    if (success) {
      info('图表初始化成功: $chartType ($symbol)', metadata: metadata);
      if (initTime != null) {
        logPerformance('chart_initialization', initTime, metadata: metadata);
      }
    } else {
      super.error('图表初始化失败: $chartType', metadata: metadata);
    }

    logBusinessEvent('chart_initialization', metadata);
  }

  /// 数据加载日志
  void logDataLoading({
    required String chartId,
    required String symbol,
    required String timeframe,
    required bool success,
    int? dataPoints,
    Duration? loadTime,
    String? dataSource,
    String? error,
  }) {
    final metadata = {
      'chart_id': chartId,
      'symbol': symbol,
      'timeframe': timeframe,
      'success': success,
      'data_points': dataPoints,
      'data_source': dataSource,
      if (loadTime != null) 'load_time_ms': loadTime.inMilliseconds,
      if (error != null) 'error': error,
      'timestamp': DateTime.now().toIso8601String(),
    };

    if (success) {
      info('图表数据加载成功: $symbol ($timeframe)', metadata: metadata);
      if (loadTime != null) {
        logPerformance('chart_data_loading', loadTime, metadata: metadata);
      }
    } else {
      warning('图表数据加载失败: $symbol ($timeframe)', metadata: metadata);
    }

    logBusinessEvent('chart_data_loading', metadata);
  }

  /// 图表渲染日志
  void logChartRendering({
    required String chartId,
    required String symbol,
    required bool success,
    int? dataPoints,
    Duration? renderTime,
    Map<String, dynamic>? renderConfig,
    String? error,
  }) {
    final metadata = {
      'chart_id': chartId,
      'symbol': symbol,
      'success': success,
      'data_points': dataPoints,
      'render_config': renderConfig,
      if (renderTime != null) 'render_time_ms': renderTime.inMilliseconds,
      if (error != null) 'error': error,
      'timestamp': DateTime.now().toIso8601String(),
    };

    if (success) {
      debug('图表渲染成功: $symbol', metadata: metadata);
      if (renderTime != null) {
        logPerformance('chart_rendering', renderTime, metadata: metadata);
      }
    } else {
      warning('图表渲染失败: $symbol', metadata: metadata);
    }

    logBusinessEvent('chart_rendering', metadata);
  }

  /// 时间框架切换日志
  void logTimeframeChange({
    required String chartId,
    required String symbol,
    required String fromTimeframe,
    required String toTimeframe,
    required bool success,
    Duration? switchTime,
    int? newDataPoints,
    String? error,
  }) {
    final metadata = {
      'chart_id': chartId,
      'symbol': symbol,
      'from_timeframe': fromTimeframe,
      'to_timeframe': toTimeframe,
      'success': success,
      'new_data_points': newDataPoints,
      if (switchTime != null) 'switch_time_ms': switchTime.inMilliseconds,
      if (error != null) 'error': error,
      'timestamp': DateTime.now().toIso8601String(),
    };

    if (success) {
      info('时间框架切换成功: $fromTimeframe -> $toTimeframe', metadata: metadata);
      if (switchTime != null) {
        logPerformance('timeframe_switch', switchTime, metadata: metadata);
      }
    } else {
      warning('时间框架切换失败: $fromTimeframe -> $toTimeframe', metadata: metadata);
    }

    logUserAction('timeframe_change', null, context: metadata);
  }

  /// 图表交互日志
  void logChartInteraction({
    required String chartId,
    required String symbol,
    required String
    interactionType, // 'zoom', 'pan', 'crosshair', 'click', 'hover'
    Map<String, dynamic>? interactionData,
    String? userId,
  }) {
    final metadata = {
      'chart_id': chartId,
      'symbol': symbol,
      'interaction_type': interactionType,
      'interaction_data': interactionData,
      'user_id': userId,
      'timestamp': DateTime.now().toIso8601String(),
    };

    debug('图表交互: $interactionType ($symbol)', metadata: metadata);
    logUserAction('chart_interaction', userId, context: metadata);
  }

  /// 技术指标添加日志
  void logIndicatorAdded({
    required String chartId,
    required String symbol,
    required String indicatorType, // 'MA', 'RSI', 'MACD', 'Bollinger'
    required bool success,
    Map<String, dynamic>? indicatorParams,
    Duration? calculationTime,
    String? error,
  }) {
    final metadata = {
      'chart_id': chartId,
      'symbol': symbol,
      'indicator_type': indicatorType,
      'success': success,
      'indicator_params': indicatorParams,
      if (calculationTime != null)
        'calculation_time_ms': calculationTime.inMilliseconds,
      if (error != null) 'error': error,
      'timestamp': DateTime.now().toIso8601String(),
    };

    if (success) {
      info('技术指标添加成功: $indicatorType', metadata: metadata);
      if (calculationTime != null) {
        logPerformance(
          'indicator_calculation',
          calculationTime,
          metadata: metadata,
        );
      }
    } else {
      warning('技术指标添加失败: $indicatorType', metadata: metadata);
    }

    logBusinessEvent('indicator_added', metadata);
  }

  /// 图表样式变更日志
  void logStyleChange({
    required String chartId,
    required String symbol,
    required String styleType, // 'theme', 'color', 'line_style', 'grid'
    required Map<String, dynamic> styleChanges,
    String? userId,
  }) {
    final metadata = {
      'chart_id': chartId,
      'symbol': symbol,
      'style_type': styleType,
      'style_changes': styleChanges,
      'user_id': userId,
      'timestamp': DateTime.now().toIso8601String(),
    };

    info('图表样式变更: $styleType', metadata: metadata);
    logUserAction('chart_style_change', userId, context: metadata);
  }

  /// 图表导出日志
  void logChartExport({
    required String chartId,
    required String symbol,
    required String exportFormat, // 'png', 'jpg', 'svg', 'pdf'
    required bool success,
    String? fileName,
    int? fileSize,
    Duration? exportTime,
    String? error,
  }) {
    final metadata = {
      'chart_id': chartId,
      'symbol': symbol,
      'export_format': exportFormat,
      'success': success,
      'file_name': fileName,
      'file_size_bytes': fileSize,
      if (exportTime != null) 'export_time_ms': exportTime.inMilliseconds,
      if (error != null) 'error': error,
      'timestamp': DateTime.now().toIso8601String(),
    };

    if (success) {
      info('图表导出成功: $exportFormat', metadata: metadata);
      if (exportTime != null) {
        logPerformance('chart_export', exportTime, metadata: metadata);
      }
    } else {
      warning('图表导出失败: $exportFormat', metadata: metadata);
    }

    logBusinessEvent('chart_export', metadata);
  }

  /// 实时数据更新日志
  void logRealtimeUpdate({
    required String chartId,
    required String symbol,
    required bool success,
    int? updatedPoints,
    Duration? updateTime,
    String? updateSource,
    String? error,
  }) {
    final metadata = {
      'chart_id': chartId,
      'symbol': symbol,
      'success': success,
      'updated_points': updatedPoints,
      'update_source': updateSource,
      if (updateTime != null) 'update_time_ms': updateTime.inMilliseconds,
      if (error != null) 'error': error,
      'timestamp': DateTime.now().toIso8601String(),
    };

    if (success) {
      debug('图表实时更新成功: $symbol', metadata: metadata);
      if (updateTime != null) {
        logPerformance('realtime_update', updateTime, metadata: metadata);
      }
    } else {
      warning('图表实时更新失败: $symbol', metadata: metadata);
    }
  }

  /// 图表性能监控日志
  void logPerformanceMetrics({
    required String chartId,
    required String symbol,
    required Map<String, dynamic> performanceMetrics,
    String? timeframe,
    bool? performanceAlert,
  }) {
    final metadata = {
      'chart_id': chartId,
      'symbol': symbol,
      'timeframe': timeframe,
      'performance_metrics': performanceMetrics,
      'performance_alert': performanceAlert,
      'timestamp': DateTime.now().toIso8601String(),
    };

    if (performanceAlert == true) {
      warning('图表性能告警: $symbol', metadata: metadata);
    } else {
      debug('图表性能监控: $symbol', metadata: metadata);
    }

    logBusinessEvent('chart_performance_metrics', metadata);
  }

  /// 图表错误处理日志
  void logChartError({
    required String chartId,
    required String symbol,
    required String errorType, // 'data_error', 'render_error', 'config_error'
    required String errorMessage,
    String? errorCode,
    Map<String, dynamic>? errorContext,
    bool? willRecover,
    String? recoveryAction,
  }) {
    final metadata = {
      'chart_id': chartId,
      'symbol': symbol,
      'error_type': errorType,
      'error_code': errorCode,
      'error_context': errorContext,
      'will_recover': willRecover,
      'recovery_action': recoveryAction,
      'timestamp': DateTime.now().toIso8601String(),
    };

    if (willRecover == true) {
      warning('图表错误 (将恢复): $errorType - $errorMessage', metadata: metadata);
    } else {
      error('图表错误: $errorType - $errorMessage', metadata: metadata);
    }

    logBusinessEvent('chart_error', metadata);
  }

  /// 图表缓存操作日志
  void logCacheOperation({
    required String operation, // 'hit', 'miss', 'set', 'invalidate'
    required String cacheKey,
    String? symbol,
    String? timeframe,
    int? cacheSize,
    Duration? operationTime,
  }) {
    final metadata = {
      'operation': operation,
      'cache_key': cacheKey,
      'symbol': symbol,
      'timeframe': timeframe,
      'cache_size_bytes': cacheSize,
      if (operationTime != null)
        'operation_time_ms': operationTime.inMilliseconds,
      'timestamp': DateTime.now().toIso8601String(),
    };

    switch (operation) {
      case 'hit':
        debug('图表缓存命中: $cacheKey', metadata: metadata);
        break;
      case 'miss':
        debug('图表缓存未命中: $cacheKey', metadata: metadata);
        break;
      case 'set':
        debug('图表缓存设置: $cacheKey', metadata: metadata);
        break;
      case 'invalidate':
        info('图表缓存失效: $cacheKey', metadata: metadata);
        break;
      default:
        debug('图表缓存操作: $operation', metadata: metadata);
    }
  }

  /// 图表配置变更日志
  void logConfigurationChange({
    required String chartId,
    required String configType, // 'layout', 'data_source', 'display_options'
    required Map<String, dynamic> oldConfig,
    required Map<String, dynamic> newConfig,
    String? userId,
  }) {
    final metadata = {
      'chart_id': chartId,
      'config_type': configType,
      'old_config': oldConfig,
      'new_config': newConfig,
      'config_changes': _calculateConfigChanges(oldConfig, newConfig),
      'user_id': userId,
      'timestamp': DateTime.now().toIso8601String(),
    };

    info('图表配置变更: $configType', metadata: metadata);
    logUserAction('chart_config_change', userId, context: metadata);
  }

  /// 计算配置变更
  Map<String, dynamic> _calculateConfigChanges(
    Map<String, dynamic> oldConfig,
    Map<String, dynamic> newConfig,
  ) {
    final changes = <String, dynamic>{};
    final allKeys = {...oldConfig.keys, ...newConfig.keys};

    for (final key in allKeys) {
      final oldValue = oldConfig[key];
      final newValue = newConfig[key];
      if (oldValue != newValue) {
        changes[key] = {'from': oldValue, 'to': newValue};
      }
    }

    return changes;
  }
}

/// 图表模块日志工具类
class ChartLoggerUtils {
  static final ChartLogger _logger = ChartLogger();

  // 基础日志方法
  static void debug(String message, {Map<String, dynamic>? metadata}) =>
      _logger.debug(message, metadata: metadata);

  static void info(String message, {Map<String, dynamic>? metadata}) =>
      _logger.info(message, metadata: metadata);

  static void warning(String message, {Map<String, dynamic>? metadata}) =>
      _logger.warning(message, metadata: metadata);

  static void error(
    String message, {
    dynamic error,
    StackTrace? stackTrace,
    Map<String, dynamic>? metadata,
  }) => _logger.error(
    message,
    error: error,
    stackTrace: stackTrace,
    metadata: metadata,
  );

  // 图表特定方法
  static void logChartInitialization({
    required String chartId,
    required String chartType,
    required bool success,
    String? symbol,
    String? timeframe,
    Duration? initTime,
    Map<String, dynamic>? chartConfig,
    String? error,
  }) => _logger.logChartInitialization(
    chartId: chartId,
    chartType: chartType,
    success: success,
    symbol: symbol,
    timeframe: timeframe,
    initTime: initTime,
    chartConfig: chartConfig,
    error: error,
  );

  static void logDataLoading({
    required String chartId,
    required String symbol,
    required String timeframe,
    required bool success,
    int? dataPoints,
    Duration? loadTime,
    String? dataSource,
    String? error,
  }) => _logger.logDataLoading(
    chartId: chartId,
    symbol: symbol,
    timeframe: timeframe,
    success: success,
    dataPoints: dataPoints,
    loadTime: loadTime,
    dataSource: dataSource,
    error: error,
  );

  static void logTimeframeChange({
    required String chartId,
    required String symbol,
    required String fromTimeframe,
    required String toTimeframe,
    required bool success,
    Duration? switchTime,
    int? newDataPoints,
    String? error,
  }) => _logger.logTimeframeChange(
    chartId: chartId,
    symbol: symbol,
    fromTimeframe: fromTimeframe,
    toTimeframe: toTimeframe,
    success: success,
    switchTime: switchTime,
    newDataPoints: newDataPoints,
    error: error,
  );
}
