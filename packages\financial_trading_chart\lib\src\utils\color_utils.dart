import 'dart:math' as math;
import 'package:flutter/material.dart';

/// 颜色工具类
///
/// 提供颜色处理、转换、生成等功能，为图表主题和样式提供支持
class ColorUtils {
  ColorUtils._();

  /// 从十六进制字符串创建颜色
  ///
  /// 支持格式：#RGB, #ARGB, #RRGGBB, #AARRGGBB
  static Color fromHex(String hexString) {
    final buffer = StringBuffer();
    if (hexString.length == 6 || hexString.length == 7) buffer.write('ff');
    buffer.write(hexString.replaceFirst('#', ''));
    return Color(int.parse(buffer.toString(), radix: 16));
  }

  /// 将颜色转换为十六进制字符串
  static String toHex(Color color, {bool includeAlpha = true}) {
    if (includeAlpha) {
      return '#${color.value.toRadixString(16).padLeft(8, '0').toUpperCase()}';
    } else {
      return '#${(color.value & 0xFFFFFF).toRadixString(16).padLeft(6, '0').toUpperCase()}';
    }
  }

  /// 创建渐变色
  static LinearGradient createGradient({
    required Color startColor,
    required Color endColor,
    AlignmentGeometry begin = Alignment.topCenter,
    AlignmentGeometry end = Alignment.bottomCenter,
    List<double>? stops,
  }) {
    return LinearGradient(
      begin: begin,
      end: end,
      colors: [startColor, endColor],
      stops: stops,
    );
  }

  /// 创建多色渐变
  static LinearGradient createMultiColorGradient({
    required List<Color> colors,
    AlignmentGeometry begin = Alignment.topCenter,
    AlignmentGeometry end = Alignment.bottomCenter,
    List<double>? stops,
  }) {
    return LinearGradient(begin: begin, end: end, colors: colors, stops: stops);
  }

  /// 创建径向渐变
  static RadialGradient createRadialGradient({
    required Color centerColor,
    required Color edgeColor,
    Alignment center = Alignment.center,
    double radius = 0.5,
    List<double>? stops,
  }) {
    return RadialGradient(
      center: center,
      radius: radius,
      colors: [centerColor, edgeColor],
      stops: stops,
    );
  }

  /// 调整颜色亮度
  static Color adjustBrightness(Color color, double factor) {
    final hsl = HSLColor.fromColor(color);
    final lightness = (hsl.lightness * factor).clamp(0.0, 1.0);
    return hsl.withLightness(lightness).toColor();
  }

  /// 调整颜色饱和度
  static Color adjustSaturation(Color color, double factor) {
    final hsl = HSLColor.fromColor(color);
    final saturation = (hsl.saturation * factor).clamp(0.0, 1.0);
    return hsl.withSaturation(saturation).toColor();
  }

  /// 调整颜色透明度
  static Color adjustOpacity(Color color, double opacity) {
    return color.withOpacity(opacity.clamp(0.0, 1.0));
  }

  /// 混合两种颜色
  static Color blendColors(Color color1, Color color2, double ratio) {
    final r = (color1.red * (1 - ratio) + color2.red * ratio).round();
    final g = (color1.green * (1 - ratio) + color2.green * ratio).round();
    final b = (color1.blue * (1 - ratio) + color2.blue * ratio).round();
    final a = (color1.alpha * (1 - ratio) + color2.alpha * ratio).round();
    return Color.fromARGB(a, r, g, b);
  }

  /// 获取颜色的互补色
  static Color getComplementaryColor(Color color) {
    final hsl = HSLColor.fromColor(color);
    final complementaryHue = (hsl.hue + 180) % 360;
    return hsl.withHue(complementaryHue).toColor();
  }

  /// 获取颜色的类似色
  static List<Color> getAnalogousColors(Color color, {int count = 2}) {
    final hsl = HSLColor.fromColor(color);
    final colors = <Color>[];

    for (int i = 1; i <= count; i++) {
      final hue1 = (hsl.hue + 30 * i) % 360;
      final hue2 = (hsl.hue - 30 * i) % 360;
      colors.add(hsl.withHue(hue1).toColor());
      colors.add(hsl.withHue(hue2).toColor());
    }

    return colors;
  }

  /// 获取颜色的三元色
  static List<Color> getTriadicColors(Color color) {
    final hsl = HSLColor.fromColor(color);
    return [
      hsl.withHue((hsl.hue + 120) % 360).toColor(),
      hsl.withHue((hsl.hue + 240) % 360).toColor(),
    ];
  }

  /// 生成颜色调色板
  static List<Color> generatePalette({
    required Color baseColor,
    int count = 5,
    PaletteType type = PaletteType.monochromatic,
  }) {
    switch (type) {
      case PaletteType.monochromatic:
        return _generateMonochromaticPalette(baseColor, count);
      case PaletteType.analogous:
        return _generateAnalogousPalette(baseColor, count);
      case PaletteType.complementary:
        return _generateComplementaryPalette(baseColor, count);
      case PaletteType.triadic:
        return _generateTriadicPalette(baseColor, count);
      case PaletteType.tetradic:
        return _generateTetradicPalette(baseColor, count);
    }
  }

  /// 生成单色调色板
  static List<Color> _generateMonochromaticPalette(Color baseColor, int count) {
    final hsl = HSLColor.fromColor(baseColor);
    final colors = <Color>[];

    for (int i = 0; i < count; i++) {
      final lightness = (0.2 + (0.6 * i / (count - 1))).clamp(0.0, 1.0);
      colors.add(hsl.withLightness(lightness).toColor());
    }

    return colors;
  }

  /// 生成类似色调色板
  static List<Color> _generateAnalogousPalette(Color baseColor, int count) {
    final hsl = HSLColor.fromColor(baseColor);
    final colors = <Color>[baseColor];

    final step = 60 / count;
    for (int i = 1; i < count; i++) {
      final hue = (hsl.hue + step * i) % 360;
      colors.add(hsl.withHue(hue).toColor());
    }

    return colors;
  }

  /// 生成互补色调色板
  static List<Color> _generateComplementaryPalette(Color baseColor, int count) {
    final complementary = getComplementaryColor(baseColor);
    final colors = <Color>[baseColor];

    for (int i = 1; i < count; i++) {
      final ratio = i / (count - 1);
      colors.add(blendColors(baseColor, complementary, ratio));
    }

    return colors;
  }

  /// 生成三元色调色板
  static List<Color> _generateTriadicPalette(Color baseColor, int count) {
    final triadic = getTriadicColors(baseColor);
    final colors = <Color>[baseColor, ...triadic];

    while (colors.length < count) {
      final index = colors.length % 3;
      final baseForVariation = colors[index];
      colors.add(adjustBrightness(baseForVariation, 0.8));
    }

    return colors.take(count).toList();
  }

  /// 生成四元色调色板
  static List<Color> _generateTetradicPalette(Color baseColor, int count) {
    final hsl = HSLColor.fromColor(baseColor);
    final colors = <Color>[
      baseColor,
      hsl.withHue((hsl.hue + 90) % 360).toColor(),
      hsl.withHue((hsl.hue + 180) % 360).toColor(),
      hsl.withHue((hsl.hue + 270) % 360).toColor(),
    ];

    while (colors.length < count) {
      final index = colors.length % 4;
      final baseForVariation = colors[index];
      colors.add(adjustBrightness(baseForVariation, 0.7));
    }

    return colors.take(count).toList();
  }

  /// 判断颜色是否为深色
  static bool isDark(Color color) {
    final luminance = color.computeLuminance();
    return luminance < 0.5;
  }

  /// 判断颜色是否为浅色
  static bool isLight(Color color) {
    return !isDark(color);
  }

  /// 获取适合的文字颜色（黑色或白色）
  static Color getContrastingTextColor(Color backgroundColor) {
    return isDark(backgroundColor) ? Colors.white : Colors.black;
  }

  /// 计算两种颜色的对比度
  static double calculateContrast(Color color1, Color color2) {
    final luminance1 = color1.computeLuminance();
    final luminance2 = color2.computeLuminance();

    final lighter = math.max(luminance1, luminance2);
    final darker = math.min(luminance1, luminance2);

    return (lighter + 0.05) / (darker + 0.05);
  }

  /// 生成随机颜色
  static Color generateRandomColor({
    double? minBrightness,
    double? maxBrightness,
    double? saturation,
  }) {
    final random = math.Random();
    final hue = random.nextDouble() * 360;
    final sat = saturation ?? (0.5 + random.nextDouble() * 0.5);
    final lightness = minBrightness != null && maxBrightness != null
        ? minBrightness + random.nextDouble() * (maxBrightness - minBrightness)
        : 0.3 + random.nextDouble() * 0.4;

    return HSLColor.fromAHSL(1.0, hue, sat, lightness).toColor();
  }
}

/// 金融图表常用颜色
class FinancialColors {
  FinancialColors._();

  static const Color bullish = Color(0xFF26A69A); // 上涨/牛市
  static const Color bearish = Color(0xFFEF5350); // 下跌/熊市
  static const Color neutral = Color(0xFF9E9E9E); // 中性
  static const Color volume = Color(0xFF546E7A); // 成交量
  static const Color grid = Color(0xFF1A2332); // 网格
  static const Color background = Color(0xFF0D1421); // 背景
  static const Color text = Color(0xFFE0E6ED); // 文字
  static const Color border = Color(0xFF2A3441); // 边框
  static const Color crosshair = Color(0xFF9E9E9E); // 十字线
  static const Color selection = Color(0xFF42A5F5); // 选中

  /// 获取趋势颜色
  static Color getTrendColor(double change) {
    if (change > 0) return bullish;
    if (change < 0) return bearish;
    return neutral;
  }

  /// 获取成交量颜色
  static Color getVolumeColor(bool isUp, {double opacity = 0.6}) {
    return (isUp ? bullish : bearish).withOpacity(opacity);
  }
}

/// 调色板类型
enum PaletteType {
  /// 单色
  monochromatic,

  /// 类似色
  analogous,

  /// 互补色
  complementary,

  /// 三元色
  triadic,

  /// 四元色
  tetradic,
}
