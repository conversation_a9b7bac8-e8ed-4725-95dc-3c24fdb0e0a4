import 'dart:math' as math;
import 'package:flutter/foundation.dart';

/// 性能工具类
/// 
/// 提供性能相关的工具方法
class PerformanceUtils {
  /// 计算帧率
  static double calculateFPS(List<int> frameTimes) {
    if (frameTimes.isEmpty) return 0.0;
    
    final avgFrameTime = frameTimes.reduce((a, b) => a + b) / frameTimes.length;
    return avgFrameTime > 0 ? 1000.0 / avgFrameTime : 0.0;
  }
  
  /// 计算平均值
  static double calculateAverage(List<double> values) {
    if (values.isEmpty) return 0.0;
    return values.reduce((a, b) => a + b) / values.length;
  }
  
  /// 计算中位数
  static double calculateMedian(List<double> values) {
    if (values.isEmpty) return 0.0;
    
    final sorted = List<double>.from(values)..sort();
    final middle = sorted.length ~/ 2;
    
    if (sorted.length % 2 == 0) {
      return (sorted[middle - 1] + sorted[middle]) / 2;
    } else {
      return sorted[middle];
    }
  }
  
  /// 计算百分位数
  static double calculatePercentile(List<double> values, double percentile) {
    if (values.isEmpty) return 0.0;
    
    final sorted = List<double>.from(values)..sort();
    final index = (percentile / 100) * (sorted.length - 1);
    
    if (index == index.floor()) {
      return sorted[index.toInt()];
    } else {
      final lower = sorted[index.floor()];
      final upper = sorted[index.ceil()];
      final fraction = index - index.floor();
      return lower + (upper - lower) * fraction;
    }
  }
  
  /// 计算标准差
  static double calculateStandardDeviation(List<double> values) {
    if (values.isEmpty) return 0.0;
    
    final mean = calculateAverage(values);
    final variance = values
        .map((value) => math.pow(value - mean, 2))
        .reduce((a, b) => a + b) / values.length;
    
    return math.sqrt(variance);
  }
  
  /// 检测性能异常
  static List<PerformanceAnomaly> detectAnomalies(List<double> values, {
    double threshold = 2.0,
  }) {
    if (values.length < 10) return [];
    
    final mean = calculateAverage(values);
    final stdDev = calculateStandardDeviation(values);
    final anomalies = <PerformanceAnomaly>[];
    
    for (int i = 0; i < values.length; i++) {
      final value = values[i];
      final zScore = (value - mean) / stdDev;
      
      if (zScore.abs() > threshold) {
        anomalies.add(PerformanceAnomaly(
          index: i,
          value: value,
          zScore: zScore,
          severity: _getAnomalySeverity(zScore.abs()),
        ));
      }
    }
    
    return anomalies;
  }
  
  /// 获取异常严重程度
  static AnomalySeverity _getAnomalySeverity(double zScore) {
    if (zScore > 3.0) return AnomalySeverity.critical;
    if (zScore > 2.5) return AnomalySeverity.high;
    if (zScore > 2.0) return AnomalySeverity.medium;
    return AnomalySeverity.low;
  }
  
  /// 计算性能评分
  static double calculatePerformanceScore({
    required double fps,
    required double avgRenderTime,
    required int memoryUsage,
    required int frameDrops,
  }) {
    double score = 100.0;
    
    // FPS评分 (40%)
    if (fps >= 60) {
      score *= 1.0;
    } else if (fps >= 55) {
      score *= 0.95;
    } else if (fps >= 45) {
      score *= 0.85;
    } else if (fps >= 30) {
      score *= 0.7;
    } else {
      score *= 0.5;
    }
    
    // 渲染时间评分 (30%)
    if (avgRenderTime <= 8) {
      score *= 1.0;
    } else if (avgRenderTime <= 16) {
      score *= 0.9;
    } else if (avgRenderTime <= 33) {
      score *= 0.7;
    } else {
      score *= 0.5;
    }
    
    // 内存使用评分 (20%)
    final memoryMB = memoryUsage / (1024 * 1024);
    if (memoryMB <= 50) {
      score *= 1.0;
    } else if (memoryMB <= 100) {
      score *= 0.9;
    } else if (memoryMB <= 150) {
      score *= 0.8;
    } else {
      score *= 0.6;
    }
    
    // 掉帧评分 (10%)
    if (frameDrops <= 5) {
      score *= 1.0;
    } else if (frameDrops <= 10) {
      score *= 0.95;
    } else if (frameDrops <= 20) {
      score *= 0.9;
    } else {
      score *= 0.8;
    }
    
    return math.max(0.0, math.min(100.0, score));
  }
  
  /// 生成性能报告
  static String generatePerformanceReport({
    required double fps,
    required double avgRenderTime,
    required double maxRenderTime,
    required int memoryUsage,
    required int frameDrops,
    required int totalFrames,
  }) {
    final score = calculatePerformanceScore(
      fps: fps,
      avgRenderTime: avgRenderTime,
      memoryUsage: memoryUsage,
      frameDrops: frameDrops,
    );
    
    final buffer = StringBuffer();
    buffer.writeln('=== 性能报告 ===');
    buffer.writeln('总体评分: ${score.toStringAsFixed(1)}/100');
    buffer.writeln('');
    buffer.writeln('帧率指标:');
    buffer.writeln('  - 平均FPS: ${fps.toStringAsFixed(1)}');
    buffer.writeln('  - 总帧数: $totalFrames');
    buffer.writeln('  - 掉帧次数: $frameDrops');
    buffer.writeln('');
    buffer.writeln('渲染指标:');
    buffer.writeln('  - 平均渲染时间: ${avgRenderTime.toStringAsFixed(1)}ms');
    buffer.writeln('  - 最大渲染时间: ${maxRenderTime.toStringAsFixed(1)}ms');
    buffer.writeln('');
    buffer.writeln('内存指标:');
    buffer.writeln('  - 内存使用: ${(memoryUsage / 1024 / 1024).toStringAsFixed(1)}MB');
    buffer.writeln('');
    buffer.writeln('性能等级: ${_getPerformanceGrade(score)}');
    buffer.writeln('================');
    
    return buffer.toString();
  }
  
  /// 获取性能等级
  static String _getPerformanceGrade(double score) {
    if (score >= 90) return '优秀 (A)';
    if (score >= 80) return '良好 (B)';
    if (score >= 70) return '一般 (C)';
    if (score >= 60) return '及格 (D)';
    return '不及格 (F)';
  }
  
  /// 格式化内存大小
  static String formatMemorySize(int bytes) {
    if (bytes < 1024) {
      return '${bytes}B';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)}KB';
    } else if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / 1024 / 1024).toStringAsFixed(1)}MB';
    } else {
      return '${(bytes / 1024 / 1024 / 1024).toStringAsFixed(1)}GB';
    }
  }
  
  /// 格式化时间
  static String formatDuration(Duration duration) {
    if (duration.inMilliseconds < 1000) {
      return '${duration.inMilliseconds}ms';
    } else if (duration.inSeconds < 60) {
      return '${duration.inSeconds}s';
    } else if (duration.inMinutes < 60) {
      return '${duration.inMinutes}m ${duration.inSeconds % 60}s';
    } else {
      return '${duration.inHours}h ${duration.inMinutes % 60}m';
    }
  }
  
  /// 计算数据压缩率
  static double calculateCompressionRatio(int originalSize, int compressedSize) {
    if (originalSize == 0) return 0.0;
    return (originalSize - compressedSize) / originalSize;
  }
  
  /// 估算内存使用量
  static int estimateMemoryUsage({
    required int dataPoints,
    required int indicators,
    required bool enableCaching,
  }) {
    // 基础数据内存使用 (每个数据点约100字节)
    int baseMemory = dataPoints * 100;
    
    // 指标内存使用 (每个指标约为基础数据的20%)
    int indicatorMemory = (baseMemory * 0.2 * indicators).toInt();
    
    // 缓存内存使用
    int cacheMemory = enableCaching ? (baseMemory * 0.3).toInt() : 0;
    
    // 渲染内存使用 (固定开销)
    int renderMemory = 10 * 1024 * 1024; // 10MB
    
    return baseMemory + indicatorMemory + cacheMemory + renderMemory;
  }
  
  /// 获取性能优化建议
  static List<String> getOptimizationSuggestions({
    required double fps,
    required double avgRenderTime,
    required int memoryUsage,
    required int frameDrops,
    required int dataPoints,
  }) {
    final suggestions = <String>[];
    
    // FPS优化建议
    if (fps < 45) {
      suggestions.add('FPS过低，建议启用数据虚拟化或减少数据点数量');
    }
    
    // 渲染时间优化建议
    if (avgRenderTime > 16) {
      suggestions.add('渲染时间过长，建议启用渲染缓存或批量渲染');
    }
    
    // 内存优化建议
    final memoryMB = memoryUsage / (1024 * 1024);
    if (memoryMB > 150) {
      suggestions.add('内存使用过高，建议启用内存优化或清理缓存');
    }
    
    // 掉帧优化建议
    if (frameDrops > 10) {
      suggestions.add('掉帧过多，建议优化交互响应或减少动画复杂度');
    }
    
    // 数据量优化建议
    if (dataPoints > 10000) {
      suggestions.add('数据点过多，建议启用数据采样或分页加载');
    }
    
    return suggestions;
  }
  
  /// 性能基准测试
  static Future<BenchmarkResult> runBenchmark({
    required Function() testFunction,
    int iterations = 100,
  }) async {
    final times = <int>[];
    final stopwatch = Stopwatch();
    
    // 预热
    for (int i = 0; i < 10; i++) {
      testFunction();
    }
    
    // 正式测试
    for (int i = 0; i < iterations; i++) {
      stopwatch.start();
      testFunction();
      stopwatch.stop();
      
      times.add(stopwatch.elapsedMicroseconds);
      stopwatch.reset();
    }
    
    final avgTime = times.reduce((a, b) => a + b) / times.length;
    final minTime = times.reduce(math.min);
    final maxTime = times.reduce(math.max);
    
    return BenchmarkResult(
      iterations: iterations,
      averageTime: avgTime,
      minTime: minTime.toDouble(),
      maxTime: maxTime.toDouble(),
      times: times.map((t) => t.toDouble()).toList(),
    );
  }
}

/// 性能异常
@immutable
class PerformanceAnomaly {
  final int index;
  final double value;
  final double zScore;
  final AnomalySeverity severity;
  
  const PerformanceAnomaly({
    required this.index,
    required this.value,
    required this.zScore,
    required this.severity,
  });
  
  @override
  String toString() {
    return 'PerformanceAnomaly('
           'index: $index, '
           'value: $value, '
           'zScore: ${zScore.toStringAsFixed(2)}, '
           'severity: $severity)';
  }
}

/// 异常严重程度
enum AnomalySeverity {
  low,
  medium,
  high,
  critical,
}

/// 基准测试结果
@immutable
class BenchmarkResult {
  final int iterations;
  final double averageTime;
  final double minTime;
  final double maxTime;
  final List<double> times;
  
  const BenchmarkResult({
    required this.iterations,
    required this.averageTime,
    required this.minTime,
    required this.maxTime,
    required this.times,
  });
  
  /// 标准差
  double get standardDeviation {
    return PerformanceUtils.calculateStandardDeviation(times);
  }
  
  /// 95百分位数
  double get p95 {
    return PerformanceUtils.calculatePercentile(times, 95);
  }
  
  /// 99百分位数
  double get p99 {
    return PerformanceUtils.calculatePercentile(times, 99);
  }
  
  @override
  String toString() {
    return 'BenchmarkResult('
           'iterations: $iterations, '
           'avg: ${averageTime.toStringAsFixed(1)}μs, '
           'min: ${minTime.toStringAsFixed(1)}μs, '
           'max: ${maxTime.toStringAsFixed(1)}μs, '
           'p95: ${p95.toStringAsFixed(1)}μs, '
           'p99: ${p99.toStringAsFixed(1)}μs)';
  }
}
