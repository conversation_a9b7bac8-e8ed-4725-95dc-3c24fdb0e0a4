import 'dart:convert';
import 'package:flutter/foundation.dart';

/// 图表桥接器
/// 
/// 负责 Flutter 与 WebView 中的 JavaScript 图表之间的通信
class ChartBridge {
  /// 数据请求回调
  final Function(Map<String, dynamic>)? onDataRequest;

  /// 图表事件回调
  final Function(TradingViewEvent)? onChartEvent;

  /// 错误回调
  final Function(String)? onError;

  /// 消息处理器映射
  final Map<String, Function(Map<String, dynamic>)> _messageHandlers = {};

  ChartBridge({
    this.onDataRequest,
    this.onChartEvent,
    this.onError,
  }) {
    _initializeHandlers();
  }

  /// 初始化消息处理器
  void _initializeHandlers() {
    _messageHandlers['chartReady'] = _handleChartReady;
    _messageHandlers['dataUpdated'] = _handleDataUpdated;
    _messageHandlers['themeUpdated'] = _handleThemeUpdated;
    _messageHandlers['crosshairMove'] = _handleCrosshairMove;
    _messageHandlers['chartClick'] = _handleChartClick;
    _messageHandlers['error'] = _handleError;
    _messageHandlers['dataRequest'] = _handleDataRequest;
    _messageHandlers['zoomChanged'] = _handleZoomChanged;
    _messageHandlers['timeRangeChanged'] = _handleTimeRangeChanged;
  }

  /// 处理来自 WebView 的消息
  void handleMessage(String message) {
    try {
      final data = jsonDecode(message) as Map<String, dynamic>;
      final type = data['type'] as String?;
      final payload = data['data'] as Map<String, dynamic>? ?? {};

      if (type != null && _messageHandlers.containsKey(type)) {
        _messageHandlers[type]!(payload);
      } else {
        debugPrint('Unknown message type: $type');
      }
    } catch (e) {
      debugPrint('Error handling message: $e');
      _notifyError('Message parsing error: $e');
    }
  }

  /// 处理图表就绪事件
  void _handleChartReady(Map<String, dynamic> data) {
    _notifyChartEvent(TradingViewEvent(
      type: TradingViewEventType.chartReady,
      data: data,
    ));
  }

  /// 处理数据更新事件
  void _handleDataUpdated(Map<String, dynamic> data) {
    _notifyChartEvent(TradingViewEvent(
      type: TradingViewEventType.dataUpdated,
      data: data,
    ));
  }

  /// 处理主题更新事件
  void _handleThemeUpdated(Map<String, dynamic> data) {
    _notifyChartEvent(TradingViewEvent(
      type: TradingViewEventType.themeUpdated,
      data: data,
    ));
  }

  /// 处理十字线移动事件
  void _handleCrosshairMove(Map<String, dynamic> data) {
    _notifyChartEvent(TradingViewEvent(
      type: TradingViewEventType.crosshairMove,
      data: data,
    ));
  }

  /// 处理图表点击事件
  void _handleChartClick(Map<String, dynamic> data) {
    _notifyChartEvent(TradingViewEvent(
      type: TradingViewEventType.chartClick,
      data: data,
    ));
  }

  /// 处理错误事件
  void _handleError(Map<String, dynamic> data) {
    final message = data['message'] as String? ?? 'Unknown error';
    _notifyError(message);
  }

  /// 处理数据请求事件
  void _handleDataRequest(Map<String, dynamic> data) {
    if (onDataRequest != null) {
      onDataRequest!(data);
    }
  }

  /// 处理缩放变化事件
  void _handleZoomChanged(Map<String, dynamic> data) {
    _notifyChartEvent(TradingViewEvent(
      type: TradingViewEventType.zoomChanged,
      data: data,
    ));
  }

  /// 处理时间范围变化事件
  void _handleTimeRangeChanged(Map<String, dynamic> data) {
    _notifyChartEvent(TradingViewEvent(
      type: TradingViewEventType.timeRangeChanged,
      data: data,
    ));
  }

  /// 通知图表事件
  void _notifyChartEvent(TradingViewEvent event) {
    if (onChartEvent != null) {
      onChartEvent!(event);
    }
  }

  /// 通知错误
  void _notifyError(String error) {
    if (onError != null) {
      onError!(error);
    }
  }

  /// 发送消息到 WebView
  String createMessage(String type, Map<String, dynamic> data) {
    return jsonEncode({
      'type': type,
      'data': data,
    });
  }

  /// 创建数据更新消息
  String createDataUpdateMessage(List<Map<String, dynamic>> data) {
    return createMessage('updateData', {'data': data});
  }

  /// 创建主题更新消息
  String createThemeUpdateMessage(Map<String, dynamic> theme) {
    return createMessage('updateTheme', theme);
  }

  /// 创建配置更新消息
  String createConfigUpdateMessage(Map<String, dynamic> config) {
    return createMessage('updateConfig', config);
  }

  /// 创建缩放消息
  String createZoomMessage(double scale) {
    return createMessage('zoom', {'scale': scale});
  }

  /// 创建平移消息
  String createPanMessage(double deltaX, double deltaY) {
    return createMessage('pan', {'deltaX': deltaX, 'deltaY': deltaY});
  }

  /// 创建时间范围设置消息
  String createSetTimeRangeMessage(String from, String to) {
    return createMessage('setTimeRange', {'from': from, 'to': to});
  }

  /// 创建指标添加消息
  String createAddIndicatorMessage(String type, Map<String, dynamic> options) {
    return createMessage('addIndicator', {'type': type, 'options': options});
  }

  /// 创建指标移除消息
  String createRemoveIndicatorMessage(String indicatorId) {
    return createMessage('removeIndicator', {'indicatorId': indicatorId});
  }

  /// 创建标记添加消息
  String createAddMarkerMessage(Map<String, dynamic> marker) {
    return createMessage('addMarker', marker);
  }

  /// 创建标记移除消息
  String createRemoveMarkerMessage(String markerId) {
    return createMessage('removeMarker', {'markerId': markerId});
  }

  /// 创建截图消息
  String createTakeScreenshotMessage() {
    return createMessage('takeScreenshot', {});
  }

  /// 创建导出数据消息
  String createExportDataMessage(String format) {
    return createMessage('exportData', {'format': format});
  }

  /// 释放资源
  void dispose() {
    _messageHandlers.clear();
  }
}

/// TradingView 事件
class TradingViewEvent {
  /// 事件类型
  final TradingViewEventType type;

  /// 事件数据
  final Map<String, dynamic> data;

  /// 时间戳
  final DateTime timestamp;

  TradingViewEvent({
    required this.type,
    required this.data,
    DateTime? timestamp,
  }) : timestamp = timestamp ?? DateTime.now();

  @override
  String toString() {
    return 'TradingViewEvent(type: $type, data: $data, timestamp: $timestamp)';
  }
}

/// TradingView 事件类型
enum TradingViewEventType {
  /// 图表就绪
  chartReady,

  /// 数据更新
  dataUpdated,

  /// 主题更新
  themeUpdated,

  /// 十字线移动
  crosshairMove,

  /// 图表点击
  chartClick,

  /// 缩放变化
  zoomChanged,

  /// 时间范围变化
  timeRangeChanged,

  /// 指标添加
  indicatorAdded,

  /// 指标移除
  indicatorRemoved,

  /// 标记添加
  markerAdded,

  /// 标记移除
  markerRemoved,

  /// 截图完成
  screenshotTaken,

  /// 数据导出
  dataExported,

  /// 错误
  error,
}

/// 图表消息构建器
class ChartMessageBuilder {
  static const String _typeKey = 'type';
  static const String _dataKey = 'data';

  /// 构建基础消息
  static Map<String, dynamic> buildMessage(String type, [Map<String, dynamic>? data]) {
    return {
      _typeKey: type,
      _dataKey: data ?? {},
    };
  }

  /// 构建数据消息
  static Map<String, dynamic> buildDataMessage(List<Map<String, dynamic>> data) {
    return buildMessage('setData', {'data': data});
  }

  /// 构建配置消息
  static Map<String, dynamic> buildConfigMessage(Map<String, dynamic> config) {
    return buildMessage('setConfig', config);
  }

  /// 构建主题消息
  static Map<String, dynamic> buildThemeMessage(Map<String, dynamic> theme) {
    return buildMessage('setTheme', theme);
  }

  /// 构建命令消息
  static Map<String, dynamic> buildCommandMessage(String command, [Map<String, dynamic>? params]) {
    return buildMessage('command', {
      'command': command,
      'params': params ?? {},
    });
  }

  /// 解析消息
  static Map<String, dynamic>? parseMessage(String message) {
    try {
      final data = jsonDecode(message);
      if (data is Map<String, dynamic> && 
          data.containsKey(_typeKey) && 
          data.containsKey(_dataKey)) {
        return data;
      }
    } catch (e) {
      debugPrint('Error parsing message: $e');
    }
    return null;
  }

  /// 获取消息类型
  static String? getMessageType(Map<String, dynamic> message) {
    return message[_typeKey] as String?;
  }

  /// 获取消息数据
  static Map<String, dynamic>? getMessageData(Map<String, dynamic> message) {
    return message[_dataKey] as Map<String, dynamic>?;
  }
}
