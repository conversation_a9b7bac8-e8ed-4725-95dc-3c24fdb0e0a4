import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'dart:convert';
import '../models/chart_data.dart';
import '../config/chart_theme.dart';
import 'chart_bridge.dart';

/// TradingView 图表组件
///
/// 基于 WebView 集成 TradingView 图表库，提供专业的金融图表功能
class TradingViewChart extends StatefulWidget {
  /// 图表数据
  final List<KLineData> data;

  /// 图表主题
  final ChartTheme theme;

  /// 图表配置
  final TradingViewConfig config;

  /// 数据更新回调
  final ValueChanged<List<KLineData>>? onDataUpdated;

  /// 图表事件回调
  final ValueChanged<TradingViewEvent>? onChartEvent;

  /// 错误回调
  final ValueChanged<String>? onError;

  const TradingViewChart({
    Key? key,
    required this.data,
    required this.theme,
    this.config = const TradingViewConfig(),
    this.onDataUpdated,
    this.onChartEvent,
    this.onError,
  }) : super(key: key);

  @override
  State<TradingViewChart> createState() => _TradingViewChartState();
}

class _TradingViewChartState extends State<TradingViewChart> {
  late WebViewController _controller;
  late ChartBridge _bridge;
  bool _isLoaded = false;
  String? _error;

  @override
  void initState() {
    super.initState();
    _initializeWebView();
    _bridge = ChartBridge(
      onDataRequest: _handleDataRequest,
      onChartEvent: _handleChartEvent,
      onError: _handleError,
    );
  }

  /// 初始化 WebView
  void _initializeWebView() {
    _controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setBackgroundColor(widget.theme.backgroundColor)
      ..setNavigationDelegate(
        NavigationDelegate(
          onProgress: (int progress) {
            // 加载进度
          },
          onPageStarted: (String url) {
            setState(() {
              _isLoaded = false;
              _error = null;
            });
          },
          onPageFinished: (String url) {
            _onPageLoaded();
          },
          onWebResourceError: (WebResourceError error) {
            _handleError('WebView Error: ${error.description}');
          },
        ),
      )
      ..addJavaScriptChannel(
        'ChartBridge',
        onMessageReceived: (JavaScriptMessage message) {
          _bridge.handleMessage(message.message);
        },
      );

    _loadChart();
  }

  /// 加载图表
  void _loadChart() {
    final htmlContent = _generateHtmlContent();
    _controller.loadHtmlString(htmlContent);
  }

  /// 生成 HTML 内容
  String _generateHtmlContent() {
    final themeData = _generateThemeData();
    final configData = _generateConfigData();
    final initialData = _generateInitialData();

    return '''
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TradingView Chart</title>
    <script type="text/javascript" src="https://unpkg.com/lightweight-charts/dist/lightweight-charts.standalone.production.js"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            background-color: ${_colorToHex(widget.theme.backgroundColor)};
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        #chart-container {
            width: 100%;
            height: 100vh;
        }
    </style>
</head>
<body>
    <div id="chart-container"></div>
    
    <script>
        // 图表配置
        const chartConfig = $configData;
        
        // 主题配置
        const themeConfig = $themeData;
        
        // 初始数据
        const initialData = $initialData;
        
        // 创建图表
        const chart = LightweightCharts.createChart(
            document.getElementById('chart-container'),
            {
                ...chartConfig,
                ...themeConfig,
            }
        );
        
        // 创建蜡烛图系列
        const candlestickSeries = chart.addCandlestickSeries({
            upColor: '${_colorToHex(widget.theme.upColor)}',
            downColor: '${_colorToHex(widget.theme.downColor)}',
            borderVisible: false,
            wickUpColor: '${_colorToHex(widget.theme.upColor)}',
            wickDownColor: '${_colorToHex(widget.theme.downColor)}',
        });
        
        // 设置初始数据
        if (initialData && initialData.length > 0) {
            candlestickSeries.setData(initialData);
        }
        
        // 创建成交量系列
        const volumeSeries = chart.addHistogramSeries({
            color: '${_colorToHex(widget.theme.volumeColor)}',
            priceFormat: {
                type: 'volume',
            },
            priceScaleId: '',
            scaleMargins: {
                top: 0.8,
                bottom: 0,
            },
        });
        
        // 图表事件处理
        chart.subscribeCrosshairMove((param) => {
            if (param.time) {
                const data = param.seriesPrices.get(candlestickSeries);
                if (data) {
                    sendMessage('crosshairMove', {
                        time: param.time,
                        data: data
                    });
                }
            }
        });
        
        chart.subscribeClick((param) => {
            sendMessage('chartClick', {
                time: param.time,
                point: param.point
            });
        });
        
        // 消息发送函数
        function sendMessage(type, data) {
            if (window.ChartBridge) {
                window.ChartBridge.postMessage(JSON.stringify({
                    type: type,
                    data: data
                }));
            }
        }
        
        // 全局函数，供 Flutter 调用
        window.updateChartData = function(data) {
            try {
                const parsedData = JSON.parse(data);
                candlestickSeries.setData(parsedData);
                sendMessage('dataUpdated', { count: parsedData.length });
            } catch (error) {
                sendMessage('error', { message: error.message });
            }
        };
        
        window.updateChartTheme = function(theme) {
            try {
                const parsedTheme = JSON.parse(theme);
                chart.applyOptions(parsedTheme);
                sendMessage('themeUpdated', {});
            } catch (error) {
                sendMessage('error', { message: error.message });
            }
        };
        
        window.resizeChart = function() {
            chart.resize();
        };
        
        // 页面加载完成通知
        window.addEventListener('load', function() {
            sendMessage('chartReady', {});
        });
        
        // 页面大小变化处理
        window.addEventListener('resize', function() {
            chart.resize();
        });
    </script>
</body>
</html>
    ''';
  }

  /// 生成主题数据
  String _generateThemeData() {
    final theme = {
      'layout': {
        'backgroundColor': _colorToHex(widget.theme.backgroundColor),
        'textColor': _colorToHex(widget.theme.textColor),
      },
      'grid': {
        'vertLines': {'color': _colorToHex(widget.theme.gridColor)},
        'horzLines': {'color': _colorToHex(widget.theme.gridColor)},
      },
      'crosshair': {
        'mode': 0, // Normal
      },
      'priceScale': {'borderColor': _colorToHex(widget.theme.borderColor)},
      'timeScale': {
        'borderColor': _colorToHex(widget.theme.borderColor),
        'timeVisible': true,
        'secondsVisible': false,
      },
    };

    return jsonEncode(theme);
  }

  /// 生成配置数据
  String _generateConfigData() {
    final config = {
      'width': 0, // 自适应宽度
      'height': 0, // 自适应高度
      'rightPriceScale': {
        'visible': widget.config.showPriceScale,
        'borderVisible': widget.config.showBorder,
      },
      'leftPriceScale': {'visible': false},
      'timeScale': {
        'visible': widget.config.showTimeScale,
        'borderVisible': widget.config.showBorder,
      },
      'crosshair': {'mode': widget.config.crosshairMode},
      'handleScroll': {
        'mouseWheel': widget.config.enableMouseWheel,
        'pressedMouseMove': widget.config.enablePan,
      },
      'handleScale': {
        'axisPressedMouseMove': widget.config.enableAxisScale,
        'mouseWheel': widget.config.enableMouseWheelScale,
        'pinch': widget.config.enablePinchScale,
      },
    };

    return jsonEncode(config);
  }

  /// 生成初始数据
  String _generateInitialData() {
    final data = widget.data
        .map(
          (kline) => {
            'time': kline.time,
            'open': kline.open,
            'high': kline.high,
            'low': kline.low,
            'close': kline.close,
          },
        )
        .toList();

    return jsonEncode(data);
  }

  /// 颜色转换为十六进制
  String _colorToHex(Color color) {
    return '#${color.value.toRadixString(16).substring(2)}';
  }

  /// 页面加载完成
  void _onPageLoaded() {
    setState(() {
      _isLoaded = true;
    });
  }

  /// 处理数据请求
  void _handleDataRequest(Map<String, dynamic> request) {
    // 处理来自图表的数据请求
    if (widget.onDataUpdated != null) {
      widget.onDataUpdated!(widget.data);
    }
  }

  /// 处理图表事件
  void _handleChartEvent(TradingViewEvent event) {
    if (widget.onChartEvent != null) {
      widget.onChartEvent!(event);
    }
  }

  /// 处理错误
  void _handleError(String error) {
    setState(() {
      _error = error;
    });

    if (widget.onError != null) {
      widget.onError!(error);
    }
  }

  /// 更新图表数据
  void updateData(List<KLineData> newData) {
    if (_isLoaded) {
      final dataJson = _generateInitialData();
      _controller.runJavaScript('updateChartData(\'$dataJson\')');
    }
  }

  /// 更新图表主题
  void updateTheme(ChartTheme newTheme) {
    if (_isLoaded) {
      final themeJson = _generateThemeData();
      _controller.runJavaScript('updateChartTheme(\'$themeJson\')');
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 48, color: widget.theme.textColor),
            const SizedBox(height: 16),
            Text(
              'Chart Error',
              style: TextStyle(
                color: widget.theme.textColor,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _error!,
              style: TextStyle(
                color: widget.theme.textColor.withOpacity(0.7),
                fontSize: 14,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return Stack(
      children: [
        WebViewWidget(controller: _controller),
        if (!_isLoaded)
          Container(
            color: widget.theme.backgroundColor,
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(
                      widget.theme.selectionColor,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Loading Chart...',
                    style: TextStyle(
                      color: widget.theme.textColor,
                      fontSize: 16,
                    ),
                  ),
                ],
              ),
            ),
          ),
      ],
    );
  }

  @override
  void dispose() {
    _bridge.dispose();
    super.dispose();
  }
}

/// TradingView 配置
class TradingViewConfig {
  /// 是否显示价格刻度
  final bool showPriceScale;

  /// 是否显示时间刻度
  final bool showTimeScale;

  /// 是否显示边框
  final bool showBorder;

  /// 十字线模式
  final int crosshairMode;

  /// 是否启用鼠标滚轮
  final bool enableMouseWheel;

  /// 是否启用平移
  final bool enablePan;

  /// 是否启用坐标轴缩放
  final bool enableAxisScale;

  /// 是否启用鼠标滚轮缩放
  final bool enableMouseWheelScale;

  /// 是否启用捏合缩放
  final bool enablePinchScale;

  const TradingViewConfig({
    this.showPriceScale = true,
    this.showTimeScale = true,
    this.showBorder = true,
    this.crosshairMode = 0,
    this.enableMouseWheel = true,
    this.enablePan = true,
    this.enableAxisScale = true,
    this.enableMouseWheelScale = true,
    this.enablePinchScale = true,
  });
}
