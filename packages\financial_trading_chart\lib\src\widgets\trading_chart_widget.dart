import 'dart:convert';
import 'dart:math';
import '../models/chart_data.dart';
import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';

// 创建一个公共的 State Key，以便外部可以调用内部方法
final GlobalKey<TradingChartWidgetState> tradingChartKey = GlobalKey();

class TradingChartWidget extends StatefulWidget {
  /// 可见范围变化回调
  final Function(Map<String, dynamic>)? onVisibleRangeChanged;

  const TradingChartWidget({super.key, this.onVisibleRangeChanged});

  @override
  State<TradingChartWidget> createState() => TradingChartWidgetState();
}

class TradingChartWidgetState extends State<TradingChartWidget> {
  late final WebViewController _controller;
  bool _isChartReady = false;
  List<KLineData>? _pendingData; // 缓存待设置的数据
  String? _pendingTimeFrame; // 缓存待设置的时间段

  @override
  void initState() {
    super.initState();
    _initWebViewController();
  }

  void _initWebViewController() {
    _controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setBackgroundColor(const Color(0x00000000))
      ..setNavigationDelegate(
        NavigationDelegate(
          onPageFinished: (String url) {
            // 页面加载完成，可以开始进行一些初始化调用
          },
        ),
      )
      // 添加 JS -> Flutter 的通信桥梁
      ..addJavaScriptChannel(
        'ChartBridge',
        onMessageReceived: (JavaScriptMessage message) {
          try {
            final data = jsonDecode(message.message);
            debugPrint('📨 收到图表事件: ${data['type'] ?? data['event']}');

            if (data['event'] == 'onChartReady') {
              setState(() {
                _isChartReady = true;
              });
              debugPrint('✅ 图表已准备就绪');
              // 如果有缓存的数据，立即设置
              if (_pendingData != null) {
                _setDataInternal(_pendingData!, timeFrame: _pendingTimeFrame);
                _pendingData = null;
                _pendingTimeFrame = null;
              }
            } else if (data['type'] == 'visibleRangeChanged') {
              // 🔑 处理可见范围变化事件
              debugPrint('📊 图表可见范围变化: ${data['data']}');
              if (widget.onVisibleRangeChanged != null) {
                widget.onVisibleRangeChanged!(data['data']);
              }
            }
          } catch (e) {
            debugPrint('❌ 处理图表消息失败: $e');
          }
        },
      );
    // *** 关键修改：使用 loadFlutterAsset ***
    final assetPath = 'packages/financial_trading_chart/assets/html/chart.html';
    _controller.loadFlutterAsset(assetPath);
    // _loadHtmlFromAssets();
  }

  /// --- 公共 API：设置历史K线数据 ---
  void setData(List<KLineData> data, {String? timeFrame}) {
    if (!_isChartReady) {
      // 图表未准备好，缓存数据
      _pendingData = List.from(data);
      _pendingTimeFrame = timeFrame;
      debugPrint('📦 Chart not ready, caching data for later use.');
      return;
    }
    _setDataInternal(data, timeFrame: timeFrame);
  }

  /// 内部方法：实际设置数据
  void _setDataInternal(List<KLineData> data, {String? timeFrame}) {
    final jsonString = jsonEncode(data.map((d) => d.toJson()).toList());
    debugPrint(
      '➡️ Sending data to JS: ${jsonString.substring(0, 100)}...',
    ); // 打印部分数据

    // 🔍 调试：打印前几条数据的详细信息
    if (data.isNotEmpty) {
      debugPrint('🔍 Flutter端数据样本:');
      for (int i = 0; i < min(3, data.length); i++) {
        final item = data[i];
        debugPrint('  数据项 $i: ${item.toJson()}');
      }
      debugPrint('🔍 时间周期: $timeFrame');
    }

    // 构建JavaScript调用，包含timeFrame参数
    final timeFrameParam = timeFrame != null ? "'$timeFrame'" : 'null';

    // 🔧 添加安全检查，确保JavaScript对象已初始化
    final safeJavaScript =
        '''
      if (window.flutterChart && typeof window.flutterChart.setData === 'function') {
        window.flutterChart.setData(`$jsonString`, $timeFrameParam);
        console.log('✅ setData called successfully');
      } else {
        console.error('❌ window.flutterChart.setData is not available');
        console.log('window.flutterChart:', window.flutterChart);
      }
    ''';

    _controller.runJavaScript(safeJavaScript);
  }

  /// --- 公共 API：更新单条K线数据 ---
  void updateData(KLineData candle) {
    if (!_isChartReady) return;
    final jsonString = jsonEncode(candle.toJson());
    _controller.runJavaScript('window.flutterChart.update($jsonString)');
  }

  /// --- 公共 API：添加历史数据（保持当前可见范围）---
  void addHistoricalData(List<KLineData> data, {String? timeFrame}) {
    if (!_isChartReady) {
      // 图表未准备好，缓存数据
      _pendingData = List.from(data);
      _pendingTimeFrame = timeFrame;
      debugPrint('📦 Chart not ready, caching historical data for later use.');
      return;
    }
    _addHistoricalDataInternal(data, timeFrame: timeFrame);
  }

  /// 内部方法：实际添加历史数据
  void _addHistoricalDataInternal(List<KLineData> data, {String? timeFrame}) {
    final jsonString = jsonEncode(data.map((d) => d.toJson()).toList());
    debugPrint('➡️ Adding historical data to JS: ${data.length} items');

    // 构建JavaScript调用，包含timeFrame参数
    final timeFrameParam = timeFrame != null ? "'$timeFrame'" : 'null';

    // 🔧 添加安全检查，确保JavaScript对象已初始化
    final safeJavaScript =
        '''
      if (window.flutterChart && typeof window.flutterChart.addHistoricalData === 'function') {
        window.flutterChart.addHistoricalData(`$jsonString`, $timeFrameParam);
        console.log('✅ addHistoricalData called successfully');
      } else {
        console.error('❌ window.flutterChart.addHistoricalData is not available');
        console.log('window.flutterChart:', window.flutterChart);
        console.log('typeof addHistoricalData:', typeof (window.flutterChart && window.flutterChart.addHistoricalData));
      }
    ''';

    _controller.runJavaScript(safeJavaScript);
  }

  /// --- 公共 API：切换主题 ---
  void setTheme(String theme) {
    if (!_isChartReady) return;
    _controller.runJavaScript('window.flutterChart.setTheme("$theme")');
  }

  @override
  Widget build(BuildContext context) {
    return WebViewWidget(controller: _controller);
  }
}
