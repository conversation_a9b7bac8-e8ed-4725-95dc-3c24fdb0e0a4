// 时间格式转换测试脚本
// 可以在浏览器控制台中运行此脚本来验证时间转换逻辑

console.log('=== 时间格式转换测试 ===');

// 测试数据
const testData = [
    {
        name: 'ISO8601 timestamp',
        data: {
            timestamp: '2024-01-01T00:00:00.000Z',
            open: 100,
            high: 105,
            low: 98,
            close: 103
        }
    },
    {
        name: 'Date string time',
        data: {
            time: '2024-01-02',
            open: 103,
            high: 108,
            low: 101,
            close: 106
        }
    },
    {
        name: 'Millisecond timestamp',
        data: {
            time: 1704153600000, // 2024-01-02 00:00:00
            open: 106,
            high: 110,
            low: 104,
            close: 108
        }
    },
    {
        name: 'Second timestamp',
        data: {
            time: 1704240000, // 2024-01-03 00:00:00
            open: 108,
            high: 112,
            low: 106,
            close: 110
        }
    },
    {
        name: 'Invalid timestamp',
        data: {
            timestamp: 'invalid-date',
            open: 110,
            high: 115,
            low: 108,
            close: 113
        }
    }
];

// 时间转换函数（从 chart_controller.js 复制）
function convertTimeFormat(item, index) {
    try {
        let timeValue;
        
        if (item.timestamp) {
            // 优先使用 timestamp 字段（ISO8601 格式）
            const date = new Date(item.timestamp);
            if (isNaN(date.getTime())) {
                throw new Error('Invalid timestamp format');
            }
            // 转换为 YYYY-MM-DD 格式
            timeValue = date.toISOString().split('T')[0];
        } else if (item.time) {
            // 备用 time 字段
            if (typeof item.time === 'string') {
                // 如果已经是 YYYY-MM-DD 格式，直接使用
                if (/^\d{4}-\d{2}-\d{2}$/.test(item.time)) {
                    timeValue = item.time;
                } else {
                    // 否则尝试解析为日期
                    const date = new Date(item.time);
                    if (isNaN(date.getTime())) {
                        throw new Error('Invalid time format');
                    }
                    timeValue = date.toISOString().split('T')[0];
                }
            } else if (typeof item.time === 'number') {
                // 如果是数字时间戳，转换为日期
                const timestamp = item.time > 1000000000000 ? item.time : item.time * 1000;
                const date = new Date(timestamp);
                timeValue = date.toISOString().split('T')[0];
            } else {
                throw new Error('Unsupported time format');
            }
        } else {
            throw new Error('No timestamp or time field found');
        }

        // 验证数值字段
        const open = parseFloat(item.open);
        const high = parseFloat(item.high);
        const low = parseFloat(item.low);
        const close = parseFloat(item.close);

        if (isNaN(open) || isNaN(high) || isNaN(low) || isNaN(close)) {
            throw new Error('Invalid OHLC values');
        }

        // 验证 OHLC 数据的逻辑性
        if (high < Math.max(open, close) || low > Math.min(open, close)) {
            console.warn(`⚠️ Invalid OHLC data at index ${index}: high=${high}, low=${low}, open=${open}, close=${close}`);
        }

        return {
            time: timeValue,
            open: open,
            high: high,
            low: low,
            close: close
        };
    } catch (error) {
        console.error(`❌ Error processing data item ${index}:`, error.message, item);
        return null;
    }
}

// 运行测试
console.log('\n--- 开始测试 ---');

testData.forEach((test, index) => {
    console.log(`\n${index + 1}. 测试: ${test.name}`);
    console.log('输入数据:', test.data);
    
    const result = convertTimeFormat(test.data, index);
    
    if (result) {
        console.log('✅ 转换成功:', result);
        console.log(`   时间格式: ${result.time} (${typeof result.time})`);
    } else {
        console.log('❌ 转换失败');
    }
});

console.log('\n--- 测试完成 ---');

// 验证 LightweightCharts 兼容性
console.log('\n=== LightweightCharts 兼容性测试 ===');

const chartCompatibleData = testData
    .map((test, index) => convertTimeFormat(test.data, index))
    .filter(item => item !== null);

console.log('图表兼容数据:', chartCompatibleData);

// 检查时间排序
const timeValues = chartCompatibleData.map(item => item.time).sort();
console.log('时间排序:', timeValues);

// 检查时间格式一致性
const timeFormatRegex = /^\d{4}-\d{2}-\d{2}$/;
const allValidFormat = chartCompatibleData.every(item => timeFormatRegex.test(item.time));
console.log('时间格式一致性:', allValidFormat ? '✅ 通过' : '❌ 失败');

// 模拟 LightweightCharts 数据设置
console.log('\n=== 模拟图表数据设置 ===');
try {
    // 这里模拟 candlestickSeries.setData() 调用
    console.log('📊 模拟设置数据到图表...');
    console.log('数据点数量:', chartCompatibleData.length);
    console.log('时间范围:', 
        chartCompatibleData.length > 0 ? 
        `${chartCompatibleData[0].time} 到 ${chartCompatibleData[chartCompatibleData.length - 1].time}` : 
        '无数据'
    );
    console.log('✅ 模拟设置成功');
} catch (error) {
    console.error('❌ 模拟设置失败:', error);
}

console.log('\n🎉 所有测试完成！');

// 导出测试函数供其他地方使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        convertTimeFormat,
        testData,
        chartCompatibleData
    };
}
