import 'package:financial_app_core/financial_app_core.dart';

/// WebSocket客户端模块专用日志管理器
///
/// 提供WebSocket客户端模块特定的日志记录功能
class WsLogger extends ModuleLogger {
  static final WsLogger _instance = WsLogger._internal();
  factory WsLogger() => _instance;
  WsLogger._internal();

  @override
  String get moduleName => 'WebSocket';

  // ==================== WebSocket特定的日志方法 ====================

  /// WebSocket连接日志
  void logConnection({
    required String url,
    required String
    action, // 'connecting', 'connected', 'disconnected', 'failed'
    required bool success,
    String? connectionId,
    Duration? connectionTime,
    String? reason,
    String? error,
    Map<String, dynamic>? connectionInfo,
  }) {
    final metadata = {
      'url': url,
      'action': action,
      'success': success,
      'connection_id': connectionId,
      if (connectionTime != null)
        'connection_time_ms': connectionTime.inMilliseconds,
      'reason': reason,
      if (error != null) 'error': error,
      'connection_info': connectionInfo,
      'timestamp': DateTime.now().toIso8601String(),
    };

    switch (action) {
      case 'connecting':
        info('WebSocket连接中: $url', metadata: metadata);
        break;
      case 'connected':
        info('WebSocket连接成功: $url', metadata: metadata);
        if (connectionTime != null) {
          logPerformance(
            'websocket_connection',
            connectionTime,
            metadata: metadata,
          );
        }
        break;
      case 'disconnected':
        warning('WebSocket连接断开: $url', metadata: metadata);
        break;
      case 'failed':
        super.error('WebSocket连接失败: $url', metadata: metadata);
        break;
      default:
        info('WebSocket连接事件: $action', metadata: metadata);
    }

    logBusinessEvent('websocket_connection', metadata);
  }

  /// WebSocket重连日志
  void logReconnection({
    required String url,
    required int attemptNumber,
    required bool success,
    Duration? reconnectDelay,
    Duration? reconnectTime,
    String? strategy, // 'exponential_backoff', 'fixed_interval', 'immediate'
    String? error,
  }) {
    final metadata = {
      'url': url,
      'attempt_number': attemptNumber,
      'success': success,
      'reconnect_strategy': strategy,
      if (reconnectDelay != null)
        'reconnect_delay_ms': reconnectDelay.inMilliseconds,
      if (reconnectTime != null)
        'reconnect_time_ms': reconnectTime.inMilliseconds,
      if (error != null) 'error': error,
      'timestamp': DateTime.now().toIso8601String(),
    };

    if (success) {
      info('WebSocket重连成功 (第${attemptNumber}次尝试)', metadata: metadata);
      if (reconnectTime != null) {
        logPerformance(
          'websocket_reconnection',
          reconnectTime,
          metadata: metadata,
        );
      }
    } else {
      warning('WebSocket重连失败 (第${attemptNumber}次尝试)', metadata: metadata);
    }

    logBusinessEvent('websocket_reconnection', metadata);
  }

  /// 消息发送日志
  void logMessageSent({
    required String connectionId,
    required String messageType,
    required bool success,
    int? messageSize,
    String? messageId,
    Map<String, dynamic>? messageData,
    Duration? sendTime,
    String? error,
  }) {
    final metadata = {
      'connection_id': connectionId,
      'message_type': messageType,
      'message_id': messageId,
      'success': success,
      'message_size_bytes': messageSize,
      'message_data': messageData,
      if (sendTime != null) 'send_time_ms': sendTime.inMilliseconds,
      if (error != null) 'error': error,
      'timestamp': DateTime.now().toIso8601String(),
    };

    if (success) {
      debug('WebSocket消息发送成功: $messageType', metadata: metadata);
      if (sendTime != null) {
        logPerformance('websocket_message_send', sendTime, metadata: metadata);
      }
    } else {
      warning('WebSocket消息发送失败: $messageType', metadata: metadata);
    }

    logBusinessEvent('websocket_message_sent', metadata);
  }

  /// 消息接收日志
  void logMessageReceived({
    required String connectionId,
    required String messageType,
    required bool success,
    int? messageSize,
    String? messageId,
    Map<String, dynamic>? messageData,
    Duration? processingTime,
    String? error,
  }) {
    final metadata = {
      'connection_id': connectionId,
      'message_type': messageType,
      'message_id': messageId,
      'success': success,
      'message_size_bytes': messageSize,
      'message_data': messageData,
      if (processingTime != null)
        'processing_time_ms': processingTime.inMilliseconds,
      if (error != null) 'error': error,
      'timestamp': DateTime.now().toIso8601String(),
    };

    if (success) {
      debug('WebSocket消息接收成功: $messageType', metadata: metadata);
      if (processingTime != null) {
        logPerformance(
          'websocket_message_processing',
          processingTime,
          metadata: metadata,
        );
      }
    } else {
      warning('WebSocket消息接收失败: $messageType', metadata: metadata);
    }

    logBusinessEvent('websocket_message_received', metadata);
  }

  /// 订阅管理日志
  void logSubscription({
    required String connectionId,
    required String channel,
    required String action, // 'subscribe', 'unsubscribe'
    required bool success,
    List<String>? symbols,
    Map<String, dynamic>? subscriptionParams,
    String? subscriptionId,
    String? error,
  }) {
    final metadata = {
      'connection_id': connectionId,
      'channel': channel,
      'action': action,
      'success': success,
      'symbols': symbols,
      'symbols_count': symbols?.length ?? 0,
      'subscription_params': subscriptionParams,
      'subscription_id': subscriptionId,
      if (error != null) 'error': error,
      'timestamp': DateTime.now().toIso8601String(),
    };

    if (success) {
      info(
        'WebSocket${action == 'subscribe' ? '订阅' : '取消订阅'}成功: $channel',
        metadata: metadata,
      );
    } else {
      warning(
        'WebSocket${action == 'subscribe' ? '订阅' : '取消订阅'}失败: $channel',
        metadata: metadata,
      );
    }

    logBusinessEvent('websocket_subscription', metadata);
  }

  /// 心跳检测日志
  void logHeartbeat({
    required String connectionId,
    required String action, // 'send', 'receive', 'timeout'
    required bool success,
    Duration? responseTime,
    int? sequenceNumber,
    String? error,
  }) {
    final metadata = {
      'connection_id': connectionId,
      'action': action,
      'success': success,
      'sequence_number': sequenceNumber,
      if (responseTime != null) 'response_time_ms': responseTime.inMilliseconds,
      if (error != null) 'error': error,
      'timestamp': DateTime.now().toIso8601String(),
    };

    switch (action) {
      case 'send':
        debug('WebSocket心跳发送', metadata: metadata);
        break;
      case 'receive':
        debug('WebSocket心跳接收', metadata: metadata);
        if (responseTime != null) {
          logPerformance(
            'websocket_heartbeat',
            responseTime,
            metadata: metadata,
          );
        }
        break;
      case 'timeout':
        warning('WebSocket心跳超时', metadata: metadata);
        break;
      default:
        debug('WebSocket心跳事件: $action', metadata: metadata);
    }

    logBusinessEvent('websocket_heartbeat', metadata);
  }

  /// 连接池管理日志
  void logConnectionPool({
    required String action, // 'create', 'destroy', 'reuse', 'cleanup'
    required bool success,
    int? activeConnections,
    int? totalConnections,
    int? maxConnections,
    String? poolId,
    String? error,
  }) {
    final metadata = {
      'action': action,
      'success': success,
      'pool_id': poolId,
      'active_connections': activeConnections,
      'total_connections': totalConnections,
      'max_connections': maxConnections,
      'pool_utilization': activeConnections != null && maxConnections != null
          ? activeConnections / maxConnections
          : null,
      if (error != null) 'error': error,
      'timestamp': DateTime.now().toIso8601String(),
    };

    if (success) {
      info('WebSocket连接池操作成功: $action', metadata: metadata);
    } else {
      warning('WebSocket连接池操作失败: $action', metadata: metadata);
    }

    logBusinessEvent('websocket_connection_pool', metadata);
  }

  /// 数据流监控日志
  void logDataStream({
    required String connectionId,
    required String
    streamType, // 'market_data', 'trade_updates', 'account_updates'
    required Map<String, dynamic> streamMetrics,
    String? streamId,
    bool? isHealthy,
  }) {
    final metadata = {
      'connection_id': connectionId,
      'stream_id': streamId,
      'stream_type': streamType,
      'stream_metrics': streamMetrics,
      'is_healthy': isHealthy,
      'timestamp': DateTime.now().toIso8601String(),
    };

    if (isHealthy == true) {
      debug('WebSocket数据流正常: $streamType', metadata: metadata);
    } else if (isHealthy == false) {
      warning('WebSocket数据流异常: $streamType', metadata: metadata);
    } else {
      info('WebSocket数据流监控: $streamType', metadata: metadata);
    }

    logBusinessEvent('websocket_data_stream', metadata);
  }

  /// 错误处理日志
  void logError({
    required String connectionId,
    required String
    errorType, // 'connection_error', 'message_error', 'protocol_error'
    required String errorMessage,
    String? errorCode,
    Map<String, dynamic>? errorContext,
    bool? willRecover,
    String? recoveryAction,
  }) {
    final metadata = {
      'connection_id': connectionId,
      'error_type': errorType,
      'error_code': errorCode,
      'error_context': errorContext,
      'will_recover': willRecover,
      'recovery_action': recoveryAction,
      'timestamp': DateTime.now().toIso8601String(),
    };

    if (willRecover == true) {
      warning(
        'WebSocket错误 (将恢复): $errorType - $errorMessage',
        metadata: metadata,
      );
    } else {
      error('WebSocket错误: $errorType - $errorMessage', metadata: metadata);
    }

    logBusinessEvent('websocket_error', metadata);
  }

  /// 性能统计日志
  void logPerformanceStats({
    required String connectionId,
    required Duration period,
    required Map<String, dynamic> stats,
    String? connectionUrl,
  }) {
    final metadata = {
      'connection_id': connectionId,
      'connection_url': connectionUrl,
      'period_ms': period.inMilliseconds,
      'stats': stats,
      'timestamp': DateTime.now().toIso8601String(),
    };

    info('WebSocket性能统计', metadata: metadata);
    logBusinessEvent('websocket_performance_stats', metadata);
  }

  /// 连接质量监控日志
  void logConnectionQuality({
    required String connectionId,
    required Map<String, dynamic> qualityMetrics,
    required String qualityLevel, // 'excellent', 'good', 'fair', 'poor'
    List<String>? issues,
    Map<String, dynamic>? recommendations,
  }) {
    final metadata = {
      'connection_id': connectionId,
      'quality_metrics': qualityMetrics,
      'quality_level': qualityLevel,
      'issues': issues,
      'issues_count': issues?.length ?? 0,
      'recommendations': recommendations,
      'timestamp': DateTime.now().toIso8601String(),
    };

    switch (qualityLevel) {
      case 'excellent':
      case 'good':
        debug('WebSocket连接质量: $qualityLevel', metadata: metadata);
        break;
      case 'fair':
        info('WebSocket连接质量: $qualityLevel', metadata: metadata);
        break;
      case 'poor':
        warning('WebSocket连接质量: $qualityLevel', metadata: metadata);
        break;
      default:
        info('WebSocket连接质量监控: $qualityLevel', metadata: metadata);
    }

    logBusinessEvent('websocket_connection_quality', metadata);
  }
}

/// WebSocket模块日志工具类
class WsLoggerUtils {
  static final WsLogger _logger = WsLogger();

  // 基础日志方法
  static void debug(String message, {Map<String, dynamic>? metadata}) =>
      _logger.debug(message, metadata: metadata);

  static void info(String message, {Map<String, dynamic>? metadata}) =>
      _logger.info(message, metadata: metadata);

  static void warning(String message, {Map<String, dynamic>? metadata}) =>
      _logger.warning(message, metadata: metadata);

  static void error(
    String message, {
    dynamic error,
    StackTrace? stackTrace,
    Map<String, dynamic>? metadata,
  }) => _logger.error(
    message,
    error: error,
    stackTrace: stackTrace,
    metadata: metadata,
  );

  // WebSocket特定方法
  static void logConnection({
    required String url,
    required String action,
    required bool success,
    String? connectionId,
    Duration? connectionTime,
    String? reason,
    String? error,
    Map<String, dynamic>? connectionInfo,
  }) => _logger.logConnection(
    url: url,
    action: action,
    success: success,
    connectionId: connectionId,
    connectionTime: connectionTime,
    reason: reason,
    error: error,
    connectionInfo: connectionInfo,
  );

  static void logSubscription({
    required String connectionId,
    required String channel,
    required String action,
    required bool success,
    List<String>? symbols,
    Map<String, dynamic>? subscriptionParams,
    String? subscriptionId,
    String? error,
  }) => _logger.logSubscription(
    connectionId: connectionId,
    channel: channel,
    action: action,
    success: success,
    symbols: symbols,
    subscriptionParams: subscriptionParams,
    subscriptionId: subscriptionId,
    error: error,
  );

  static void logError({
    required String connectionId,
    required String errorType,
    required String errorMessage,
    String? errorCode,
    Map<String, dynamic>? errorContext,
    bool? willRecover,
    String? recoveryAction,
  }) => _logger.logError(
    connectionId: connectionId,
    errorType: errorType,
    errorMessage: errorMessage,
    errorCode: errorCode,
    errorContext: errorContext,
    willRecover: willRecover,
    recoveryAction: recoveryAction,
  );
}
