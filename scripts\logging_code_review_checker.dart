import 'dart:io';

/// 日志代码审查检查工具
/// 
/// 自动检查代码中的日志使用是否符合团队规范
Future<void> main(List<String> args) async {
  print('🔍 开始日志代码审查检查...\n');
  
  if (args.isEmpty) {
    print('用法: dart logging_code_review_checker.dart <文件路径或目录路径>');
    print('示例: dart logging_code_review_checker.dart packages/financial_app_market/lib');
    return;
  }
  
  final targetPath = args[0];
  final target = File(targetPath);
  final targetDir = Directory(targetPath);
  
  final checker = LoggingCodeReviewChecker();
  
  if (await target.exists()) {
    // 检查单个文件
    await checker.checkFile(target);
  } else if (await targetDir.exists()) {
    // 检查目录下的所有Dart文件
    await checker.checkDirectory(targetDir);
  } else {
    print('❌ 路径不存在: $targetPath');
    return;
  }
  
  // 生成检查报告
  checker.generateReport();
}

/// 日志代码审查检查器
class LoggingCodeReviewChecker {
  final List<CodeIssue> _issues = [];
  final Map<String, int> _statistics = {
    'total_files': 0,
    'files_with_issues': 0,
    'total_issues': 0,
    'critical_issues': 0,
    'warning_issues': 0,
    'info_issues': 0,
  };
  
  /// 检查目录
  Future<void> checkDirectory(Directory dir) async {
    await for (final entity in dir.list(recursive: true)) {
      if (entity is File && entity.path.endsWith('.dart')) {
        await checkFile(entity);
      }
    }
  }
  
  /// 检查单个文件
  Future<void> checkFile(File file) async {
    try {
      final content = await file.readAsString();
      _statistics['total_files'] = (_statistics['total_files']! + 1);
      
      final fileIssues = <CodeIssue>[];
      
      // 检查各种日志使用问题
      fileIssues.addAll(_checkOldLoggerUsage(file.path, content));
      fileIssues.addAll(_checkPrintStatements(file.path, content));
      fileIssues.addAll(_checkSensitiveInformation(file.path, content));
      fileIssues.addAll(_checkLogLevelUsage(file.path, content));
      fileIssues.addAll(_checkMetadataUsage(file.path, content));
      fileIssues.addAll(_checkErrorHandling(file.path, content));
      
      if (fileIssues.isNotEmpty) {
        _statistics['files_with_issues'] = (_statistics['files_with_issues']! + 1);
        _issues.addAll(fileIssues);
        
        print('📄 ${file.path}');
        for (final issue in fileIssues) {
          print('  ${_getIssueIcon(issue.severity)} 第${issue.line}行: ${issue.message}');
          if (issue.suggestion != null) {
            print('    💡 建议: ${issue.suggestion}');
          }
        }
        print('');
      }
      
    } catch (e) {
      print('❌ 无法读取文件: ${file.path} - $e');
    }
  }
  
  /// 检查旧的日志使用方式
  List<CodeIssue> _checkOldLoggerUsage(String filePath, String content) {
    final issues = <CodeIssue>[];
    final lines = content.split('\n');
    
    for (int i = 0; i < lines.length; i++) {
      final line = lines[i];
      
      // 检查AppLogger.logModule使用
      if (line.contains('AppLogger.logModule')) {
        issues.add(CodeIssue(
          filePath: filePath,
          line: i + 1,
          severity: IssueSeverity.critical,
          type: IssueType.oldLoggerUsage,
          message: '使用了旧的AppLogger.logModule方式',
          suggestion: '请使用对应模块的LoggerUtils类，如MarketLoggerUtils.info()',
        ));
      }
      
      // 检查developer.log使用
      if (line.contains('developer.log')) {
        issues.add(CodeIssue(
          filePath: filePath,
          line: i + 1,
          severity: IssueSeverity.warning,
          type: IssueType.oldLoggerUsage,
          message: '使用了developer.log',
          suggestion: '请使用对应模块的LoggerUtils类',
        ));
      }
    }
    
    return issues;
  }
  
  /// 检查print语句
  List<CodeIssue> _checkPrintStatements(String filePath, String content) {
    final issues = <CodeIssue>[];
    final lines = content.split('\n');
    
    for (int i = 0; i < lines.length; i++) {
      final line = lines[i].trim();
      
      // 跳过注释行
      if (line.startsWith('//') || line.startsWith('*') || line.startsWith('/*')) {
        continue;
      }
      
      // 检查print语句（排除字符串中的print）
      if (RegExp(r'\bprint\s*\(').hasMatch(line)) {
        // 检查是否在测试文件中
        final isTestFile = filePath.contains('test/') || filePath.endsWith('_test.dart');
        
        issues.add(CodeIssue(
          filePath: filePath,
          line: i + 1,
          severity: isTestFile ? IssueSeverity.info : IssueSeverity.warning,
          type: IssueType.printStatement,
          message: isTestFile ? '测试文件中使用了print语句' : '业务代码中使用了print语句',
          suggestion: isTestFile ? '测试文件中的print可以保留' : '请使用对应模块的LoggerUtils类',
        ));
      }
    }
    
    return issues;
  }
  
  /// 检查敏感信息
  List<CodeIssue> _checkSensitiveInformation(String filePath, String content) {
    final issues = <CodeIssue>[];
    final lines = content.split('\n');
    
    final sensitivePatterns = [
      RegExp(r'["\']password["\']', caseSensitive: false),
      RegExp(r'["\']token["\']', caseSensitive: false),
      RegExp(r'["\']secret["\']', caseSensitive: false),
      RegExp(r'["\']api_key["\']', caseSensitive: false),
      RegExp(r'["\']auth["\']', caseSensitive: false),
    ];
    
    for (int i = 0; i < lines.length; i++) {
      final line = lines[i];
      
      // 跳过注释和常量定义
      if (line.trim().startsWith('//') || line.contains('const') || line.contains('static final')) {
        continue;
      }
      
      for (final pattern in sensitivePatterns) {
        if (pattern.hasMatch(line) && line.contains('LoggerUtils')) {
          issues.add(CodeIssue(
            filePath: filePath,
            line: i + 1,
            severity: IssueSeverity.critical,
            type: IssueType.sensitiveInfo,
            message: '可能在日志中记录了敏感信息',
            suggestion: '请确保敏感信息已被过滤，或使用LogConfigManager.filterSensitiveData()',
          ));
        }
      }
    }
    
    return issues;
  }
  
  /// 检查日志级别使用
  List<CodeIssue> _checkLogLevelUsage(String filePath, String content) {
    final issues = <CodeIssue>[];
    final lines = content.split('\n');
    
    for (int i = 0; i < lines.length; i++) {
      final line = lines[i];
      
      // 检查是否在循环中使用了过多的日志
      if ((line.contains('for (') || line.contains('while (')) && 
          i + 10 < lines.length) {
        
        bool hasLoggingInLoop = false;
        for (int j = i + 1; j < i + 10 && j < lines.length; j++) {
          if (lines[j].contains('LoggerUtils.')) {
            hasLoggingInLoop = true;
            break;
          }
        }
        
        if (hasLoggingInLoop) {
          issues.add(CodeIssue(
            filePath: filePath,
            line: i + 1,
            severity: IssueSeverity.warning,
            type: IssueType.performanceIssue,
            message: '在循环中使用了日志记录',
            suggestion: '考虑使用批量日志记录或条件日志记录以避免性能问题',
          ));
        }
      }
    }
    
    return issues;
  }
  
  /// 检查元数据使用
  List<CodeIssue> _checkMetadataUsage(String filePath, String content) {
    final issues = <CodeIssue>[];
    final lines = content.split('\n');
    
    for (int i = 0; i < lines.length; i++) {
      final line = lines[i];
      
      // 检查LoggerUtils调用是否包含元数据
      if (line.contains('LoggerUtils.info(') || line.contains('LoggerUtils.error(')) {
        // 简单检查是否包含metadata参数
        if (!line.contains('metadata:') && !line.contains('{')) {
          // 检查下一行是否有metadata
          bool hasMetadata = false;
          for (int j = i + 1; j < i + 5 && j < lines.length; j++) {
            if (lines[j].contains('metadata:') || lines[j].contains('});')) {
              hasMetadata = true;
              break;
            }
          }
          
          if (!hasMetadata) {
            issues.add(CodeIssue(
              filePath: filePath,
              line: i + 1,
              severity: IssueSeverity.info,
              type: IssueType.missingMetadata,
              message: '日志调用可能缺少元数据',
              suggestion: '考虑添加相关的元数据信息以便调试和分析',
            ));
          }
        }
      }
    }
    
    return issues;
  }
  
  /// 检查错误处理
  List<CodeIssue> _checkErrorHandling(String filePath, String content) {
    final issues = <CodeIssue>[];
    final lines = content.split('\n');
    
    for (int i = 0; i < lines.length; i++) {
      final line = lines[i].trim();
      
      // 检查catch块是否有适当的日志记录
      if (line.startsWith('} catch (')) {
        bool hasErrorLogging = false;
        
        // 检查catch块中是否有错误日志
        for (int j = i + 1; j < lines.length; j++) {
          final catchLine = lines[j].trim();
          
          if (catchLine.startsWith('}')) {
            break;
          }
          
          if (catchLine.contains('LoggerUtils.error(') || 
              catchLine.contains('LoggerUtils.warning(')) {
            hasErrorLogging = true;
            break;
          }
        }
        
        if (!hasErrorLogging) {
          issues.add(CodeIssue(
            filePath: filePath,
            line: i + 1,
            severity: IssueSeverity.warning,
            type: IssueType.missingErrorLog,
            message: 'catch块中缺少错误日志记录',
            suggestion: '建议在catch块中添加适当的错误日志记录',
          ));
        }
      }
    }
    
    return issues;
  }
  
  /// 生成检查报告
  void generateReport() {
    print('📊 日志代码审查报告');
    print('=' * 50);
    
    // 统计信息
    _statistics['total_issues'] = _issues.length;
    for (final issue in _issues) {
      switch (issue.severity) {
        case IssueSeverity.critical:
          _statistics['critical_issues'] = (_statistics['critical_issues']! + 1);
          break;
        case IssueSeverity.warning:
          _statistics['warning_issues'] = (_statistics['warning_issues']! + 1);
          break;
        case IssueSeverity.info:
          _statistics['info_issues'] = (_statistics['info_issues']! + 1);
          break;
      }
    }
    
    print('📈 统计信息:');
    print('  • 检查文件数: ${_statistics['total_files']}');
    print('  • 有问题文件数: ${_statistics['files_with_issues']}');
    print('  • 总问题数: ${_statistics['total_issues']}');
    print('  • 严重问题: ${_statistics['critical_issues']}');
    print('  • 警告问题: ${_statistics['warning_issues']}');
    print('  • 信息问题: ${_statistics['info_issues']}');
    print('');
    
    // 问题分类统计
    final issuesByType = <IssueType, int>{};
    for (final issue in _issues) {
      issuesByType[issue.type] = (issuesByType[issue.type] ?? 0) + 1;
    }
    
    if (issuesByType.isNotEmpty) {
      print('📋 问题分类:');
      issuesByType.forEach((type, count) {
        print('  • ${_getIssueTypeName(type)}: $count');
      });
      print('');
    }
    
    // 建议
    if (_statistics['total_issues']! > 0) {
      print('💡 改进建议:');
      
      if (_statistics['critical_issues']! > 0) {
        print('  🚨 请优先处理严重问题，这些问题可能影响系统安全性或稳定性');
      }
      
      if (issuesByType.containsKey(IssueType.oldLoggerUsage)) {
        print('  🔄 建议将旧的日志调用方式迁移到新的模块化日志系统');
      }
      
      if (issuesByType.containsKey(IssueType.printStatement)) {
        print('  📝 建议将print语句替换为正式的日志调用');
      }
      
      print('  📚 请参考团队日志编写规范: docs/LOGGING_STANDARDS.md');
    } else {
      print('🎉 恭喜！没有发现日志相关问题，代码质量良好！');
    }
    
    print('');
    print('检查完成时间: ${DateTime.now()}');
  }
  
  /// 获取问题严重程度图标
  String _getIssueIcon(IssueSeverity severity) {
    switch (severity) {
      case IssueSeverity.critical:
        return '🚨';
      case IssueSeverity.warning:
        return '⚠️';
      case IssueSeverity.info:
        return 'ℹ️';
    }
  }
  
  /// 获取问题类型名称
  String _getIssueTypeName(IssueType type) {
    switch (type) {
      case IssueType.oldLoggerUsage:
        return '旧日志使用方式';
      case IssueType.printStatement:
        return 'print语句使用';
      case IssueType.sensitiveInfo:
        return '敏感信息泄露';
      case IssueType.performanceIssue:
        return '性能问题';
      case IssueType.missingMetadata:
        return '缺少元数据';
      case IssueType.missingErrorLog:
        return '缺少错误日志';
    }
  }
}

/// 代码问题
class CodeIssue {
  final String filePath;
  final int line;
  final IssueSeverity severity;
  final IssueType type;
  final String message;
  final String? suggestion;
  
  CodeIssue({
    required this.filePath,
    required this.line,
    required this.severity,
    required this.type,
    required this.message,
    this.suggestion,
  });
}

/// 问题严重程度
enum IssueSeverity {
  critical,
  warning,
  info,
}

/// 问题类型
enum IssueType {
  oldLoggerUsage,
  printStatement,
  sensitiveInfo,
  performanceIssue,
  missingMetadata,
  missingErrorLog,
}
