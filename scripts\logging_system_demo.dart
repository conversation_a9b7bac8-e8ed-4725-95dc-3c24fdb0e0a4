import 'dart:io';
import 'dart:convert';

/// 日志系统功能演示脚本
///
/// 展示新日志系统的各种功能和使用方法
Future<void> main() async {
  print('🎯 日志系统功能演示');
  print('=' * 50);

  await _demonstrateBasicLogging();
  await _demonstrateConfigManagement();
  await _demonstrateAggregationAndAnalysis();
  await _demonstrateMonitoringAndAlerts();
  await _demonstrateCodeReviewTools();

  print('\n🎉 演示完成！');
  print('📚 更多信息请查看:');
  print('  • 使用规范: docs/LOGGING_STANDARDS.md');
  print('  • 最佳实践: docs/LOGGING_BEST_PRACTICES.md');
  print('  • 培训指南: docs/LOGGING_TRAINING_GUIDE.md');
  print('  • 优化报告: LOGGING_OPTIMIZATION_REPORT.md');
}

/// 演示基础日志记录
Future<void> _demonstrateBasicLogging() async {
  print('\n📝 1. 基础日志记录演示');
  print('-' * 30);

  print('✅ 模块化日志调用示例:');
  print('''
// 市场模块日志
MarketLoggerUtils.info('📊 股票数据获取成功', metadata: {
  'symbols': ['AAPL', 'GOOGL'],
  'count': 2,
  'response_time_ms': 150,
});

// 认证模块日志
AuthLoggerUtils.logLogin(
  username: 'user123',
  success: true,
  loginMethod: 'password',
);

// 交易模块日志
TradeLoggerUtils.error('❌ 订单创建失败', 
  error: exception,
  metadata: {
    'order_id': 'ORD-001',
    'symbol': 'AAPL',
  },
);
''');

  print('✅ 专业业务方法示例:');
  print('''
// 使用专门的业务日志方法
AuthLoggerUtils.logLogin(username: 'user', success: true);
MarketLoggerUtils.logStockDataFetch(symbols: ['AAPL'], success: true);
TradeLoggerUtils.logOrderCreated(orderId: 'ORD-001', success: true);
''');
}

/// 演示配置管理
Future<void> _demonstrateConfigManagement() async {
  print('\n⚙️ 2. 配置管理演示');
  print('-' * 30);

  print('✅ 动态配置切换:');
  print('''
// 切换到开发模式（显示所有日志）
await LogConfigManager.switchToDevelopmentMode();

// 切换到简洁模式（隐藏调试日志）
await LogConfigManager.switchToConciseMode();

// 切换到生产模式（最小日志输出）
await LogConfigManager.switchToProductionMode();

// 查看当前配置状态
LogConfigManager.printConfigStatus();
''');

  print('✅ 敏感信息过滤:');
  print('''
// 自动过滤敏感信息
final filteredData = LogConfigManager.filterSensitiveData({
  'username': 'user123',
  'password': 'secret123',  // 会被过滤为 ***FILTERED***
  'token': 'abc123',        // 会被过滤为 ***FILTERED***
  'balance': 1000.0,        // 正常显示
});

// 添加自定义敏感关键词
LogConfigManager.addSensitiveKey('custom_secret');
''');

  print('✅ 性能统计:');
  print('''
// 查看日志统计信息
LogConfigManager.printLogStatistics();

// 获取详细统计数据
final stats = LogConfigManager.getLogStatistics();
print('总日志数: \${stats['total_logs']}');
print('错误率: \${stats['error_rate']}');
''');
}

/// 演示聚合和分析
Future<void> _demonstrateAggregationAndAnalysis() async {
  print('\n📊 3. 日志聚合和分析演示');
  print('-' * 30);

  print('✅ 日志聚合:');
  print('''
final aggregator = LogAggregator.instance;

// 添加日志记录（通常由日志系统自动调用）
aggregator.addLogRecord(AppLogRecord(
  timestamp: DateTime.now(),
  module: 'Market',
  level: LogLevel.info,
  message: '股票数据获取成功',
  metadata: {'symbols': ['AAPL'], 'count': 1},
));
''');

  print('✅ 性能分析:');
  print('''
// 获取性能报告
final perfReport = aggregator.getPerformanceReport();
print('总日志数: \${perfReport['summary']['total_logs']}');
print('错误率: \${perfReport['summary']['error_rate']}');
print('平均响应时间: \${perfReport['summary']['average_duration']}');

// 按模块查看性能
final modules = perfReport['modules'];
modules.forEach((module, metrics) {
  print('模块 \$module: \${metrics['total_logs']} 条日志');
});
''');

  print('✅ 错误分析:');
  print('''
// 获取错误分析报告
final errorReport = aggregator.getErrorAnalysisReport();
final errorAnalysis = errorReport['error_analysis'];

print('错误模式数: \${errorAnalysis['total_error_patterns']}');
print('总错误数: \${errorAnalysis['total_errors']}');

// 查看最频繁的错误
final topErrors = errorAnalysis['top_errors'];
for (final error in topErrors) {
  print('错误: \${error['module']}:\${error['category']} - \${error['count']} 次');
}
''');

  print('✅ 日志导出:');
  print('''
// 导出为JSON格式
final jsonLogs = await aggregator.exportLogs(
  format: 'json',
  startTime: DateTime.now().subtract(Duration(hours: 24)),
  modules: ['Market', 'Trade'],
  levels: [LogLevel.error, LogLevel.warning],
);

// 导出为CSV格式
final csvLogs = await aggregator.exportLogs(
  format: 'csv',
  startTime: DateTime.now().subtract(Duration(hours: 1)),
);

// 导出为文本格式
final textLogs = await aggregator.exportLogs(format: 'txt');
''');
}

/// 演示监控和告警
Future<void> _demonstrateMonitoringAndAlerts() async {
  print('\n🔍 4. 监控和告警演示');
  print('-' * 30);

  print('✅ 启动监控:');
  print('''
final monitor = LogMonitoringDashboard.instance;

// 添加告警回调
monitor.addAlertCallback((alert) {
  print('🚨 收到告警: \${alert.message}');
  print('   严重程度: \${alert.severity}');
  print('   类型: \${alert.type}');
  
  // 可以发送邮件、推送通知等
  // emailService.sendAlert(alert);
  // pushService.sendNotification(alert);
});

// 设置告警阈值
monitor.setThreshold('error_rate_threshold', 5.0);  // 错误率5%
monitor.setThreshold('response_time_threshold', 1000);  // 响应时间1秒

// 开始监控
monitor.startMonitoring();
''');

  print('✅ 监控状态:');
  print('''
// 查看监控状态
final status = monitor.getMonitoringStatus();
print('监控状态: \${status['is_monitoring']}');
print('告警阈值: \${status['thresholds']}');
print('回调数量: \${status['alert_callbacks_count']}');

// 查看缓存状态
final bufferStatus = LogAggregator.instance.getBufferStatus();
print('缓存使用率: \${bufferStatus['buffer_usage']}');
print('性能指标数: \${bufferStatus['performance_metrics_count']}');
''');
}

/// 演示代码审查工具
Future<void> _demonstrateCodeReviewTools() async {
  print('\n🔧 5. 代码审查工具演示');
  print('-' * 30);

  print('✅ 使用代码审查检查工具:');
  print('''
# 检查单个文件
dart scripts/logging_code_review_checker.dart lib/services/market_service.dart

# 检查整个模块
dart scripts/logging_code_review_checker.dart packages/financial_app_market/lib

# 检查所有模块
dart scripts/logging_code_review_checker.dart packages
''');

  print('✅ 检查报告示例:');
  print('''
📊 日志代码审查报告
==================================================
📈 统计信息:
  • 检查文件数: 25
  • 有问题文件数: 3
  • 总问题数: 8
  • 严重问题: 2
  • 警告问题: 4
  • 信息问题: 2

📋 问题分类:
  • 旧日志使用方式: 2
  • print语句使用: 3
  • 缺少元数据: 2
  • 缺少错误日志: 1

💡 改进建议:
  🚨 请优先处理严重问题，这些问题可能影响系统安全性或稳定性
  🔄 建议将旧的日志调用方式迁移到新的模块化日志系统
  📝 建议将print语句替换为正式的日志调用
  📚 请参考团队日志编写规范: docs/LOGGING_STANDARDS.md
''');

  print('✅ 日志迁移验证:');
  print('''
# 验证日志迁移情况
dart scripts/verify_logger_migration.dart

# 输出示例:
🔍 开始验证日志系统迁移情况...

📦 检查模块: financial_app_market
  ✅ 已有专用Logger: MarketLogger
  🎉 日志迁移完成！

📦 检查模块: financial_app_auth
  ✅ 已有专用Logger: AuthLogger
  🎉 日志迁移完成！

📊 日志迁移总结报告
==================================================
📈 迁移进度: 8/8 个模块已完成迁移
📊 迁移完成率: 100.0%

🎉 恭喜！所有模块的日志系统迁移已完成！
✨ 代码质量: 安全、稳定、高效
''');
}
