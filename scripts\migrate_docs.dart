#!/usr/bin/env dart

import 'dart:io';
import 'dart:convert';

/// 文档迁移工具
/// 
/// 将项目中散落的各种文档整理到统一的documentation目录中
class DocumentMigrator {
  static const String sourceDir = '.';
  static const String targetDir = 'documentation';
  static const String archiveDir = 'documentation/archive';
  
  /// 文档分类映射
  static const Map<String, String> categoryMapping = {
    // 用户指南
    'USAGE_EXAMPLES.md': 'user-guide/usage-examples.md',
    'QUICK_REFERENCE.md': 'user-guide/quick-reference.md',
    'DEV_QUICK_REFERENCE.md': 'user-guide/dev-quick-reference.md',
    
    // 开发者指南
    'DEVELOPER_GUIDE.md': 'developer-guide/developer-guide.md',
    'DEVELOPER_QUICK_START_GUIDE.md': 'developer-guide/quick-start-guide.md',
    'DEVELOPMENT_TOOLS_GUIDE.md': 'developer-guide/development-tools.md',
    'TOOLS_AND_SCRIPTS_GUIDE.md': 'developer-guide/tools-and-scripts.md',
    'DEVELOPMENT_EXPERIENCE_OPTIMIZATION.md': 'developer-guide/experience-optimization.md',
    
    // 系统设计
    'DETAILED_ARCHITECTURE_GUIDE.md': 'system-design/detailed-architecture.md',
    'ARCHITECTURE_STANDARDS.md': 'system-design/architecture-standards.md',
    'MODULE_STRUCTURE_ANALYSIS.md': 'system-design/module-structure.md',
    
    // API参考
    'GLOBAL_WEBSOCKET_USAGE_GUIDE.md': 'api-reference/websocket-usage.md',
    
    // 部署运维
    'BUILD_OPTIMIZATION_REPORT.md': 'deployment/build-optimization.md',
    'PERFORMANCE_OPTIMIZATION_REPORT.md': 'deployment/performance-optimization.md',
    
    // 架构文档
    'ARCHITECTURE_DECISION_RECORD.md': 'architecture/decision-record.md',
    'ARCHITECTURE_DECISION_RECORDS.md': 'architecture/decision-records.md',
    'PROVIDER_TO_BLOC_MIGRATION_SUMMARY.md': 'architecture/bloc-migration.md',
    'BLOC_MIGRATION_COMPLETE_REPORT.md': 'architecture/bloc-migration-complete.md',
    
    // 教程示例
    'KLINE_TRADING_PAGE_OPTIMIZATION_REPORT.md': 'tutorials/kline-trading-optimization.md',
    'TRADING_CHART_OPTIMIZATION_REPORT.md': 'tutorials/trading-chart-optimization.md',
    'WEBSOCKET_OPTIMIZATION_REPORT.md': 'tutorials/websocket-optimization.md',
    'ASSETS_MODULE_OPTIMIZATION_REPORT.md': 'tutorials/assets-optimization.md',
    
    // 故障排查
    'TEST_COVERAGE_REPORT.md': 'troubleshooting/test-coverage.md',
    'CODE_QUALITY_REPORT.md': 'troubleshooting/code-quality.md',
    'DEPENDENCY_OPTIMIZATION_REPORT.md': 'troubleshooting/dependency-optimization.md',
    
    // 变更记录
    'PROJECT_OPTIMIZATION_COMPLETE_REPORT.md': 'changelog/project-optimization.md',
    'ALL_MODULES_OPTIMIZATION_COMPLETE.md': 'changelog/modules-optimization.md',
    'MODULE_OPTIMIZATION_SUMMARY.md': 'changelog/module-optimization-summary.md',
    'DOCUMENTATION_COMPLETION_REPORT.md': 'changelog/documentation-completion.md',
    'DOCUMENTATION_SUMMARY.md': 'changelog/documentation-summary.md',
    'PROJECT_SUMMARY.md': 'changelog/project-summary.md',
  };
  
  /// 需要归档的旧文档
  static const List<String> archiveFiles = [
    'README_TOOLS.md',
  ];
  
  static Future<void> main(List<String> args) async {
    print('📚 开始文档迁移...');
    
    final migrator = DocumentMigrator();
    
    try {
      // 1. 创建目标目录结构
      await migrator._createDirectories();
      
      // 2. 迁移根目录文档
      await migrator._migrateRootDocuments();
      
      // 3. 迁移docs目录文档
      await migrator._migrateDocsDirectory();
      
      // 4. 归档旧文档
      await migrator._archiveOldDocuments();
      
      // 5. 生成迁移报告
      await migrator._generateMigrationReport();
      
      print('✅ 文档迁移完成！');
      
    } catch (e) {
      print('❌ 文档迁移失败: $e');
      exit(1);
    }
  }
  
  /// 创建目标目录结构
  Future<void> _createDirectories() async {
    print('📁 创建目录结构...');
    
    final directories = [
      '$targetDir/user-guide',
      '$targetDir/developer-guide', 
      '$targetDir/system-design',
      '$targetDir/api-reference',
      '$targetDir/deployment',
      '$targetDir/architecture',
      '$targetDir/tutorials',
      '$targetDir/troubleshooting',
      '$targetDir/changelog',
      '$targetDir/assets',
      archiveDir,
    ];
    
    for (final dir in directories) {
      await Directory(dir).create(recursive: true);
      print('  ✓ 创建目录: $dir');
    }
  }
  
  /// 迁移根目录文档
  Future<void> _migrateRootDocuments() async {
    print('📄 迁移根目录文档...');
    
    final rootDir = Directory(sourceDir);
    final files = await rootDir.list().where((entity) => 
        entity is File && entity.path.endsWith('.md')).toList();
    
    for (final file in files) {
      final fileName = file.path.split(Platform.pathSeparator).last;
      
      // 跳过README.md
      if (fileName == 'README.md') continue;
      
      if (categoryMapping.containsKey(fileName)) {
        final targetPath = '$targetDir/${categoryMapping[fileName]!}';
        await _migrateFile(file.path, targetPath, fileName);
      } else if (archiveFiles.contains(fileName)) {
        final targetPath = '$archiveDir/$fileName';
        await _migrateFile(file.path, targetPath, fileName);
      } else {
        // 未分类的文档移动到archive
        final targetPath = '$archiveDir/$fileName';
        await _migrateFile(file.path, targetPath, fileName);
        print('  ⚠️ 未分类文档已归档: $fileName');
      }
    }
  }
  
  /// 迁移docs目录文档
  Future<void> _migrateDocsDirectory() async {
    print('📂 迁移docs目录文档...');
    
    final docsDir = Directory('docs');
    if (!await docsDir.exists()) {
      print('  ℹ️ docs目录不存在，跳过');
      return;
    }
    
    // 迁移docs目录下的文档到archive
    await _migrateDirectoryToArchive('docs', 'old-docs');
  }
  
  /// 将整个目录迁移到归档
  Future<void> _migrateDirectoryToArchive(String sourceDir, String archiveSubDir) async {
    final source = Directory(sourceDir);
    final target = Directory('$archiveDir/$archiveSubDir');
    
    if (!await source.exists()) return;
    
    await target.create(recursive: true);
    
    await for (final entity in source.list(recursive: true)) {
      if (entity is File) {
        final relativePath = entity.path.substring(sourceDir.length + 1);
        final targetPath = '${target.path}/$relativePath';
        
        // 确保目标目录存在
        final targetFile = File(targetPath);
        await targetFile.parent.create(recursive: true);
        
        // 复制文件
        await entity.copy(targetPath);
        print('  ✓ 归档: $relativePath');
      }
    }
  }
  
  /// 归档旧文档
  Future<void> _archiveOldDocuments() async {
    print('🗃️ 归档旧文档...');
    
    // 这里可以添加其他需要归档的文档处理逻辑
    print('  ✓ 旧文档归档完成');
  }
  
  /// 迁移单个文件
  Future<void> _migrateFile(String sourcePath, String targetPath, String fileName) async {
    try {
      final sourceFile = File(sourcePath);
      final targetFile = File(targetPath);
      
      // 确保目标目录存在
      await targetFile.parent.create(recursive: true);
      
      // 读取源文件内容
      final content = await sourceFile.readAsString();
      
      // 添加迁移说明
      final migratedContent = _addMigrationHeader(content, fileName);
      
      // 写入目标文件
      await targetFile.writeAsString(migratedContent);
      
      print('  ✓ 迁移: $fileName -> $targetPath');
      
    } catch (e) {
      print('  ❌ 迁移失败: $fileName - $e');
    }
  }
  
  /// 添加迁移说明头部
  String _addMigrationHeader(String content, String originalFileName) {
    final header = '''
> **📋 文档迁移说明**
> 
> 本文档已从项目根目录迁移到统一的文档管理系统中。
> - **原文件名**: $originalFileName
> - **迁移时间**: ${DateTime.now().toIso8601String()}
> - **迁移工具**: scripts/migrate_docs.dart
> 
> 如需查看最新的文档结构，请参考 [文档中心](../README.md)。

---

''';
    
    return header + content;
  }
  
  /// 生成迁移报告
  Future<void> _generateMigrationReport() async {
    print('📊 生成迁移报告...');
    
    final report = '''
# 📚 文档迁移报告

## 🎯 迁移概述

本次文档迁移将项目中散落的各种文档整理到统一的 `documentation` 目录中，建立了清晰的文档分类体系。

## 📊 迁移统计

### 📁 目录结构
- **用户指南** (user-guide): ${_countFiles('$targetDir/user-guide')} 个文档
- **开发者指南** (developer-guide): ${_countFiles('$targetDir/developer-guide')} 个文档  
- **系统设计** (system-design): ${_countFiles('$targetDir/system-design')} 个文档
- **API参考** (api-reference): ${_countFiles('$targetDir/api-reference')} 个文档
- **部署运维** (deployment): ${_countFiles('$targetDir/deployment')} 个文档
- **架构文档** (architecture): ${_countFiles('$targetDir/architecture')} 个文档
- **教程示例** (tutorials): ${_countFiles('$targetDir/tutorials')} 个文档
- **故障排查** (troubleshooting): ${_countFiles('$targetDir/troubleshooting')} 个文档
- **变更记录** (changelog): ${_countFiles('$targetDir/changelog')} 个文档
- **归档文档** (archive): ${_countFiles(archiveDir)} 个文档

### 📋 迁移详情

#### 用户指南文档
${_generateCategoryReport('user-guide')}

#### 开发者指南文档  
${_generateCategoryReport('developer-guide')}

#### 系统设计文档
${_generateCategoryReport('system-design')}

#### API参考文档
${_generateCategoryReport('api-reference')}

#### 部署运维文档
${_generateCategoryReport('deployment')}

#### 架构文档
${_generateCategoryReport('architecture')}

#### 教程示例文档
${_generateCategoryReport('tutorials')}

#### 故障排查文档
${_generateCategoryReport('troubleshooting')}

#### 变更记录文档
${_generateCategoryReport('changelog')}

## 🔄 后续维护

### 📝 文档更新流程
1. 所有新文档应直接创建在 `documentation` 目录的相应分类中
2. 更新现有文档时，请在对应的新位置进行修改
3. 定期检查和更新文档内容，确保信息的准确性

### 🗃️ 归档文档处理
- 归档文档保存在 `documentation/archive` 目录中
- 这些文档仅作为历史参考，不建议继续使用
- 如需要其中的信息，建议整合到新的文档体系中

## ✅ 迁移完成

文档迁移已成功完成！新的文档结构更加清晰、易于维护和查找。

**迁移时间**: ${DateTime.now().toIso8601String()}
**迁移工具**: scripts/migrate_docs.dart
''';
    
    final reportFile = File('$targetDir/MIGRATION_REPORT.md');
    await reportFile.writeAsString(report);
    
    print('  ✓ 迁移报告已生成: ${reportFile.path}');
  }
  
  /// 统计目录下的文件数量
  int _countFiles(String dirPath) {
    try {
      final dir = Directory(dirPath);
      if (!dir.existsSync()) return 0;
      
      return dir.listSync(recursive: true)
          .where((entity) => entity is File && entity.path.endsWith('.md'))
          .length;
    } catch (e) {
      return 0;
    }
  }
  
  /// 生成分类报告
  String _generateCategoryReport(String category) {
    final categoryMappings = categoryMapping.entries
        .where((entry) => entry.value.startsWith('$category/'))
        .toList();
    
    if (categoryMappings.isEmpty) {
      return '- 暂无文档';
    }
    
    return categoryMappings
        .map((entry) => '- ${entry.key} → ${entry.value}')
        .join('\n');
  }
}

void main(List<String> args) async {
  await DocumentMigrator.main(args);
}
