import 'dart:io';

/// 迁移financial_app_market模块的日志调用
/// 将AppLogger.logModule调用替换为MarketLoggerUtils调用
Future<void> main() async {
  print('🔄 开始迁移financial_app_market模块的日志调用...');

  final marketBlocFile = File(
      'packages/financial_app_market/lib/src/presentation/bloc/market_bloc.dart');

  if (!marketBlocFile.existsSync()) {
    print('❌ 文件不存在: ${marketBlocFile.path}');
    return;
  }

  String content = await marketBlocFile.readAsString();
  int replacementCount = 0;

  // 替换所有的AppLogger.logModule调用
  content = content.replaceAllMapped(
    RegExp(
        r"AppLogger\.logModule\(\s*'MarketBloc',\s*LogLevel\.(\w+),\s*([^,]+),\s*(.*?)\);",
        multiLine: true,
        dotAll: true),
    (match) {
      final logLevel = match.group(1)!;
      final message = match.group(2)!;
      final rest = match.group(3)!;

      replacementCount++;

      // 根据日志级别选择对应的方法
      String methodName;
      switch (logLevel) {
        case 'debug':
          methodName = 'debug';
          break;
        case 'info':
          methodName = 'info';
          break;
        case 'warning':
          methodName = 'warning';
          break;
        case 'error':
          methodName = 'error';
          break;
        default:
          methodName = 'info';
      }

      // 构建新的调用
      if (rest.trim().isEmpty) {
        return 'MarketLoggerUtils.$methodName($message);';
      } else {
        return 'MarketLoggerUtils.$methodName($message, $rest);';
      }
    },
  );

  // 写回文件
  await marketBlocFile.writeAsString(content);

  print('✅ 迁移完成！');
  print('📊 替换了 $replacementCount 个AppLogger.logModule调用');
  print('🎯 所有调用已替换为MarketLoggerUtils方法');
}
