import 'dart:io';

/// 验证所有模块的日志迁移情况
/// 检查是否还有旧的日志调用需要迁移
Future<void> main() async {
  print('🔍 开始验证日志系统迁移情况...\n');

  final packagesDir = Directory('packages');
  if (!packagesDir.existsSync()) {
    print('❌ packages目录不存在');
    return;
  }

  final migrationReport = <String, Map<String, dynamic>>{};

  // 检查所有financial_app_开头的模块
  await for (final entity in packagesDir.list()) {
    if (entity is Directory) {
      final moduleName = entity.path.split(Platform.pathSeparator).last;
      if (moduleName.startsWith('financial_app_') ||
          moduleName.startsWith('financial_')) {
        print('📦 检查模块: $moduleName');
        final moduleReport = await analyzeModule(entity, moduleName);
        migrationReport[moduleName] = moduleReport;

        // 打印模块报告
        printModuleReport(moduleName, moduleReport);
        print('');
      }
    }
  }

  // 生成总结报告
  printSummaryReport(migrationReport);
}

/// 分析单个模块的日志使用情况
Future<Map<String, dynamic>> analyzeModule(
    Directory moduleDir, String moduleName) async {
  final report = {
    'hasLogger': false,
    'loggerPath': '',
    'oldLoggerCalls': <String>[],
    'printStatements': <String>[],
    'developerLogs': <String>[],
    'totalFiles': 0,
    'checkedFiles': 0,
  };

  // 检查是否有专用Logger
  final loggerFile = await findLoggerFile(moduleDir, moduleName);
  if (loggerFile != null) {
    report['hasLogger'] = true;
    report['loggerPath'] = loggerFile.path;
  }

  // 扫描所有Dart文件
  await for (final entity in moduleDir.list(recursive: true)) {
    if (entity is File && entity.path.endsWith('.dart')) {
      report['totalFiles'] = (report['totalFiles'] as int) + 1;

      try {
        final content = await entity.readAsString();
        report['checkedFiles'] = (report['checkedFiles'] as int) + 1;

        // 检查旧的AppLogger.logModule调用
        final appLoggerMatches =
            RegExp(r'AppLogger\.logModule\(').allMatches(content);
        final oldCalls = report['oldLoggerCalls'] as List<String>;
        for (final match in appLoggerMatches) {
          final line = _getLineNumber(content, match.start);
          oldCalls.add('${entity.path}:$line');
        }

        // 检查print语句
        final printMatches = RegExp(r'print\(').allMatches(content);
        final prints = report['printStatements'] as List<String>;
        for (final match in printMatches) {
          final line = _getLineNumber(content, match.start);
          prints.add('${entity.path}:$line');
        }

        // 检查developer.log
        final devLogMatches = RegExp(r'developer\.log\(').allMatches(content);
        final devLogs = report['developerLogs'] as List<String>;
        for (final match in devLogMatches) {
          final line = _getLineNumber(content, match.start);
          devLogs.add('${entity.path}:$line');
        }
      } catch (e) {
        // 忽略读取错误的文件
      }
    }
  }

  return report;
}

/// 查找模块的Logger文件
Future<File?> findLoggerFile(Directory moduleDir, String moduleName) async {
  final possiblePaths = [
    '${moduleDir.path}/lib/src/utils/${_getLoggerFileName(moduleName)}',
    '${moduleDir.path}/lib/src/logger/${_getLoggerFileName(moduleName)}',
    '${moduleDir.path}/lib/utils/${_getLoggerFileName(moduleName)}',
  ];

  for (final path in possiblePaths) {
    final file = File(path);
    if (await file.exists()) {
      return file;
    }
  }

  return null;
}

/// 根据模块名生成Logger文件名
String _getLoggerFileName(String moduleName) {
  if (moduleName == 'financial_app_auth') return 'auth_logger.dart';
  if (moduleName == 'financial_app_market') return 'market_logger.dart';
  if (moduleName == 'financial_app_trade') return 'trade_logger.dart';
  if (moduleName == 'financial_app_portfolio') return 'portfolio_logger.dart';
  if (moduleName == 'financial_app_notification')
    return 'notification_logger.dart';
  if (moduleName == 'financial_ws_client') return 'ws_logger.dart';
  if (moduleName == 'financial_trading_chart') return 'chart_logger.dart';
  if (moduleName == 'financial_app_assets') return 'assets_logger.dart';
  if (moduleName == 'financial_app_main') return 'main_logger.dart';
  if (moduleName == 'financial_app_core') return 'app_logger.dart';

  return 'logger.dart';
}

/// 获取字符位置对应的行号
int _getLineNumber(String content, int position) {
  return content.substring(0, position).split('\n').length;
}

/// 打印单个模块的报告
void printModuleReport(String moduleName, Map<String, dynamic> report) {
  final hasLogger = report['hasLogger'] as bool;
  final oldCalls = report['oldLoggerCalls'] as List<String>;
  final prints = report['printStatements'] as List<String>;
  final devLogs = report['developerLogs'] as List<String>;

  if (hasLogger) {
    print('  ✅ 已有专用Logger: ${report['loggerPath']}');
  } else {
    print('  ❌ 缺少专用Logger');
  }

  if (oldCalls.isNotEmpty) {
    print('  ⚠️  发现 ${oldCalls.length} 个旧的AppLogger.logModule调用:');
    for (final call in oldCalls.take(3)) {
      print('     - $call');
    }
    if (oldCalls.length > 3) {
      print('     - ... 还有 ${oldCalls.length - 3} 个');
    }
  }

  if (prints.isNotEmpty) {
    print('  📝 发现 ${prints.length} 个print语句');
  }

  if (devLogs.isNotEmpty) {
    print('  🔧 发现 ${devLogs.length} 个developer.log调用');
  }

  if (hasLogger && oldCalls.isEmpty && prints.isEmpty && devLogs.isEmpty) {
    print('  🎉 日志迁移完成！');
  }
}

/// 打印总结报告
void printSummaryReport(Map<String, Map<String, dynamic>> migrationReport) {
  print('📊 日志迁移总结报告');
  print('=' * 50);

  int totalModules = migrationReport.length;
  int migratedModules = 0;
  int totalOldCalls = 0;
  int totalPrints = 0;
  int totalDevLogs = 0;

  for (final entry in migrationReport.entries) {
    final report = entry.value;
    final hasLogger = report['hasLogger'] as bool;
    final oldCalls = (report['oldLoggerCalls'] as List<String>).length;
    final prints = (report['printStatements'] as List<String>).length;
    final devLogs = (report['developerLogs'] as List<String>).length;

    if (hasLogger && oldCalls == 0) {
      migratedModules++;
    }

    totalOldCalls += oldCalls;
    totalPrints += prints;
    totalDevLogs += devLogs;
  }

  print('📈 迁移进度: $migratedModules/$totalModules 个模块已完成迁移');
  print(
      '📊 迁移完成率: ${(migratedModules / totalModules * 100).toStringAsFixed(1)}%');
  print('');
  print('🔍 发现的问题:');
  print('  - 旧的AppLogger.logModule调用: $totalOldCalls 个');
  print('  - print语句: $totalPrints 个');
  print('  - developer.log调用: $totalDevLogs 个');

  if (totalOldCalls == 0 && totalPrints == 0 && totalDevLogs == 0) {
    print('');
    print('🎉 恭喜！所有模块的日志系统迁移已完成！');
    print('✨ 代码质量: 安全、稳定、高效');
  } else {
    print('');
    print('⚠️  仍有部分日志调用需要迁移');
    print('💡 建议: 继续完成剩余模块的日志迁移工作');
  }
}
